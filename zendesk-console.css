/* Applying global styles to body  */
.su__viewed-results,
.su_page_rating_box,
.su__viewed-results *,
.su__no-view-results *,
#su_autocomplete-block,
#su_autocomplete-block *,
.su__wrapper,
#su__wrapper,
#su__wrapper * {
  font-family: system-ui, -apple-system, 'system-ui', 'Segoe UI', Roboto, Oxygen-Sans, Ubuntu,
    Cantarell, 'Helvetica Neue', Arial, sans-serif;
  box-sizing: border-box;
}
.su__filter-with-lang .su__mob-facetshow > .su__px-3.su__flex-vcenter.su__h-100 {
  padding: 0 4px;
}

.su__filter-with-lang .su__mob-facetshow > .su__px-3.su__flex-vcenter.su__h-100,
.su__filter-with-lang .su__mob-facetshow {
  width: 86px;
}

.su__search-view .su__showing-lable {
  font-size: 0.76rem;
}

.su__result-content .su__content-view .su__list-items {
  padding: 5px;
  margin: 5px 0;
}

.su__result-content .su__search-view {
  margin: 0;
  padding: 0;
}

.su__result-content .su__ribbon-title {
  padding: 2px 6px;
  line-height: normal;
}

.su__result-content .su__list-item-desc {
  line-height: normal;
  margin-top: 8px;
  font-size: 11px;
}

.su__result-content .su__list-item-title a {
  font-size: 13px;
}

::-webkit-scrollbar {
  width: 5px;
  height: 5px;
  background-color: #f1f1f1;
}

::-webkit-scrollbar-track {
  box-shadow: inset 0 0 4px rgba(29, 29, 29, 0.171);
  -webkit-box-shadow: inset 0 0 4px rgba(29, 29, 29, 0.171);
  background-color: #f1f1f1;
  border-radius: 10px;
  -webkit-border-radius: 10px;
  -moz-border-radius: 10px;
  -ms-border-radius: 10px;
  -o-border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background-image: -webkit-gradient(
    linear,
    left bottom,
    left top,
    color-stop(0.44, #505050),
    color-stop(0.72, #707070),
    color-stop(0.86, #383838)
  );
  -webkit-border-radius: 10px;
  -moz-border-radius: 10px;
  -ms-border-radius: 10px;
  -o-border-radius: 10px;
}

.su__thumbnail_align {
  margin-bottom: 8px;
}

@media (max-width: 767px) {
  .su__result-content .su__grid-content .su__list-items:nth-child(odd),
  .su__result-content .su__grid-content .su__list-items:nth-child(even) {
    margin: 5px 0;
  }

  .su__search-facet-input {
    padding: 4px 24px 4px 34px !important;
  }

  .su_letter_space {
    letter-spacing: 0.5px;
  }

  .su__font_bookmark {
    font-size: 26px;
  }

  .su__font_bookmark_text {
    font-size: 12px !important;
  }

  .su__close_bookmark {
    height: 14px;
    width: 14px;
  }
}

@media (max-width: 480px) {
  .su__pagination span span[type='button'] {
    background: #fff none repeat scroll 0 0;
    padding: 2px 8px !important;
    line-height: normal;
    -moz-appearance: none;
    -webkit-appearance: none;
    appearance: none;
    font-size: 12px !important;
    margin-right: 4px !important;
  }

  .su__pagination span span[type='button'].su__pagination-active {
    color: #fff;
    background-color: #1770d4;
    border-color: #1770d4;
  }
}

/* attach to ticket css */
.su__ticketIcon {
  position: absolute;
  box-shadow: 0 0 6px 0 rgba(82, 82, 82, 0.21);
  background-color: #006dcc !important;
  border: solid 1px #d7dbdd;
  white-space: nowrap;
  border-radius: 3px;
  padding: 2px;
  z-index: 1;
}

.su__ticket {
  list-style-type: none;
  padding-left: 0;
  margin-bottom: 0;
}

.su-other-elements-section {
  overflow: hidden;
  font-size: 12px;
  font-weight: 400;
  color: #006efc;
}

.su-attach-case {
  margin-left: 0;
}

.su_ticket_attach {
  margin-left: 0;
  font-size: 12px;
  padding-left: 3px;
  padding-right: 3px;
}

.su-case-attach {
  color: #fff !important;
  font-weight: bold;
}

.su__cursor {
  cursor: pointer;
  outline: none;
}

.su__modal_fullwidth {
  width: 100%;
}

.su__attachTicket {
  position: relative;
  cursor: pointer;
  top: 2px;
}

.su__multiversion_font {
  color: #404040 !important;
}

.su_nested_align {
  margin: 0;
}

.su__slider_btn_position::after,
.su__slider_btn_left::after {
  top: 7px !important;
}

.su__remove-hover-color {
  color: rgba(70, 70, 70, 0.8) !important;
}

.su__nav_align {
  padding: 2px;
}

.su__transition_citation {
  transition: transform 0.5s;
  transform: translate(0, -105%) !important;
}

.su__transition_in {
  transform: translate(0, 4%) !important;
}

.su__center-gpt-widget sup {
  line-height: unset;
}

.su__arrow-down-zendesk {
  right: 0 !important;
}
.su__zendesk-flex-wrap-reverse {
  -ms-flex-wrap: wrap-reverse;
  flex-wrap: wrap-reverse;
}
