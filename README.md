---

To Setup React Search client for local setup, Follow below instructions :-

[React Search Client Local Setup Doc](https://docs.google.com/document/d/1slBuSvrydyE12msFFDn1hGgqk65AlO_aEKOCNFUt0tU/edit#heading=h.snxc2jbcp98)

Breif :

1. Download Search client from desginer tab.
2. Extract folder and open in editor.
3. Open File src > redux > variables.js
4. Update below variable in variables.js file.

- searchClientType ( replace scConfiguration.searchClientTypeId with searchClientType id)
- uid (replace scConfiguration.uid with SC uid)
- Access token (replace scConfiguration.accessToken with access token)
- instanceName (replace scConfiguration.search with cloudfront url)
- Add an.js script in src > index.html or comment sid : \_gr_utility_functions.getCookie("\_gz_taid") property from search and autocomplete
  call variables

5. run --> npm install
6. then open Search client by --> npm start
7. App will be running on 5000 port. (http://localhost:5000/)
