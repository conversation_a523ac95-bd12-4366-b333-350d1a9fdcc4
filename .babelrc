{
  "presets": [
    [
      "@babel/preset-env",
      {
        "useBuiltIns": "usage",
        // "debug":true,
        "corejs": 3
      }
    ],
    [
      "@babel/preset-react",
      {
        "throwIfNamespace": false
      }
    ]
  ],
  "plugins": [
    "@babel/plugin-proposal-class-properties",
    "@babel/plugin-proposal-optional-chaining",
    "@babel/plugin-proposal-nullish-coalescing-operator"
  ]
}
