import Spinner from 'components/section-components/spinner/index';
const { waitForWindowVariable, mounter, MOUNT_POINTS } = require('constants/constants');
const React = require('react');
const { Provider } = require('react-redux');
require('isomorphic-fetch');
require('../src/assets/css/style.css');

const loader = () => mounter(<Spinner isError={false} />, MOUNT_POINTS.searchApp);
loader();

waitForWindowVariable('scConfiguration', 15000)
  .then(() => {
    const configureStore = require('./redux/configureStore').default;
    const App = require('./App').default;
    const store = configureStore();
    mounter(
      <Provider store={store}>
        <App />
      </Provider>,
      MOUNT_POINTS.searchApp
    );
  })
  .catch((error) => {
    console.log(error);
    loader();
  });
// If you want your app to work offline and load faster, you can change
// unregister() to register() below. Note this comes with some pitfalls.
// Learn more about service workers: https://bit.ly/CRA-PWA
