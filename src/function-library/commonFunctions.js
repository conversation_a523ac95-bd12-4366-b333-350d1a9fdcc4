import variables from '../redux/variables';
export const setWildcardToggle = (v) =>
  v
    ? sessionStorage.setItem('toggleWildcardSearch', 'true')
    : sessionStorage.removeItem('toggleWildcardSearch');
export const getWildcardToggle = () => !!sessionStorage.getItem('toggleWildcardSearch');

/**
 * sleep - adds wait time for a specified duration
 * @param {number} t - time in ms
 * @returns {Promise} - promise resolve
 */
export const sleep = async (t) => await new Promise((res) => setTimeout(() => res(), [t]));

export const breakCheck = (effectId, latestEffectIdRef, animatorRefs) => {
  const exit = effectId !== latestEffectIdRef.current;
  exit && animatorRefs.forEach((ref) => clearTimeout(ref));

  return exit;
};

/**
 * Animates text by displaying it with a typing effect
 * @param {string} str - The text to animate
 * @param {string|number} effectId - Unique ID for the current effect
 * @param {React.MutableRefObject} latestEffectIdRef - Ref containing the latest effect ID
 * @param {Array} animatorRefs - Array of timeouts to store the animation reference
 * @param {Function} setTriggers - State setter for loader/printing/etc states
 * @param {Function} setContent - State setter for the content display
 * @param {string} failText - Text to show on failure
 * @param {Function} checkForError - Function to check for errors
 * @param {Array} errorArray - Array of possible error indicators
 */
export const animateText = (
  str,
  effectId,
  latestEffectIdRef,
  animatorRefs,
  setTriggers,
  setContent,
  failText,
  checkForError = null,
  errorArray = null
) => {
  setTriggers((prev) => ({ ...prev, loader: false, printing: true }));
  setContent(() => '');
  variables.gptStreaming = true;
  const printer = async () => {
    setContent(() => str);
    if (str === failText) {
      setTriggers((prevState) => ({ ...prevState, hasError: true }));
    } else if (checkForError && errorArray) {
      checkForError(str, errorArray);
    }
    setTriggers((prev) => ({ ...prev, printing: false }));
  };

  if (breakCheck(effectId, latestEffectIdRef, animatorRefs)) return;
  animatorRefs.forEach((ref) => clearTimeout(ref));
  animatorRefs.push(setTimeout(() => printer()));
};
export const readStream = async (
  responseStream,
  effectId,
  latestEffectIdRef,
  animatorRefs,
  setTriggers,
  setContent,
  failText,
  checkForError = null,
  setResponseStatus = null,
  onJsonParsed = null,
  setErrorObject = null,
  setNoAnswerArray = null
) => {
  setTriggers((prev) => ({ ...prev, hasError: false, loader: true }));
  if (breakCheck(effectId, latestEffectIdRef, animatorRefs)) return;

  /* Populate Text */
  setContent(() => '');

  if (!responseStream || !responseStream.body) {
    setTriggers((prev) => ({ ...prev, loader: false, hasError: true }));
    animateText(
      failText,
      effectId,
      latestEffectIdRef,
      animatorRefs,
      setTriggers,
      setContent,
      failText
    );
    return;
  }

  const reader = await responseStream.body.getReader();
  setTriggers((prev) => ({ ...prev, loader: false, printing: true }));
  const decoder = new TextDecoder('utf8');
  let i;
  let answer = '';
  let partialAccumulator = '';
  let noAnswer;
  let errorCodes = {
    401: { message: 'Unable to fetch a response for the query.', showButton: false },
    500: { message: 'Response can not be generated at the moment.', showButton: false },
    400: { message: 'Response can not be generated at the moment.', showButton: false },
    502: { message: 'Response can not be generated at the moment.', showButton: false },
    429: { message: 'Response can not be generated at the moment.', showButton: false },
    503: { message: 'Response can not be generated at the moment.', showButton: false },
    408: { message: 'Unable to fetch a response for the query.', showButton: true }
  };

  try {
    // eslint-disable-next-line no-constant-condition
    while (true) {
      const { done, value } = await reader.read();
      if (done) break;

      let dataArray = [];
      decoder
        ?.decode?.(value)
        ?.split(variables.STREAM_DELIMITER)
        .filter(Boolean)
        .forEach((data) => {
          let jsonString = data;

          try {
            let validJson = partialAccumulator + data;

            let errorHandlingParsedJSON = JSON.parse(validJson);
            if (setResponseStatus && errorHandlingParsedJSON.success !== undefined) {
              setResponseStatus(errorHandlingParsedJSON.success ? 'success' : 'fail');
            }

            if (
              setErrorObject &&
              Object.keys(errorCodes)
                .map(Number)
                .includes(JSON.parse(errorHandlingParsedJSON.status))
            ) {
              setErrorObject({
                show: true,
                statusCode: errorHandlingParsedJSON.status,
                message: errorCodes[errorHandlingParsedJSON.status].message,
                showRetry: errorCodes[errorHandlingParsedJSON.status].showButton
              });
            } else if (setErrorObject) {
              setErrorObject({
                show: false,
                statusCode: '',
                message: '',
                showRetry: false
              });

              validJson
                .split(variables.STREAM_DELIMITER)
                .filter(Boolean)
                .forEach((item) => {
                  jsonString = item;
                  const parsedJSON = JSON.parse(item);

                  // Call the onJsonParsed callback if provided
                  if (onJsonParsed) {
                    onJsonParsed(parsedJSON);
                  }

                  partialAccumulator = '';
                  noAnswer = parsedJSON && parsedJSON.data && parsedJSON.data.no_answer;
                  if (setNoAnswerArray) {
                    setNoAnswerArray(noAnswer);
                  }

                  dataArray.push(parsedJSON?.data?.choices?.[0]?.delta?.content || '');
                });
            }
          } catch (e) {
            partialAccumulator += jsonString || '';
            setTriggers((prev) => ({ ...prev, loader: false, hasError: true }));
            if (setResponseStatus) {
              setResponseStatus('frontendError');
            }
          }
        });

      /* Iterate and update (automatically animates) */
      i = 0;
      if (variables.gptStreaming) {
        while (i < dataArray.length) {
          if (breakCheck(effectId, latestEffectIdRef, animatorRefs)) return;
          answer += dataArray[i] || '';
          setContent(() => answer);
          i++;
          if (checkForError && noAnswer) {
            checkForError(answer, noAnswer);
          }
        }
      } else {
        // Animate single chunk (for streaming OFF)
        animateText(
          dataArray[0],
          effectId,
          latestEffectIdRef,
          animatorRefs,
          setTriggers,
          setContent,
          failText,
          checkForError,
          noAnswer
        );
      }

      if (breakCheck(effectId, latestEffectIdRef, animatorRefs)) return;
    }

    if (!answer) {
      setTriggers((prev) => ({ ...prev, loader: false, hasError: true }));
      animateText(
        failText,
        effectId,
        latestEffectIdRef,
        animatorRefs,
        setTriggers,
        setContent,
        failText
      );
      return;
    }

    setTriggers((prev) => ({ ...prev, printing: false }));
  } catch (error) {
    console.error('Error reading stream:', error);
    setTriggers((prev) => ({ ...prev, loader: false, hasError: true }));
    if (setResponseStatus) {
      setResponseStatus('frontendError');
    }
    animateText(
      failText,
      effectId,
      latestEffectIdRef,
      animatorRefs,
      setTriggers,
      setContent,
      failText
    );
  }
};

/**
 * Utility to check for errors in the response
 * @param {string} inputString - The input string to check for errors
 * @param {Array} errorArray - Array of error patterns to check against
 * @param {Function} setTriggers - State setter for the component triggers
 */
export const checkForError = (inputString, errorArray, setTriggers) => {
  if (errorArray) {
    let filteredArray = errorArray.filter((x) => x.startsWith(inputString));
    if (filteredArray && filteredArray.length) {
      setTriggers((prevState) => ({ ...prevState, hasError: true }));
    } else {
      setTriggers((prevState) => ({ ...prevState, hasError: false }));
    }
  }
};

/**
 * Copy text to clipboard and handle the UI feedback
 * @param {React.RefObject} contentRef - Ref to the content element
 * @param {Function} setShowCopy - State setter for copy notification
 * @param {Function} setShowToast - State setter for toast notification (optional)
 */
export const copyToClipboard = (contentRef, setShowCopy, setShowToast = null) => {
  if (setShowToast) setShowToast(false);
  if (contentRef.current) {
    const contentToCopy = contentRef.current.textContent;
    navigator.clipboard
      .writeText(contentToCopy)
      .then(() => {
        setShowCopy(true);
        setTimeout(() => {
          setShowCopy(false);
        }, 2000);
      })
      .catch((error) => {
        console.error('Error copying text to clipboard:', error);
      });
  }
};

/**
 * Show a toast notification and close it after a delay
 * @param {Function} setShowToast - State setter for toast display
 * @param {Function} setShowCopy - State setter for copy notification (optional)
 */
export const showAndCloseToast = (setShowToast, setShowCopy = null) => {
  if (setShowCopy) setShowCopy(false);
  setShowToast(true);
  setTimeout(() => {
    setShowToast(false);
  }, 2000);
};
