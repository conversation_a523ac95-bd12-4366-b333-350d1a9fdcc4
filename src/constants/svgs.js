export const SVGS = {
  emptyFolder: ({ width = '248.788', height = '185.401' }) => `
    <svg xmlns="http://www.w3.org/2000/svg" width=${width} height=${height} viewBox="0 0 248.788 185.401">
    <g id="Group_23" data-name="Group 23" transform="translate(0 -54.953)">
        <g id="Background_Complete" data-name="Background Complete" transform="translate(0 54.953)">
        <g id="Group_1" data-name="Group 1">
            <rect id="Rectangle_1" data-name="Rectangle 1" width="248.788" height="0.124" transform="translate(0 162.929)" fill="#ebebeb"/>
            <rect id="Rectangle_2" data-name="Rectangle 2" width="16.481" height="0.124" transform="translate(207.38 170.938)" fill="#ebebeb"/>
            <rect id="Rectangle_3" data-name="Rectangle 3" width="35.232" height="0.124" transform="translate(165.883 170.938)" fill="#ebebeb"/>
            <rect id="Rectangle_4" data-name="Rectangle 4" width="9.55" height="0.124" transform="translate(136.904 166.318)" fill="#ebebeb"/>
            <rect id="Rectangle_5" data-name="Rectangle 5" width="17.062" height="0.124" transform="translate(26.102 167.154)" fill="#ebebeb"/>
            <rect id="Rectangle_6" data-name="Rectangle 6" width="31.023" height="0.124" transform="translate(47.594 167.154)" fill="#ebebeb"/>
            <rect id="Rectangle_7" data-name="Rectangle 7" width="18.318" height="0.124" transform="translate(93.71 169.255)" fill="#ebebeb"/>
            <path id="Path_1" data-name="Path 1" d="M137.129,195.691H41.047a2.844,2.844,0,0,1-2.84-2.84V57.793a2.843,2.843,0,0,1,2.84-2.84h96.082a2.843,2.843,0,0,1,2.84,2.84V192.851A2.844,2.844,0,0,1,137.129,195.691ZM41.047,55.077a2.718,2.718,0,0,0-2.716,2.715V192.851a2.719,2.719,0,0,0,2.716,2.716h96.082a2.718,2.718,0,0,0,2.715-2.716V57.793a2.718,2.718,0,0,0-2.715-2.715Z" transform="translate(-19.196 -54.953)" fill="#ebebeb"/>
            <path id="Path_2" data-name="Path 2" d="M353.426,195.691H257.345a2.843,2.843,0,0,1-2.84-2.84V57.793a2.843,2.843,0,0,1,2.84-2.84h96.081a2.843,2.843,0,0,1,2.841,2.84V192.851A2.844,2.844,0,0,1,353.426,195.691ZM257.345,55.077a2.718,2.718,0,0,0-2.715,2.715V192.851a2.718,2.718,0,0,0,2.715,2.716h96.081a2.719,2.719,0,0,0,2.716-2.716V57.793a2.719,2.719,0,0,0-2.716-2.715Z" transform="translate(-127.869 -54.953)" fill="#ebebeb"/>
        </g>
        <g id="Group_10" data-name="Group 10" transform="translate(29.637 10.299)">
            <g id="Group_4" data-name="Group 4" transform="translate(6.596)">
            <g id="Group_3" data-name="Group 3">
                <g id="Group_2" data-name="Group 2">
                <rect id="Rectangle_8" data-name="Rectangle 8" width="67.521" height="58.501" transform="translate(69.021 58.501) rotate(180)" fill="#e6e6e6"/>
                <rect id="Rectangle_9" data-name="Rectangle 9" width="67.521" height="58.501" transform="translate(67.521 58.501) rotate(180)" fill="#f5f5f5"/>
                <rect id="Rectangle_10" data-name="Rectangle 10" width="54.319" height="62.134" transform="translate(64.827 2.091) rotate(90)" fill="#fafafa"/>
                <path id="Path_3" data-name="Path 3" d="M129.24,134.172,140.5,79.853H116.947l-11.257,54.319Z" transform="translate(-89.333 -77.762)" fill="#fff"/>
                <rect id="Rectangle_11" data-name="Rectangle 11" width="54.319" height="1.134" transform="translate(3.828 2.091) rotate(90)" fill="#e6e6e6"/>
                <rect id="Rectangle_12" data-name="Rectangle 12" width="65.293" height="2.807" transform="translate(66.407 46.67) rotate(180)" fill="#f5f5f5"/>
                </g>
            </g>
            </g>
            <g id="Group_5" data-name="Group 5" transform="translate(0 102.909)">
            <rect id="Rectangle_13" data-name="Rectangle 13" width="10.781" height="46.758" transform="translate(83.27 46.758) rotate(180)" fill="#f0f0f0"/>
            <rect id="Rectangle_14" data-name="Rectangle 14" width="79.13" height="2.963" transform="translate(81.2 49.721) rotate(180)" fill="#f0f0f0"/>
            <rect id="Rectangle_15" data-name="Rectangle 15" width="72.489" height="46.758" transform="translate(0 0)" fill="#f5f5f5"/>
            <rect id="Rectangle_16" data-name="Rectangle 16" width="64.011" height="15.335" transform="translate(4.239 5.755)" fill="#f0f0f0"/>
            <rect id="Rectangle_17" data-name="Rectangle 17" width="64.011" height="15.335" transform="translate(4.239 25.668)" fill="#f0f0f0"/>
            <path id="Path_4" data-name="Path 4" d="M96.67,309.438h34.5a1.055,1.055,0,0,0,1.052-1.051h0a1.055,1.055,0,0,0-1.052-1.051H96.67a1.055,1.055,0,0,0-1.051,1.051h0A1.055,1.055,0,0,0,96.67,309.438Z" transform="translate(-77.678 -294.964)" fill="#f5f5f5"/>
            <path id="Path_5" data-name="Path 5" d="M96.67,349.457h34.5a1.055,1.055,0,0,0,1.052-1.051h0a1.055,1.055,0,0,0-1.052-1.051H96.67a1.055,1.055,0,0,0-1.051,1.051h0A1.055,1.055,0,0,0,96.67,349.457Z" transform="translate(-77.678 -315.07)" fill="#f5f5f5"/>
            </g>
            <g id="Group_7" data-name="Group 7" transform="translate(169.315 13.197)">
            <path id="Path_6" data-name="Path 6" d="M424.276,119.553c6.14-1.692,8.24-12.832-7.277-17.379C420.422,113.377,413.571,122.5,424.276,119.553Z" transform="translate(-408.462 -102.174)" fill="#ebebeb"/>
            <path id="Path_7" data-name="Path 7" d="M417.013,133.327c4.839-5.232.262-17.06-17.172-12.228C409.7,129.73,408.577,142.448,417.013,133.327Z" transform="translate(-399.841 -111.112)" fill="#e0e0e0"/>
            <path id="Path_8" data-name="Path 8" d="M423.466,136.2c-4.052-4.01,1.751-17.967,15.126-11.5C431.014,131.952,430.531,143.2,423.466,136.2Z" transform="translate(-411.073 -112.648)" fill="#fafafa"/>
            <g id="Group_6" data-name="Group 6" transform="translate(6.454 23.062)">
                <path id="Path_9" data-name="Path 9" d="M415.736,157.169h11.787l.89-7.975H414.846Z" transform="translate(-413.833 -148.86)" fill="#e6e6e6"/>
                <path id="Path_10" data-name="Path 10" d="M413.691,150.282h13.832a.88.88,0,0,0,.88-.88h0a.88.88,0,0,0-.88-.88H413.691a.88.88,0,0,0-.88.88h0A.88.88,0,0,0,413.691,150.282Z" transform="translate(-412.811 -148.522)" fill="#e6e6e6"/>
            </g>
            </g>
            <g id="Group_9" data-name="Group 9" transform="translate(66.575 44.569)">
            <path id="Path_11" data-name="Path 11" d="M205.946,259.776h0V246.269H217.2V232.761h11.253V219.253H239.7V205.746h11.253V192.238h11.253V178.73h11.253V165.222h73.143V273.284H194.693V259.776Z" transform="translate(-194.03 -165.222)" fill="#fafafa"/>
            <path id="Path_12" data-name="Path 12" d="M330.33,259.776h0V246.269h11.253V232.761h11.253V219.253h11.253V205.746h11.253V192.238h11.253V178.73h11.253V165.222H409.1V273.284H319.078V259.776Z" transform="translate(-256.524 -165.222)" fill="#e0e0e0"/>
            <g id="Group_8" data-name="Group 8">
                <rect id="Rectangle_18" data-name="Rectangle 18" width="62.554" height="4.052" transform="translate(0 94.554)" fill="#ebebeb"/>
                <rect id="Rectangle_19" data-name="Rectangle 19" width="62.554" height="4.052" transform="translate(11.253 81.046)" fill="#ebebeb"/>
                <rect id="Rectangle_20" data-name="Rectangle 20" width="62.554" height="4.052" transform="translate(22.506 67.539)" fill="#ebebeb"/>
                <rect id="Rectangle_21" data-name="Rectangle 21" width="62.554" height="4.052" transform="translate(33.759 54.031)" fill="#ebebeb"/>
                <rect id="Rectangle_22" data-name="Rectangle 22" width="62.554" height="4.052" transform="translate(45.011 40.523)" fill="#ebebeb"/>
                <rect id="Rectangle_23" data-name="Rectangle 23" width="62.554" height="4.052" transform="translate(56.264 27.015)" fill="#ebebeb"/>
                <rect id="Rectangle_24" data-name="Rectangle 24" width="62.554" height="4.052" transform="translate(67.517 13.508)" fill="#ebebeb"/>
                <rect id="Rectangle_25" data-name="Rectangle 25" width="62.554" height="4.052" transform="translate(78.77)" fill="#ebebeb"/>
            </g>
            </g>
        </g>
        </g>
        <g id="Shadow" transform="translate(27.92 229.086)">
        <ellipse id="_Path_" data-name="&lt;Path&gt;" cx="96.475" cy="5.634" rx="96.475" ry="5.634" fill="#f5f5f5"/>
        </g>
        <g id="Character" transform="translate(90.446 57.699)">
        <g id="Group_19" data-name="Group 19">
            <path id="Path_15" data-name="Path 15" d="M315.925,158.351c-.086,1.491-.26,2.862-.454,4.281s-.458,2.8-.759,4.2a46.688,46.688,0,0,1-2.512,8.246l-.449,1-.113.25-.056.125c-.018.04-.033.076-.077.159a3.935,3.935,0,0,1-.574.863,3.364,3.364,0,0,1-.952.752,3.305,3.305,0,0,1-1.137.357,4.293,4.293,0,0,1-1.807-.188,8.805,8.805,0,0,1-2.308-1.191,19.7,19.7,0,0,1-1.769-1.418,41.149,41.149,0,0,1-5.622-6.437l1.651-1.4c.969.92,1.981,1.848,2.991,2.73a39.673,39.673,0,0,0,3.1,2.486,16.12,16.12,0,0,0,1.571,1.017,5.347,5.347,0,0,0,1.4.6c.173.036.292.015.237,0a.54.54,0,0,0-.317.14c-.029.026-.03.023-.022-.007l.16-.416.347-.9a53.337,53.337,0,0,0,2.017-7.641c.261-1.306.447-2.631.644-3.952s.34-2.679.454-3.937Z" transform="translate(-239.834 -109.511)" fill="#ffc3bd"/>
            <path id="Path_16" data-name="Path 16" d="M325.456,147.31c-1.854.472-3.152,8.1-3.152,8.1l5.11,3.475s4.9-7.7,3.473-9.737C329.4,147.029,328.161,146.622,325.456,147.31Z" transform="translate(-252.379 -103.947)" fill="#407bff"/>
            <g id="Group_13" data-name="Group 13" transform="translate(69.925 43.056)">
            <path id="Path_17" data-name="Path 17" d="M325.456,147.31c-1.854.472-3.152,8.1-3.152,8.1l5.11,3.475s4.9-7.7,3.473-9.737C329.4,147.029,328.161,146.622,325.456,147.31Z" transform="translate(-322.304 -147.003)" fill="#407bff"/>
            <path id="Path_18" data-name="Path 18" d="M325.456,147.31c-1.854.472-3.152,8.1-3.152,8.1l5.11,3.475s4.9-7.7,3.473-9.737C329.4,147.029,328.161,146.622,325.456,147.31Z" transform="translate(-322.304 -147.003)" opacity="0.4"/>
            </g>
            <path id="Path_19" data-name="Path 19" d="M327.536,151.047l-1.425,7.692,2.872,1.948Z" transform="translate(-254.292 -105.979)" opacity="0.3"/>
            <path id="Path_20" data-name="Path 20" d="M297,173.408l-2.431-3.839-1.379,4.363s1.994,2.091,3.8,1.592Z" transform="translate(-237.751 -115.285)" fill="#ffc3bd"/>
            <path id="Path_21" data-name="Path 21" d="M287.834,167.017l-.956,3.715,3.141,1.918,1.378-4.363Z" transform="translate(-234.58 -114.003)" fill="#ffc3bd"/>
            <path id="Path_22" data-name="Path 22" d="M343.955,395.234h-4.5l-.3-10.419h4.5Z" transform="translate(-260.845 -223.429)" fill="#ffc3bd"/>
            <path id="Path_23" data-name="Path 23" d="M417.207,356.68l-3.429,2.913-7.848-7,3.429-2.913Z" transform="translate(-294.394 -205.774)" fill="#ffc3bd"/>
            <path id="Path_24" data-name="Path 24" d="M418.089,364.934l3.326-3.806a.356.356,0,0,1,.468-.068l3.391,2.2a.723.723,0,0,1,.128,1.07c-1.182,1.307-1.819,1.879-3.282,3.553-.9,1.03-2.107,2.628-3.35,4.05-1.215,1.391-2.715.269-2.325-.434a8.808,8.808,0,0,0,1.358-5.813A1.172,1.172,0,0,1,418.089,364.934Z" transform="translate(-299.646 -211.464)" fill="#263238"/>
            <path id="Path_25" data-name="Path 25" d="M332.072,404.708h5.055a.356.356,0,0,1,.359.307l.576,4a.723.723,0,0,1-.721.8c-1.762-.03-2.611-.134-4.834-.134-1.367,0-4.2.142-6.085.142-1.847,0-1.989-1.867-1.2-2.037a12.2,12.2,0,0,0,6.1-2.8A1.175,1.175,0,0,1,332.072,404.708Z" transform="translate(-253.616 -233.424)" fill="#263238"/>
            <g id="Group_14" data-name="Group 14" transform="translate(78.311 143.903)">
            <path id="Path_26" data-name="Path 26" d="M339.157,384.821l.154,5.371h4.5l-.154-5.371Z" transform="translate(-339.157 -367.334)" opacity="0.2"/>
            <path id="Path_27" data-name="Path 27" d="M409.363,349.678l-3.431,2.914,4.047,3.609,3.431-2.914Z" transform="translate(-372.707 -349.678)" opacity="0.2"/>
            </g>
            <g id="Group_15" data-name="Group 15" transform="translate(72.542 42.061)">
            <path id="Path_28" data-name="Path 28" d="M328.1,146.306s-2.144.755,2.15,27.156h18.273c-.307-7.437-.315-12.023,3.239-27.286a53.9,53.9,0,0,0-7.76-1.022,58.172,58.172,0,0,0-8.3,0C332.14,145.479,328.1,146.306,328.1,146.306Z" transform="translate(-327.563 -145.003)" fill="#407bff"/>
            <path id="Path_29" data-name="Path 29" d="M328.1,146.306s-2.144.755,2.15,27.156h18.273c-.307-7.437-.315-12.023,3.239-27.286a53.9,53.9,0,0,0-7.76-1.022,58.172,58.172,0,0,0-8.3,0C332.14,145.479,328.1,146.306,328.1,146.306Z" transform="translate(-327.563 -145.003)" opacity="0.4"/>
            </g>
            <path id="Path_30" data-name="Path 30" d="M350.466,145.152a58.391,58.391,0,0,0-8.3,0c-.448.04-.906.09-1.358.144a2.212,2.212,0,0,0-.249,1.941c.582,1.448,2.682,1.617,4.214,1.617,5.657,0,6.613-3.3,6.653-3.443l.055-.2C351.143,145.191,350.8,145.167,350.466,145.152Z" transform="translate(-261.486 -102.943)" fill="#fff"/>
            <path id="Path_31" data-name="Path 31" d="M350.648,126.528c-.523,2.67-1.046,7.561.822,9.343,0,0-.73,2.708-5.691,2.708-5.455,0-2.607-2.708-2.607-2.708,2.977-.711,2.9-2.92,2.382-4.994Z" transform="translate(-262.49 -93.66)" fill="#ffc3bd"/>
            <path id="Path_32" data-name="Path 32" d="M351.729,130.094l-3.015,2.572a8.565,8.565,0,0,1,.256,1.473,3.674,3.674,0,0,0,2.82-2.6A3.994,3.994,0,0,0,351.729,130.094Z" transform="translate(-265.648 -95.451)" opacity="0.2"/>
            <path id="Path_33" data-name="Path 33" d="M352.382,82.733c-.463-1.564-.862-4.035,1.933-5.127a4.361,4.361,0,0,1-.017-5.332,3.575,3.575,0,0,1,3.383-.939s.6-3.637,5.02-3,2.453,3.565,2.453,3.565,6.037-1.2,6.44,4.491-3.063,4.985-3.063,4.985,2.692,2.257,1.2,4.741-4.941,1.628-4.941,1.628-1.622,4.387-5.443,1.891A31.145,31.145,0,0,1,352.382,82.733Z" transform="translate(-267.325 -64.384)" fill="#263238"/>
            <path id="Path_34" data-name="Path 34" d="M333.833,101.029a3.361,3.361,0,0,0,.484,4.957c3.21,2.051,7.852-6.544,6.645-7.574S335.329,99.1,333.833,101.029Z" transform="translate(-257.741 -79.379)" fill="#263238"/>
            <path id="Path_35" data-name="Path 35" d="M356.591,94.422a3.96,3.96,0,0,0-4.917,1.324c-1.628,3.128,5.761,8.768,7.9,4.855C360.846,98.278,358.767,95.3,356.591,94.422Z" transform="translate(-267.018 -77.361)" fill="#166bca"/>
            <path id="Path_36" data-name="Path 36" d="M347.308,107.948c.153,4.463.313,6.348-1.71,8.84-3.042,3.748-8.557,2.96-10.04-1.367-1.335-3.895-1.361-10.538,2.813-12.721A6.09,6.09,0,0,1,347.308,107.948Z" transform="translate(-258.637 -81.327)" fill="#ffc3bd"/>
            <path id="Path_37" data-name="Path 37" d="M347.172,107.068a3.571,3.571,0,0,1-2.165-3.959c-.987-.041-3.173-.4-3.991-1.633-1.552.976-4.831,1.987-5.188.336s4.6-5.919,8.336-6.246c4.447-.389,8.838,3.985,7.7,8.167S347.172,107.068,347.172,107.068Z" transform="translate(-259.165 -78.092)" fill="#263238"/>
            <path id="Path_38" data-name="Path 38" d="M329.656,202.2s-3.549,34.659-3.254,48.379c.307,14.271,5.536,46.82,5.536,46.82h6.084s-.963-31.715-.425-45.731c.586-15.28,5.44-49.469,5.44-49.469Z" transform="translate(-254.429 -131.678)" fill="#263238"/>
            <path id="Path_39" data-name="Path 39" d="M343.1,391.382h-7.138l-.524-2.277,7.994-.279Z" transform="translate(-258.976 -225.444)" fill="#407bff"/>
            <path id="Path_40" data-name="Path 40" d="M346.6,217.363c-4.488,9.029-.9,25.5.686,31.626.814-9.2,2.186-20.453,3.256-28.714C349.863,215.6,348.671,213.2,346.6,217.363Z" transform="translate(-263.492 -138.22)" opacity="0.2"/>
            <path id="Path_41" data-name="Path 41" d="M342.658,202.2s4.139,36.032,7.265,47.6c3.711,13.734,26.764,32.218,26.764,32.218l4.676-3.972s-16.709-20.078-19.546-27.313c-6.056-15.442-.872-38.455-5.712-48.532Z" transform="translate(-262.605 -131.678)" fill="#263238"/>
            <path id="Path_42" data-name="Path 42" d="M413.9,351.7l-5.8,4.928-2.167-1.3,6.563-6.1Z" transform="translate(-294.396 -205.546)" fill="#166bca"/>
            <path id="Path_43" data-name="Path 43" d="M345.825,117.141c.034.362-.129.673-.365.7s-.456-.252-.49-.613.129-.672.365-.7S345.79,116.779,345.825,117.141Z" transform="translate(-263.764 -88.635)" fill="#263238"/>
            <path id="Path_44" data-name="Path 44" d="M337.618,117.928c.035.361-.129.673-.365.7s-.456-.252-.491-.613.129-.673.365-.7S337.583,117.566,337.618,117.928Z" transform="translate(-259.641 -89.03)" fill="#263238"/>
            <path id="Path_45" data-name="Path 45" d="M336.814,117.168l-.892-.167S336.432,117.636,336.814,117.168Z" transform="translate(-259.221 -88.873)" fill="#263238"/>
            <path id="Path_46" data-name="Path 46" d="M339.7,119.588a9.706,9.706,0,0,1-1.067,2.429,1.56,1.56,0,0,0,1.311.118Z" transform="translate(-260.582 -90.173)" fill="#ed847e"/>
            <path id="Path_47" data-name="Path 47" d="M344.578,125.584a3.53,3.53,0,0,1-.6.11.107.107,0,0,1-.116-.1.106.106,0,0,1,.1-.116,2.871,2.871,0,0,0,2.226-1.342.106.106,0,0,1,.189.1A2.974,2.974,0,0,1,344.578,125.584Z" transform="translate(-263.209 -92.431)" fill="#263238"/>
            <path id="Path_48" data-name="Path 48" d="M358.93,118.443a3.262,3.262,0,0,1-1.232,2.166c-.909.7-1.787-.014-1.889-1.094-.093-.972.269-2.5,1.352-2.776A1.466,1.466,0,0,1,358.93,118.443Z" transform="translate(-269.205 -88.718)" fill="#ffc3bd"/>
            <path id="Path_49" data-name="Path 49" d="M345.546,111.894a.214.214,0,0,1-.167-.022,1.7,1.7,0,0,0-1.532-.151.213.213,0,1,1-.186-.384,2.114,2.114,0,0,1,1.938.169.213.213,0,0,1,.073.293A.211.211,0,0,1,345.546,111.894Z" transform="translate(-263.048 -85.948)" fill="#263238"/>
            <path id="Path_50" data-name="Path 50" d="M334.2,113.651a.208.208,0,0,1-.157-.017.213.213,0,0,1-.088-.289,2.115,2.115,0,0,1,1.578-1.138.213.213,0,0,1,.037.425,1.687,1.687,0,0,0-1.239.915A.211.211,0,0,1,334.2,113.651Z" transform="translate(-258.219 -86.464)" fill="#263238"/>
            <path id="Path_51" data-name="Path 51" d="M373.655,157.114a71.482,71.482,0,0,1,2.62,7.594,47.389,47.389,0,0,1,1.65,7.981c.086.687.109,1.406.143,2.116l0,.066v.111l-.006.229c-.01.144-.023.291-.046.421a5.247,5.247,0,0,1-.168.738,7.288,7.288,0,0,1-.49,1.2,17.481,17.481,0,0,1-2.522,3.529,42.784,42.784,0,0,1-5.955,5.435l-1.458-1.6c1.658-1.933,3.332-3.92,4.747-5.952a16.425,16.425,0,0,0,1.744-3.006,4.308,4.308,0,0,0,.2-.631,1.2,1.2,0,0,0,.028-.219.419.419,0,0,0,0-.065c0-.008,0-.009-.005-.014l-.007-.066c-.065-.59-.112-1.173-.223-1.776a54.753,54.753,0,0,0-1.784-7.262c-.762-2.406-1.612-4.853-2.507-7.178Z" transform="translate(-275.05 -109.027)" fill="#ffc3bd"/>
            <g id="Group_16" data-name="Group 16" transform="translate(91.703 43.133)">
            <path id="Path_52" data-name="Path 52" d="M371.108,147.257c1.831.557,4.029,6.861,4.029,6.861l-7.1,5.078s-2.59-5.262-1.821-7.629C367.015,149.1,369.019,146.622,371.108,147.257Z" transform="translate(-366.071 -147.157)" fill="#407bff"/>
            <path id="Path_53" data-name="Path 53" d="M371.108,147.257c1.831.557,4.029,6.861,4.029,6.861l-7.1,5.078s-2.59-5.262-1.821-7.629C367.015,149.1,369.019,146.622,371.108,147.257Z" transform="translate(-366.071 -147.157)" opacity="0.4"/>
            </g>
            <path id="Path_54" data-name="Path 54" d="M416.76,371.6a.621.621,0,0,1-.476-.243A.363.363,0,0,1,416.2,371c.166-.55,1.73-1,1.907-1.048a.106.106,0,0,1,.122.152c-.279.529-.833,1.427-1.4,1.492A.61.61,0,0,1,416.76,371.6Zm1.155-1.369c-.62.2-1.425.541-1.513.83a.152.152,0,0,0,.043.156.4.4,0,0,0,.357.167C417.109,371.352,417.517,370.926,417.915,370.233Z" transform="translate(-299.545 -215.961)" fill="#407bff"/>
            <path id="Path_55" data-name="Path 55" d="M417.281,369.464a1.845,1.845,0,0,1-1.3-.364.371.371,0,0,1,.012-.466.554.554,0,0,1,.4-.246c.645-.083,1.628.764,1.669.8a.107.107,0,0,1-.035.181A2.5,2.5,0,0,1,417.281,369.464Zm-.791-.869a.5.5,0,0,0-.068,0,.344.344,0,0,0-.255.154c-.077.113-.051.175-.019.216a2.287,2.287,0,0,0,1.616.248A2.69,2.69,0,0,0,416.49,368.6Z" transform="translate(-299.405 -215.173)" fill="#407bff"/>
            <path id="Path_56" data-name="Path 56" d="M334.755,405.138a1.689,1.689,0,0,1-1.091-.277.589.589,0,0,1-.192-.519.341.341,0,0,1,.18-.283c.551-.3,2.345.614,2.548.719a.107.107,0,0,1-.028.2A7.646,7.646,0,0,1,334.755,405.138Zm-.8-.931a.426.426,0,0,0-.2.038.133.133,0,0,0-.071.115.382.382,0,0,0,.123.341,2.866,2.866,0,0,0,2.01.123A5.782,5.782,0,0,0,333.953,404.207Z" transform="translate(-257.988 -233.067)" fill="#407bff"/>
            <path id="Path_57" data-name="Path 57" d="M337.281,403.913a.1.1,0,0,1-.044-.009c-.536-.242-1.6-1.213-1.514-1.722.019-.121.105-.271.4-.3a.776.776,0,0,1,.59.184,3,3,0,0,1,.674,1.734.106.106,0,0,1-.106.115Zm-1.072-1.824a.563.563,0,0,0-.064,0c-.195.019-.207.1-.211.123-.05.306.67,1.069,1.217,1.4a2.471,2.471,0,0,0-.573-1.388A.559.559,0,0,0,336.209,402.09Z" transform="translate(-259.119 -232.002)" fill="#407bff"/>
            <g id="Group_17" data-name="Group 17" transform="translate(74.66 69.31)">
            <path id="Path_58" data-name="Path 58" d="M350.933,199.9l.467,1.636c.064.127-.087.256-.3.256H332.3c-.165,0-.3-.081-.312-.184l-.165-1.636c-.011-.113.131-.209.312-.209h18.5A.331.331,0,0,1,350.933,199.9Z" transform="translate(-331.819 -199.766)" fill="#407bff"/>
            <path id="Path_59" data-name="Path 59" d="M350.933,199.9l.467,1.636c.064.127-.087.256-.3.256H332.3c-.165,0-.3-.081-.312-.184l-.165-1.636c-.011-.113.131-.209.312-.209h18.5A.331.331,0,0,1,350.933,199.9Z" transform="translate(-331.819 -199.766)" fill="#fff" opacity="0.3"/>
            </g>
            <path id="Path_60" data-name="Path 60" d="M364.81,201.826h.5c.1,0,.174-.051.167-.113l-.232-2.125c-.007-.062-.093-.113-.192-.113h-.5c-.1,0-.174.051-.167.113l.233,2.125C364.625,201.775,364.711,201.826,364.81,201.826Z" transform="translate(-273.521 -130.309)" fill="#263238"/>
            <path id="Path_61" data-name="Path 61" d="M335.148,201.826h.5c.1,0,.174-.051.167-.113l-.232-2.125c-.007-.062-.093-.113-.192-.113h-.5c-.1,0-.174.051-.167.113l.232,2.125C334.963,201.775,335.049,201.826,335.148,201.826Z" transform="translate(-258.618 -130.309)" fill="#263238"/>
            <path id="Path_62" data-name="Path 62" d="M349.979,201.826h.5c.1,0,.174-.051.167-.113l-.232-2.125c-.007-.062-.093-.113-.192-.113h-.5c-.1,0-.174.051-.167.113l.233,2.125C349.794,201.775,349.88,201.826,349.979,201.826Z" transform="translate(-266.07 -130.309)" fill="#263238"/>
            <path id="Path_63" data-name="Path 63" d="M345.021,116.381l-.892-.167S344.639,116.849,345.021,116.381Z" transform="translate(-263.344 -88.478)" fill="#263238"/>
            <g id="Group_18" data-name="Group 18">
            <path id="Path_64" data-name="Path 64" d="M249.906,154.67A45.728,45.728,0,1,1,275.97,95.481,45.728,45.728,0,0,1,249.906,154.67Z" transform="translate(-184.701 -63.403)" fill="#407bff" opacity="0.2"/>
            <path id="Path_65" data-name="Path 65" d="M249.279,67.076l-35.863,81.457c-.555-.418-1.1-.851-1.636-1.293a45.12,45.12,0,0,1-8.954-9.992l0,0L233.8,66.887A45.37,45.37,0,0,1,249.279,67.076Z" transform="translate(-192.35 -63.406)" fill="#fff" opacity="0.1"/>
            <path id="Path_66" data-name="Path 66" d="M292.98,84.4l-33.719,76.58a45.677,45.677,0,0,1-20.2-4.686l36.819-83.628A45.745,45.745,0,0,1,292.98,84.4Z" transform="translate(-210.554 -66.6)" fill="#fff" opacity="0.1"/>
            <path id="Path_67" data-name="Path 67" d="M192.774,78.308a48.641,48.641,0,1,1,6.834,68.452A48.643,48.643,0,0,1,192.774,78.308ZM264.983,137.4a44.665,44.665,0,1,0-62.857,6.282A44.665,44.665,0,0,0,264.983,137.4Z" transform="translate(-181.772 -60.471)" fill="#166bca"/>
            <path id="Path_68" data-name="Path 68" d="M353.224,214.328c4.791,2.861,9.349,6.007,13.828,9.25s8.842,6.626,13.139,10.091,8.507,7.037,12.572,10.784q3.056,2.8,5.984,5.761c1.949,1.976,3.87,3.987,5.674,6.14a2.8,2.8,0,0,1-3.487,4.261c-2.467-1.343-4.819-2.827-7.141-4.347s-4.6-3.1-6.831-4.726c-4.478-3.244-8.812-6.663-13.058-10.19s-8.425-7.136-12.491-10.883-8.05-7.595-11.8-11.725a2.883,2.883,0,0,1,3.612-4.414Z" transform="translate(-265.723 -137.568)" fill="#166bca"/>
            </g>
            <path id="Path_69" data-name="Path 69" d="M363.535,212.017l-4.806.731,3.441,3.015s2.716-.986,2.983-2.837Z" transform="translate(-270.68 -136.611)" fill="#ffc3bd"/>
            <path id="Path_70" data-name="Path 70" d="M353.514,216.238l3.018,2.369,3.687-2.407-4.11-2.714Z" transform="translate(-268.06 -137.349)" fill="#ffc3bd"/>
        </g>
        </g>
        <g id="Folder" transform="translate(40.659 136.579)">
        <g id="Group_22" data-name="Group 22" transform="translate(0 0)">
            <path id="Path_71" data-name="Path 71" d="M209.722,225.365h0a6.321,6.321,0,0,1,5.826-4.143h39.831a4.7,4.7,0,0,1,4.836,5.34l-10.287,86.354a6.261,6.261,0,0,1-6.108,5.34h-126.4a4.7,4.7,0,0,1-4.835-5.34l8.64-72.535a6.262,6.262,0,0,1,6.108-5.341h68.778A14.758,14.758,0,0,0,209.722,225.365Z" transform="translate(-97.207 -220.116)" fill="#166bca"/>
            <g id="Group_20" data-name="Group 20" transform="translate(10.438)">
            <path id="Path_72" data-name="Path 72" d="M117.235,318.525H249.293l7.525-88.858H124.76Z" transform="translate(-109.998 -224.359)" fill="#f0f0f0"/>
            <path id="Path_73" data-name="Path 73" d="M117.235,318.525H249.293l5.445-88.858H124.76Z" transform="translate(-109.998 -224.359)" fill="#e0e0e0"/>
            <path id="Path_74" data-name="Path 74" d="M117.235,315.677H249.293L253.832,224H121.774Z" transform="translate(-109.998 -221.512)" fill="#f0f0f0"/>
            <path id="Path_75" data-name="Path 75" d="M117.235,315.677H249.293l1.8-91.677H121.774Z" transform="translate(-109.998 -221.512)" fill="#e0e0e0"/>
            <path id="Path_76" data-name="Path 76" d="M117.235,313.165H249.293L250.08,219H118.022Z" transform="translate(-109.998 -219)" fill="#f0f0f0"/>
            <path id="Path_77" data-name="Path 77" d="M115.357,314.966H247.415l-2.71-92.382H113.5Z" transform="translate(-108.12 -220.8)" fill="#e0e0e0"/>
            <path id="Path_78" data-name="Path 78" d="M113.278,315.677H245.335L241.416,224H109.358Z" transform="translate(-106.041 -221.512)" fill="#f0f0f0"/>
            <path id="Path_79" data-name="Path 79" d="M110.933,317.073H242.991l-6.242-90.3H104.691Z" transform="translate(-103.696 -222.908)" fill="#e0e0e0"/>
            <path id="Path_80" data-name="Path 80" d="M109.928,317.855H241.986l-7.237-89.521H102.691Z" transform="translate(-102.691 -223.689)" fill="#f0f0f0"/>
            </g>
            <g id="Group_21" data-name="Group 21" transform="translate(0 6.611)">
            <path id="Path_81" data-name="Path 81" d="M165.519,236.532h0a4.306,4.306,0,0,1,4.5-4.245h39.83a6.977,6.977,0,0,1,6.541,5.472l15.735,80.586a4.375,4.375,0,0,1-4.4,5.472H101.33a6.977,6.977,0,0,1-6.541-5.472l-12.97-66.426a4.375,4.375,0,0,1,4.4-5.472H155C160.954,246.447,165.317,242.335,165.519,236.532Z" transform="translate(-81.714 -232.287)" fill="#166bca"/>
            </g>
        </g>
        </g>
    </g>
    </svg>
    `
};
