import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import variables from './redux/variables';
import { recommendations } from './redux/ducks';
import Recommendations from 'components/feature-components/recommendations/index.jsx';

const RecommendationWidget = () => {
  try {
    const dispatch = useDispatch();
    const [redirectURL, setRedirectURL] = useState('');
    let recommendationsResults = useSelector((state) => state.recommendationsResults);

    useEffect(() => {
      let searchStringForWidget = '';
      const config = window.scConfiguration?.recommendations_widget_config;
      let widgetRecommendationsEnabled;
      if (config) {
        widgetRecommendationsEnabled = window.scConfiguration?.rec_widget;
        setRedirectURL(config.rec_widget_redirect_url);
        if (
          !config.rec_widget_regex ||
          !location.href.match(new RegExp(config.rec_widget_regex, 'gm'))
        ) {
          searchStringForWidget = [
            document.getElementsByTagName('title')[0]?.text.trim() || '',
            document.getElementsByTagName('h1')[0]?.textContent.trim() || '',
            document.getElementsByTagName('h2')[0]?.textContent.trim() || ''
          ];
        }
      }
      variables.searchCallVariables.searchString =
        searchStringForWidget != '' ? JSON.stringify(searchStringForWidget) : searchStringForWidget;
      variables.searchCallVariables.isRecommendationsWidget = true;
      !!widgetRecommendationsEnabled &&
        dispatch(recommendations.recommendationsStart(variables.searchCallVariables));
    }, []);
    return (
      <div className="su__d-block su__border-radius">
        {recommendationsResults &&
          recommendationsResults.result &&
          recommendationsResults.result.hits.length !== 0 && (
            <Recommendations
              recommendationsResults={recommendationsResults}
              redirectURL={redirectURL}
            />
          )}
      </div>
    );
  } catch (e) {
    console.log('Error in recommendation component', e);
    return <div></div>;
  }
};

export default RecommendationWidget;
