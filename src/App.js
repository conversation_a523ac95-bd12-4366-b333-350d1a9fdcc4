import React, { Fragment, useEffect, useState, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  search,
  recommendations,
  facetPreferenceCall,
  pageRating,
  advertisementSearch
} from './redux/ducks';
import variables from './redux/variables';
import SearchPageFeedback from 'components/feature-components/search-page-feedback/index.jsx';
import SearchFeedbackModal from 'components/feature-components/search-feedback-modal/index.jsx';

import Search from 'components/feature-components/search-box/index.jsx';
import AdvanceSearch from 'components/feature-components/advance-search/index.jsx';
import ContentTotal from 'components/section-components/content-total/index.jsx';
import Bookmarks from 'components/feature-components/bookmarks/index.jsx';
import Language from 'components/feature-components/language/index.jsx';
import SortBy from 'components/feature-components/sort-by/index.jsx';
import DidYouMean from 'components/feature-components/did-you-mean/index.jsx';
import ClearFilter from 'components/feature-components/clear-filter/index.jsx';
import StickyFacets from 'components/feature-components/sticky-facets/index.jsx';
import Advertisement from 'components/feature-components/advertisement/index.jsx';
import FeaturedSnippet from 'components/feature-components/feature-snippet/index.jsx';
import ToggleView from 'components/feature-components/list-grid-view/index.jsx';
import Setting from 'components/feature-components/settings/index.jsx';
import Recommendations from 'components/feature-components/recommendations/index.jsx';
import SimilarSearch from 'components/feature-components/similar-search/index.jsx';
import NoResult from 'components/feature-components/no-result/index.jsx';
import 'components/feature-components/language/i18n.jsx';
import UnderConstruction404 from 'components/feature-components/under-consruction/error404.jsx';
import SandboxGptFeedback from 'components/feature-components/sandbox-gpt-feedback/index.jsx';

/**Component related to Facet and Content Source Tab  */
import Facet from 'components/feature-components/facets/index.jsx';
import MobileFacet from 'components/feature-components/facets-mobile/index.jsx';
import TopFacets from 'components/feature-components/content-source-tabs/index.jsx';

/**Component related to pagination  */
import NavigatePagination from 'components/feature-components/pagination-navigate/index.jsx';
import ResultsPerPage from 'components/feature-components/results-per-page/index.jsx';
import LoadMoreResults from 'components/feature-components/pagination-load-more/index.jsx';
import ScrollToTop from 'components/feature-components/go-to-page-top/index.jsx';

/**Component related to Result section (content tile) */
import ContentTile from 'components/section-components/content-tile/index.jsx';
import ListView from 'components/section-components/list-view/index.jsx';
import GridView from 'components/section-components/grid-view/index.jsx';
import Image from 'components/section-components/image/index.jsx';
import Icon from 'components/section-components/icon/index.jsx';
import Title from 'components/section-components/title/index.jsx';
import SourceLabel from 'components/section-components/source-label/index.jsx';
import SearchTuning from 'components/section-components/auto-tune/index.jsx';
import Solved from 'components/section-components/solved/index.jsx';
import LiveCounts from 'components/section-components/live-counts/index.jsx';
import Href from 'components/section-components/href/index.jsx';
import Summary from 'components/section-components/summary/index.jsx';
import Metadata from 'components/section-components/meta-data/index.jsx';
import MultiVersion from 'components/section-components/multi-version/index.jsx';
import Preview from 'components/section-components/preview/index.jsx';
import AttachToTicketIcon from 'components/section-components/attach-to-ticket-icon/index.jsx';
import dataFormater from './function-library/dataFormatter';
import utilityMethods from './redux/utilities/utility-methods';
import { a11y, tabIndexes } from '../src/constants/a11y';
import StaticStrings from 'StaticStrings';
import { useTranslation } from 'react-i18next';
import { useDevice } from 'function-library/hooks';
import FeaturedSnippetAnimation from 'components/feature-components/feature-snippets-animation/index';
import CitationsPortal from 'components/feature-components/citations-portal/index.jsx';
import RelevancyTile from 'components/section-components/relevancy-tile/index';

import { refreshJwtToken } from './redux/searchClientTypes';
import { SC_IDS, STATUS_CODES } from 'constants/constants';
import AttachmentPreview from 'components/section-components/attachment-preview/index';
import { dummySearchResult } from 'constants/constants';

const App = () => {
  try {
    const { t } = useTranslation();
    let searchSuggest,
      facetPreferenceResults,
      loading,
      recommendationsResults,
      error,
      validationError,
      firstLoad;
    [
      searchSuggest,
      facetPreferenceResults,
      loading,
      recommendationsResults,
      error,
      validationError,
      firstLoad
    ] = useSelector((state) => {
      return [
        state.searchResult,
        state.facetPreferenceResult,
        state.inProgress,
        state.recommendationsResults,
        state.error,
        state.validationError,
        state.firstLoad
      ];
    });
    /** Refs */
    const memoryRefs = [];
    /** Information deduced from search Result state */
    let {
      advertisements,
      didYouMean,
      featuredSnippetResult,
      languageManager,
      loadMoreResults,
      navigatePagination,
      noResult,
      recommendation,
      similarSearches,
      viewedResult,
      allContentHideFacet,
      searchPageFeedback,
      searchResultFeedback
    } = dataFormater(searchSuggest);
    const topActionsView = utilityMethods.isTopActionsView();

    // FOR SKELETON LOADING
    if (firstLoad) {
      searchSuggest = dummySearchResult.searchResult;
      noResult = false;
    }

    const [searchError, setSearchError] = useState(false);

    /** chrome sc sso loader */
    const [ssoLoader, setSSoLoader] = useState(false);

    /** conditionally render component for differnt devices */
    const { isDeviceDesktop, isDeviceIpad, isDeviceMobile } = useDevice();
    /**
     * isListView true - List View
     * isListView false - Grid View
     */
    const [isListView, setIsListView] = useState(true);
    const [isGridView, setIsGridView] = useState(false);
    const [isTitleClicked, setTitleClicked] = useState(null);
    const [originalSessionId, setOriginalSessionId] = useState(
      // eslint-disable-next-line no-undef
      _gr_utility_functions.getCookie('_gz_sid')
    );
    const [limitReached, setLimitReached] = useState(false);
    const [bookmarkListIconActive, setBookmarkListIconActive] = useState(false);
    const [gptResponse, setGptResponse] = useState('');
    const [showSandboxForm, setShowSandboxForm] = useState(false);
    const dispatch = useDispatch();
    const [savedResultBookmarkClicked, setSavedResultBookmarkClicked] = useState(false);
    const [cache, setCache] = useState({});
    const [
      { height, width, href, target, diamondPositionX, diamondPositionY, Opacity },
      setDimensions
    ] = useState({
      height: '',
      width: '',
      diamondPositionX: '',
      diamondPositionY: '',
      Opacity: ''
    });
    const modalRef = useRef(null);
    const [showPortal, setshowPortal] = useState(false);
    const [ShowHeaderComponents, setShowHeaderComponents] = useState(false);
    const [isAdvanceSearchCall, setIsAdvanceSearchCall] = useState(false);
    const [clickBasedAutoTrigger, setclickBasedAutoTrigger] = useState(
      parseInt(sessionStorage.getItem('clickBasedAutoTrigger'), 10) || 0
    );
    const [isPopupDismissed, setIsPopupDismissed] = useState(() => {
      return sessionStorage.getItem('isFeedbackPopupDismissed') === 'true';
    });
    const [isMultiStepQuestions, setisMultiStepQuestions] = useState({
      isMultiForm: false,
      step: 0
    });
    const [isThanksModel, setThanksModel] = useState({ isOpen: false, message: '' });
    const [isThankyouShow, setThankyouShow] = useState(false);
    const [isTooEarlyFeedbackClicked, setIsTooEarlyFeedbackClicked] = useState(() => {
      return sessionStorage.getItem('isTooEarlyFeedback') === 'true';
    });
    const [ThankYouModal, setThankYouModal] = useState(true);
    const [isSupportPageUrl, setisSupportPageUrl] = useState(!!window.scConfiguration?.supportUrl);
    const [isSearchFeedbackModal, setisSearchFeedbackModal] = useState(false);
    const [isButtonActive, setIsButtonActive] = useState(false);
    let facetPreferenceData;
    if (variables.searchCallVariables.aggregations.length)
      facetPreferenceData = facetPreferenceResults.aggregationsArray;
    else facetPreferenceData = searchSuggest.aggregationsArray;
    let mergedFacet = [];
    if (facetPreferenceResults && facetPreferenceResults.merged_facets)
      mergedFacet = facetPreferenceResults.merged_facets;
    else if (searchSuggest && searchSuggest.merged_facets)
      mergedFacet = searchSuggest.merged_facets;

    let isSmartFacets =
      searchSuggest &&
      searchSuggest.searchClientSettings &&
      searchSuggest.searchClientSettings.smartFacets;
    let smartFacetsAggregation = searchSuggest && searchSuggest.smartAggregations;
    let arrayAggregation = searchSuggest && searchSuggest.aggregationsArray;
    let gptActive =
      searchSuggest &&
      searchSuggest.searchClientSettings &&
      searchSuggest.searchClientSettings.gptConfig &&
      searchSuggest.searchClientSettings.gptConfig.gptActive;
    let configurations =
      searchSuggest &&
      searchSuggest.searchClientSettings &&
      searchSuggest.searchClientSettings.SCsalesforceConsoleConfigurations;
    configurations = configurations && JSON.parse(configurations);
    let urlOpensInNewTab = configurations && configurations.searchResultsOpensNewBrowserTab == '1';
    let attachmentPreview =
      searchSuggest &&
      searchSuggest.searchClientSettings &&
      searchSuggest.searchClientSettings.attachmentPreview;

    const [SearchboxClicked, setSearchboxClicked] = useState(false);
    let hideFacets = true;
    if (arrayAggregation && arrayAggregation.length !== 0) {
      for (let index = 1; index < arrayAggregation.length; index++) {
        if (arrayAggregation[index].values.length !== 0) {
          hideFacets = false;
        }
      }
    }
    useEffect(() => {
      if (error) {
        setSearchError(true);
      } else {
        setSearchError(false);
      }
    }, [error]);
    const bookmarkListIconFunc = () => {
      setBookmarkListIconActive(!bookmarkListIconActive);
    };

    const savedResultLimitReachedFunc = () => {
      setLimitReached(!limitReached);
    };

    useEffect(() => {
      document.body.classList.add('su__app-running');
    });

    /** Initialize Application OnLoad Timers Here **/
    useEffect(() => {
      const clearIntervalsAndFetch = async () => {
        utilityMethods.clearIntervalsAndTimeouts(memoryRefs);
        await handleClientType();
      };

      const handleClientType = async () => {
        if (variables.searchClientType === SC_IDS.HIGHER_LOGIC_VANILLA) {
          setSSoLoader(false);
          await handleVanillaAuth();
        } else if (variables.searchClientType === SC_IDS.INTRANET_SEARCH) {
          setSSoLoader(true);
          await handleSSO();
        }
      };

      const handleVanillaAuth = async () => {
        const apiStatus = await utilityMethods.vanillaAuthUpdater();
        if (apiStatus) {
          memoryRefs.push(
            utilityMethods.withInterval(utilityMethods.vanillaAuthUpdater, 60 * 1000)
          );
        }
      };

      const handleSSO = async () => {
        try {
          const hscToken = getHscToken();
          if (hscToken) {
            await processSSO(hscToken);
          } else {
            redirectToSAML();
          }
        } catch (error) {
          console.log(' [error]: ', error);
        }
      };

      const getHscToken = () => {
        return document?.cookie
          ?.split(`hscToken_${variables.searchCallVariables.uid}=`)?.[1]
          ?.split(';')?.[0];
      };

      const processSSO = async (hscToken) => {
        const su__chromeConfig = JSON.parse(decodeURIComponent(hscToken));
        window.jwtBearer = su__chromeConfig?.token;
        setSSoLoader(false);

        if (su__chromeConfig?.email) {
          // eslint-disable-next-line no-undef
          GzAnalytics.setUser(su__chromeConfig.email);
          variables.searchCallVariables['userName'] = su__chromeConfig.name;
          variables.searchCallVariables['email'] = window.su_utm;
          variables.autocompleteCallVariables['email'] = window.su_utm;
        }
      };

      const redirectToSAML = () => {
        const searchString =
          window?.location?.search?.split('searchString=')?.[1]?.split('&')?.[0] || '';
        const aggregations =
          window?.location?.search?.split('aggregations=')?.[1]?.split('&')?.[0] || '';
        window.location = `${variables.searchClientProps.instanceName}/saml/auth?uid=${variables.searchCallVariables.uid}&searchString=${searchString}&aggregations=${aggregations}`;
      };

      clearIntervalsAndFetch();

      return () => utilityMethods.clearIntervalsAndTimeouts(memoryRefs); // Cleanup
    }, []);

    /**
     * getCache - gets answer from cache for a query & context
     * @param {string} query
     * @param {string} context
     * @returns {string} cached answer
     */

    const getCache = (query, context) => {
      const keys = Object.keys(cache);
      const cacheKey = keys.find((key) => key.includes(`${query}-${context}`));
      return cacheKey
        ? {
            cacheKey,
            cacheObj: cache[cacheKey]
          }
        : {};
    };

    const onSubmitForm = (searchString, gptContext) => {
      const { cacheKey, cacheObj } = getCache(searchString, gptContext);
      setCache((prev) => {
        const newCache = {
          ...prev,
          [cacheKey]: { ...cacheObj, feedbackSubmitted: true }
        };
        return newCache;
      });
    };

    const resultBookmarkClickedFunc = () => {
      setSavedResultBookmarkClicked(!savedResultBookmarkClicked);
    };

    useEffect(() => {
      setTitleClicked(false);
      if (advertisements) {
        dispatch(advertisementSearch.advStart(variables.searchCallVariables));
      }
    }, [searchSuggest]);

    /**
     * refresh jwt and make search call for chrome sc
     */
    useEffect(() => {
      const refreshJwtAndSearch = async () => {
        if (
          searchSuggest.message === STATUS_CODES.AUTH_EXPIRED.message &&
          searchSuggest.statusCode === STATUS_CODES.AUTH_EXPIRED.statusCode
        ) {
          try {
            let paramsUrlReq = await refreshJwtToken();
            const response = await fetch(paramsUrlReq.url, paramsUrlReq.req);
            if (!response.ok) {
              throw Error(response.statusText);
            }
            const resp = response;
            const results = await resp.json();
            window.jwtBearer = results.hscToken;
            performSearchCall();
          } catch (error) {
            console.error('[ Error during JWT refresh or search call ]:', error);
          }
        }
      };

      refreshJwtAndSearch();
    }, [searchSuggest.message, searchSuggest.statusCode]);

    /**
     * Initialising search call / facet-preference call
     */
    const performSearchCall = () => {
      variables.searchSource = 'searchbox';
      dispatch(search.start(variables.searchCallVariables));
      if (
        variables.searchClientType == SC_IDS.ZENDESK_SUPPORT_CONSOLE &&
        window.caseSubject == variables.searchCallVariables.searchString
      ) {
        variables.searchCallVariables.searchString = '';
      }

      let variablesClone = JSON.parse(JSON.stringify(variables.searchCallVariables));
      variablesClone.aggregations = [];
      if (variables.searchClientType != SC_IDS.ZENDESK_SUPPORT_CONSOLE) {
        dispatch(facetPreferenceCall.start(variablesClone));
      }
    };
    useEffect(() => {
      let check = 0;
      if (
        variables.searchClientType == SC_IDS.ZENDESK_SUPPORT_CONSOLE ||
        variables.searchClientType == SC_IDS.ZENDESK_GUIDE ||
        variables.searchClientType == SC_IDS.ZENDESK_SUPPORT ||
        variables.searchClientType == SC_IDS.FRESHSERVICE
      ) {
        if (
          variables.searchClientType == SC_IDS.ZENDESK_SUPPORT_CONSOLE &&
          window.location.href.split('app_guid=')[1]
        ) {
          window.su__zendeskAppId = window.location.href.split('app_guid=')[1].split('&origin')[0];
          window.zeneskAppUrl =
            `app_guid=${window.su__zendeskAppId}&origin=${decodeURIComponent(
              window.location.href.split('&origin=')[1]
            )}&` || '';
        }
        const intervalId = setInterval(() => {
          check++;
          if (
            (variables.searchClientType == SC_IDS.ZENDESK_SUPPORT_CONSOLE &&
              window.caseSubject != '' &&
              window.su_utm) ||
            ((variables.searchClientType == SC_IDS.ZENDESK_GUIDE ||
              variables.searchClientType == SC_IDS.ZENDESK_SUPPORT ||
              variables.searchClientType == SC_IDS.FRESHSERVICE) &&
              window.su_utm) ||
            check > 3
          ) {
            clearInterval(intervalId);
            performSearchCall();
          }
        }, 100);
      } else {
        performSearchCall();
      }
    }, []);

    /**
     * Initialising recommendations call
     */
    useEffect(() => {
      if (recommendation) {
        dispatch(recommendations.recommendationsStart(variables.searchCallVariables));
      }
    }, [recommendation]);

    /**
     *  Feedback state Initialize
     */
    const [isOpenFeedback, setOpenFeedback] = useState(false);
    const [isFeedbackGiven, setIsFeedbackGiven] = useState(false);
    useEffect(() => {
      if (searchResultFeedback || searchPageFeedback) {
        setisMultiStepQuestions((prev) => {
          return { ...prev, isMultiForm: false, step: 0 };
        });
        dispatch(pageRating.start(variables));
      }
    }, [searchResultFeedback, searchPageFeedback]);

    /**
     * This function calculates the position for citation preview modal to be opened.It returns X and Y position
     * where modal to be opened.
     * @param {*} svgContainer - it consists of bounding dimensions related to container
     * @param {*} modalWidth - static modal width
     * @param {*} modalHeight - static modal height
     * @param {*} posX - x-axis position
     * @param {*} posY - y-axis position
     * @returns {number} x and y axis position
     */
    const calculatePosForCitationModal = (svgContainer, modalWidth, modalHeight) => {
      const svgContainerRect = svgContainer.getBoundingClientRect();
      const svgContainerLeft = svgContainerRect.left;
      const svgContainerBottom = svgContainerRect.bottom;
      const svgContainerTop = svgContainerRect.top;
      const spaceRight = window.innerWidth - svgContainerLeft;
      const diamondHeight = 25;
      let pos_X =
        spaceRight > modalWidth + 100 ? svgContainerLeft : svgContainerLeft - (modalWidth - 30);
      let pos_Y =
        svgContainerTop > modalHeight ? svgContainerTop - (modalHeight + 20) : svgContainerBottom;
      let diamondPosX = spaceRight > modalWidth ? 'right' : 'left';
      let diamondPosY = svgContainerTop > modalHeight + diamondHeight ? 'bottom' : 'top';
      return { pos_X, pos_Y, diamondPosX, diamondPosY };
    };

    /**
     * This funtion excutes when user hovers over citation article links and it will open the modal for citations.
     * @param {*} event event contains all info related to citation svg element
     */
    const setPreview = (event) => {
      openCitationModal();
      let modalWidth = 454;
      let modalHeight = 75;
      const svgContainer = event.target;
      let posX, posY;
      setDimensions((prev) => ({
        ...prev,
        width: modalWidth,
        height: modalHeight,
        href: svgContainer && svgContainer.getAttribute('data-url'),
        target: '_blank',
        Opacity: 0
      }));
      if (
        (isDeviceMobile || isDeviceIpad) &&
        variables.searchClientType === SC_IDS.ZENDESK_SUPPORT_CONSOLE
      ) {
        posY = 0;
      }
      if (
        modalRef?.current?.childNodes[0]?.classList.contains('su__gpt-preview-article-link') &&
        !isDeviceMobile &&
        !isDeviceIpad
      ) {
        const previewArticleLink = modalRef.current.childNodes[0];
        const targetElement = previewArticleLink.querySelector('.su__citation_modal_container');

        if (targetElement) {
          const height = targetElement.getBoundingClientRect().height;
          const width = targetElement.getBoundingClientRect().width;
          const {
            pos_X: newX,
            pos_Y: newY,
            diamondPosX: newdiamondPosX,
            diamondPosY: newdiamondPosY
          } = calculatePosForCitationModal(svgContainer, width, height);
          posX = newX;
          posY = newY;
          setDimensions((prev) => ({
            ...prev,
            diamondPositionX: newdiamondPosX,
            diamondPositionY: newdiamondPosY
          }));
        }
      }
      setDimensions((prev) => ({
        ...prev,
        width: posX,
        height: posY,
        href: svgContainer && svgContainer.getAttribute('data-url'),
        target: '_blank',
        Opacity: 1
      }));
    };
    /**
     * The function opens the preview modal for citations
     * @param {void}
     * @returns {void}
     */

    const openCitationModal = () => {
      setshowPortal(true);
    };
    /**
     * The function closes the preview modal for citations
     * @param {void}
     * @returns {void}
     */
    const closeCitationModal = () => {
      setshowPortal(false);
    };

    if (isSmartFacets && variables.searchCallVariables.smartFacets) {
      let alreadySelected =
        arrayAggregation &&
        arrayAggregation.length &&
        arrayAggregation.filter(
          (element) =>
            element.values.length && element.values.some((subElement) => subElement.selected)
        );
      if (alreadySelected && alreadySelected.length) {
        alreadySelected.forEach((item) => {
          let type = item.key;
          let Contentname = item.values.filter((sel) => sel.selected).map((obj) => obj.Contentname);

          if (
            !variables.searchCallVariables.aggregations ||
            variables.searchCallVariables.aggregations.length === 0
          ) {
            let aggregationEntry = {
              type: type,
              filter: [...new Set(Contentname)]
            };

            variables.searchCallVariables.aggregations.push(aggregationEntry);
          } else {
            let existingAggregation = variables.searchCallVariables.aggregations.find(
              (agg) => agg.type === type
            );

            if (existingAggregation) {
              if (!existingAggregation.filter.includes(...Contentname)) {
                existingAggregation.filter.push(...Contentname);
              }
            } else {
              let aggregationEntry = {
                type: type,
                filter: [...new Set(Contentname)]
              };

              variables.searchCallVariables.aggregations.push(aggregationEntry);
            }
          }
        });
      }
      utilityMethods.updateURLParams();
    }

    const commonFeedbackProps = {
      isOpenFeedback,
      setOpenFeedback,
      clickBasedAutoTrigger,
      isFeedbackGiven,
      setIsFeedbackGiven,
      setclickBasedAutoTrigger,
      ThankYouModal,
      setThankYouModal,
      isMultiStepQuestions,
      setisMultiStepQuestions,
      isThanksModel,
      setThanksModel,
      isPopupDismissed,
      setIsPopupDismissed,
      isTooEarlyFeedbackClicked,
      setIsTooEarlyFeedbackClicked
    };

    return (
      <main className="su__main-content">
        {ssoLoader ? (
          <div className="su__sso-loader"></div>
        ) : (
          <>
            <div
              className={`su__app ${
                viewedResult ? 'su__viewed-results' : 'su__no-view-results'
              } su__results_container`}
              id={`${
                variables.searchClientType !== SC_IDS.ZENDESK_SUPPORT_CONSOLE ? 'su__wrapper' : ''
              }`}
            >
              <div className="su__mobile_head_sb">
                <div className="su__head_searchbox">
                  <Search
                    urlOpensInNewTab={urlOpensInNewTab}
                    bookmarkListIconActive={bookmarkListIconActive}
                    setSearchboxClicked={setSearchboxClicked}
                    limitReached={limitReached}
                    resultBookmarkClickedFunc={resultBookmarkClickedFunc}
                    getArrAggregation={arrayAggregation}
                    SearchboxClicked={SearchboxClicked}
                    setShowHeaderComponents={setShowHeaderComponents}
                    ShowHeaderComponents={ShowHeaderComponents}
                    isAdvanceSearchCall={isAdvanceSearchCall}
                    setIsAdvanceSearchCall={setIsAdvanceSearchCall}
                  />
                </div>

                <div
                  className={`  su__d-none   ${
                    variables.isConsoleTypeSC ? 'su__d-none' : 'su__bg-blue-grd su__head_setting'
                  }`}
                >
                  {!ShowHeaderComponents && (
                    <Setting
                      dataModalA11y="mobile"
                      allContentHide={allContentHideFacet}
                      data={facetPreferenceData}
                      merged={mergedFacet}
                    />
                  )}
                </div>
              </div>

              {!variables.hasError && (
                <Fragment>
                  <section
                    className={`su__results-section ${firstLoad ? 'su__loading_true' : ''}`}
                    id={loading && !searchError && !validationError ? 'loading' : 'noloading'}
                  >
                    {!noResult && !searchError && !validationError ? <TopFacets /> : null}
                    <section className={`${!firstLoad && loading ? 'loading_overlay-true' : ''}`}>
                      {!noResult && !searchError && !validationError && isDeviceMobile && (
                        <section className="su__w-100 su__filter-with-lang su__width_filters su__sm_mb-10">
                          <div className="su__d-flex su__w-100 su__m-lang-block su__container_filters_tab mobile-column-alignment su__align_filter_bar su__dropdowns-gaps su__flex_dir_column_mb su__align-flex-mb">
                            {allContentHideFacet && variables.allSelected ? (
                              ''
                            ) : (
                              <MobileFacet
                                aggregations={searchSuggest.aggregationsArray}
                                isButtonActive={isButtonActive}
                                setIsButtonActive={setIsButtonActive}
                              />
                            )}
                            <div
                              className={`su__d-flex su__ml-unset-rtl su__dropdowns-gaps su__flex_dir_col_mb`}
                            >
                              <SortBy />
                              {languageManager && <Language />}
                            </div>
                          </div>
                        </section>
                      )}
                      <section
                        className="su__padding-rl-0 su__w-100 su__result-content su__result-tile-padding su__padding-rl-0"
                        id="su__skiplink"
                      >
                        <div className="su__container su__container_custom">
                          {!noResult && !searchError && !validationError ? (
                            <div
                              className={
                                allContentHideFacet && variables.allSelected
                                  ? 'su__allcontent-active su__content-gutter su__col-md-12 su__m-sm-0'
                                  : 'su__allcontent-incactive su__content-gutter su__col-md-12 su__m-sm-0'
                              }
                            >
                              <div className="su__d-flex su__w-100 su__mx-sm-0 su__no-gutters su__pb-20">
                                {isDeviceDesktop ? (
                                  <div
                                    className={
                                      (allContentHideFacet && variables.allSelected) ||
                                      (hideFacets && !variables.allSelected)
                                        ? 'su__allcontentnot-show swapFilter su__col-xl-3 su__col-lg-4  su__d-none su__radius-1'
                                        : 'swapFilter su__col-xl-3 su__col-lg-4  su__d-xl-block su__radius-1 su__px-sm-0 su__mt-4'
                                    }
                                  >
                                    <Facet
                                      smartFacetsAggregation={smartFacetsAggregation}
                                      isButtonActive={isButtonActive}
                                      setIsButtonActive={setIsButtonActive}
                                    />
                                  </div>
                                ) : null}
                                <div
                                  className={`${
                                    (allContentHideFacet && variables.allSelected) ||
                                    (hideFacets && !variables.allSelected)
                                      ? 'su__allcontentshow-full swapFilterLeft su__col-xl-12 su__col-xs-12 su__mt-sm-4 su__px-xs-2 su__overflow-hide su__px-7px'
                                      : 'swapFilterLeft su__col-xs-12 su__mt-sm-4 su__px-xs-2 su__overflow-hide su__px-7px'
                                  } ${!isDeviceMobile ? 'su__col-xl-9 su__pt-3' : null}`}
                                >
                                  {didYouMean !== 0 && <DidYouMean />}
                                  <div className="su__d-flex su__showinPage_text_dimension">
                                    {!noResult && (isDeviceIpad || isDeviceDesktop) ? (
                                      <div className="su__container su__container_custom">
                                        <div
                                          className={
                                            allContentHideFacet && variables.allSelected
                                              ? 'su__allSelected-show su__ipadview-block su__d-md-flex'
                                              : 'su__allSelected-notshow su__ipadview-block su__d-md-flex'
                                          }
                                        >
                                          {!topActionsView && (
                                            <div
                                              className={` ${
                                                isDeviceIpad ? 'su__mr-10' : ''
                                              }  su__advance-icon-color su__d-flex su__align-items-center `}
                                            >
                                              <AdvanceSearch
                                                isAdvanceSearchCall={isAdvanceSearchCall}
                                                setIsAdvanceSearchCall={setIsAdvanceSearchCall}
                                              />
                                            </div>
                                          )}
                                          {isDeviceIpad && (
                                            <MobileFacet
                                              aggregations={searchSuggest.aggregationsArray}
                                              isButtonActive={isButtonActive}
                                              setIsButtonActive={setIsButtonActive}
                                            />
                                          )}
                                          {topActionsView && <ContentTotal />}

                                          <div className="su__d-flex su__ml-auto su__mr-auto-rtl su__ml-unset-rtl su__flex-gap-12px ">
                                            {!topActionsView && (
                                              <Bookmarks
                                                bookmarkListIconActive={bookmarkListIconActive}
                                                limitReached={limitReached}
                                                resultBookmarkClickedFunc={
                                                  resultBookmarkClickedFunc
                                                }
                                                getArrAggregation={arrayAggregation}
                                              />
                                            )}
                                            <SortBy />
                                            {languageManager && <Language />}
                                          </div>

                                          <ToggleView
                                            isListView={isListView}
                                            setIsListView={setIsListView}
                                            isGridView={isGridView}
                                            setIsGridView={setIsGridView}
                                            variables={variables}
                                          />
                                        </div>
                                        <StickyFacets
                                          standOut={true}
                                          bgColor="#F2F8FF"
                                          borderColor="#166DCF"
                                        />
                                      </div>
                                    ) : null}
                                    {isDeviceMobile && <ContentTotal />}
                                  </div>
                                  {(isDeviceIpad || isDeviceDesktop) && !topActionsView && (
                                    <div className="su__mt-2">
                                      <ContentTotal />
                                    </div>
                                  )}
                                  {isDeviceMobile && (
                                    <StickyFacets
                                      standOut={true}
                                      bgColor="#F2F8FF"
                                      borderColor="#166DCF"
                                    />
                                  )}
                                  <ClearFilter />
                                  <StickyFacets standOut={false} />
                                  {!variables.isConsoleTypeSC && !isDeviceMobile && (
                                    <Setting
                                      allContentHide={allContentHideFacet}
                                      isSmartFacets={isSmartFacets}
                                      smartFacetsAggregation={smartFacetsAggregation}
                                      data={facetPreferenceData}
                                      mergedFacet={mergedFacet}
                                    />
                                  )}
                                  {advertisements && <Advertisement />}
                                  {featuredSnippetResult && (
                                    <FeaturedSnippet isDeviceMobile={isDeviceMobile} />
                                  )}
                                  {gptActive && (
                                    <FeaturedSnippetAnimation
                                      setGptResponse={setGptResponse}
                                      setPreview={setPreview}
                                      openCitationModal={openCitationModal}
                                      closeCitationModal={closeCitationModal}
                                      showPortal={showPortal}
                                      modalRef={modalRef}
                                      isDeviceMobile={isDeviceMobile}
                                      isDeviceIpad={isDeviceIpad}
                                      cache={cache}
                                      setCache={setCache}
                                      setShowSandboxForm={setShowSandboxForm}
                                    />
                                  )}
                                  {window.scConfiguration &&
                                  window.scConfiguration.gpt_feedback &&
                                  searchSuggest &&
                                  searchSuggest.result &&
                                  gptActive &&
                                  showSandboxForm &&
                                  !window.showSuSearch ? (
                                    <SandboxGptFeedback
                                      searchSuggest={searchSuggest}
                                      gptResponse={gptResponse}
                                      setGptResponse={setGptResponse}
                                      onSubmitForm={onSubmitForm}
                                    />
                                  ) : null}
                                  <ContentTile
                                    bookmarkListIconFunc={bookmarkListIconFunc}
                                    savedResultLimitReachedFunc={savedResultLimitReachedFunc}
                                    savedResultBookmarkClicked={savedResultBookmarkClicked}
                                    searchResult={searchSuggest}
                                    viewedResult={viewedResult}
                                    isListView={isListView}
                                    isDeviceDesktop={isDeviceDesktop}
                                    isDeviceMobile={isDeviceMobile}
                                    gptActive={false}
                                    clickBasedAutoTrigger={clickBasedAutoTrigger}
                                    setclickBasedAutoTrigger={setclickBasedAutoTrigger}
                                  >
                                    {isListView && !isDeviceMobile && (
                                      <ListView>
                                        <Image position="left" />
                                        <Icon position="title" />
                                        <SourceLabel position="indextitle" />
                                        <Title
                                          position="title"
                                          setTitleClicked={setTitleClicked}
                                          urlOpensInNewTab={urlOpensInNewTab}
                                        />
                                        <Preview position="indextitle" />
                                        <SearchTuning position="indextitle" />
                                        <Solved position="title" />
                                        <Href position="center" />
                                        <Summary position="center" />
                                        <AttachmentPreview
                                          position="center"
                                          attachmentPreview={attachmentPreview}
                                        />
                                        <Metadata position="center" />
                                        <RelevancyTile position="end" />
                                        <MultiVersion position="end" />
                                        {[SC_IDS.KHOROS, SC_IDS.KHOROS_AURORA].includes(
                                          variables.searchClientType
                                        ) && (
                                          <LiveCounts
                                            position={
                                              variables.searchClientType === SC_IDS.KHOROS
                                                ? 'below-title'
                                                : 'center'
                                            }
                                          />
                                        )}
                                        {variables.searchClientType ==
                                          SC_IDS.ZENDESK_SUPPORT_CONSOLE && (
                                          <AttachToTicketIcon
                                            position="right"
                                            isListView={isListView}
                                          />
                                        )}
                                      </ListView>
                                    )}

                                    {(isGridView || isDeviceMobile) && (
                                      <GridView
                                        isGridView={isGridView}
                                        isDeviceMobile={isDeviceMobile}
                                      >
                                        <Image position="above-title" />
                                        <Icon position="title-second" />
                                        <SourceLabel position="above-soucelabel" />

                                        <Preview position="above-soucelabel" />
                                        <SearchTuning
                                          position="above-soucelabel"
                                          isGridView={isGridView}
                                          isDeviceMobile={isDeviceMobile}
                                        />
                                        {variables.searchClientType ==
                                          SC_IDS.ZENDESK_SUPPORT_CONSOLE && (
                                          <AttachToTicketIcon
                                            position="after-sourceLabel"
                                            isListView={isListView}
                                          />
                                        )}
                                        <Title
                                          position="title-second"
                                          isTitleClicked={isTitleClicked}
                                          setTitleClicked={setTitleClicked}
                                          urlOpensInNewTab={urlOpensInNewTab}
                                        />
                                        <Solved position="title-second" />
                                        <Href position="below-title" />
                                        <Summary position="below-title" />
                                        <AttachmentPreview
                                          position="below-title"
                                          attachmentPreview={attachmentPreview}
                                        />
                                        <Metadata position="below-title" />
                                        <RelevancyTile position="below-description" />
                                        <MultiVersion position="below-description" />
                                        {[SC_IDS.KHOROS, SC_IDS.KHOROS_AURORA].includes(
                                          variables.searchClientType
                                        ) && <LiveCounts position="below-title" />}
                                      </GridView>
                                    )}
                                  </ContentTile>
                                  <div className="su__Recommended_flex">
                                    {recommendation &&
                                      recommendationsResults &&
                                      recommendationsResults.result &&
                                      recommendationsResults.result.hits.length !== 0 && (
                                        <Recommendations
                                          recommendationsResults={recommendationsResults}
                                        />
                                      )}
                                    {similarSearches && <SimilarSearch />}
                                  </div>
                                  {loadMoreResults && <LoadMoreResults />}
                                  {(navigatePagination &&
                                    !variables.mergeResults &&
                                    searchSuggest.result &&
                                    searchSuggest.result.total > 9) ||
                                  (variables.mergeResults &&
                                    !variables.resultsInAllContentSources &&
                                    searchSuggest.result &&
                                    searchSuggest.result.total > 9) ? (
                                    <div
                                      className={`su__flex-vcenter su__w-100 footerSection su__resultPaginationbtn_align su__negative-mt-15px ${
                                        variables.searchClientType !==
                                        SC_IDS.ZENDESK_SUPPORT_CONSOLE
                                          ? 'su__mb-110'
                                          : `${isDeviceMobile ? 'su__mb-38px' : 'su__mb-110'}`
                                      } ${
                                        isDeviceMobile
                                          ? 'su__flex-vcenter su__flex-column'
                                          : 'su__mt-30'
                                      }`}
                                    >
                                      <ResultsPerPage />
                                      <NavigatePagination />
                                    </div>
                                  ) : null}
                                </div>
                              </div>
                            </div>
                          ) : (
                            <div className="su__p-3 su__sm_pr-7 su__sm_pl-7">
                              {noResult && didYouMean && !searchError && !validationError ? (
                                <DidYouMean />
                              ) : null}
                              <NoResult />
                            </div>
                          )}
                        </div>
                      </section>
                      {(isDeviceIpad || isDeviceDesktop) && (
                        <section className="su__w-100 su__ScrollToTop">
                          <div className="su__container">
                            <ScrollToTop isDeviceMobile={isDeviceMobile} />
                          </div>
                        </section>
                      )}
                      {(isDeviceIpad || isDeviceDesktop) && (
                        <section className="su__w-100 su__ScrollToTop">
                          <div className="su__container su__container_custom">
                            <ScrollToTop isDeviceMobile={isDeviceMobile} />
                          </div>
                        </section>
                      )}
                      {isDeviceMobile && (
                        <section
                          className={`${
                            variables.isConsoleTypeSC ? 'd-none' : ''
                          } su__mobile-feature su__w-100 su__position-fixed su__bottom-0 su__bg-white su__d-xs-block su__bottom_navbar_mobile su__zindex-custom-2 `}
                        >
                          <div
                            lang={variables.searchCallVariables.langAttr}
                            tabIndex={tabIndexes.tabIndex_0}
                            role={a11y.ROLES.NAVIGATION}
                            aria-label={t(StaticStrings.bottom_navigation)}
                            className="su__p-2 su__bg-white su__px-sm-1 su__py-3 su__flex-vcenter su__justify-content-between su__align-items-start su__flex_start"
                          >
                            <ScrollToTop isDeviceMobile={isDeviceMobile} />
                            <AdvanceSearch
                              isAdvanceSearchCall={isAdvanceSearchCall}
                              setIsAdvanceSearchCall={setIsAdvanceSearchCall}
                            />
                            <Bookmarks
                              bookmarkListIconActive={bookmarkListIconActive}
                              limitReached={limitReached}
                              resultBookmarkClickedFunc={resultBookmarkClickedFunc}
                              getArrAggregation={arrayAggregation}
                            />
                            {/* <Setting
                        allContentHide={allContentHideFacet}
                        data={facetPreferenceData}
                        merged={mergedFacet}
                      /> */}
                          </div>
                        </section>
                      )}
                    </section>
                    {searchPageFeedback && <SearchPageFeedback {...commonFeedbackProps} />}

                    <SearchFeedbackModal
                      {...commonFeedbackProps}
                      searchPageFeedback={searchPageFeedback}
                      originalSessionId={originalSessionId}
                      setOriginalSessionId={setOriginalSessionId}
                      searchResult={searchSuggest}
                      isSupportPageUrl={isSupportPageUrl}
                      setisSupportPageUrl={setisSupportPageUrl}
                      isSearchFeedbackModal={isSearchFeedbackModal}
                      setisSearchFeedbackModal={setisSearchFeedbackModal}
                      isThankyouShow={isThankyouShow}
                      setThankyouShow={setThankyouShow}
                    />
                  </section>
                </Fragment>
              )}
            </div>
            {showPortal && (
              <CitationsPortal
                width={width}
                height={height}
                href={href}
                target={target}
                searchResult={searchSuggest}
                showPortal={showPortal}
                setshowPortal={setshowPortal}
                modalRef={modalRef}
                isDeviceMobile={isDeviceMobile}
                isDeviceIpad={isDeviceIpad}
                diamondPositionX={diamondPositionX}
                diamondPositionY={diamondPositionY}
                Opacity={Opacity}
                viewedResult={viewedResult}
              />
            )}
          </>
        )}
      </main>
    );
  } catch (e) {
    console.log(`Error in App.js`, e);
    return (
      <div>
        <UnderConstruction404 />
      </div>
    );
  }
};

export default App;
