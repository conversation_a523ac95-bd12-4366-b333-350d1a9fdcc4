const React = require('react');
const { Provider } = require('react-redux');
require('isomorphic-fetch');
require('../src/assets/css/SearchboxStyles.css');
const { waitForWindowVariable, mounter, MOUNT_POINTS } = require('constants/constants');
const { default: Spinner } = require('components/section-components/spinner/index');

const loader = () =>
  mounter(<Spinner isError={false} isAutoComplete={true} />, MOUNT_POINTS.autocompleteApp);
loader();

waitForWindowVariable('scConfiguration', 15000)
  .then(() => {
    let AutocompleteApp = require('./App-autocomplete').default;
    let configureStore = require('./redux/configureStore').default;
    const store = configureStore();

    mounter(
      <Provider store={store}>
        <AutocompleteApp />
      </Provider>,
      MOUNT_POINTS.autocompleteApp
    );
  })
  .catch((error) => {
    console.log(error);
    loader();
  });

// If you want your app to work offline and load faster, you can change
// unregister() to register() below. Note this comes with some pitfalls.
// Learn more about service workers: https://bit.ly/CRA-PWA
