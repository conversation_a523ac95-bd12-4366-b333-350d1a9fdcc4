/* eslint-disable no-undef */
import React, { useState, useEffect } from 'react';
import { pageRating } from './redux/ducks';
import { useDispatch, useSelector } from 'react-redux';
import variables from './redux/variables';
import Icons from './assets/svg-icon/svg';

import FeedbackConfirmation from 'components/section-components/feedback-confirmation/index.jsx';
import FeedbackYesOrNo from 'components/section-components/feedback-yes-no/index.jsx';
import FeedbackTextual from 'components/section-components/feedback-textual/index.jsx';
import SearchFeedbackModal from 'components/feature-components/search-feedback-modal/index.jsx';
import StaticStrings from 'StaticStrings';
import { A11Y_HIDDEN, tabIndexes } from 'constants/a11y';
import { SC_IDS } from 'constants/constants';

const STARS = 'Stars';
const EMOTICONS = 'Emoticons';

const safeJsonParser = (input) => {
  if (typeof input !== 'string') return undefined;
  try {
    return JSON.parse(input);
  } catch (e) {
    return undefined;
  }
};

const App = () => {
  try {
    let pageRatingArray;
    let pageRatingInstanceArray;
    let searchpageRatingInstanceArray;
    const dispatch = useDispatch();
    let { contentSearchExp, pageRatingInstance, pageRatingCustomization, searchFeedback } =
      useSelector((state) => state.pageRatingResult);
    let { enabled } = contentSearchExp ? safeJsonParser(contentSearchExp) : '';
    let {
      selectedAck,
      selectedHeader,
      searchToggle,
      submitButton,
      selectedPostion,
      pageFeedbackToggle,
      selectedPageTemplete,
      selectedPageTextFeedback
    } = pageRatingCustomization ? safeJsonParser(pageRatingCustomization) : '';
    let {
      searchFeedbackToggle,
      selectedSearchAcknowledgement,
      selectedSearchTemplate,
      searchPageFeedback
    } = searchFeedback ? safeJsonParser(searchFeedback) : '';
    pageRatingArray = pageRatingInstance && safeJsonParser(pageRatingInstance);
    pageRatingInstanceArray = pageRatingInstance && safeJsonParser(pageRatingInstance);
    searchpageRatingInstanceArray = pageRatingInstance && safeJsonParser(pageRatingInstance);
    const [yesNoRating, setYesNoRating] = useState(null);
    const [showhide, setshowhide] = useState(false);
    const [isThankyouShow, setThankyouShow] = useState(false);
    const [showResults, setShowResults] = useState(false);
    const [rating, setRating] = useState(0);
    const [ThankYouModal, setThankYouModal] = useState(true);
    const [pageRateFeed, setpageRateFeed] = useState(0);
    const [feedback_type, setfeedback_type] = useState(0);
    const [starhoverRating, setstarHoverRating] = useState(0);
    const [hoverRating, setHoverRating] = useState(0);
    const [textInputFeedback, settextInputFeedback] = useState('');
    const [followupEmail, setFollowupEmail] = useState('');
    const [textAreaFeedback, settextAreaFeedback] = useState('');
    const [isValid, setIsValid] = useState(false);
    const [isEmailPrefilled, setIsEmailPrefilled] = useState('');
    const [isFeedbackGiven, setIsFeedbackGiven] = useState(false);
    const [isMultiStepQuestions, setisMultiStepQuestions] = useState({
      isMultiForm: false,
      step: 0
    });
    const [isThanksModel, setThanksModel] = useState({ isOpen: false, message: '' });
    const [isTooEarlyFeedbackClicked, setIsTooEarlyFeedbackClicked] = useState(false);
    const [isSearchFeedbackModal, setisSearchFeedbackModal] = useState(false);
    const [isOpenFeedback, setOpenFeedback] = useState(true);
    const [emailValidity, setEmailValidity] = useState(false);
    let isPageRating = true;
    const emailRegex = new RegExp(/^[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,}$/i);
    const isSearchExpFeedback = localStorage.getItem('isSearchExpFeedback') === 'true';
    if (isSearchExpFeedback === true) {
      searchToggle = false;
    }

    const [isSupportPageUrl, setisSupportPageUrl] = useState(
      window.scConfiguration.supportUrl !== '' ? true : false
    );
    const [isPopupDismissed, setIsPopupDismissed] = useState(() => {
      return sessionStorage.getItem('isFeedbackPopupDismissed') === 'true';
    });
    const handleSaveRating = (rate) => {
      setRating(rate);
    };
    const handlenewSaverating = (rate) => {
      setpageRateFeed(rate);
      if (selectedSearchTemplate == EMOTICONS || selectedSearchTemplate == STARS) {
        setfeedback_type(1);
      }
    };
    const starhanleMouseEnter = (index) => {
      setstarHoverRating(index);
    };
    const starhandleMouseLeave = () => {
      setstarHoverRating(0);
    };
    const hanleMouseEnter = (index) => {
      setHoverRating(index);
    };
    const handleMouseLeave = () => {
      setHoverRating(0);
    };
    const regexMatch = (pageRatingArray) => {
      if (pageRatingArray) {
        for (let i of pageRatingArray) {
          if (window.location.href && window.location.href.match(i['regex'])) {
            return true;
          }
        }
      }
      return false;
    };
    const updateLocalStorage = (articlesFeedbackArray, currentTime, currentLoc) => {
      let updated = false;
      const feedbackDelay = 1 * 24 * 60 * 60 * 1000;
      if (articlesFeedbackArray.length) {
        articlesFeedbackArray = articlesFeedbackArray.map((i) => {
          if (i.articleUrl === currentLoc) {
            i.articleTimeStamp = currentTime + feedbackDelay;
            updated = true;
          }
          return i;
        });
      }
      if (!updated) {
        articlesFeedbackArray.push({
          articleUrl: location.href,
          articleTimeStamp: currentTime + feedbackDelay
        });
      }
      localStorage.setItem('articlesSaved', JSON.stringify(articlesFeedbackArray));
    };

    const getLocalStorageData = () => {
      const articlesFeedbackArray = JSON.parse(localStorage.getItem('articlesSaved')) || [];
      const currentLoc = location.href;
      const currentTime = Date.now();
      return [articlesFeedbackArray, currentLoc, currentTime];
    };
    useEffect(() => {
      let [articlesFeedbackArray, currentLoc, currentTime] = getLocalStorageData();
      if (pageRateFeed === 0) {
        let thankYouFlag = false;
        if (articlesFeedbackArray.length) {
          const filteredArray = articlesFeedbackArray.filter((i) => i.articleUrl === currentLoc);
          if (filteredArray.length) {
            const [item] = filteredArray;
            if (item.articleTimeStamp > currentTime) {
              thankYouFlag = true;
              setThankYouModal(false);
            } else {
              localStorage.removeItem('isSearchExpFeedback');
              sessionStorage.removeItem('isFeedbackSubmitted');
              sessionStorage.removeItem('isFeedbackPopupDismissed');
              localStorage.removeItem('isFeedbackPopupDismissed');
            }
          }
        }
        setThankyouShow(thankYouFlag);
      } else if (pageRateFeed != 0 && !searchToggle && !pageFeedbackToggle) {
        updateLocalStorage(articlesFeedbackArray, currentTime, currentLoc);
        storePageRatingData(0);
      } else if (pageRateFeed != 0 && searchToggle && !pageFeedbackToggle) {
        updateLocalStorage(articlesFeedbackArray, currentTime, currentLoc);
        storePageRatingData(0);
      } else {
        setShowResults(true);
      }
    }, [pageRateFeed]);
    const instanceRegexMatch = (pageRatingInstanceArray) => {
      if (pageRatingInstanceArray) {
        for (let i of pageRatingInstanceArray) {
          if (
            typeof document.referrer != 'undefined' &&
            document.referrer.match(i['instance_regex'])
          ) {
            return true;
          }
        }
      }
      return false;
    };

    const searchinstanceRegexMatch = (searchpageRatingInstanceArray) => {
      if (searchpageRatingInstanceArray) {
        for (let value of searchpageRatingInstanceArray) {
          if (
            typeof document.referrer != 'undefined' &&
            document.referrer.match(value?.search_regex || '')
          ) {
            return true;
          }
        }
      }
      return false;
    };
    const commingFromSearch = searchinstanceRegexMatch(searchpageRatingInstanceArray);
    const storePageRatingFeedback = (val) => {
      setYesNoRating(val);
      let [articlesFeedbackArray, currentLoc, currentTime] = getLocalStorageData();
      if (pageFeedbackToggle) {
        setShowResults(!showResults);
      } else if (searchToggle && commingFromSearch) {
        setShowResults(!showResults);
      } else {
        updateLocalStorage(articlesFeedbackArray, currentTime, currentLoc);
        setThankyouShow(!isThankyouShow);
        gza('pagerating', {
          feedback: val,
          articlesFeedback: true,
          rating: pageRateFeed ? pageRateFeed : '',
          feedback_type: feedback_type,
          referer: document.referrer,
          window_url: window.location.href,
          articleFeedback: textInputFeedback,
          uid: variables.searchCallVariables.uid
        });
      }
      setshowhide(false);
    };
    const huddleOnChange = (event) => {
      const email = event; // Extract the value from the input field
      if (emailRegex.test(email)) {
        // If the email is valid
        setIsValid(true);
      } else {
        // If the email is invalid
        setIsValid(false);
      }
    };

    useEffect(() => {
      dispatch(pageRating.start(variables));
    }, []);
    useEffect(() => {
      if (
        variables.searchCallVariables.email &&
        variables.searchCallVariables.email.length > 0 &&
        emailRegex.test(variables.searchCallVariables.email)
      ) {
        setFollowupEmail(variables.searchCallVariables.email);
        setIsEmailPrefilled(variables.searchCallVariables.email);
        setIsValid(true);
      }
    }, []);

    useEffect(() => {
      if (variables.searchClientType === SC_IDS.KHOROS) {
        variables.getUserEmailId = LITHIUM ? LITHIUM.CommunityJsonObject.User.emailRef : '';
        if (variables.getUserEmailId.length > 0 && emailRegex.test(followupEmail)) {
          setFollowupEmail(variables.getUserEmailId);
          setIsEmailPrefilled(variables.getUserEmailId);
          setIsValid(true);
        }
      }
    }, []);

    const storePageRatingData = (val) => {
      // setThankyouShow(true);
      setYesNoRating(val);
      let [articlesFeedbackArray, currentLoc, currentTime] = getLocalStorageData();
      if (pageFeedbackToggle) {
        setShowResults(!showResults);
      } else {
        updateLocalStorage(articlesFeedbackArray, currentTime, currentLoc);
        setThankyouShow(!isThankyouShow);
        gza('pagerating', {
          feedback: val,
          articlesFeedback: true,
          rating: pageRateFeed ? pageRateFeed : '',
          feedback_type: feedback_type,
          referer: document.referrer,
          window_url: window.location.href,
          articleFeedback: textInputFeedback,
          uid: variables.searchCallVariables.uid
        });
        setRating(0);
        settextInputFeedback('');
        // setFollowupEmail('');
        settextAreaFeedback('');
        setshowhide(!showhide);
        setIsValid(false);
      }
    };
    const closeOverlay = () => {
      setShowResults(!showResults);
      setRating(0);
      settextInputFeedback('');
      setFollowupEmail(isEmailPrefilled.length > 0 ? isEmailPrefilled : '');
      settextAreaFeedback('');
      setIsValid(isEmailPrefilled.length > 0 ? true : false);
      setisSearchFeedbackModal(false);
    };
    const closeOverlayThank = () => {
      setThankYouModal(false);
      setRating(0);
      settextInputFeedback('');
      setFollowupEmail(isEmailPrefilled.length > 0 ? isEmailPrefilled : '');
      settextAreaFeedback('');
      setIsValid(isEmailPrefilled.length > 0 ? true : false);
      setisSearchFeedbackModal(false);
    };

    const checkBlur = () => {
      setEmailValidity(true);
    };

    const txtFeedbackSubmit = () => {
      let [articlesFeedbackArray, currentLoc, currentTime] = getLocalStorageData();
      updateLocalStorage(articlesFeedbackArray, currentTime, currentLoc);
      setThankyouShow(true);
      gza('pagerating', {
        feedback: yesNoRating,
        articlesFeedback: true,
        rating: pageRateFeed ? pageRateFeed : '',
        feedback_type: feedback_type,
        referer: document.referrer,
        articleFeedback: textInputFeedback,
        window_url: window.location.href,
        uid: variables.searchCallVariables.uid,
        reported_by: followupEmail
      });
      setTimeout(() => {
        setRating(0);
        settextInputFeedback('');
        setFollowupEmail('');
        settextAreaFeedback('');
        setshowhide(!showhide);
        setIsValid(false);
      }, 2000);
    };
    useEffect(() => {
      if (
        isThankyouShow &&
        ThankYouModal &&
        !isSearchExpFeedback &&
        ((searchToggle && pageFeedbackToggle) || (searchToggle && !pageFeedbackToggle))
      ) {
        setTimeout(() => {
          setThankyouShow(false);
          setisSearchFeedbackModal(true);
        }, 2000);
      }
    }, [isThankyouShow]);
    let classNameForRatingBox =
      (!isValid && followupEmail === '') || (isValid && followupEmail !== '')
        ? 'su__formnot-error'
        : 'su__formError';
    let classNameForFeedbackrow =
      selectedPostion == 'Bottom'
        ? 'su__feedshow-bottom'
        : ` ${!isSearchFeedbackModal ? 'su__feedshow-center su__zindex-3' : null}`;
    {
      /* Determine modal position classes */
    }
    const modalPositionClass =
      selectedPostion === 'Bottom'
        ? 'su__feedshow-bottom'
        : 'su__feedshow-center su__zindex-3 su__thanks-popupWidth';

    {
      /* Determine overlay visibility classes */
    }
    const overlayVisibilityClass =
      selectedPostion === 'Center'
        ? 'su__feedshow-overlay su__d-block su__zindex-1'
        : 'su__feedshow-overlay su__d-none';
    let isSubmitButtonDisabled =
      !(
        rating ||
        textAreaFeedback.trim() ||
        textInputFeedback.trim() ||
        (followupEmail && isValid)
      ) ||
      (followupEmail && !isValid);
    const feedbackProps = {
      starhoverRating,
      starhandleMouseLeave,
      starhanleMouseEnter,
      handlenewSaverating,
      pageRateFeed,
      hoverRating,
      handleMouseLeave,
      hanleMouseEnter,
      handleSaveRating,
      selectedPageTemplete,
      selectedHeader,
      storeRate: searchFeedbackToggle || searchToggle,
      storePageRatingData,
      storePageRatingFeedback,
      rating
    };
    const SuccessIcon = () => (
      <svg
        width="36"
        height="36"
        viewBox="0 0 36 36"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g id="Group" filter="url(#filter0_d_1922_53)">
          <path
            id="Vector"
            d="M16.0824 20.9576L12.8974 17.7727C12.8045 17.6777 12.6932 17.6028 12.5702 17.5525C12.4472 17.5023 12.3153 17.4777 12.1824 17.4804C12.0422 17.4807 11.9036 17.5096 11.775 17.5655C11.6464 17.6214 11.5306 17.7031 11.4348 17.8054C11.2474 17.9962 11.1423 18.2529 11.1423 18.5204C11.1423 18.7878 11.2474 19.0446 11.4348 19.2353L15.4001 23.2326C15.4881 23.325 15.594 23.3985 15.7113 23.4488C15.8286 23.4991 15.9549 23.525 16.0825 23.525C16.2102 23.525 16.3365 23.4991 16.4538 23.4488C16.5711 23.3985 16.677 23.325 16.765 23.2326L24.5326 15.4651C24.6351 15.3694 24.7169 15.2537 24.7728 15.1251C24.8287 14.9965 24.8575 14.8578 24.8575 14.7176C24.8575 14.5773 24.8287 14.4386 24.7728 14.31C24.7169 14.1814 24.6351 14.0657 24.5326 13.9701C24.3251 13.7863 24.0559 13.6875 23.7787 13.6935C23.5016 13.6995 23.2369 13.8098 23.0376 14.0024L16.0824 20.9576ZM18 30.9997C16.2684 31.0089 14.5535 30.6604 12.9628 29.9761C9.83808 28.6498 7.35033 26.1622 6.02391 23.0376C5.33955 21.4468 4.99106 19.7318 5.00027 18C4.98891 16.2576 5.33739 14.5316 6.02391 12.9301C6.68038 11.3867 7.6295 9.98506 8.81885 8.80251C10.0129 7.62367 11.4189 6.68095 12.9628 6.02391C14.5535 5.3396 16.2684 4.99111 18 5.00027C19.7424 4.98891 21.4684 5.33739 23.0699 6.02391C26.1833 7.33899 28.661 9.81666 29.9761 12.9301C30.6626 14.5316 31.0111 16.2576 30.9997 18C31.0089 19.7318 30.6605 21.4468 29.9761 23.0376C29.319 24.5814 28.3763 25.9872 27.1975 27.1812C26.0149 28.3705 24.6133 29.3196 23.0699 29.9761C21.4684 30.6626 19.7424 31.0111 18 30.9997Z"
            fill="url(#paint0_linear_1922_53)"
          />
        </g>
        <defs>
          <filter
            id="filter0_d_1922_53"
            x="0.54295"
            y="0.54295"
            width="34.9141"
            height="34.9141"
            filterUnits="userSpaceOnUse"
            colorInterpolationFilters="sRGB"
          >
            <feFlood floodOpacity="0" result="BackgroundImageFix" />
            <feColorMatrix
              in="SourceAlpha"
              type="matrix"
              values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
              result="hardAlpha"
            />
            <feOffset />
            <feGaussianBlur stdDeviation="2.22853" />
            <feColorMatrix
              type="matrix"
              values="0 0 0 0 0 0 0 0 0 0.239216 0 0 0 0 1 0 0 0 0.122 0"
            />
            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1922_53" />
            <feBlend
              mode="normal"
              in="SourceGraphic"
              in2="effect1_dropShadow_1922_53"
              result="shape"
            />
          </filter>
          <linearGradient
            id="paint0_linear_1922_53"
            x1="26.164"
            y1="5"
            x2="9.836"
            y2="40.36"
            gradientUnits="userSpaceOnUse"
          >
            <stop stopColor="#1770D4" />
            <stop offset="0.467" stopColor="#1A4AD1" />
            <stop offset="1" stopColor="#1A4AD1" />
          </linearGradient>
        </defs>
      </svg>
    );
    const shouldShowTextFeedback =
      showResults &&
      pageFeedbackToggle &&
      !isSearchFeedbackModal &&
      ThankYouModal &&
      (searchToggle ? ThankYouModal : !isThankyouShow);

    const shouldShowSearchFeedbackModal = isSearchFeedbackModal && ThankYouModal && searchToggle;
    const showFeedbackButton =
      pageFeedbackToggle &&
      commingFromSearch &&
      ThankYouModal &&
      !isThankyouShow &&
      !isSearchFeedbackModal;

    const showThankYouMessage =
      pageFeedbackToggle &&
      commingFromSearch &&
      ThankYouModal &&
      !isSearchFeedbackModal &&
      isThankyouShow;

    {
      /* Conditions for showing the Thank You modal */
    }
    const shouldShowThankYouModal = isThankyouShow && ThankYouModal && !searchToggle;

    const searchFeedbackModalProps = {
      searchPageFeedback,
      isFeedbackGiven,
      setIsFeedbackGiven,
      isOpenFeedback,
      setOpenFeedback,
      isMultiStepQuestions,
      setisMultiStepQuestions,
      isThanksModel,
      ThankYouModal,
      setThankYouModal,
      setThanksModel,
      isSupportPageUrl,
      setisSupportPageUrl,
      setclickBasedAutoTrigger: () => {},
      clickBasedAutoTrigger: null,
      getLocalStorageData,
      updateLocalStorage,
      isPopupDismissed,
      setIsPopupDismissed,
      isTooEarlyFeedbackClicked,
      setIsTooEarlyFeedbackClicked,
      isSearchFeedbackModal,
      setisSearchFeedbackModal,
      isPageRating,
      setThankyouShow
    };
    let classNameForOverlay =
      selectedPostion == 'Center'
        ? 'su__feedshow-overlay su__d-block su__zindex-1'
        : 'su__feedshow-overlay su__d-none';

    useEffect(() => {
      if (isThankyouShow && (pageFeedbackToggle || !pageFeedbackToggle) && !searchToggle) {
        setTimeout(() => {
          closeOverlay();
          closeOverlayThank();
        }, 2000);
      }
    }, [isThankyouShow]);

    // showhide - this variable used to show hide only thumbs up/down widget
    // pageFeedbackToggle - used to show hide text feedback
    // search toggle - used to show hide only search feedback
    // show results - variable to handle show hide certain conditions
    // commingFromSearch - it describe if our window.location.href matches with as that of search_regex and it is to make sure that
    // user is coming from our search page only and then only we will show feedback icon
    // isThankyouShow - on false of this flag we show widget and on true show pop up
    // ThankYouModal - in its false state we show thank you as widget
    // followUpToggle - now not needed this one( we do not need in html)

    return (
      <div>
        {enabled && (
          <>
            {instanceRegexMatch(pageRatingInstanceArray) && (
              <>
                {regexMatch(pageRatingArray) && (
                  <div className={`su_page_rating_box ${classNameForRatingBox}`}>
                    {!showhide && !isThankyouShow && ThankYouModal && (
                      <FeedbackYesOrNo {...feedbackProps} />
                    )}
                    {isThankyouShow &&
                      ThankYouModal &&
                      searchToggle &&
                      !pageFeedbackToggle &&
                      !isTooEarlyFeedbackClicked && (
                        <div className="su__thankyou-container su__thankyou-widget">
                          <figure className="su__thanks-svg">{SuccessIcon()}</figure>
                          <span>Thank You! Your feedback helps</span>
                        </div>
                      )}
                    {shouldShowTextFeedback && (
                      <>
                        <div
                          className={`${classNameForFeedbackrow} su__pagerating-row su__py-3 su__px-3 su__border su__position-relative 475`}
                        >
                          <div
                            className="su__close-svg su__position-absolute su__flex-vcenter su__bg-light-gray  su__bg-white-circle su__radius-50"
                            onClick={() => {
                              setShowResults(!showResults);
                              closeOverlay();
                            }}
                          >
                            <Icons
                              className="su__close-icon su__cursor"
                              IconName="Close"
                              width="12"
                              height="12"
                              color="#919bb0"
                            />
                          </div>
                          <div>
                            <FeedbackTextual
                              selectedPageTextFeedback={selectedPageTextFeedback}
                              storePageRatingFeedback={storePageRatingFeedback}
                              textInputFeedback={textInputFeedback}
                              settextInputFeedback={settextInputFeedback}
                            />
                            <div className={'su__feed-email-box su__pagerating-box su__my-3'}>
                              <label
                                lang={variables.searchCallVariables.langAttr}
                                className="su__feed-desc  su__mt-2 su__font-13 su__f-bold su__color-lgray"
                              >
                                {StaticStrings.email_text}
                              </label>
                              <input
                                lang={variables.searchCallVariables.langAttr}
                                className="su__input-feedack su__form-control su__w-100 su__su__font-14 su__text-black su__border su__radius-2 su__height_email su__mt-2"
                                type="email"
                                name="su__feedback-email"
                                id="su__feedback-email"
                                aria-label={StaticStrings.email_text}
                                value={followupEmail}
                                onInput={(e) => huddleOnChange(e.target.value)}
                                onChange={(e) => setFollowupEmail(e.target.value)}
                                onFocus={() => {
                                  setEmailValidity(false);
                                  /** changed aria-describedy approach to mitigate NVDA + firefox announcing twice issue as below*/
                                  const announce = document.getElementById('invalidEmailText');
                                  if (announce) {
                                    announce.textContent = ''; // set the alert text to empty string
                                    setTimeout(() => {
                                      announce.textContent = isValid
                                        ? StaticStrings.invalid_email
                                        : ''; // reset to actual value after a delay
                                    }, 200);
                                  }
                                }}
                                onBlur={checkBlur}
                              />
                              <span
                                lang={variables.searchCallVariables.langAttr}
                                aria-hidden="true"
                                className={`su__error-msg ${!isValid ? 'su__d-block' : ''}`} // Toggle visibility based on validity and focus state
                              >
                                {(!isValid && followupEmail === '') ||
                                (!isValid && !emailValidity && followupEmail !== '') ||
                                (isValid && emailValidity && followupEmail !== '')
                                  ? ''
                                  : StaticStrings.invalid_email}
                              </span>
                              <div
                                lang={variables.searchCallVariables.langAttr}
                                id={'invalidEmailText'}
                                aria-live="polite"
                                aria-atomic="true"
                                tabIndex={tabIndexes.tabIndex_minus_1}
                                className={A11Y_HIDDEN}
                              >
                                {!isValid ? StaticStrings.invalid_email : ''}
                              </div>
                            </div>
                          </div>

                          {showFeedbackButton ? (
                            <button
                              disabled={isSubmitButtonDisabled}
                              onClick={txtFeedbackSubmit}
                              className="222 su__feedback-btn su__hover-bg-blue su__text-white-hover su__font-12 su__mt-1 su__px-2 su__py-1 su__radius su__bg-white su__border su__btn su__border_skyblue su__text-blue"
                            >
                              {submitButton}
                            </button>
                          ) : (
                            showThankYouMessage && (
                              <div className="su__thankyou-container">
                                <figure className="su__thanks-svg">{SuccessIcon()}</figure>
                                <span>Thank You! Your feedback helps</span>
                              </div>
                            )
                          )}
                        </div>
                        <div onClick={closeOverlay} className={`${classNameForOverlay}`}></div>
                      </>
                    )}

                    {shouldShowSearchFeedbackModal && (
                      <SearchFeedbackModal {...searchFeedbackModalProps} />
                    )}
                    {/* This below piece of code shows thank you modal as a widget */}
                    {!ThankYouModal && (
                      <>
                        <div className="su__feedshow-bottom su__pagerating-lay">
                          <FeedbackConfirmation
                            selectedAck={selectedAck}
                            searchToggle={searchToggle}
                            selectedSearchAcknowledgement={selectedSearchAcknowledgement}
                            ThankYouModal={ThankYouModal}
                            isThanksModel={isThanksModel}
                            setThanksModel={setThanksModel}
                          />
                        </div>
                        {/* <div onClick={closeOverlay, closeOverlayThank} className={`${selectedPostion == 'Center' ? 'su__feedshow-overlay su__d-block su__zindex-1' : 'su__feedshow-overlay su__d-none'}`}></div> */}
                      </>
                    )}
                    {/* This below piece of code shows thank you modal as a pop up */}
                    {shouldShowThankYouModal && (
                      <>
                        <div className={`${modalPositionClass} su__pagerating-lay`}>
                          {selectedPostion !== 'Bottom' && (
                            <div
                              className="su__thanks-closeIcon su__position-absolute su__flex-vcenter su__bg-light-gray su__bg-white-circle su__radius-50"
                              onClick={closeOverlayThank}
                            ></div>
                          )}
                          <FeedbackConfirmation
                            selectedAck={selectedAck}
                            searchToggle={searchToggle}
                            selectedSearchAcknowledgement={selectedSearchAcknowledgement}
                            isThanksModel={isThanksModel}
                            setThanksModel={setThanksModel}
                          />
                        </div>
                        <div
                          onClick={() => {
                            closeOverlay();
                            closeOverlayThank();
                          }}
                          className={overlayVisibilityClass}
                        ></div>
                      </>
                    )}
                  </div>
                )}
              </>
            )}
          </>
        )}
      </div>
    );
  } catch (e) {
    console.log('Error in page rating component', e);
    return <div></div>;
  }
};

export default App;
