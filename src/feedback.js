const React = require('react');
const { Provider } = require('react-redux');
require('isomorphic-fetch');
require('../src/assets/css/feedback.css');
require('../src/assets/css/style.css');
const { waitForWindowVariable, mounter, MOUNT_POINTS } = require('constants/constants');

const loader = () => mounter(<></>, MOUNT_POINTS.pageRatingApp);
loader();
waitForWindowVariable('scConfiguration', 15000)
  .then(() => {
    const App = require('./App-feedback').default;
    const configureStore = require('./redux/configureStore').default;
    const store = configureStore();
    mounter(
      <Provider store={store}>
        <App />
      </Provider>,
      MOUNT_POINTS.pageRatingApp
    );
  })

  .catch((error) => {
    console.log(error);
  });

// If you want your app to work offline and load faster, you can change
// unregister() to register() below. Note this comes with some pitfalls.
// Learn more about service workers: https://bit.ly/CRA-PWA
