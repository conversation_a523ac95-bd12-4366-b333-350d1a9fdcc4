/**
 * Icon Colors using CSS Custom Properties
 * All colors now reference CSS variables defined in style.css
 */
export default {
  SearchCrossIcon: 'var(--su__searchapp-mediumgray)',
  SearchIcon: 'var(--su__searchapp-dimgray)',
  AdvanceSearchIconFill: 'var(--su__searchapp-white)',
  AdvanceSearchEnabledIconFill: 'var(--su__searchapp-white)',
  AdvanceSearchCrossIcon: 'var(--su__searchapp-black)',
  AutoCompleteClearIcon: 'var(--su__searchapp-lightgray-75)',
  AutoCompleteSearchIcon: 'var(--su__searchapp-darkgray)',
  AutoTunesvgFill: 'var(--su__searchapp-darkgray-8b)',
  boostedSvgFill: 'none',
  SaveBookmarksIcon: 'var(--su__searchapp-darkgray-53)',
  SaveBookmarksCrossIcon: 'var(--su__searchapp-mediumgray)',
  ListBookmarksEmptyIcon: 'var(--su__searchapp-bluegray)',
  ListBookMarksNotEmptyIcon: 'var(--su__searchapp-darkgray-53)',
  ListBookmarksInModalIcon: 'var(--su__searchapp-white)',
  ListBookmarksCrossIcon: 'var(--su__searchapp-white)',
  ToggleViewGridIcon: 'var(--su__searchapp-darkgray-53)',
  SettingsGearIcon: 'var(--su__searchapp-white)',
  SettingseditLayoutIcon: 'var(--su__searchapp-white)',
  SettingsSearchTipIcon: 'var(--su__searchapp-orange)',
  FilterListFacetSearchIcon: 'var(--su__searchapp-white)',
  FilterListFacetSearchCloseIcon: 'var(--su__searchapp-darkblue)',
  FacetPreferenceCrossIcon: 'var(--su__searchapp-darkgray)',
  FacetPreferenceShow_HideEye: 'var(--su__searchapp-charcoal)',
  FacetPreferenceSaveBookmark: 'var(--su__searchapp-lightblue-82)',
  mobileFacetsCloseIcon: 'var(--su__searchapp-white)',
  FeaturedSnippetThumbsup_down: 'var(--su__searchapp-white)',
  FeaturedSnippetSvgFill: 'var(--su__searchapp-black)',
  FeatureSnippetSvgPlayGrey: 'var(--su__searchapp-lightgray-cf)',
  ScrollToTopIcon: 'var(--su__searchapp-white)',
  KnowledgeGraphThumbsup_down: 'var(--su__searchapp-white)',
  SearchResultFeedbackCloseIcon: 'var(--su__searchapp-mediumgray)',
  SearchResultFeedbackViewMore: 'var(--su__searchapp-gray-91)',
  PreviewIconFill: 'var(--su__searchapp-mediumgray)',
  saveArticleIconFill: 'transparent',
  SolvedsvgFill: 'var(--su__searchapp-green)',
  UnderConstructionsvgFill: 'var(--su__searchapp-blue)',
  StickyFacetsSvgFill: 'var(--su__searchapp-bluegray)',
  autolearn_svg_fill: 'var(--su__searchapp-darkgray-58)',
  search_tip_svg_fill: 'var(--su__searchapp-darkgray-58)',
  settings_svg_fill: 'var(--su__searchapp-darkgray-58)',
  saved_result_fill: 'var(--su__searchapp-deepskyblue-light)',
  facet_search_icon_fill: 'var(--su__searchapp-deepskyblue-light)',
  simillar_search_icon: 'var(--su__searchapp-blue-16)',
  pagination_btn_fill: 'var(--su__searchapp-blue-16)',
  save_and_list_bookmark_Icon: 'var(--su__searchapp-gray)'
};
