/* eslint-disable react/prop-types */
/* global jest, it, expect, describe, beforeEach, afterEach */

import React from 'react';
import { mount } from 'enzyme';
import { act } from 'react-dom/test-utils';
import App from './App';
import { useDispatch, useSelector } from 'react-redux';
import state from '__mocks__/state';
import { SC_IDS, STATUS_CODES } from 'constants/constants';
import variables from '__mocks__/variables';
import utilityMethods from 'redux/utilities/utility-methods';
import { v4 as uuid } from 'uuid';
import { useTranslation } from 'react-i18next';

// Mock the redux hooks
jest.mock('react-redux', () => ({
  ...jest.requireActual('react-redux'),
  useDispatch: jest.fn(),
  useSelector: jest.fn()
}));

jest.mock('./redux/utilities/utility-methods', () => {
  const actual = jest.requireActual('./redux/utilities/utility-methods');
  return {
    __esModule: true,
    default: {
      ...actual.default,
      vanillaAuthUpdater: jest.fn().mockReturnValue(true),
      clearIntervalsAndTimeouts: jest.fn()
    }
  };
});

jest.mock('react', () => ({
  ...jest.requireActual('react'),
  useRef: jest.fn().mockImplementation(() => ({
    current: {
      childNodes: [
        {
          classList: {
            contains: jest.fn().mockReturnValue(true)
          },
          querySelector: jest.fn().mockReturnValue({
            getBoundingClientRect: jest.fn().mockReturnValue({ width: '100', height: '100' })
          })
        }
      ]
    }
  }))
}));

// Mocking other dependencies
jest.mock('./redux/ducks', () => ({
  search: {
    start: jest.fn()
  },
  recommendations: {
    recommendationsStart: jest.fn()
  },
  facetPreferenceCall: {
    start: jest.fn()
  },
  pageRating: {
    start: jest.fn()
  },
  advertisementSearch: {
    start: jest.fn()
  }
}));

jest.mock('./function-library/dataFormatter', () => ({
  __esModule: true,
  default: jest.fn(() => ({
    recommendation: true,
    searchPageFeedback: true,
    searchResultFeedback: true
  }))
}));

jest.mock('./redux/variables', () => require('__mocks__/variables'));

const validator = {
  FeatureSnippetsAnimationProps: jest.fn(),
  CitationsPortalProps: jest.fn(),
  SearchProps: jest.fn()
};

// eslint-disable-next-line react/prop-types
const CitationsPortal = ({ height, width }) => {
  validator.CitationsPortalProps({
    height,
    width
  });
  return <div>CitationsPortal</div>;
};

jest.mock('components/feature-components/citations-portal/index.jsx', () => CitationsPortal);
jest.mock('components/feature-components/search-page-feedback/index.jsx', () => {
  const SearchPageFeedback = () => <div>SearchPageFeedback</div>;
  return SearchPageFeedback;
});
jest.mock('components/feature-components/search-feedback-modal/index.jsx', () => {
  const SearchFeedbackModal = () => <div>SearchFeedbackModal</div>;
  return SearchFeedbackModal;
});
// eslint-disable-next-line react/prop-types
const Search = ({ bookmarkListIconActive, limitReached }) => {
  validator.SearchProps({ bookmarkListIconActive, limitReached });
  return <div>Search</div>;
};
jest.mock('components/feature-components/search-box/index.jsx', () => Search);
jest.mock('components/feature-components/advance-search/index.jsx', () => {
  const AdvanceSearch = () => <div>AdvanceSearch</div>;
  return AdvanceSearch;
});
jest.mock('components/section-components/content-total/index.jsx', () => {
  const ContentTotal = () => <div>ContentTotal</div>;
  return ContentTotal;
});
jest.mock('components/feature-components/bookmarks/index.jsx', () => {
  const Bookmarks = () => <div>Bookmarks</div>;
  return Bookmarks;
});
jest.mock('components/feature-components/language/index.jsx', () => {
  const Language = () => <div>Language</div>;
  return Language;
});
jest.mock('components/feature-components/sort-by/index.jsx', () => {
  const SortBy = () => <div>SortBy</div>;
  return SortBy;
});
jest.mock('components/feature-components/did-you-mean/index.jsx', () => {
  const DidYouMean = () => <div>DidYouMean</div>;
  return DidYouMean;
});
jest.mock('components/feature-components/clear-filter/index.jsx', () => {
  const ClearFilter = () => <div>ClearFilter</div>;
  return ClearFilter;
});
jest.mock('components/feature-components/sticky-facets/index.jsx', () => {
  const StickyFacets = () => <div>StickyFacets</div>;
  return StickyFacets;
});
jest.mock('components/feature-components/advertisement/index.jsx', () => {
  const Advertisement = () => <div>Advertisement</div>;
  return Advertisement;
});
jest.mock('components/feature-components/feature-snippet/index.jsx', () => {
  const FeaturedSnippet = () => <div>FeaturedSnippet</div>;
  return FeaturedSnippet;
});
jest.mock('components/feature-components/list-grid-view/index.jsx', () => {
  const ToggleView = () => <div>ToggleView</div>;
  return ToggleView;
});
jest.mock('components/feature-components/settings/index.jsx', () => {
  const Setting = () => <div>Setting</div>;
  return Setting;
});
jest.mock('components/feature-components/recommendations/index.jsx', () => {
  const Recommendations = () => <div>Recommendations</div>;
  return Recommendations;
});
jest.mock('components/feature-components/similar-search/index.jsx', () => {
  const SimilarSearch = () => <div>SimilarSearch</div>;
  return SimilarSearch;
});
jest.mock('components/feature-components/no-result/index.jsx', () => {
  const NoResult = () => <div>NoResult</div>;
  return NoResult;
});
jest.mock('components/feature-components/language/i18n.jsx', () => {});
jest.mock('components/feature-components/under-consruction/error404.jsx', () => {
  const UnderConstruction404 = () => <div>UnderConstruction404</div>;
  return UnderConstruction404;
});
jest.mock('components/feature-components/facets/index.jsx', () => {
  const Facet = () => <div>Facet</div>;
  return Facet;
});
jest.mock('components/feature-components/facets-mobile/index.jsx', () => {
  const MobileFacet = () => <div>MobileFacet</div>;
  return MobileFacet;
});
jest.mock('components/feature-components/content-source-tabs/index.jsx', () => {
  const TopFacets = () => <div>TopFacets</div>;
  return TopFacets;
});
jest.mock('components/feature-components/pagination-navigate/index.jsx', () => {
  const NavigatePagination = () => <div>NavigatePagination</div>;
  return NavigatePagination;
});
jest.mock('components/feature-components/results-per-page/index.jsx', () => {
  const ResultsPerPage = () => <div>ResultsPerPage</div>;
  return ResultsPerPage;
});
jest.mock('components/feature-components/pagination-load-more/index.jsx', () => {
  const LoadMoreResults = () => <div>LoadMoreResults</div>;
  return LoadMoreResults;
});
jest.mock('components/feature-components/go-to-page-top/index.jsx', () => {
  const ScrollToTop = () => <div>ScrollToTop</div>;
  return ScrollToTop;
});
jest.mock('components/section-components/content-tile/index.jsx', () => {
  // eslint-disable-next-line react/prop-types
  const ContentTile = ({ bookmarkListIconFunc, savedResultLimitReachedFunc }) => (
    <>
      <div>ContentTile</div>
      <button data-test-id="su__test_bookmarkListIconFunc" onClick={bookmarkListIconFunc}>
        Click
      </button>
      <button
        data-test-id="su__test_savedResultLimitReachedFunc"
        onClick={savedResultLimitReachedFunc}
      >
        Click
      </button>
    </>
  );
  return ContentTile;
});
jest.mock('components/section-components/list-view/index.jsx', () => {
  const ListView = () => <div>ListView</div>;
  return ListView;
});

const SandboxGptFeedback = ({ onSubmitForm }) => {
  return (
    <>
      <div>SandboxGptFeedback</div>
      <button data-test-id="su__sbox_form_submit" onClick={onSubmitForm}>
        Click
      </button>
    </>
  );
};

jest.mock('components/feature-components/sandbox-gpt-feedback/index.jsx', () => SandboxGptFeedback);
jest.mock('components/section-components/grid-view/index.jsx', () => {
  const GridView = () => <div>GridView</div>;
  return GridView;
});
jest.mock('components/section-components/image/index.jsx', () => {
  const Image = () => <div>Image</div>;
  return Image;
});
jest.mock('components/section-components/icon/index.jsx', () => {
  const Icon = () => <div>Icon</div>;
  return Icon;
});
jest.mock('components/section-components/title/index.jsx', () => {
  const Title = () => <div>Title</div>;
  return Title;
});
jest.mock('components/section-components/source-label/index.jsx', () => {
  const SourceLabel = () => <div>SourceLabel</div>;
  return SourceLabel;
});
jest.mock('components/section-components/auto-tune/index.jsx', () => {
  const SearchTuning = () => <div>SearchTuning</div>;
  return SearchTuning;
});
jest.mock('components/section-components/solved/index.jsx', () => {
  const Solved = () => <div>Solved</div>;
  return Solved;
});
jest.mock('components/section-components/live-counts/index.jsx', () => {
  const LiveCounts = () => <div>LiveCounts</div>;
  return LiveCounts;
});
jest.mock('components/section-components/href/index.jsx', () => {
  const Href = () => <div>Href</div>;
  return Href;
});
jest.mock('components/section-components/summary/index.jsx', () => {
  const Summary = () => <div>Summary</div>;
  return Summary;
});
jest.mock('components/section-components/meta-data/index.jsx', () => {
  const Metadata = () => <div>Metadata</div>;
  return Metadata;
});
jest.mock('components/section-components/multi-version/index.jsx', () => {
  const MultiVersion = () => <div>MultiVersion</div>;
  return MultiVersion;
});
jest.mock('components/section-components/preview/index.jsx', () => {
  const Preview = () => <div>Preview</div>;
  return Preview;
});
jest.mock('components/section-components/attach-to-ticket-icon/index.jsx', () => {
  const AttachToTicketIcon = () => <div>AttachToTicketIcon</div>;
  return AttachToTicketIcon;
});
// jest.mock('./redux/utilities/utility-methods', () => ({}));
jest.mock('../src/constants/a11y', () => ({
  a11y: {},
  tabIndexes: {}
}));
jest.mock('StaticStrings', () => ({}));
jest.mock('react-i18next', () => ({
  useTranslation: jest.fn().mockReturnValue({ t: (key) => key })
}));
jest.mock('function-library/hooks', () => ({
  useDevice: jest.fn(() => ({ isDeviceDesktop: true, isDeviceIpad: false, isDeviceMobile: false }))
}));
window['_gr_utility_functions'] = { getCookie: jest.fn() };
const FeaturedSnippetAnimation = ({
  openCitationModal,
  closeCitationModal,
  setPreview,
  cache,
  setShowSandboxForm,
  showPortal
}) => {
  validator.FeatureSnippetsAnimationProps({ showPortal, cache });
  return (
    <div>
      <span>FeaturedSnippetAnimation</span>
      <span></span>
      <button
        data-test-id="su_test_animation"
        onClick={!showPortal ? openCitationModal : closeCitationModal}
      >
        Click
      </button>
      <button data-test-id="su_test_sbox_form" onClick={setShowSandboxForm}>
        Click
      </button>
      <button data-test-id="su_test_preview" onClick={setPreview}>
        Click
      </button>
    </div>
  );
};
jest.mock(
  'components/feature-components/feature-snippets-animation/index',
  () => FeaturedSnippetAnimation
);

const clearValidator = () => {
  Object.keys(validator).forEach((key) => {
    validator[key].mockClear();
  });
};

describe('App Component', () => {
  let useDispatchMock = jest.fn();
  let originalLocation = window.location;

  beforeEach(() => {
    window.location = originalLocation;
    useDispatch.mockReturnValue(useDispatchMock);
    state.searchResult.gptActive = true;
    state.searchResult.searchClientSettings.gptConfig.gptActive = true;
    state.facetPreferenceResult = state.searchResult;
    state.searchResult.searchClientSettings.smartFacets = true;
    useSelector.mockImplementation((selector) => selector(state));
  });

  afterEach(() => {
    jest.clearAllMocks();
    clearValidator();
    window.location = originalLocation;
  });

  it('renders without crashing', () => {
    const wrapper = mount(<App />);
    expect(wrapper.exists()).toBe(true);
  });

  it('should render feature animation snippet when GPT is active', () => {
    const wrapper = mount(<App />);
    expect(wrapper.contains(FeaturedSnippetAnimation)).toBe(true);
  });

  it('should update feature animation showPortal state when opencitations OR Close Citations is clicked', () => {
    const wrapper = mount(<App />);
    clearValidator();
    wrapper.find(`[data-test-id="su_test_animation"]`).simulate('click'); // simulate open
    expect(validator.FeatureSnippetsAnimationProps).toHaveBeenCalledWith(
      expect.objectContaining({
        showPortal: true
      })
    );
    wrapper.find(`[data-test-id="su_test_animation"]`).simulate('click'); // simulate close
    expect(validator.FeatureSnippetsAnimationProps).toHaveBeenCalledWith(
      expect.objectContaining({
        showPortal: false
      })
    );
  });

  it('should update CitationsPortal state when setPreview is clicked', () => {
    const wrapper = mount(<App />);
    clearValidator();
    wrapper.find(`[data-test-id="su_test_preview"]`).simulate('click');
    expect(validator.CitationsPortalProps).toHaveBeenCalledTimes(1);
  });

  it('should let Search component know about bookmarks and its limits once bookmarks are added', () => {
    const wrapper = mount(<App />);
    clearValidator();
    wrapper.find(`[data-test-id="su__test_bookmarkListIconFunc"]`).simulate('click');
    expect(validator.SearchProps).toHaveBeenCalledWith(
      expect.objectContaining({ bookmarkListIconActive: true, limitReached: false })
    );
    clearValidator();
    wrapper.find(`[data-test-id="su__test_savedResultLimitReachedFunc"]`).simulate('click');
    expect(validator.SearchProps).toHaveBeenCalledWith(
      expect.objectContaining({ bookmarkListIconActive: true, limitReached: true })
    );
  });

  it('fires jwt refresh if search call returns auth expired code / message', async () => {
    const mockState = JSON.parse(JSON.stringify(state));
    mockState.searchResult = {
      statusCode: STATUS_CODES.AUTH_EXPIRED.statusCode,
      message: STATUS_CODES.AUTH_EXPIRED.message
    };
    const original = global.fetch;
    global.fetch = jest.fn();
    fetch.mockImplementation(() => ({
      ok: true,
      json: jest.fn().mockResolvedValueOnce({ hscToken: 'customToken' })
    }));
    useSelector.mockImplementation((selector) => selector(mockState));
    let wrapper;
    await act(async () => {
      wrapper = mount(<App />);
      wrapper.update();
    });
    // expect(1).toBe(1);
    expect(window['jwtBearer']).toBe('customToken');
    global.fetch = original;
  });

  it('logs error if jwt refresh call fails for auth expired code / message', async () => {
    const mockState = JSON.parse(JSON.stringify(state));
    mockState.searchResult = {
      statusCode: STATUS_CODES.AUTH_EXPIRED.statusCode,
      message: STATUS_CODES.AUTH_EXPIRED.message
    };
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
    const original = global.fetch;
    global.fetch = jest.fn();
    fetch.mockImplementation(() => ({ ok: false, statusText: 'error' }));
    useSelector.mockImplementation((selector) => selector(mockState));
    clearValidator();
    let wrapper;
    await act(async () => {
      wrapper = mount(<App />);
      wrapper.update();
    });
    expect(consoleSpy).toHaveBeenCalledWith(
      '[ Error during JWT refresh or search call ]:',
      expect.any(Error)
    );
    global.fetch = original;
  });

  it('should submit feedback form for sandbox', () => {
    window['scConfiguration'] = {
      gpt_feedback: true
    };
    clearValidator();
    const wrapper = mount(<App />);
    wrapper.find(`[data-test-id="su_test_sbox_form"]`).simulate('click'); // enable sbndbox form
    clearValidator();
    wrapper.find(`[data-test-id="su__sbox_form_submit"]`).simulate('click'); // submit the form
    // caceh should get updated
    expect(validator.FeatureSnippetsAnimationProps).toHaveBeenCalledWith(
      expect.objectContaining({
        cache: expect.objectContaining({
          undefined: expect.objectContaining({
            feedbackSubmitted: true // confirms form submitted state
          })
        })
      })
    );
  });

  it('should trigger vanilla auth if sc type is HIGHER_LOGIC_VANILLA', () => {
    variables.searchClientType = SC_IDS.HIGHER_LOGIC_VANILLA;
    mount(<App />);
    expect(utilityMethods.vanillaAuthUpdater).toHaveBeenCalledTimes(1);
  });

  it('should trigger sso flow if sc type is INTRANET_SEARCH', () => {
    variables.searchClientType = SC_IDS.INTRANET_SEARCH;
    const uid = uuid();
    const hscToken = {
      email: '<EMAIL>',
      name: 'mockName'
    };
    variables.searchCallVariables.uid = uid;
    Object.defineProperty(document, 'cookie', {
      value: `hscToken_${uid}=${JSON.stringify(hscToken)};`,
      writable: true
    });
    window['GzAnalytics'] = {
      setUser: jest.fn()
    };
    window['location'] = {
      assign: jest.fn()
    };
    mount(<App />);
    expect(utilityMethods.vanillaAuthUpdater).not.toHaveBeenCalled();
    expect(window.GzAnalytics.setUser).toHaveBeenCalledWith(hscToken.email);
    // if email is not found in hscToken, redirect to sso
    Object.defineProperty(document, 'cookie', {
      value: `hscToken_${uid}=;`,
      writable: true
    });
    const app = mount(<App />);
    app.unmount(); // cleanup
    expect(utilityMethods.clearIntervalsAndTimeouts).toHaveBeenCalled();
  });
  it('should catch error in case component encounters one', () => {
    const consoleSpy = jest.spyOn(console, 'log').mockImplementation(() => {});
    useTranslation.mockImplementation(() => {
      throw new Error('error');
    });
    const wrapper = mount(<App />);
    wrapper.update();
    expect(wrapper.html()).toBe(`<div><div>UnderConstruction404</div></div>`);
    expect(consoleSpy).toHaveBeenCalledWith('Error in App.js', expect.any(Error));
    consoleSpy.mockRestore();
  });
});
