:root {
  /* ---------- Hex Colors ---------- */
  --su__recommendationapp-lightgrey: #d3d3d3;
  --su__recommendationapp-deepskyblue-light: #1770d4;
  --su__recommendationapp-gray: #666;
  --su__recommendationapp-white: #fff;
  --su__recommendationapp-black-rgba: 0, 0, 0;
  --su__recommendationapp-blue-6: rgba(33, 34, 52, 0.788);
  --su__recommendationapp-gray-44: rgba(125, 112, 112, 0.439);
}

@font-face {
  font-family: 'Montserrat';
  font-style: normal;
  font-weight: 300;
  src: local('Montserrat Light'), local(montserrat-light),
    url(https:fonts.gstatic.com/s/montserrat/v14/JTURjIg1_i6t8kCHKm45_cJD3gTD_u50.woff2)
      format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}

@font-face {
  font-family: 'Montserrat';
  font-style: normal;
  font-weight: 400;
  src: local('Montserrat Regular'), local(montserrat-regular),
    url(https:fonts.gstatic.com/s/montserrat/v14/JTUSjIg1_i6t8kCHKm459Wlhyw.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F,
    U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: 'Montserrat';
  font-style: normal;
  font-weight: 500;
  src: local('Montserrat Medium'), local(montserrat-medium),
    url(https://fonts.gstatic.com/s/montserrat/v14/JTURjIg1_i6t8kCHKm45_ZpC3gnD_g.woff2)
      format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F,
    U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: 'Montserrat';
  font-style: normal;
  font-weight: 600;
  src: local('Montserrat SemiBold'), local(montserrat-semibold),
    url(https://fonts.gstatic.com/s/montserrat/v14/JTURjIg1_i6t8kCHKm45_bZF3gnD_g.woff2)
      format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F,
    U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

#su__recommendation-widget * {
  font-family: 'Montserrat', sans-serif;
  box-sizing: border-box;
}

.su__font-12 {
  font-size: 12px;
  line-height: 19px;
}
.su__font-15 {
  font-size: 15px;
}
.su__line-height-20 {
  line-height: 20px;
}

.su__line-height-22 {
  line-height: 22px;
}

.su__whitespace-initial {
  white-space: initial;
}

.su__text-decoration-none li a {
  text-decoration: none;
}

.su__font-weight-bold {
  font-weight: 700;
}

.su__sm-shadow {
  -webkit-box-shadow: 0 1px 5px 0 rgba(var(--su__recommendationapp-black-rgba), 0.14);
  -moz-box-shadow: 0 1px 5px 0 rgba(var(--su__recommendationapp-black-rgba), 0.14);
  box-shadow: 0 1px 5px 0 rgba(var(--su__recommendationapp-black-rgba), 0.14);
}

.su__border {
  border: 1px solid var(--su__recommendationapp-lightgrey);
}

.su__border-b {
  border-bottom: 1px solid var(--su__recommendationapp-lightgrey);
}

.su__position-relative {
  position: relative;
}

.su__w-30 {
  width: 30%;
}

.su__w-100 {
  width: 100%;
}

.su__d-flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

.su__mb-3,
.su__my-3,
.su__rtl .su__rtlmb-3,
.su__rtl .su__rtlmy-3 {
  margin-bottom: 1rem;
}

.su__m-0,
.su__rtl .su__rtlm-0 {
  margin: 0;
}

.su__p-1,
.su__rtl .su__rtlp-1 {
  padding: 0.25rem;
}

.su__pt-1,
.su__py-1,
.su__rtl .su__rtlpt-1,
.su__rtl .su__rtlpy-1 {
  padding-top: 0.25rem;
}

.su__pr-1,
.su__px-1,
.su__rtl .su__rtlpr-1,
.su__rtl .su__rtlpx-1 {
  padding-right: 0.25rem;
}

.su__pb-1,
.su__py-1,
.su__rtl .su__rtlpb-1,
.su__rtl .su__rtlpy-1 {
  padding-bottom: 0.25rem;
}

.su__pt-2,
.su__py-2,
.su__rtl .su__rtlpt-2,
.su__rtl .su__rtlpy-2 {
  padding-top: 0.5rem;
}

.su__pb-2,
.su__py-2,
.su__rtl .su__rtlpb-2,
.su__rtl .su__rtlpy-2 {
  padding-bottom: 0.5rem;
}

.su__pl-2,
.su__px-2,
.su__rtl .su__rtlpl-2,
.su__rtl .su__rtlpx-2 {
  padding-left: 0.5rem;
}

.su__p-3,
.su__rtl .su__rtlp-3 {
  padding: 1rem;
}

.su__pt-3,
.su__py-3,
.su__rtl .su__rtlpt-3,
.su__rtl .su__rtlpy-3 {
  padding-top: 1rem;
}

.su__pr-3,
.su__px-3,
.su__rtl .su__rtlpr-3,
.su__rtl .su__rtlpx-3 {
  padding-right: 1rem;
}

.su__pb-3,
.su__py-3,
.su__rtl .su__rtlpb-3,
.su__rtl .su__rtlpy-3 {
  padding-bottom: 1rem;
}

.su__pl-3,
.su__px-3,
.su__rtl .su__rtlpl-3,
.su__rtl .su__rtlpx-3 {
  padding-left: 1rem;
}

.su__p-0,
.su__rtl .su__rtlp-0 {
  padding: 0;
}

.su__text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.su__text-center {
  text-align: center;
}

.su__text-decoration {
  text-decoration: none;
}

.su__d-block {
  display: block;
}
.su__my-0 {
  margin-top: 0;
  margin-bottom: 0;
}
.su__justify-content-between {
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
}

.su__align-items-center {
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.su__grid-content .su__list-items .su__list-item-title {
  margin-top: 0.15rem;
  flex-direction: column;
  align-items: stretch;
}

.su__loading1 .su__loading-view {
  position: relative;
  user-select: none;
  pointer-events: none;
}

.su__listed-item .su__list-item-title {
  max-width: calc(100% - 20px);
}

.su__Recommended_Article {
  font-size: 17px;
  color: var(--su__recommendationapp-jetgray);
  font-weight: 500;
  line-height: 22px;
  font-family: 'Montserrat', sans-serif;
}

.su__Recommended_Articles-R {
  border-radius: 12px;
}
.su__recommendations-tag-content {
  display: inline-block;
}

.su__recommendation-label {
  padding: 5px 6px;
  font-size: 11px;
  background: var(--su__recommendationapp-white) 0% 0% no-repeat padding-box;
  border: 1px solid var(--su__recommendationapp-lightgrey);
  border-radius: 5px;
  opacity: 1;
  display: inline-block;
  color: var(--su__recommendationapp-gray);
  margin-bottom: 2px;
}

.su__recommendations-ViewMore {
  width: 100%;
  text-align: center;
  padding: 15px 0;
}

.su__recommendations-ViewMore-text {
  color: var(--su__recommendationapp-deepskyblue-light) !important;
  font-size: 14px;
}

.su__recommendations-ViewMore-text:hover {
  text-decoration: underline !important;
  cursor: pointer !important;
}

.su__Recomended_border-t {
  border-top: 2px solid var(--su__recommendationapp-lightgrey);
}

.su__recommendations-content {
  letter-spacing: 0;
  color: var(--su__recommendationapp-jetgray);
}

.su__border-b:hover .su__recommendations-content {
  color: var(--su__recommendationapp-deepskyblue-light);
}

.su__recommendations-content:hover {
  text-decoration: underline;
}

.su__Recomended_border-b {
  border-bottom: 1px solid var(--su__recommendationapp-lightgrey);
}

.su__recommendations-title {
  line-height: 15px;
  font-weight: normal;
}

#su__recommendation-widget .su__Recommended_Article-section {
  min-width: 100%;
  max-width: 100%;
  margin-right: 0;
}

.su__Recomended_border-widget-b {
  border-bottom: 2px solid var(--su__recommendationapp-lightgrey);
}

.su__Recomended_border-widget-b:hover {
  border-bottom: 2px solid var(--su__recommendationapp-deepskyblue-light);
}

.su__Recomended_border-b:last-child {
  border: none;
}

.su__flex-column {
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
}
.su__recommendation-inner-div {
  background: var(--su__recommendationapp-white) 0% 0% no-repeat padding-box;
  box-shadow: 0 0 6px rgba(var(--su__recommendationapp-black-rgba), 0.161);
  border-radius: 4px;
  margin-top: 0.5rem;
}
.su__recommdation-heading {
  padding-bottom: 0.5rem;
}
@media (min-width: 1200px) {
  .su__d-xl-none {
    display: none;
  }

  .su__d-xl-inline {
    display: inline;
  }

  .su__d-xl-inline-block {
    display: inline-block;
  }

  .su__d-xl-block {
    display: block;
  }

  .su__d-xl-table {
    display: table;
  }

  .su__d-xl-flex {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
  }

  .su__d-xl-inline-flex {
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
  }
}

.su__Recommended_Article-max-width {
  max-width: 100%;
  margin-right: 18px;
  text-align: left;
  word-break: break-word;
}

#su__recommendation-widge {
  margin: 5px;
}

.su__recommendations-content:focus {
  color: var(--su__recommendationapp-blue-6);
  text-decoration: none;
}

.su__recommendations-ViewMore a:focus {
  color: var(--su__recommendationapp-deepskyblue-light);
  text-decoration: none;
}
.su__recommdation-article {
  padding-top: 0.5rem;
  font-size: 15px;
  font-weight: 400;
}

.su__truncate-two-lines {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}
