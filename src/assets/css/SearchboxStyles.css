:root {
  /* ---------- Hex Colors ---------- */
  --su__searchboxapp-deepskyblue-light: #1770d4;
  --su__searchboxapp-darkgray: #333;
  --su__searchboxapp-violet: #609;
  --su__searchboxapp-gray: #666;
  --su__searchboxapp-lightblue-alt: #92c4f3;
  --su__searchboxapp-paleblue-cool: #d9ebff;
  --su__searchboxapp-lightgray-14: #f2f2f2;
  --su__searchboxapp-white: #fff;
  --su__searchboxapp-black-rgba: 0, 0, 0;
  --su__searchboxapp-jetgray-rgba: 102, 102, 102;
  --su__searchboxapp-gray-44: rgba(125, 112, 112, 0.439);
  --su__searchapp-deepskyblue-light: #1770d4;
}

@font-face {
  font-family: 'Montserrat';
  font-style: normal;
  font-weight: 300;
  src: local('Montserrat Light'), local(montserrat-light),
    url(https://fonts.gstatic.com/s/montserrat/v14/JTURjIg1_i6t8kCHKm45_cJD3gTD_u50.woff2)
      format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}

@font-face {
  font-family: 'Montserrat';
  font-style: normal;
  font-weight: 400;
  src: local('Montserrat Regular'), local(montserrat-regular),
    url(https://fonts.gstatic.com/s/montserrat/v14/JTUSjIg1_i6t8kCHKm459Wlhyw.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F,
    U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: 'Montserrat';
  font-style: normal;
  font-weight: 500;
  src: local('Montserrat Medium'), local(montserrat-medium),
    url(https://fonts.gstatic.com/s/montserrat/v14/JTURjIg1_i6t8kCHKm45_ZpC3gnD_g.woff2)
      format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F,
    U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: 'Montserrat';
  font-style: normal;
  font-weight: 600;
  src: local('Montserrat SemiBold'), local(montserrat-semibold),
    url(https://fonts.gstatic.com/s/montserrat/v14/JTURjIg1_i6t8kCHKm45_bZF3gnD_g.woff2)
      format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F,
    U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

.su__app-running,
.su__autocomplete-running {
  margin: 0;
  padding: 0;
}

.su__app {
  background: var(--su__searchboxapp-lightgray-14);
  display: inline-block;
  width: 100%;
  position: relative;
}

.su__wrapper ul {
  list-style: none;
}

.su__wrapper .su__tooltip {
  display: inline;
}

.su__viewed-results,
.su_page_rating_box,
.su__viewed-results *,
.su__no-view-results,
#su_autocomplete-block,
#su_autocomplete-block *,
.su__wrapper,
#su__wrapper,
#su__wrapper * {
  font-family: 'Montserrat', sans-serif;
  box-sizing: border-box;
}

.su__viewed-results a:focus-visible {
  border: 1px solid var(--su__searchboxapp-deepskyblue-light);
}

.su__viewed-results a:visited,
.su__viewed-results a:visited h2,
.su__viewed-results a:visited .highlight {
  color: var(--su__searchboxapp-violet) !important;
}
.su__viewed-results .su__multi-version-txt {
  color: var(--su__searchboxapp-lightblue-alt);
}

.su__viewed-results .su__bookmarkSavedResultText:visited {
  color: var(--su__searchboxapp-deepskyblue-light) !important;
  text-decoration: none !important;
}

.su__viewed-results .su__deletedSavedResultText {
  color: var(--su__searchboxapp-deepskyblue-light) !important;
  text-decoration: none !important;
}

.su__viewed-results .su__deletedSavedResultText:hover {
  color: var(--su__searchboxapp-deepskyblue-light) !important;
  text-decoration: none !important;
}

.su__viewed-results .su__deletedSavedResultText:visited {
  color: var(--su__searchboxapp-deepskyblue-light) !important;
  text-decoration: none !important;
}

.su__w-100 {
  width: 100%;
}

.su__pt-4,
.su__py-4,
.su__rtl .su__rtlpt-4,
.su__rtl .su__rtlpy-4 {
  padding-top: 1.5rem;
}

.su__pb-4,
.su__py-4,
.su__rtl .su__rtlpb-4,
.su__rtl .su__rtlpy-4 {
  padding-bottom: 1.5rem;
}
.su__mb-1,
.su__my-1,
.su__rtl .su__rtlmb-1,
.su__rtl .su__rtlmy-1 {
  margin-bottom: 0.25rem;
}
.su__bg-blue-grd {
  color: var(--su__searchboxapp-white);
  background-color: var(--su__searchboxapp-deepskyblue-light);
}
.su__container {
  width: 100%;
  max-width: 1200px;
  padding: 0 15px;
  margin: 0 auto;
}

@media (min-width: 320px) {
  .su__container {
    max-width: 96%;
    padding: 0 4px;
  }
}

@media (min-width: 576px) {
  .su__container {
    max-width: 640px;
    padding: 0 5px;
  }
}

@media (min-width: 768px) {
  .su__container {
    max-width: 736px;
  }
}

@media (min-width: 992px) {
  .su__container {
    max-width: 960px;
  }
}

@media (min-width: 1200px) {
  .su__container {
    max-width: 1280px;
  }
}

@media (min-width: 1400px) {
  .su__container {
    max-width: 1370px;
  }
}

@media (min-width: 1600px) {
  .su__container {
    max-width: 1570px;
  }
}

.su__container ul {
  list-style: none;
  margin: 0;
}

.su__m-0,
.su__rtl .su__rtlm-0 {
  margin: 0;
}

.su__position-relative {
  position: relative;
}

.su__radius-2 {
  -webkit-border-radius: 6px;
  -moz-border-radius: 6px;
  border-radius: 6px;
  -ms-border-radius: 6px;
  -o-border-radius: 6px;
}

.su__d-flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

.su__input-search {
  background: var(--su__searchboxapp-lightgray-14);
  padding: 0 40px 0 40px;
  height: 42px;
  box-shadow: 0 1px 3px 0 rgba(var(--su__searchboxapp-black-rgba), 0.2),
    0 1px 1px 0 rgba(var(--su__searchboxapp-black-rgba), 0.14),
    0 2px 1px -1px rgba(var(--su__searchboxapp-black-rgba), 0.12);
  font-family: 'Montserrat', sans-serif;
}
.su__input-search:focus:not(:focus-visible) {
  outline: 1px solid transparent;
}

.su__rtl .su__input-search {
  padding: 0 1.4rem 0 5rem;
}
.su__input-search::-ms-clear {
  display: none;
}

.su__text-black {
  color: rgba(var(--su__searchboxapp-jetgray-rgba), 0.8);
}

.su__border-none {
  border: none;
}

.su__input-close {
  right: 25px;
  top: 13px;
  display: flex;
  height: 19px;
  align-items: center;
}
.su__rtl .su__input-close {
  right: auto;
  left: 46px;
  top: 13px;
}

.su__mr-4,
.su__mx-4,
.su__rtl .su__rtlmr-4,
.su__rtl .su__rtlmx-4 {
  margin-right: 1.5rem;
}

.su__animate-zoom {
  -webkit-animation: zoomin 400ms 200ms both;
  animation: zoomIn 400ms 200ms both;
}

.su__position-absolute {
  position: absolute;
}

.su__cursor:not(:focus-visible) {
  cursor: pointer;
  outline: none;
}

.su__btn:not(:focus-visible) {
  border: none;
  outline: none;
  cursor: pointer;
}

.su__btn,
.su__filters-button,
.su__btn-block {
  background-image: none !important;
}

.su__search_btn {
  left: 0;
  padding: 9px 10px 5px;
  height: 100%;
}

#auto .su__search_btn {
  height: 100%;
  padding: 0 13px;
}

#auto .su__search_btn svg {
  visibility: visible;
}

.su__flex-vcenter {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.su__zindex-1 {
  z-index: 9;
}

.su__zindex-2 {
  z-index: 99;
}

.su__zindex-3 {
  z-index: 999;
}

.su__zindex-4 {
  z-index: 9999;
}

.su__zindex-5 {
  z-index: 99999;
}

.su__zindex-custom {
  z-index: 10;
}

.su__bg-transparent,
.su__btn-transparent {
  background-color: transparent;
}

.su__rtl .su__rtlleft {
  right: auto;
  left: 0;
}

.su__rtl .su__rtlleft-auto {
  left: auto;
}

.su__linear-loader {
  position: absolute;
  left: 3px;
  width: calc(100% - 6px);
  bottom: 0;
}

.su__autocomplete-suggestion .su__suggesticon {
  top: 0;
}

@media all and (-ms-high-contrast: none) {
  .su__select-control::-ms-expand {
    display: none;
  }

  .su__select-control {
    -webkit-appearance: none;
    appearance: none;
  }

  .su__lang-icons {
    top: 3px;
  }

  .su__wrapper .su__tooltip {
    display: inline;
  }

  .su__Language-boxs {
    width: 120px;
    margin: 0 10px;
  }

  .su__filter-checkboxs,
  .su__toggle-input,
  .su__filter-checkbox {
    visibility: hidden;
  }

  .su__switch-view-layout-2 {
    margin: 0 0 0 20px;
  }

  .su__fillter-sortby-layout-2 {
    margin: 0 65px 0 15px;
  }

  .su__autocomplete-suggestion .su__suggesticon {
    top: -2px;
  }

  .su__filters-button:focus,
  .su__key-focus:focus,
  .su__tabs:focus,
  .su__filter-toggle:focus {
    box-shadow: none;
  }

  .su__filter-toggle input[type='radio'] + label::before,
  .su__filter-toggle input[type='checkbox'] + label::before {
    top: 2px;
  }

  .su__filter-toggle input[type='radio']:checked + label::after {
    top: 4px;
    left: 6px;
  }

  .su__filter-toggle input[type='checkbox']:checked + label::after {
    top: 2px;
  }

  .su__nested-arrow .su__arrow-right {
    top: -1px;
    left: 0;
  }

  .swapFilterLeft .footerSection {
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }
}

.su__allContent .su-tabsSection.su__bg-white {
  border-top: none;
  padding-left: 0.5rem;
  margin-right: 3rem;
  margin-top: 1rem;
}

.su__bg-white,
.su__btn-white {
  background-color: var(--su__searchboxapp-white);
}

.su__sm-shadow {
  -webkit-box-shadow: 0 1px 5px 0 rgba(var(--su__searchboxapp-black-rgba), 0.14);
  -moz-box-shadow: 0 1px 5px 0 rgba(var(--su__searchboxapp-black-rgba), 0.14);
  box-shadow: 0 1px 5px 0 rgba(var(--su__searchboxapp-black-rgba), 0.14);
}

.su__suggestions-box {
  height: auto;
  max-height: 300px;
  overflow-x: hidden;
}

.su__suggestions-box::before,
.su__no-suggestions::before {
  content: '';
  left: 0;
  right: 0;
  bottom: 10px;
  position: absolute;
  top: 0;
  background: var(--su__searchboxapp-white);
  z-index: -1;
}

.su__minscroller::-webkit-scrollbar {
  width: 4px;
  height: 0;
  background-color: var(--su__searchboxapp-lightgray-14);
}

.su__minscroller::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 6px rgba(var(--su__searchboxapp-black-rgba), 0.3);
  box-shadow: inset 0 0 6px rgba(var(--su__searchboxapp-black-rgba), 0.3);
  background-color: var(--su__searchboxapp-lightgray-14);
  border-radius: 10px;
  -webkit-border-radius: 10px;
  -moz-border-radius: 10px;
  -ms-border-radius: 10px;
  -o-border-radius: 10px;
}

.su__minscroller::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background: var(--su__searchboxapp-gray);
  -webkit-border-radius: 10px;
  -moz-border-radius: 10px;
  -ms-border-radius: 10px;
  -o-border-radius: 10px;
}

.su__minscroller {
  scrollbar-width: thin;
  scrollbar-color: var(--su__searchboxapp-gray) transparent;
}

.su__suggestions-list {
  padding: 8px 15px;
  line-height: 30px;
  height: auto;
  font-size: 12px;
}

.auto-suggestion .su__bg-gray-hover:hover,
.auto-suggestion .su__bg-gray-hover:focus {
  background: var(--su__searchboxapp-paleblue-cool);
  border-radius: 4px;
}
.su__autosuggestion_container {
  margin: 0 24px 0 10px;
}

.su__bg-light-gray,
.su__btn-light-gray,
.su__bg-gray-hover:hover {
  background: var(--su__searchboxapp-lightgray-14);
}
.su__font-14 {
  font-size: 14px;
  line-height: 20px;
}

.su__highlight_result {
  background-color: var(--su__searchboxapp-paleblue-cool);
  border-radius: 4px;
}

.su__text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.su__product-sugt-row {
  position: absolute;
  width: 100%;
  left: 0;
  right: 0;
  overflow-y: auto;
  max-height: 250px;
}

.su__product-length-1 {
  bottom: -88px;
}

.su__product-length-2 {
  bottom: -176px;
}

.su__product-length-3 {
  bottom: -249px;
}

.su__product-length-4 {
  bottom: -352px;
}

.su__product-length-5 {
  bottom: -441px;
}

.su__product-suggestion {
  background: var(--su__searchboxapp-white);
  padding: 12px 15px;
  line-height: normal;
  height: auto;
  font-size: 11px;
  color: var(--su__searchboxapp-darkgray);
  border: 1px dotted var(--su__searchboxapp-gray-44);
}

.su__flex-wrap {
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.su__flex-wrap-reverse {
  -ms-flex-wrap: wrap-reverse;
  flex-wrap: wrap-reverse;
}
.su__mb-3,
.su__my-3,
.su__rtl .su__rtlmb-3,
.su__rtl .su__rtlmy-3 {
  margin-bottom: 1rem;
}

.su__f-bold {
  font-weight: 600;
}

.su__font-13 {
  font-size: 13px;
  line-height: 18px;
}
.su__fill-red {
  fill: var(--su__searchboxapp-deepskyblue-light);
}

.su__ml-3,
.su__mx-3,
.su__rtl .su__rtlml-3,
.su__rtl.su__rtlmx-3 {
  margin-left: 1rem;
}

.su__product-sugg-category {
  background: var(--su__searchboxapp-white);
  border-radius: 4px;
  border: 1px solid var(--su__searchboxapp-gray);
  padding: 4px 15px;
  white-space: nowrap;
  margin-right: 8px;
}

.su__product-sugg-category.su__product-viewmore {
  padding-left: 22px;
}

.su__product-sugg-category.su__product-active {
  background: var(--su__searchboxapp-deepskyblue-light);
  color: var(--su__searchboxapp-white);
  border-color: var(--su__searchboxapp-deepskyblue-light);
}
.su__cursor_pointer {
  cursor: pointer;
}

.su__recent_search_text {
  font-size: 13px;
  color: var(--su__searchboxapp-gray);
  margin-top: 10px;
  margin-left: 10px;
  margin-bottom: 14px;
}

.su__autoSuggestion {
  border-radius: 0 0 10px 10px;
}
.su__autosuggestion-icon {
  height: 20px;
}

.su__recent-icon {
  position: relative;
  top: 4px;
}
.su__align-items-center {
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.su__mb-10 {
  margin-bottom: 10px;
}
.su__suggesticon {
  position: relative;
  display: flex;
  align-items: center;
}

.su__mr-2,
.su__mx-2,
.su__rtl .su__rtlmr-2,
.su__rtl .su__rtlmx-2 {
  margin-right: 0.5rem;
}

.su__fs-media .su__featured-thumbnail > .su__mr-2 {
  margin: 0;
}

.su__recentSearch_result {
  margin-left: 7px;
  font-size: 16px;
  color: var(--su__searchboxapp-darkgray);
}
.su__line-height-n {
  line-height: normal;
}
.su__color-lgray,
.su__hover-lgray:hover {
  color: var(--su__searchboxapp-deepskyblue-light);
}

.su__text-decoration {
  text-decoration: none;
}

.su__color-black {
  color: var(--su__searchboxapp-darkgray);
}
.su__align_flex {
  display: flex;
}
.su__font-11 {
  font-size: 11px;
}
.su__color-gray {
  color: var(--su__searchboxapp-darkgray);
}
.su__description-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: inline-block;
  padding-left: 5px;
}
.su__autoSuggestion-border {
  border-radius: 6px;
  overflow: hidden;
}

.su__suggestions-box_mobile {
  height: auto;
  overflow-x: hidden;
}
.su__suggestions-box-height {
  max-height: 416px;
}
.su__mb-4,
.su__my-4,
.su__rtl .su__rtlmb-4,
.su__rtl .su__rtlmy-4 {
  margin-bottom: 1.5rem;
}

.su__font-16 {
  font-size: 16px;
}

.su__autocomplete_alignment {
  margin-left: 7px;
  line-height: 5px;
  gap: 5px;
}

.su__flex-column {
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
}

.su__mt-6px {
  margin-top: 6px;
}

.su__rtl .su__recent_search_text {
  margin-right: 20px;
}

.su__rtl .su__suggested_text {
  margin-right: 20px;
}

.su__center-element {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
}

.su__padding_10 {
  padding: 10px;
}

.su__flex-hcenter {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
}

.su__sso-loader-autocomplete {
  margin: auto;
  width: 30px;
  height: 30px;
  border: 5px solid #f3f3f3;
  border-top: 5px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.su__sso-loader {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  margin: auto;
  width: 50px;
  height: 50px;
  border: 5px solid var(--su__searchapp-lightgray-14);
  border-top: 5px solid var(--su__searchapp-deepskyblue-light);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@-webkit-keyframes spin {
  from {
    -webkit-transform: rotate(0deg);
  }

  to {
    -webkit-transform: rotate(360deg);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

.highlight {
  color: var(--su__searchapp-deepskyblue-light);
}
