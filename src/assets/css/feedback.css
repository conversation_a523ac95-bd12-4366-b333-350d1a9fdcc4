:root {
  /* ---------- Hex Colors ---------- */
  --su__feedbackapp-vividgreen: #1e8202;
  --su__feedbackapp-red: #e91b37;
  --su__feedbackapp-white: #fff;
  --su__feedbackapp-lightgrey: #d3d3d3;
  --su__feedbackapp-deepskyblue-light: #1770d4;
  --su__feedbackapp-paleblue-cool: #d9ebff;
  --su__feedbackapp-lightgray-14: #f2f2f2;
  --su__feedbackapp-darkgray: #333;
  --su__feedbackapp-black-rgba: 0, 0, 0;
  --su__feedbackapp-jetgray-rgba: 102, 102, 102;
  --su__feedbackapp-rgba-gray-30: rgba(236, 236, 236, 0.3);
}

@font-face {
  font-family: 'Montserrat';
  font-style: normal;
  font-weight: 300;
  src: local('Montserrat Light'), local(montserrat-light),
    url(https://fonts.gstatic.com/s/montserrat/v14/JTURjIg1_i6t8kCHKm45_cJD3gTD_u50.woff2)
      format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}

@font-face {
  font-family: 'Montserrat';
  font-style: normal;
  font-weight: 400;
  src: local('Montserrat Regular'), local(montserrat-regular),
    url(https://fonts.gstatic.com/s/montserrat/v14/JTUSjIg1_i6t8kCHKm459Wlhyw.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F,
    U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: 'Montserrat';
  font-style: normal;
  font-weight: 500;
  src: local('Montserrat Medium'), local(montserrat-medium),
    url(https://fonts.gstatic.com/s/montserrat/v14/JTURjIg1_i6t8kCHKm45_ZpC3gnD_g.woff2)
      format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F,
    U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: 'Montserrat';
  font-style: normal;
  font-weight: 600;
  src: local('Montserrat SemiBold'), local(montserrat-semibold),
    url(https://fonts.gstatic.com/s/montserrat/v14/JTURjIg1_i6t8kCHKm45_bZF3gnD_g.woff2)
      format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F,
    U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

.su__font-12 {
  font-size: 12px;
  line-height: 19px;
}
.su__font-13 {
  font-size: 13px !important;
  line-height: 18px;
}
.su__font-16 {
  font-size: 16px;
}
.su__font-17 {
  font-size: 17px;
}
.su__font-11 {
  font-size: 11px;
}
.su__font-10 {
  font-size: 10px;
}
.su__fontsize-13 {
  font-size: 13px;
}
.su__line-height-16 {
  line-height: 16px;
}

#suPageRateApp * {
  font-family: 'Montserrat', sans-serif;
  box-sizing: border-box;
}

.su_page_rating_box {
  display: block;
  padding: 10px;
}

.su_page_rating_box p {
  margin: 3px 5px;
}

.su_page_rating_box .su-featured-text {
  color: var(--su__feedbackapp-darkgray);
  font-size: 12px;
  padding: 10px 10px 5px;
  float: right;
}

.su_page_rating_box .su_helpful {
  display: inline-block;
  border: 1px solid var(--su__feedbackapp-vividgreen);
  background-color: var(--su__feedbackapp-vividgreen);
  padding: 2px 10px;
  border-radius: 2px;
  color: var(--su__feedbackapp-white);
}

.su_page_rating_box .su_helpful:hover {
  cursor: pointer;
  box-shadow: 2px 2px 3px 3px;
}

.su_page_rating_box .su_unhelpful {
  display: inline-block;
  border: 1px solid var(--su__feedbackapp-red);
  background-color: var(--su__feedbackapp-red);
  padding: 2px 10px;
  border-radius: 2px;
  color: var(--su__feedbackapp-white);
}

.su_page_rating_box .su_unhelpful:hover {
  cursor: pointer;
  box-shadow: 2px 2px 3px 3px;
}

.su_page_rating_box .su_feedback_success {
  color: var(--su__feedbackapp-white);
  font-size: 14px;
  padding: 10px;
  background-color: var(--su__feedbackapp-vividgreen);
  border-radius: 3px;
  width: 20%;
  text-align: center;
}

.su_feedback_form {
  border: 1px solid var(--su__feedbackapp-lightgray-14);
  color: var(--su__feedbackapp-white);
  font-size: 14px;
  padding: 10px 40px;
  text-align: center;
  background: var(--su__feedbackapp-white);
  box-shadow: 0 1px 7px var(--su__feedbackapp-lightgrey);
  border-radius: 6px;
  max-width: 350px;
}

.su__pagerating-row {
  background: var(--su__feedbackapp-white);
  box-shadow: 0 1px 7px rgba(var(--su__feedbackapp-black-rgba), 0.161);
}

.su__pagerating-row,
.su_feedback_form {
  max-width: max-content;
  margin-bottom: 5px;
}

.su__pagerating-lay .su__close-svg {
  width: 12px;
  height: 12px;
}

.su__feedback-label {
  padding-right: 14px;
}

.su__feedback-modal,
.su__feedshow-bottom,
.su__feedshow-center {
  overflow: hidden;
  word-break: break-all;
}

.su__feed-desc {
  padding-right: 15px;
}

.su__feedbackform_custom {
  max-width: none !important;
  min-width: auto;
  width: fit-content;
}
.su__zindex-3 {
  z-index: 999;
}
.su__position-relative {
  position: relative;
}
.su__border {
  border: 1px solid var(--su__feedbackapp-lightgrey);
}
.su__feedback-pops {
  background: var(--su__feedbackapp-white) 0% 0% no-repeat padding-box;
  box-shadow: 0 0 20px rgba(var(--su__feedbackapp-black-rgba), 0.161);
  border-radius: 4px;
}
.su__feedshow-center {
  max-width: 480px;
  position: fixed;
  z-index: 99;
  left: 0;
  right: 0;
  margin: auto;
  top: 50%;
  transform: translateY(-50%);
  border-radius: 6px;
}
.su__feedshow-bottom {
  max-width: 480px;
}

.su__d-none {
  display: none;
}
.su__justify-content-center {
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
}

.su__text-left {
  text-align: left;
}

.su__flex-1,
.su__media-body {
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

.su__ml-3,
.su__mx-3,
.su__rtl .su__rtlml-3,
.su__rtl.su__rtlmx-3 {
  margin-left: 1rem;
}

.su__mr-3,
.su__mx-3,
.su__rtl .su__rtlmr-3,
.su__rtl .su__rtlmx-3 {
  margin-right: 1rem;
}
.su__pr-3,
.su__px-3,
.su__rtl .su__rtlpr-3,
.su__rtl .su__rtlpx-3 {
  padding-right: 1rem;
}
.su__pl-3,
.su__px-3,
.su__rtl .su__rtlpl-3,
.su__rtl .su__rtlpx-3 {
  padding-left: 1rem;
}
.su__mb-0,
.su__my-0,
.su__rtl .su__rtlmb-0,
.su__rtl .su__rtlmy-0 {
  margin-bottom: 0;
}

.su__mt-0,
.su__my-0,
.su__rtl .su__rtlmt-0,
.su__rtl .su__rtlmy-0 {
  margin-top: 0;
}
.su__pt-3,
.su__py-3,
.su__rtl .su__rtlpt-3,
.su__rtl .su__rtlpy-3 {
  padding-top: 1rem;
}
.su__pb-3,
.su__py-3,
.su__rtl .su__rtlpb-3,
.su__rtl .su__rtlpy-3 {
  padding-bottom: 1rem;
}
.su__f-medium {
  font-weight: 500;
}

.su__close-svg {
  right: 6px;
  top: 19px;
}
.su__radius-1 {
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  border-radius: 4px;
  -ms-border-radius: 4px;
  -o-border-radius: 4px;
}
.su__position-absolute {
  position: absolute;
}

.su__radius-50 {
  border-radius: 50%;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -ms-border-radius: 50%;
  -o-border-radius: 50%;
}

.su__bg-white-circle {
  background: var(--su__feedbackapp-rgba-gray-30);
}

.su__bg-light-gray,
.su__btn-light-gray,
.su__bg-gray-hover:hover {
  background: var(--su__feedbackapp-lightgray-14);
}
.su__mt-2,
.su__my-2,
.su__rtl .su__rtlmt-2,
.su__rtl .su__rtlmy-2 {
  margin-top: 0.5rem;
}
.su__mb-3,
.su__my-3,
.su__rtl .su__rtlmb-3,
.su__rtl .su__rtlmy-3 {
  margin-bottom: 1rem;
}
.su__mb-2,
.su__my-2,
.su__rtl .su__rtlmb-2,
.su__rtl .su__rtlmy-2 {
  margin-bottom: 0.5rem;
}

.su__mt-3,
.su__my-3,
.su__rtl .su__rtlmt-3,
.su__rtl .su__rtlmy-3 {
  margin-top: 1rem;
}
.su__feedtext-area:focus:not(:focus-visible),
.su__input-feedack:not(:focus-visible) {
  border: 1px solid var(--su__feedbackapp-lightgrey);
  outline: none;
}

.su__thanks_min_width {
  min-width: max-content;
}

.su__thanks_modal_padding {
  padding: 10px 42px 20px 42px;
}
.su_thumb-yes {
  margin-right: 0 !important;
}
.su__height_email {
  height: 40px;
}
.su__thankyou-container {
  display: flex;
  justify-content: center;
  height: 36px;
  align-items: center;
  background: var(--su__feedbackapp-paleblue-cool);
  border-radius: 4px;
}
.su__thanks-popupWidth {
  width: 350px;
}
.su__thanks-svg {
  margin: 6px 8px 0 0;
}
.su__input-feedback-height {
  height: 97px;
}
.su__thankyou-widget {
  height: 54px;
  width: 334px;
}
@media (max-width: 767px) {
  .su__form-control {
    padding: 0.475rem 0.55rem;
    height: 36px;
  }
}
.su__w-100 {
  width: 100%;
}
.su__justify-content-between {
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
}
.su__form-control {
  box-sizing: border-box;
  display: block;
  width: 100%;
  padding: 0.72rem;
  line-height: normal;
  background-color: var(--su__feedbackapp-white);
  background-clip: padding-box;
  border: 1px solid var(--su__feedbackapp-lightgrey);
  border-radius: 0.25rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  font-size: 14px;
  color: var(--su__feedbackapp-darkgray);
}
.su__radius-2 {
  -webkit-border-radius: 6px;
  -moz-border-radius: 6px;
  border-radius: 6px;
  -ms-border-radius: 6px;
  -o-border-radius: 6px;
}

.su__text-black {
  color: rgba(var(--su__feedbackapp-jetgray-rgba), 0.8);
}
.su__pt_5px {
  padding-top: 5px;
}
.su__color-lgray,
.su__hover-lgray:hover {
  color: var(--su__feedbackapp-darkgray);
}

.su__f-normal {
  font-weight: 400;
}
.su__flex-vcenter {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

@media (max-width: 767px) {
  .su__emoji_border {
    width: 48px;
    height: 48px;
    border: 1px solid var(--su__feedbackapp-lightgrey);
    display: flex;
    justify-content: center;
    align-items: center;
    margin-right: 18px;
    border-radius: 35px;
  }
}
.su__emoji_border {
  width: 68px;
  height: 65px;
  border: 1px solid var(--su__feedbackapp-lightgrey);
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 20px;
  border-radius: 35px;
}
.su__cursor:not(:focus-visible) {
  cursor: pointer;
  outline: none;
}
@media (max-width: 767px) {
  .su__emoji {
    width: 25px;
    height: 25px;
    background-size: 100%;
  }
}
.su__emoji-icon0 {
  background-image: url('data:image/svg+xml;base64,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');
}

.su__emoji-icon1 {
  background-image: url('data:image/svg+xml;base64,PHN2ZyBpZD0iR3JvdXBfMTg2NzciIGRhdGEtbmFtZT0iR3JvdXAgMTg2NzciIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgd2lkdGg9IjQwLjU4OCIgaGVpZ2h0PSI0MC41ODgiIHZpZXdCb3g9IjAgMCA0MC41ODggNDAuNTg4Ij4KICA8cGF0aCBpZD0iVmVjdG9yIiBkPSJNMjAuMjk0LDM2LjcyM0ExNi40MjksMTYuNDI5LDAsMSwwLDMuODY2LDIwLjI5NCwxNi40MjksMTYuNDI5LDAsMCwwLDIwLjI5NCwzNi43MjNaTTQwLjU4OCwyMC4yOTRBMjAuMjk0LDIwLjI5NCwwLDEsMSwyMC4yOTQsMCwyMC4yOTQsMjAuMjk0LDAsMCwxLDQwLjU4OCwyMC4yOTRaIiBmaWxsPSIjMTc3MGQ0IiBmaWxsLXJ1bGU9ImV2ZW5vZGQiLz4KICA8cGF0aCBpZD0iVmVjdG9yLTIiIGRhdGEtbmFtZT0iVmVjdG9yIiBkPSJNOC44LDE2LjcxOGExLjIxOCwxLjIxOCwwLDAsMSwyLjQzNSwwLDEuODI2LDEuODI2LDAsMCwwLDMuNjUzLDAsMS4yMTgsMS4yMTgsMCwwLDEsMi40MzUsMCw0LjI2Miw0LjI2MiwwLDEsMS04LjUyNCwwWiIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoMC4xMyAwLjIyOCkiIGZpbGw9IiMxNzcwZDQiLz4KICA8cGF0aCBpZD0iVmVjdG9yLTMiIGRhdGEtbmFtZT0iVmVjdG9yIiBkPSJNMjIuOCwxNi43MThhMS4yMTgsMS4yMTgsMCwwLDEsMi40MzUsMCwxLjgyNiwxLjgyNiwwLDEsMCwzLjY1MywwLDEuMjE4LDEuMjE4LDAsMCwxLDIuNDM1LDAsNC4yNjIsNC4yNjIsMCwwLDEtOC41MjQsMFoiIHRyYW5zZm9ybT0idHJhbnNsYXRlKDAuMzM2IDAuMjI4KSIgZmlsbD0iIzE3NzBkNCIvPgogIDxwYXRoIGlkPSJWZWN0b3ItNCIgZGF0YS1uYW1lPSJWZWN0b3IiIGQ9Ik0xNi45LDI4LjQyNmExLjMxOSwxLjMxOSwwLDAsMS0xLjc1My0xLjk3Miw3LjE1OCw3LjE1OCwwLDAsMSw1LjEtMS43NTEsNy4wMjUsNy4wMjUsMCwwLDEsNC43NjMsMS43NDgsMS4zMTksMS4zMTksMCwxLDEtMS43NDcsMS45NzcsNC40MjIsNC40MjIsMCwwLDAtMy4wNTctMS4wODhBNC41NjUsNC41NjUsMCwwLDAsMTYuOSwyOC40MjZaIiB0cmFuc2Zvcm09InRyYW5zbGF0ZSgwLjIxNiAwLjM2MykiIGZpbGw9IiMxNzcwZDQiLz4KPC9zdmc+Cg==');
}

.su__emoji-icon2 {
  background-image: url('data:image/svg+xml;base64,PHN2ZyBpZD0iR3JvdXBfMTg2NzgiIGRhdGEtbmFtZT0iR3JvdXAgMTg2NzgiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgd2lkdGg9IjQwLjU4OCIgaGVpZ2h0PSI0MC41ODgiIHZpZXdCb3g9IjAgMCA0MC41ODggNDAuNTg4Ij4KICA8cGF0aCBpZD0iVmVjdG9yIiBkPSJNMjAuMjk0LDM2LjcyM0ExNi40MjksMTYuNDI5LDAsMSwwLDMuODY2LDIwLjI5NCwxNi40MjksMTYuNDI5LDAsMCwwLDIwLjI5NCwzNi43MjNaTTQwLjU4OCwyMC4yOTRBMjAuMjk0LDIwLjI5NCwwLDEsMSwyMC4yOTQsMCwyMC4yOTQsMjAuMjk0LDAsMCwxLDQwLjU4OCwyMC4yOTRaIiBmaWxsPSIjMTc3MGQ0IiBmaWxsLXJ1bGU9ImV2ZW5vZGQiLz4KICA8cGF0aCBpZD0iVmVjdG9yLTIiIGRhdGEtbmFtZT0iVmVjdG9yIiBkPSJNMTYuMDg4LDE3LjA0NEEzLjA0NCwzLjA0NCwwLDEsMSwxMy4wNDQsMTQsMy4wNDQsMy4wNDQsMCwwLDEsMTYuMDg4LDE3LjA0NFoiIHRyYW5zZm9ybT0idHJhbnNsYXRlKDAuMTQ3IDAuMjA2KSIgZmlsbD0iIzE3NzBkNCIgZmlsbC1ydWxlPSJldmVub2RkIi8+CiAgPHBhdGggaWQ9IlZlY3Rvci0zIiBkYXRhLW5hbWU9IlZlY3RvciIgZD0iTTMwLjA4OCwxNy4wNDRBMy4wNDQsMy4wNDQsMCwxLDEsMjcuMDQ0LDE0LDMuMDQ0LDMuMDQ0LDAsMCwxLDMwLjA4OCwxNy4wNDRaIiB0cmFuc2Zvcm09InRyYW5zbGF0ZSgwLjM1MyAwLjIwNikiIGZpbGw9IiMxNzcwZDQiIGZpbGwtcnVsZT0iZXZlbm9kZCIvPgogIDxwYXRoIGlkPSJWZWN0b3ItNCIgZGF0YS1uYW1lPSJWZWN0b3IiIGQ9Ik0xMi41LDI4LjAyMkExLjUyMiwxLjUyMiwwLDAsMSwxNC4wMjIsMjYuNUgyNi4yYTEuNTIyLDEuNTIyLDAsMSwxLDAsMy4wNDRIMTQuMDIyQTEuNTIyLDEuNTIyLDAsMCwxLDEyLjUsMjguMDIyWiIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoMC4xODUgMC4zOSkiIGZpbGw9IiMxNzcwZDQiIGZpbGwtcnVsZT0iZXZlbm9kZCIvPgo8L3N2Zz4K');
}

.su__emoji-icon3 {
  background-image: url('data:image/svg+xml;base64,PHN2ZyBpZD0iR3JvdXBfMTg2NzkiIGRhdGEtbmFtZT0iR3JvdXAgMTg2NzkiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgd2lkdGg9IjQwLjU4OCIgaGVpZ2h0PSI0MC41ODgiIHZpZXdCb3g9IjAgMCA0MC41ODggNDAuNTg4Ij4KICA8cGF0aCBpZD0iVmVjdG9yIiBkPSJNMjAuMjk0LDM2LjcyM0ExNi40MjksMTYuNDI5LDAsMSwwLDMuODY2LDIwLjI5NCwxNi40MjksMTYuNDI5LDAsMCwwLDIwLjI5NCwzNi43MjNaTTQwLjU4OCwyMC4yOTRBMjAuMjk0LDIwLjI5NCwwLDEsMSwyMC4yOTQsMCwyMC4yOTQsMjAuMjk0LDAsMCwxLDQwLjU4OCwyMC4yOTRaIiBmaWxsPSIjMTc3MGQ0IiBmaWxsLXJ1bGU9ImV2ZW5vZGQiLz4KICA8cGF0aCBpZD0iVmVjdG9yLTIiIGRhdGEtbmFtZT0iVmVjdG9yIiBkPSJNMjAuMjcyLDI1Ljc0NEE1LjQwNiw1LjQwNiwwLDAsMCwyNS4xLDIzLjI4YTEuMzE5LDEuMzE5LDAsMCwxLDIuMTg1LDEuNDc4LDguMDMyLDguMDMyLDAsMCwxLTcuMDE3LDMuNjI0LDguODg0LDguODg0LDAsMCwxLTcuMjktMy41NDksMS4zMTksMS4zMTksMCwxLDEsMi4wNzYtMS42MjgsNi4yNTMsNi4yNTMsMCwwLDAsNS4yMTQsMi41MzlaIiB0cmFuc2Zvcm09InRyYW5zbGF0ZSgwLjE4NiAwLjMzNCkiIGZpbGw9IiMxNzcwZDQiLz4KICA8cGF0aCBpZD0iVmVjdG9yLTMiIGRhdGEtbmFtZT0iVmVjdG9yIiBkPSJNMjQuODMzLDE1Ljc1MkExLjExNiwxLjExNiwwLDEsMSwyMy4yLDE0LjIzYTMuNzgsMy43OCwwLDAsMSwyLjkzMS0xLjIyNEEzLjY3MywzLjY3MywwLDAsMSwyOC45LDE0LjIzNWExLjExNiwxLjExNiwwLDEsMS0xLjY0MiwxLjUxMiwxLjQ1OSwxLjQ1OSwwLDAsMC0xLjE1LS41MUExLjU2OCwxLjU2OCwwLDAsMCwyNC44MzMsMTUuNzUyWiIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoMC4zMzYgMC4xOTEpIiBmaWxsPSIjMTc3MGQ0Ii8+CiAgPHBhdGggaWQ9IlZlY3Rvci00IiBkYXRhLW5hbWU9IlZlY3RvciIgZD0iTTEyLjgzMiwxNS43NTJBMS4xMTYsMS4xMTYsMCwwLDEsMTEuMiwxNC4yM2EzLjc4LDMuNzgsMCwwLDEsMi45MzEtMS4yMjRBMy42NzMsMy42NzMsMCwwLDEsMTYuOSwxNC4yMzVhMS4xMTYsMS4xMTYsMCwxLDEtMS42NDIsMS41MTIsMS40NTksMS40NTksMCwwLDAtMS4xNS0uNTFBMS41NjgsMS41NjgsMCwwLDAsMTIuODMyLDE1Ljc1MloiIHRyYW5zZm9ybT0idHJhbnNsYXRlKDAuMTYgMC4xOTEpIiBmaWxsPSIjMTc3MGQ0Ii8+Cjwvc3ZnPgo=');
}

.su__emoji-icon4 {
  background-image: url('data:image/svg+xml;base64,PHN2ZyBpZD0iR3JvdXBfMTg2ODAiIGRhdGEtbmFtZT0iR3JvdXAgMTg2ODAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgd2lkdGg9IjQwLjU4OCIgaGVpZ2h0PSI0MC41ODgiIHZpZXdCb3g9IjAgMCA0MC41ODggNDAuNTg4Ij4KICA8cGF0aCBpZD0iVmVjdG9yIiBkPSJNMjAuMjk0LDM2LjcyM0ExNi40MjksMTYuNDI5LDAsMSwwLDMuODY2LDIwLjI5NCwxNi40MjksMTYuNDI5LDAsMCwwLDIwLjI5NCwzNi43MjNaTTQwLjU4OCwyMC4yOTRBMjAuMjk0LDIwLjI5NCwwLDEsMSwyMC4yOTQsMCwyMC4yOTQsMjAuMjk0LDAsMCwxLDQwLjU4OCwyMC4yOTRaIiBmaWxsPSIjMTc3MGQ0IiBmaWxsLXJ1bGU9ImV2ZW5vZGQiLz4KICA8cGF0aCBpZD0iVmVjdG9yLTIiIGRhdGEtbmFtZT0iVmVjdG9yIiBkPSJNMTEuMTE2LDIzYTIuMjE2LDIuMjE2LDAsMCwxLDIuMi0yLjQ3OEgyNi45NDVBMi4yMTYsMi4yMTYsMCwwLDEsMjkuMTQ1LDIzYTkuMTc3LDkuMTc3LDAsMCwxLTkuMDE1LDguMTU2QTkuMTc3LDkuMTc3LDAsMCwxLDExLjExNiwyM1oiIHRyYW5zZm9ybT0idHJhbnNsYXRlKDAuMTYzIDAuMzAyKSIgZmlsbD0iIzE3NzBkNCIvPgogIDxwYXRoIGlkPSJWZWN0b3ItMyIgZGF0YS1uYW1lPSJWZWN0b3IiIGQ9Ik0yNC45LDE1Ljk4M2ExLjExNiwxLjExNiwwLDAsMS0xLjc1OS0xLjM3NCwzLjgzNSwzLjgzNSwwLDAsMSwzLjIwNi0xLjU3LDMuNzQ2LDMuNzQ2LDAsMCwxLDMuMDIyLDEuNTc1QTEuMTE2LDEuMTE2LDAsMCwxLDI3LjYsMTUuOTc3YTEuNTQ1LDEuNTQ1LDAsMCwwLTEuMjg3LS43MDcsMS42MjYsMS42MjYsMCwwLDAtMS40MTUuNzEyWiIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoMC4zMzcgMC4xOTIpIiBmaWxsPSIjMTc3MGQ0Ii8+CiAgPHBhdGggaWQ9IlZlY3Rvci00IiBkYXRhLW5hbWU9IlZlY3RvciIgZD0iTTEyLjksMTUuOTgzYTEuMTE2LDEuMTE2LDAsMCwxLTEuNzU5LTEuMzc0LDMuODM1LDMuODM1LDAsMCwxLDMuMjA2LTEuNTcsMy43NDYsMy43NDYsMCwwLDEsMy4wMjIsMS41NzVBMS4xMTYsMS4xMTYsMCwxLDEsMTUuNiwxNS45NzdhMS41NDUsMS41NDUsMCwwLDAtMS4yODctLjcwNywxLjYyNiwxLjYyNiwwLDAsMC0xLjQxNS43MTJaIiB0cmFuc2Zvcm09InRyYW5zbGF0ZSgwLjE2IDAuMTkyKSIgZmlsbD0iIzE3NzBkNCIvPgo8L3N2Zz4K');
}
.su__emoji {
  width: 35px;
  height: 35px;
  background-size: 100%;
}

.su__color_black {
  color: var(--su__feedbackapp-darkgray);
}

.su__feedback_pop_up_height {
  height: 150px;
}
.su__feedtext-area {
  resize: none;
  border: 1px solid var(--su__feedbackapp-lightgrey);
  border-radius: 8px;
  padding: 6px 10px;
  font-family: 'Montserrat', sans-serif;
  box-sizing: border-box;
}
.su__feedradio-group .su__feedradio-row input[type='radio'] {
  display: block;
  opacity: 0;
  width: 0;
  margin: 0;
}
.su__feedradio-row input[type='radio'] {
  display: none;
}
.su__feedradio-row input[type='radio'] + label {
  -webkit-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
}
.su__feed-labels {
  padding-left: 20px;
  margin-right: 20px;
}
.su__feedradio-row input[type='radio']:checked + label::before {
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
  box-shadow: inset 0 0 0 3px var(--su__feedbackapp-white),
    inset 0 0 0 10px var(--su__feedbackapp-deepskyblue-light);
}

.su__feedradio-row input[type='radio'] + label::before {
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
  box-shadow: inset 0 0 0 5px var(--su__feedbackapp-white),
    inset 0 0 0 10px var(--su__feedbackapp-white);
}

.su__feedradio-row input[type='radio'] + label::before,
.su__feedradio-row input[type='radio'] + label::after {
  content: '';
  position: absolute;
  top: 3px;
  left: 0;
  width: 14px;
  height: 14px;
  text-align: center;
  color: var(--su__feedbackapp-white);
  font-family: Times, serif;
  border-radius: 50%;
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
  border: 1px solid var(--su__feedbackapp-darkgray);
}

.su__feedradio-row input[type='radio']:checked + label::after {
  border: 1px solid var(--su__feedbackapp-deepskyblue-light);
}
.su__d-block {
  display: block;
}

.su__error-msg {
  display: none;
  font-size: 12px;
  color: var(--su__feedbackapp-red);
  margin-top: 5px;
}
.su__acc-hidden {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}
button.su__feedback-btn[disabled] {
  cursor: default;
}

.su__btn:not(:focus-visible) {
  border: none;
  outline: none;
  cursor: pointer;
}

.su__btn,
.su__filters-button,
.su__btn-block {
  background-image: none !important;
}

.su__border_skyblue {
  border: 1px solid var(--su__feedbackapp-deepskyblue-light);
}

.su__pl-2,
.su__px-2,
.su__rtl .su__rtlpl-2,
.su__rtl .su__rtlpx-2 {
  padding-left: 0.5rem;
}

.su__pr-2,
.su__px-2,
.su__rtl .su__rtlpr-2,
.su__rtl .su__rtlpx-2 {
  padding-right: 0.5rem;
}

.su__pb-1,
.su__py-1,
.su__rtl .su__rtlpb-1,
.su__rtl .su__rtlpy-1 {
  padding-bottom: 0.25rem;
}

.su__pt-1,
.su__py-1,
.su__rtl .su__rtlpt-1,
.su__rtl .su__rtlpy-1 {
  padding-top: 0.25rem;
}

.su__mt-1,
.su__my-1,
.su__rtl .su__rtlmt-1,
.su__rtl .su__rtlmy-1 {
  margin-top: 0.25rem;
}
.su__p-2,
.su__rtl .su__rtlp-2 {
  padding: 0.5rem;
}
.su__pt-2,
.su__py-2,
.su__rtl .su__rtlpt-2,
.su__rtl .su__rtlpy-2 {
  padding-top: 0.5rem;
}
.su__pb-2,
.su__py-2,
.su__rtl .su__rtlpb-2,
.su__rtl .su__rtlpy-2 {
  padding-bottom: 0.5rem;
}

.su__mr-2,
.su__mx-2,
.su__rtl .su__rtlmr-2,
.su__rtl .su__rtlmx-2 {
  margin-right: 0.5rem;
}
.su__radius {
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  border-radius: 3px;
  -ms-border-radius: 3px;
  -o-border-radius: 3px;
}

.su__text-blue {
  color: var(--su__feedbackapp-deepskyblue-light);
}

.su__bg-white,
.su__btn-white {
  background-color: var(--su__feedbackapp-white);
}
.su__feedshow-overlay {
  content: '';
  position: fixed;
  background: rgba(var(--su__feedbackapp-black-rgba), 0.48);
  z-index: 1;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
}
.su__bg_theme_blue {
  background: var(--su__feedbackapp-deepskyblue-light);
}
.su__emoji:hover,
.su__star:hover {
  transform: scale(1.2);
}
.su__emoji-white-icon0 {
  background-image: url('data:image/svg+xml;base64,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');
}
.su__emoji-white-icon1 {
  background-image: url('data:image/svg+xml;base64,PHN2ZyBpZD0iR3JvdXBfMTg2NzciIGRhdGEtbmFtZT0iR3JvdXAgMTg2NzciIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgd2lkdGg9IjQwLjU4OCIgaGVpZ2h0PSI0MC41ODgiIHZpZXdCb3g9IjAgMCA0MC41ODggNDAuNTg4Ij4KICA8cGF0aCBpZD0iVmVjdG9yIiBkPSJNMjAuMjk0LDM2LjcyM0ExNi40MjksMTYuNDI5LDAsMSwwLDMuODY2LDIwLjI5NCwxNi40MjksMTYuNDI5LDAsMCwwLDIwLjI5NCwzNi43MjNaTTQwLjU4OCwyMC4yOTRBMjAuMjk0LDIwLjI5NCwwLDEsMSwyMC4yOTQsMCwyMC4yOTQsMjAuMjk0LDAsMCwxLDQwLjU4OCwyMC4yOTRaIiBmaWxsPSIjZmZmIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiLz4KICA8cGF0aCBpZD0iVmVjdG9yLTIiIGRhdGEtbmFtZT0iVmVjdG9yIiBkPSJNOC44LDE2LjcxOGExLjIxOCwxLjIxOCwwLDAsMSwyLjQzNSwwLDEuODI2LDEuODI2LDAsMCwwLDMuNjUzLDAsMS4yMTgsMS4yMTgsMCwwLDEsMi40MzUsMCw0LjI2Miw0LjI2MiwwLDEsMS04LjUyNCwwWiIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoMC4xMyAwLjIyOCkiIGZpbGw9IiNmZmYiLz4KICA8cGF0aCBpZD0iVmVjdG9yLTMiIGRhdGEtbmFtZT0iVmVjdG9yIiBkPSJNMjIuOCwxNi43MThhMS4yMTgsMS4yMTgsMCwwLDEsMi40MzUsMCwxLjgyNiwxLjgyNiwwLDEsMCwzLjY1MywwLDEuMjE4LDEuMjE4LDAsMCwxLDIuNDM1LDAsNC4yNjIsNC4yNjIsMCwwLDEtOC41MjQsMFoiIHRyYW5zZm9ybT0idHJhbnNsYXRlKDAuMzM2IDAuMjI4KSIgZmlsbD0iI2ZmZiIvPgogIDxwYXRoIGlkPSJWZWN0b3ItNCIgZGF0YS1uYW1lPSJWZWN0b3IiIGQ9Ik0xNi45LDI4LjQyNmExLjMxOSwxLjMxOSwwLDAsMS0xLjc1My0xLjk3Miw3LjE1OCw3LjE1OCwwLDAsMSw1LjEtMS43NTEsNy4wMjUsNy4wMjUsMCwwLDEsNC43NjMsMS43NDgsMS4zMTksMS4zMTksMCwxLDEtMS43NDcsMS45NzcsNC40MjIsNC40MjIsMCwwLDAtMy4wNTctMS4wODhBNC41NjUsNC41NjUsMCwwLDAsMTYuOSwyOC40MjZaIiB0cmFuc2Zvcm09InRyYW5zbGF0ZSgwLjIxNiAwLjM2MykiIGZpbGw9IiNmZmYiLz4KPC9zdmc+Cg==');
}
.su__emoji-white-icon2 {
  background-image: url('data:image/svg+xml;base64,PHN2ZyBpZD0iR3JvdXBfMTg2NzgiIGRhdGEtbmFtZT0iR3JvdXAgMTg2NzgiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgd2lkdGg9IjQwLjU4OCIgaGVpZ2h0PSI0MC41ODgiIHZpZXdCb3g9IjAgMCA0MC41ODggNDAuNTg4Ij4KICA8cGF0aCBpZD0iVmVjdG9yIiBkPSJNMjAuMjk0LDM2LjcyM0ExNi40MjksMTYuNDI5LDAsMSwwLDMuODY2LDIwLjI5NCwxNi40MjksMTYuNDI5LDAsMCwwLDIwLjI5NCwzNi43MjNaTTQwLjU4OCwyMC4yOTRBMjAuMjk0LDIwLjI5NCwwLDEsMSwyMC4yOTQsMCwyMC4yOTQsMjAuMjk0LDAsMCwxLDQwLjU4OCwyMC4yOTRaIiBmaWxsPSIjZmZmIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiLz4KICA8cGF0aCBpZD0iVmVjdG9yLTIiIGRhdGEtbmFtZT0iVmVjdG9yIiBkPSJNMTYuMDg4LDE3LjA0NEEzLjA0NCwzLjA0NCwwLDEsMSwxMy4wNDQsMTQsMy4wNDQsMy4wNDQsMCwwLDEsMTYuMDg4LDE3LjA0NFoiIHRyYW5zZm9ybT0idHJhbnNsYXRlKDAuMTQ3IDAuMjA2KSIgZmlsbD0iI2ZmZiIgZmlsbC1ydWxlPSJldmVub2RkIi8+CiAgPHBhdGggaWQ9IlZlY3Rvci0zIiBkYXRhLW5hbWU9IlZlY3RvciIgZD0iTTMwLjA4OCwxNy4wNDRBMy4wNDQsMy4wNDQsMCwxLDEsMjcuMDQ0LDE0LDMuMDQ0LDMuMDQ0LDAsMCwxLDMwLjA4OCwxNy4wNDRaIiB0cmFuc2Zvcm09InRyYW5zbGF0ZSgwLjM1MyAwLjIwNikiIGZpbGw9IiNmZmYiIGZpbGwtcnVsZT0iZXZlbm9kZCIvPgogIDxwYXRoIGlkPSJWZWN0b3ItNCIgZGF0YS1uYW1lPSJWZWN0b3IiIGQ9Ik0xMi41LDI4LjAyMkExLjUyMiwxLjUyMiwwLDAsMSwxNC4wMjIsMjYuNUgyNi4yYTEuNTIyLDEuNTIyLDAsMSwxLDAsMy4wNDRIMTQuMDIyQTEuNTIyLDEuNTIyLDAsMCwxLDEyLjUsMjguMDIyWiIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoMC4xODUgMC4zOSkiIGZpbGw9IiNmZmYiIGZpbGwtcnVsZT0iZXZlbm9kZCIvPgo8L3N2Zz4K');
}
.su__emoji-white-icon3 {
  background-image: url('data:image/svg+xml;base64,PHN2ZyBpZD0iR3JvdXBfMTg2NzkiIGRhdGEtbmFtZT0iR3JvdXAgMTg2NzkiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgd2lkdGg9IjQwLjU4OCIgaGVpZ2h0PSI0MC41ODgiIHZpZXdCb3g9IjAgMCA0MC41ODggNDAuNTg4Ij4KICA8cGF0aCBpZD0iVmVjdG9yIiBkPSJNMjAuMjk0LDM2LjcyM0ExNi40MjksMTYuNDI5LDAsMSwwLDMuODY2LDIwLjI5NCwxNi40MjksMTYuNDI5LDAsMCwwLDIwLjI5NCwzNi43MjNaTTQwLjU4OCwyMC4yOTRBMjAuMjk0LDIwLjI5NCwwLDEsMSwyMC4yOTQsMCwyMC4yOTQsMjAuMjk0LDAsMCwxLDQwLjU4OCwyMC4yOTRaIiBmaWxsPSIjZmZmIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiLz4KICA8cGF0aCBpZD0iVmVjdG9yLTIiIGRhdGEtbmFtZT0iVmVjdG9yIiBkPSJNMjAuMjcyLDI1Ljc0NEE1LjQwNiw1LjQwNiwwLDAsMCwyNS4xLDIzLjI4YTEuMzE5LDEuMzE5LDAsMCwxLDIuMTg1LDEuNDc4LDguMDMyLDguMDMyLDAsMCwxLTcuMDE3LDMuNjI0LDguODg0LDguODg0LDAsMCwxLTcuMjktMy41NDksMS4zMTksMS4zMTksMCwxLDEsMi4wNzYtMS42MjgsNi4yNTMsNi4yNTMsMCwwLDAsNS4yMTQsMi41MzlaIiB0cmFuc2Zvcm09InRyYW5zbGF0ZSgwLjE4NiAwLjMzNCkiIGZpbGw9IiNmZmYiLz4KICA8cGF0aCBpZD0iVmVjdG9yLTMiIGRhdGEtbmFtZT0iVmVjdG9yIiBkPSJNMjQuODMzLDE1Ljc1MkExLjExNiwxLjExNiwwLDEsMSwyMy4yLDE0LjIzYTMuNzgsMy43OCwwLDAsMSwyLjkzMS0xLjIyNEEzLjY3MywzLjY3MywwLDAsMSwyOC45LDE0LjIzNWExLjExNiwxLjExNiwwLDEsMS0xLjY0MiwxLjUxMiwxLjQ1OSwxLjQ1OSwwLDAsMC0xLjE1LS41MUExLjU2OCwxLjU2OCwwLDAsMCwyNC44MzMsMTUuNzUyWiIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoMC4zMzYgMC4xOTEpIiBmaWxsPSIjZmZmIi8+CiAgPHBhdGggaWQ9IlZlY3Rvci00IiBkYXRhLW5hbWU9IlZlY3RvciIgZD0iTTEyLjgzMiwxNS43NTJBMS4xMTYsMS4xMTYsMCwwLDEsMTEuMiwxNC4yM2EzLjc4LDMuNzgsMCwwLDEsMi45MzEtMS4yMjRBMy42NzMsMy42NzMsMCwwLDEsMTYuOSwxNC4yMzVhMS4xMTYsMS4xMTYsMCwxLDEtMS42NDIsMS41MTIsMS40NTksMS40NTksMCwwLDAtMS4xNS0uNTFBMS41NjgsMS41NjgsMCwwLDAsMTIuODMyLDE1Ljc1MloiIHRyYW5zZm9ybT0idHJhbnNsYXRlKDAuMTYgMC4xOTEpIiBmaWxsPSIjZmZmIi8+Cjwvc3ZnPgo=');
}
.su__emoji-white-icon4 {
  background-image: url('data:image/svg+xml;base64,PHN2ZyBpZD0iR3JvdXBfMTg2ODAiIGRhdGEtbmFtZT0iR3JvdXAgMTg2ODAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgd2lkdGg9IjQwLjU4OCIgaGVpZ2h0PSI0MC41ODgiIHZpZXdCb3g9IjAgMCA0MC41ODggNDAuNTg4Ij4KICA8cGF0aCBpZD0iVmVjdG9yIiBkPSJNMjAuMjk0LDM2LjcyM0ExNi40MjksMTYuNDI5LDAsMSwwLDMuODY2LDIwLjI5NCwxNi40MjksMTYuNDI5LDAsMCwwLDIwLjI5NCwzNi43MjNaTTQwLjU4OCwyMC4yOTRBMjAuMjk0LDIwLjI5NCwwLDEsMSwyMC4yOTQsMCwyMC4yOTQsMjAuMjk0LDAsMCwxLDQwLjU4OCwyMC4yOTRaIiBmaWxsPSIjZmZmIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiLz4KICA8cGF0aCBpZD0iVmVjdG9yLTIiIGRhdGEtbmFtZT0iVmVjdG9yIiBkPSJNMTEuMTE2LDIzYTIuMjE2LDIuMjE2LDAsMCwxLDIuMi0yLjQ3OEgyNi45NDVBMi4yMTYsMi4yMTYsMCwwLDEsMjkuMTQ1LDIzYTkuMTc3LDkuMTc3LDAsMCwxLTkuMDE1LDguMTU2QTkuMTc3LDkuMTc3LDAsMCwxLDExLjExNiwyM1oiIHRyYW5zZm9ybT0idHJhbnNsYXRlKDAuMTYzIDAuMzAyKSIgZmlsbD0iI2ZmZiIvPgogIDxwYXRoIGlkPSJWZWN0b3ItMyIgZGF0YS1uYW1lPSJWZWN0b3IiIGQ9Ik0yNC45LDE1Ljk4M2ExLjExNiwxLjExNiwwLDAsMS0xLjc1OS0xLjM3NCwzLjgzNSwzLjgzNSwwLDAsMSwzLjIwNi0xLjU3LDMuNzQ2LDMuNzQ2LDAsMCwxLDMuMDIyLDEuNTc1QTEuMTE2LDEuMTE2LDAsMCwxLDI3LjYsMTUuOTc3YTEuNTQ1LDEuNTQ1LDAsMCwwLTEuMjg3LS43MDcsMS42MjYsMS42MjYsMCwwLDAtMS40MTUuNzEyWiIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoMC4zMzcgMC4xOTIpIiBmaWxsPSIjZmZmIi8+CiAgPHBhdGggaWQ9IlZlY3Rvci00IiBkYXRhLW5hbWU9IlZlY3RvciIgZD0iTTEyLjksMTUuOTgzYTEuMTE2LDEuMTE2LDAsMCwxLTEuNzU5LTEuMzc0LDMuODM1LDMuODM1LDAsMCwxLDMuMjA2LTEuNTcsMy43NDYsMy43NDYsMCwwLDEsMy4wMjIsMS41NzVBMS4xMTYsMS4xMTYsMCwxLDEsMTUuNiwxNS45NzdhMS41NDUsMS41NDUsMCwwLDAtMS4yODctLjcwNywxLjYyNiwxLjYyNiwwLDAsMC0xLjQxNS43MTJaIiB0cmFuc2Zvcm09InRyYW5zbGF0ZSgwLjE2IDAuMTkyKSIgZmlsbD0iI2ZmZiIvPgo8L3N2Zz4K');
}

.su__emoji-active {
  filter: none;
}
.su__star-gray {
  background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI1MC40ODYiIGhlaWdodD0iNDguMzYxIiB2aWV3Qm94PSIwIDAgNTAuNDg2IDQ4LjM2MSI+CiAgPHBhdGggaWQ9IlBhdGhfMTgzNjUiIGRhdGEtbmFtZT0iUGF0aCAxODM2NSIgZD0iTTI1LjI0Myw2OTkuNzZhMS45NzIsMS45NzIsMCwwLDEsMS44LDEuMTJMMzIuOTMxLDcxMi44bC4zNTUuNzE5Ljc5My4xMTUsMTMuMTU5LDEuOTEyYTIuMDEsMi4wMSwwLDAsMSwxLjExNCwzLjQyOGwtOS41MjIsOS4yODItLjU3NC41NTkuMTM1Ljc5LDIuMjQ4LDEzLjEwNmExLjk2MiwxLjk2MiwwLDAsMS0uNDMxLDEuNjIzLDIuMDU3LDIuMDU3LDAsMCwxLTEuNTUuNzM0LDIsMiwwLDAsMS0uOTM2LS4yMzlsLTExLjc3LTYuMTg4LS43MDktLjM3My0uNzA5LjM3My0xMS43Nyw2LjE4OGEyLDIsMCwwLDEtLjkzNi4yMzksMi4wNTgsMi4wNTgsMCwwLDEtMS41NS0uNzM0LDEuOTYyLDEuOTYyLDAsMCwxLS40MzEtMS42MjNMMTIuMSw3MjkuNjA5bC4xMzUtLjc5LS41NzQtLjU1OS05LjUyMi05LjI4MmEyLjAxLDIuMDEsMCwwLDEsMS4xMTQtMy40MjhsMTMuMTU5LTEuOTEyLjc5My0uMTE1LjM1NS0uNzE5LDUuODg1LTExLjkyNGExLjk3MiwxLjk3MiwwLDAsMSwxLjgtMS4xMm0wLTEuNTI0YTMuNDkyLDMuNDkyLDAsMCwwLTMuMTY5LDEuOTdMMTYuMTksNzEyLjEzLDMuMDMsNzE0LjA0MmEzLjUzNCwzLjUzNCwwLDAsMC0xLjk1OCw2LjAyOGw5LjUyMiw5LjI4Mkw4LjM0Niw3NDIuNDU4YTMuNTI0LDMuNTI0LDAsMCwwLDUuMTI3LDMuNzI1TDI1LjI0Myw3NDBsMTEuNzcsNi4xODhhMy41MjQsMy41MjQsMCwwLDAsNS4xMjctMy43MjVsLTIuMjQ4LTEzLjEwNiw5LjUyMi05LjI4MmEzLjUzNCwzLjUzNCwwLDAsMC0xLjk1OC02LjAyOEwzNC4zLDcxMi4xM2wtNS44ODUtMTEuOTI0YTMuNDkyLDMuNDkyLDAsMCwwLTMuMTY5LTEuOTciIHRyYW5zZm9ybT0idHJhbnNsYXRlKDAgLTY5OC4yMzYpIiBmaWxsPSIjNTc1NzVjIi8+Cjwvc3ZnPgo=');
}
.su__star.su__star-yellow {
  background-image: url("data:image/svg+xml,%0A%3Csvg id='Group_13343' data-name='Group 13343' xmlns='http://www.w3.org/2000/svg' width='29.265' height='28.113' viewBox='0 0 29.265 28.113'%3E%3Cpath id='Path_11307' data-name='Path 11307' d='M2118.621,585.582l2.308,4.677a2.858,2.858,0,0,0,2.151,1.563l5.162.75a2.857,2.857,0,0,1,1.583,4.873l-3.735,3.641a2.858,2.858,0,0,0-.822,2.529l.882,5.141a2.857,2.857,0,0,1-4.146,3.012l-4.617-2.427a2.857,2.857,0,0,0-2.659,0l-4.616,2.427a2.857,2.857,0,0,1-4.146-3.012l.882-5.141a2.857,2.857,0,0,0-.822-2.529l-3.735-3.641a2.857,2.857,0,0,1,1.583-4.873l5.162-.75a2.857,2.857,0,0,0,2.151-1.563l2.308-4.677A2.857,2.857,0,0,1,2118.621,585.582Z' transform='translate(-2101.427 -583.99)' fill='%23fed302'/%3E%3Cpath id='Path_11308' data-name='Path 11308' d='M2194.462,725.7a2.856,2.856,0,0,0-.822,2.529l.882,5.141a2.857,2.857,0,0,1-4.146,3.012l-4.617-2.427a2.856,2.856,0,0,0-2.659,0l-4.617,2.427a2.863,2.863,0,0,1-4.069-1.7c9.417-.877,17.379-7.889,21.043-17.665l1.156.168a2.857,2.857,0,0,1,1.583,4.873Z' transform='translate(-2169.798 -708.605)' fill='%23f0f0f0' opacity='0.88' style='mix-blend-mode: multiply;isolation: isolate'/%3E%3Cg id='Group_13342' data-name='Group 13342' transform='translate(5.193 5.034)' opacity='0.2'%3E%3Cpath id='Path_11309' data-name='Path 11309' d='M2183.531,669.059a5.582,5.582,0,0,1,.6-.4c.2-.121.4-.227.607-.329a11.194,11.194,0,0,1,1.235-.513l.156-.053c.052-.018.086-.035.13-.053s.083-.031.124-.046.081-.035.122-.052c.081-.034.159-.071.238-.107l.117-.053c.039-.019.076-.04.114-.06s.075-.04.114-.057l.112-.061.111-.06.107-.067c.036-.022.073-.04.109-.062l.106-.068c.035-.023.072-.043.106-.068s.067-.05.1-.073a7.669,7.669,0,0,0,1.523-1.381c.015-.018.029-.033.045-.054s.034-.043.051-.065l.1-.129c.069-.086.138-.173.212-.257.144-.17.3-.336.453-.5s.323-.322.5-.476a5.628,5.628,0,0,1,.565-.444l.118.058-.545,1.176c-.186.391-.369.78-.559,1.17a5.3,5.3,0,0,1-1.72,1.984,5.407,5.407,0,0,1-2.461.906l-1.285.168-1.287.154Z' transform='translate(-2183.531 -663.572)' fill='%23fff'/%3E%3C/g%3E%3C/svg%3E%0A");
}
.su__mr_20px {
  margin-right: 20px;
}

.su__star_size {
  height: 50px;
  width: 48px;
}

.su__star {
  border-radius: 50%;
  background-repeat: no-repeat;
  background-position: center;
  background-size: 90%;
  transition: 0.3s;
}

.a11y-btn {
  border: 0;
  background-color: transparent;
}
.su__p_14px {
  padding: 14px !important;
}
.su__rtl .su__cross_icon-fb_rtl {
  right: unset;
  left: 14px;
  top: 14px;
}
.su__text-white,
.su__text-white-hover:hover {
  color: var(--su__feedbackapp-white);
}
.su__opacity_pointThree {
  opacity: 0.3;
}
.su_thumb-yes .feedbackRatingthumbs:hover {
  fill: var(--su__feedbackapp-vividgreen);
}
.su_thumb-no .feedbackRatingthumbs:hover {
  fill: var(--su__feedbackapp-red);
}
.su__thankfeedback-popup {
  position: relative;
  width: 350px;
}
.su__word-break {
  word-break: break-word;
}
.su__feedback-pops .su__star {
  margin-right: 1rem;
}
@media (max-width: 600px) {
  .su__emoji_border {
    width: 48px;
    height: 48px;
    margin-right: 6px;
    border-radius: 35px;
  }
  .su__feedshow-bottom,
  .su__feedshow-center {
    max-width: 350px;
  }
  .su__emoji {
    width: 25px;
    height: 25px;
    background-size: 100%;
  }
}

@media (max-width: 450px) {
  .su__feedbackflex {
    flex-direction: column;
    padding-left: 0;
    padding-right: 0;
  }
  .su__thankfeedback-popup {
    position: relative;
    width: 250px;
  }
}
