:root {
  /* ---------- Hex Colors ---------- */
  --su__searchapp-lightpink: #ffb6c1;
  --su__searchapp-black: #000;
  --su__searchapp-deepskyblue-light: #1770d4;
  --su__searchapp-vividgreen: #1e8202;
  --su__searchapp-turquoise: #1ed7eb;
  --su__searchapp-darkgray: #333;
  --su__searchapp-lightblue: #499bf9;
  --su__searchapp-violet: #609;
  --su__searchapp-gray: #666;
  --su__searchapp-lightcornflowerblue: #7d97fc;
  --su__searchapp-electricblue: #7ef9ff;
  --su__searchapp-paleblue: #89cff0;
  --su__searchapp-paleblue-light: #89effa;
  --su__searchapp-palecyan: #8adcf5;
  --su__searchapp-skyblue-light: #8be9f7;
  --su__searchapp-pastelblue: #8fbdf3;
  --su__searchapp-taupe: #908d8d;
  --su__searchapp-lightblue-alt: #92c4f3;
  --su__searchapp-gray-standard: #9c9c9c;
  --su__searchapp-lightgrey: #d3d3d3;
  --su__searchapp-paleblue-cool: #d9ebff;
  --su__searchapp-red: #e91b37;
  --su__searchapp-lightgray-14: #f2f2f2;
  --su__searchapp-darkorange: #f48b00;
  --su__searchapp-gold: #f5be12;
  --su__searchapp-orange-dark: #fb7d0b;
  --su__searchapp-white: #fff;
  --su__searchapp-rosybrown: #bbb7b7;
  --su__searchapp-mediumgray: #57575c;
  --su__searchapp-dimgray: #707070;
  --su__searchapp-lightgray-75: #757575;
  --su__searchapp-darkgray-8b: #8b8b8b;
  --su__searchapp-darkgray-53: #535355;
  --su__searchapp-bluegray: #919bb0;
  --su__searchapp-orange: #ff9800;
  --su__searchapp-darkblue: #094f9f;
  --su__searchapp-charcoal: #292929;
  --su__searchapp-lightblue-82: #0082cacc;
  --su__searchapp-lightgray-cf: #cfcfcf;
  --su__searchapp-gray-91: #919191;
  --su__searchapp-green: #5bb543;
  --su__searchapp-blue: #008fd3;
  --su__searchapp-darkgray-58: #58585a;
  --su__searchapp-blue-16: #166fd2;
  --su__searchapp-jetgray-rgba: 102, 102, 102;
  --su__searchapp-whitesmoke-2-rgba: 245, 245, 245;
  --su__searchapp-black-rgba: 0, 0, 0;
  --su__searchapp-deepskyblue-light-rgba: 23, 112, 212;
  --su__searchapp-white-rgba: 255, 255, 255;
  --su__searchapp-cornflowerblue-light-rgba: 89, 190, 254;
  --su__searchapp-red-rgba: 255, 0, 0;
  --su__searchapp-sapphire-rgba: 21, 105, 200;
  --su__searchapp-dimgray-alt-rgba: 112, 112, 112;
  --su__searchapp-steelblue-rgba: 5, 15, 28;
  --su__searchapp-silver-20: rgba(116, 168, 227, 0.2);
  --su__searchapp-silver-60: rgba(98, 164, 242, 0.6);
  --su__searchapp-silver-54: rgba(106, 160, 250, 0.541);
  --su__searchapp-gray-44: rgba(125, 112, 112, 0.439);
  --su__searchapp-silver-30: rgba(163, 194, 255, 0.302);
  --su__searchapp-white-16: rgba(235, 224, 224, 0.161);
  --su__searchapp-silver-16: rgba(138, 156, 245, 0.161);
  --su__searchapp-teal-13: rgba(22, 108, 205, 0.129);
  --su__searchapp-gray-19: rgba(158, 158, 158, 0.188);
  --su__searchapp-blue-4: rgba(22, 109, 207, 0.039);
  --su__searchapp-rgba-gray-30: rgba(236, 236, 236, 0.3);
  --su__searchapp-rgba-darkblue-10: rgba(35, 37, 51, 0.1);
}

@font-face {
  font-family: 'Montserrat';
  font-style: normal;
  font-weight: 300;
  src: local('Montserrat Light'), local(montserrat-light),
    url(https://fonts.gstatic.com/s/montserrat/v14/JTURjIg1_i6t8kCHKm45_cJD3gTD_u50.woff2)
      format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}

@font-face {
  font-family: 'Montserrat';
  font-style: normal;
  font-weight: 400;
  src: local('Montserrat Regular'), local(montserrat-regular),
    url(https://fonts.gstatic.com/s/montserrat/v14/JTUSjIg1_i6t8kCHKm459Wlhyw.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F,
    U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: 'Montserrat';
  font-style: normal;
  font-weight: 500;
  src: local('Montserrat Medium'), local(montserrat-medium),
    url(https://fonts.gstatic.com/s/montserrat/v14/JTURjIg1_i6t8kCHKm45_ZpC3gnD_g.woff2)
      format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F,
    U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: 'Montserrat';
  font-style: normal;
  font-weight: 600;
  src: local('Montserrat SemiBold'), local(montserrat-semibold),
    url(https://fonts.gstatic.com/s/montserrat/v14/JTURjIg1_i6t8kCHKm45_bZF3gnD_g.woff2)
      format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F,
    U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

.su__viewed-results,
.su_page_rating_box,
.su__viewed-results *,
.su__no-view-results,
#su_autocomplete-block,
#su_autocomplete-block *,
.su__wrapper,
#su__wrapper,
#su__wrapper * {
  font-family: 'Montserrat', sans-serif;
  box-sizing: border-box;
}
.su__no-view-results {
  min-height: 100vh;
}

.su__app-running,
.su__autocomplete-running {
  margin: 0;
  padding: 0;
}

.su__font-9 {
  font-size: 9px;
}

.su__font-10 {
  font-size: 10px;
}

.su__font-11 {
  font-size: 11px;
}

.su__font-12px {
  font-size: 12px;
}

.su__font-12 {
  font-size: 12px;
  line-height: 19px;
}

.su__fontsize-13 {
  font-size: 13px;
}

.su__fontsize-14 {
  font-size: 14px;
}

.su__font-13 {
  font-size: 13px !important;
  line-height: 18px;
}

.su__font-14 {
  font-size: 14px;
  line-height: 20px;
}

.su__font-12-22 {
  font-size: 12px;
  line-height: 22px;
}

.su__font-17-22 {
  font-size: 17px;
  line-height: 22px;
}

.su__font-15 {
  font-size: 15px;
}

.su__font-16 {
  font-size: 16px;
}

.su__font-17 {
  font-size: 17px;
}

.su__font-18 {
  font-size: 18px;
}

.su__font-20 {
  font-size: 20px;
}

.su__font-22 {
  font-size: 22px;
}

.su__font-24 {
  font-size: 24px;
}

.su__font-32 {
  font-size: 32px;
}

.su__padding-0 {
  padding: 0;
}

.su__margin-lr-5 {
  margin: 0 5px 0 5px;
}

.su__txt-lowercase {
  text-transform: lowercase;
}

.su__confidence-high {
  color: var(--su__searchapp-vividgreen);
}

.su__confidence-medium {
  color: var(--su__searchapp-gold);
}

.su__confidence-low {
  color: var(--su__searchapp-taupe);
}

.su__txt-uppercase {
  text-transform: uppercase;
}

.su__txt-capitalize {
  text-transform: capitalize;
}

.su__word-break {
  word-break: break-word;
}

.su__word-break-all {
  word-break: break-all;
}

.su__whitespace-no {
  white-space: nowrap;
}

.su__whitespace-initial {
  white-space: initial;
}

.su_f-Montserrat {
  font-family: 'Montserrat', sans-serif;
}

.su__f-light {
  font-weight: 300;
}

.su__f-normal {
  font-weight: 400;
}

.su__f-medium {
  font-weight: 500;
}

.su__f-semibold {
  font-weight: 600;
}

.su__f-bold {
  font-weight: 700;
}

.su-featured-text {
  padding: 0 15px;
}

.su__font-bold {
  font-weight: 600;
}

.su__font-heavy {
  font-weight: 900;
}

.su__line-height-n {
  line-height: normal;
}

.su__line-height-14 {
  line-height: 14px;
}

.su__inline-list li {
  list-style: none;
  display: inline;
}

.su__text-decoration-none li a {
  text-decoration: none;
}

.su__bg-blue-grd {
  color: var(--su__searchapp-white);
  background-color: var(--su__searchapp-deepskyblue-light);
}

.su__color_grey {
  color: var(--su__searchapp-gray);
}

.su__bg-white {
  background-color: var(--su__searchapp-white);
}

.su__background-white {
  background-color: var(--su__searchapp-white) !important;
}

.su__color-white {
  color: var(--su__searchapp-white);
}

.su__color-gray {
  color: var(--su__searchapp-darkgray);
}

.su__color-lgray {
  color: var(--su__searchapp-darkgray);
}

.su__color-blue {
  color: var(--su__searchapp-deepskyblue-light);
}

.su__color-lblue {
  color: var(--su__searchapp-lightblue);
}

.hover-color-dblue:hover {
  color: var(--su__searchapp-deepskyblue-light);
}

.su__border-color {
  border-color: var(--su__searchapp-deepskyblue-light);
}

.su__bg-blue,
.su__btn-blue,
.su__hover-bg-blue:hover {
  background-color: var(--su__searchapp-deepskyblue-light);
  color: var(--su__searchapp-white);
}

.su__bg-light-gray,
.su__btn-light-gray,
.su__bg-gray-hover:hover {
  background: var(--su__searchapp-lightgray-14);
}

.su__bg-gray-40 {
  background: var(--su__searchapp-lightgray-14);
}

.su__fill-lblue {
  fill: var(--su__searchapp-deepskyblue-light);
}

.su__fill-black {
  fill: var(--su__searchapp-darkgray);
}

.su__color_black {
  color: var(--su__searchapp-darkgray);
}

.su__fill-gray {
  fill: var(--su__searchapp-gray-standard);
}

.su__fill-hover-gray:hover {
  fill: var(--su__searchapp-darkgray);
}

.su__text-lowercase {
  text-transform: lowercase;
}

.su__text-uppercase {
  text-transform: uppercase;
}

.su__text-hover-underline:hover {
  text-decoration: underline;
}

.su__text-hover-underline {
  text-decoration: underline;
}

.su__text-capitalize {
  text-transform: capitalize;
}

.su__font-weight-light {
  font-weight: 300;
}

.su__font-weight-normal {
  font-weight: 400;
}

.su__font-weight-bold {
  font-weight: 700;
}

.su__font-italic {
  font-style: italic;
}

.su__text-white,
.su__text-white-hover:hover {
  color: var(--su__searchapp-white);
}

.su__text-primary {
  color: var(--su__searchapp-deepskyblue-light);
}

.su__knowledge-head {
  color: var(--su__searchapp-darkgray);
  opacity: 1;
  font-weight: 500;
}

.su__knowledge-head-line-height {
  line-height: 23px;
}

.su__truncate-two-lines {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.su__knowledgeGraph-meta-color {
  color: var(--su__searchapp-gray);
}

.su__text-primary:hover,
a.su__text-primary:focus {
  color: var(--su__searchapp-deepskyblue-light);
}

.su__text-success {
  color: var(--su__searchapp-vividgreen);
}

.su__text-success:hover,
a.su__text-success:focus {
  color: var(--su__searchapp-vividgreen);
}

.su__text-black {
  color: rgba(var(--su__searchapp-jetgray-rgba), 0.8);
}

.su__text-black-shade {
  color: var(--su__searchapp-darkgray);
}

.su__text-dark {
  color: var(--su__searchapp-black);
}

.su__text-blue {
  color: var(--su__searchapp-deepskyblue-light);
}

.su__list-item-desc {
  color: var(--su__searchapp-darkgray);
}

.su__bg-color {
  background-color: var(--su__searchapp-deepskyblue-light);
}

.su__bg-white-hover-50:hover {
  background: rgba(var(--su__searchapp-whitesmoke-2-rgba), 0.55);
}

.su__bg-white-circle {
  background: var(--su__searchapp-rgba-gray-30);
}

.su__visible {
  visibility: visible;
}

.su__invisible {
  visibility: hidden;
}

.su__radius-0 {
  -webkit-border-radius: 2px;
  -moz-border-radius: 2px;
  border-radius: 2px;
  -ms-border-radius: 2px;
  -o-border-radius: 2px;
}

.su__load-img {
  max-width: 200px;
}

.su__radius {
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  border-radius: 3px;
  -ms-border-radius: 3px;
  -o-border-radius: 3px;
}

.su__radius-1 {
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  border-radius: 4px;
  -ms-border-radius: 4px;
  -o-border-radius: 4px;
}

.su__radius-2 {
  -webkit-border-radius: 6px;
  -moz-border-radius: 6px;
  border-radius: 6px;
  -ms-border-radius: 6px;
  -o-border-radius: 6px;
}

.su__radius-3 {
  border-radius: 20px;
  -webkit-border-radius: 20px;
  -moz-border-radius: 20px;
  -ms-border-radius: 20px;
  -o-border-radius: 20px;
}

.su__radius-50 {
  border-radius: 50%;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -ms-border-radius: 50%;
  -o-border-radius: 50%;
}

.su_radius-bottom {
  border-bottom-right-radius: 4px;
  border-bottom-left-radius: 4px;
}

.su_radius-top {
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
}

.su_radius-left {
  border-top-left-radius: 10px;
  border-bottom-left-radius: 10px;
}

.su__rotate-180,
.su__rtl .su__rotate-180-rtl {
  transform: rotate(180deg);
  -webkit-transform: rotate(180deg);
  -moz-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  -o-transform: rotate(180deg);
}

.su__rotate-90 {
  transform: rotate(90deg);
  -webkit-transform: rotate(90deg);
  -moz-transform: rotate(90deg);
  -ms-transform: rotate(90deg);
  -o-transform: rotate(90deg);
}

.su__rotate-45 {
  transform: rotate(45deg);
  -webkit-transform: rotate(45deg);
  -moz-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  -o-transform: rotate(45deg);
}

.su__shadow-lg {
  -webkit-box-shadow: 0 6px 12px rgba(var(--su__searchapp-black-rgba), 0.175);
  -moz-box-shadow: 0 6px 12px rgba(var(--su__searchapp-black-rgba), 0.175);
  box-shadow: 0 6px 12px rgba(var(--su__searchapp-black-rgba), 0.175);
}

.su__sm-shadow {
  -webkit-box-shadow: 0 1px 5px 0 rgba(var(--su__searchapp-black-rgba), 0.14);
  -moz-box-shadow: 0 1px 5px 0 rgba(var(--su__searchapp-black-rgba), 0.14);
  box-shadow: 0 1px 5px 0 rgba(var(--su__searchapp-black-rgba), 0.14);
}

.su__shadow-hover:hover,
.su__lg-shadow {
  -webkit-box-shadow: 0 3px 5px -1px rgba(var(--su__searchapp-black-rgba), 0.2),
    0 5px 8px 0 rgba(var(--su__searchapp-black-rgba), 0.14),
    0 1px 14px 0 rgba(var(--su__searchapp-black-rgba), 0.12);
  box-shadow: 0 3px 5px -1px rgba(var(--su__searchapp-black-rgba), 0.2),
    0 5px 8px 0 rgba(var(--su__searchapp-black-rgba), 0.14),
    0 1px 14px 0 rgba(var(--su__searchapp-black-rgba), 0.12);
}

.su__border {
  border: 1px solid var(--su__searchapp-lightgrey);
}

.su__br-7 {
  border-radius: 7px;
}

.su__br-unset {
  border-radius: unset;
}
.su__kh__mt-unset {
  margin-top: unset !important;
}

.su__border-t {
  border-top: 1px solid var(--su__searchapp-lightgrey);
}

.su__border-r {
  border-right: 1px solid var(--su__searchapp-lightgrey);
}

.su__border-b {
  border-bottom: 1px solid var(--su__searchapp-lightgrey);
}

.su__border-none {
  border: none;
}

.su__border-t-none {
  border-top: none;
}

.su__border-r-none {
  border-right: none;
}

.su__border-b-none {
  border-bottom: none;
}

.su__border-l-none {
  border-left: none;
}

.su__dark-border {
  border: none;
}

.su__cursor:not(:focus-visible) {
  cursor: pointer;
  outline: none;
}

.su__z-index {
  z-index: 1;
}

.su__new-modal .su__popup-text .su__heading-feedback {
  font: normal normal 17px Montserrat;
  letter-spacing: 0;
  opacity: 1;
  margin-left: 18px !important;
  margin-right: 18px !important;
}

.su__ml-mr-5 {
  margin-right: 5px;
  margin-left: 5px;
}

.su__mr-mt-10 {
  margin-right: 11px;
  margin-top: 20px;
  margin-left: 45px;
}

.su__mb-15 {
  margin-bottom: 15px;
}

.su__mb-30 {
  margin-bottom: 30px;
}

.su__mb-10 {
  margin-bottom: 10px;
}

.su__mb-4px {
  margin-bottom: 4px;
}

.su__mb-20 {
  margin-bottom: 20px;
}

.su__mb-100 {
  margin-bottom: 100px;
}

.su__mb-110 {
  margin-bottom: 110px;
}

.su__mb-14 {
  margin-bottom: 14px;
}

.su__mx-18px {
  margin-left: 18px;
  margin-right: 18px;
}

.su__negative-margin {
  margin-bottom: -3px;
}

.su_new-btn.su__second-btn {
  margin-left: 12px;
}

.su__btn-gpt {
  color: var(--su__searchapp-white);
  border: 1px solid var(--su__searchapp-deepskyblue-light);
  border-radius: 3px;
  width: 452px;
  height: 36px;
  font-size: 14px;
  background: var(--su__searchapp-deepskyblue-light) 0% 0% no-repeat padding-box;
}

.su__mt-6px {
  margin-top: 6px;
}

.su__mt-10 {
  margin-top: 10px;
}

.su__mt-12px {
  margin-top: 12px;
}

.su__mt-20 {
  margin-top: 20px;
}

.su__mt-5px {
  margin-top: 5px;
}

.su__mt-14 {
  margin-top: 14px;
}

.su__mt-25 {
  margin-top: 25px;
}

.su__mr-30 {
  margin-right: 30px;
}

.su__ml-15 {
  margin-left: 15px;
}

.su__ml-14 {
  margin-left: 14px;
}

.su__mr-14 {
  margin-right: 14px;
}

.su__ml-10 {
  margin-left: 10px;
}

.su__mr-10 {
  margin-right: 10px;
}

.su__feedtext-area-gpt {
  margin-left: 14px;
  border: 1px solid var(--su__searchapp-gray) !important;
  border-radius: 5px !important;
  width: 452px !important;
  height: 147px !important;
  font-size: 12px !important;
  padding: 10px !important;
  resize: none;
  margin-right: 18px;
}

.su_new-btn-hover:hover {
  background-color: var(--su__searchapp-gray);
  color: var(--su__searchapp-white);
}

.su__grey-color {
  color: var(--su__searchapp-gray-standard);
}

.su__pb-new {
  padding-bottom: 11px;
}

.su__say-more {
  letter-spacing: 0;
  color: var(--su__searchapp-darkgray);
  opacity: 1;
  font-size: 13px;
  margin-left: 18px;
  font-weight: 400;
  margin-right: 18px;
}

.su__bg_theme_blue {
  background: var(--su__searchapp-deepskyblue-light);
}

.su__zindex-1 {
  z-index: 9;
}

.su__zindex-custom-2 {
  z-index: 2;
}

.su__zindex-2 {
  z-index: 99;
}

.su__zindex-3 {
  z-index: 999;
}

.su__zindex-4 {
  z-index: 9999;
}

.su__zindex-5 {
  z-index: 99999;
}

.su__zindex-custom {
  z-index: 10;
}

.su__box-sizing {
  box-sizing: border-box;
}

.su__btn:not(:focus-visible) {
  border: none;
  outline: none;
  cursor: pointer;
}

.su__outline-none:not(:focus-visible) {
  outline: none;
}

.su__btn-block:not(:focus-visible),
.su__refine__search {
  border: none;
  outline: none;
  cursor: pointer;
  padding: 10px 13px;
  width: 100%;
  line-height: normal;
}

.su__form-control {
  box-sizing: border-box;
  display: block;
  width: 100%;
  padding: 12px;
  line-height: normal;
  background-color: var(--su__searchapp-white);
  background-clip: padding-box;
  border: 1px solid var(--su__searchapp-lightgrey);
  border-radius: 8px;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  font-size: 14px;
  color: var(--su__searchapp-darkgray);
}

.su__form-control::-ms-expand {
  background-color: transparent;
  border: 0;
}

.su__form-control:focus:not(:focus-visible) {
  color: var(--su__searchapp-darkgray);
  background-color: var(--su__searchapp-white);
  outline: 0;
}

.su__form-control::-webkit-input-placeholder {
  color: var(--su__searchapp-darkgray);
  opacity: 1;
}

.su__form-control::-moz-placeholder {
  color: var(--su__searchapp-darkgray);
  opacity: 1;
}

.su__form-control:-ms-input-placeholder {
  color: var(--su__searchapp-darkgray);
  opacity: 1;
}

.su__form-control::-ms-input-placeholder {
  color: var(--su__searchapp-darkgray);
  opacity: 1;
}

.su__form-control::placeholder {
  color: var(--su__searchapp-darkgray);
  opacity: 1;
}

.su__form-control:disabled,
.su__form-control[readonly] {
  background-color: var(--su__searchapp-lightgrey);
  opacity: 1;
}

.su__position-static {
  position: static;
}

.su__position-relative {
  position: relative;
}

.su__position-unset {
  position: unset;
}

.su__position-absolute {
  position: absolute;
}

.su__position-fixed {
  position: fixed;
}

.su__position-sticky {
  position: -webkit-sticky;
  position: sticky;
}

.su__right-0,
.su__rtl .su__right-0-rtl {
  right: 0;
}

.su__left-0,
.su__rtl .su__left-0-rtl {
  left: 0;
}

.su__left-3,
.su__rtl .su__left-3-rtl {
  left: 16px;
}

.su__top-0 {
  top: 0;
}

.su__bottom-0 {
  bottom: 0;
}

.su__top-10px {
  top: 10px;
}

.su__top-5px {
  top: 5px;
}

.su__trbl {
  top: 2px;
  right: 0;
  left: 0;
  z-index: 9;
}

.su__trbl-0 {
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
}

.su__fixed-top {
  position: fixed;
  top: 0;
  right: 0;
  left: 0;
  z-index: 1030;
}

.su__fixed-bottom {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1030;
}

.su__flex-1,
.su__media-body {
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

.su__flex-gap {
  gap: 4px;
}

.su__flex-gap-5px {
  gap: 5px;
}

.su__flex-gap-9px {
  gap: 9px;
}

.su__flex-gap-33px {
  gap: 33px;
}

.su__flex-gap-20px {
  gap: 20px;
}

.su__flex-gap-12px {
  gap: 12px;
}

.su__flex-gap-13 {
  gap: 13px;
}

.su__img-fluid {
  max-width: 100%;
  height: auto;
}

.su__gpt-citation {
  margin-right: 33px;
}

.su__rtl .su__gpt-citation {
  margin-right: 0;
  margin-left: 33px;
}

.su__media {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
}

.su__pagination li.disabled {
  background: transparent;
}

.su__pagination li.disabled:first-child,
.su__pagination li:last-child {
  display: none;
}

.su__pagination li a {
  position: relative;
  display: block;
  padding: 0.64px 12px;
  border-radius: 4px;
  margin-left: -1px;
  line-height: 1.25;
  color: var(--su__searchapp-deepskyblue-light);
  font-weight: 500;
  text-decoration: none;
  box-shadow: 0 2px 2px 0 var(--su__searchapp-rgba-darkblue-10);
  margin-right: 7px;
  background: var(--su__searchapp-white);
}

.su__pagination li a:hover {
  color: var(--su__searchapp-deepskyblue-light);
  background-color: var(--su__searchapp-lightgrey);
  border-color: var(--su__searchapp-lightgrey);
}

.su__pagination li a:focus:not(:focus-visible) {
  z-index: 2;
  outline: 0;
}

.su__pagination li a:not(:disabled):not(.disabled) {
  cursor: pointer;
}

.su__pagination li:first-child a {
  margin-left: 0;
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
}

.su__pagination li:last-child a {
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
}

.su__pagination li.active a {
  z-index: 1;
  color: var(--su__searchapp-white);
  background-color: var(--su__searchapp-deepskyblue-light);
  border-color: var(--su__searchapp-deepskyblue-light);
}

.su__pagination li.disabled a {
  color: var(--su__searchapp-darkgray);
  pointer-events: none;
  cursor: auto;
  background-color: var(--su__searchapp-white);
  border-color: var(--su__searchapp-lightgrey);
}

.su__pagination-lg a {
  padding: 12px 24px;
  font-size: 20px;
  line-height: 1.5;
}

.su__pagination-lg .su__pagination li:first-child a {
  border-top-left-radius: 0.48px;
  border-bottom-left-radius: 0.48px;
}

.su__pagination-lg .su__pagination li:last-child a {
  border-top-right-radius: 0.48px;
  border-bottom-right-radius: 0.48px;
}

.su__pagination-sm a {
  padding: 4px 8px;
  font-size: 14px;
  line-height: 1.5;
}

.su__pagination-sm .su__pagination li:first-child a {
  border-top-left-radius: 3px;
  border-bottom-left-radius: 3px;
}

.su__pagination-sm .su__pagination li:last-child a {
  border-top-right-radius: 3px;
  border-bottom-right-radius: 3px;
}

.su__loading .su__border {
  border-color: var(--su__searchapp-lightgray-14);
}

select.su__form-control:focus::-ms-value {
  color: var(--su__searchapp-darkgray);
  background-color: var(--su__searchapp-white);
}

.su__clear-both {
  content: '';
  clear: both;
  display: table;
}

.su__container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
}

.su__line-clamp {
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

/* Skip to main content */
.su__skip-link {
  position: absolute;
  top: 0;
  z-index: -1;
  font-family: 'Montserrat', sans-serif;
  left: 50%;
  transform: translateX(-50%);
  background: var(--su__searchapp-gold);
  color: var(--su__searchapp-black);
  font-size: 16px;
  text-decoration: none;
  border-radius: 0 0 3px 3px;
  padding: 9px 21px;
  font-weight: 500;
  box-shadow: 0 3px 6px rgba(var(--su__searchapp-black-rgba), 0.16);
}

.su__skip-link:focus-visible {
  z-index: 500;
}

/* Rich Snippet CSS Starts */

#scrollableArea {
  flex-grow: 1;
}

#scrollableArea::-webkit-scrollbar {
  width: 5px;
}

#scrollableArea::-webkit-scrollbar-track {
  border-radius: 10px;
  padding: 0 10px;
}

#scrollableArea::-webkit-scrollbar-thumb {
  background: var(--su__searchapp-lightgrey);
  border-radius: 10px;
}

.su__typed_text {
  width: 100%;
}

.su__remove_space {
  word-break: break-word;
  padding-bottom: 7px;
}

.su__snippets_container {
  border: 10px solid var(--su__searchapp-white);
  background: transparent
    linear-gradient(
      297deg,
      rgba(var(--su__searchapp-deepskyblue-light-rgba), 0.3) 0%,
      var(--su__searchapp-silver-20) 100%
    )
    0% 0% no-repeat padding-box;
  border-radius: 5px;
  overflow: hidden;
  min-height: 162px;
}

.su__typing-animation {
  border-radius: 0 0 4px 4px;
  display: flex;
  flex-direction: column;
}

.su__height-showmore {
  max-height: 240px;
  min-height: 82px;
}

.su__show-more-gpt {
  text-align: center;
  letter-spacing: 0;
  color: var(--su__searchapp-black);
  opacity: 1;
  margin-right: 8px;
  font-size: 14px;
  font-weight: 500;
}

.su__submit {
  background: var(--su__searchapp-deepskyblue-light) 0% 0% no-repeat padding-box;
  border-radius: 3px;
  color: var(--su__searchapp-white);
}

.su__submit:disabled {
  opacity: 0.5;
}

.su__black-color {
  color: var(--su__searchapp-black);
  font-weight: 500;
}

.su__modal-inner.su__thanks-modal {
  width: 350px;
  height: 168px;
  border-radius: 4px;
}

.su__margin-thanks {
  margin-left: 60px;
}

.su__margin-thanks-color {
  color: var(--su__searchapp-darkgray) !important;
  font-size: 14px !important;
  font-weight: 500;
}

.su__margin-norm {
  margin-left: 18px;
}

.su__thanks-check {
  margin-left: 135px;
  margin-top: 30px;
}

.su__copied-span {
  height: 21px;
  color: var(--su__searchapp-white);
  background: var(--su__searchapp-vividgreen);
  font-size: 12px;
  align-items: center;
  display: inline-flex;
  font-family: 'Montserrat';
  padding: 4px;
  border-radius: 3px;
  margin-right: 5px;
  margin-bottom: 3px;
}

.su__show-more-height-min {
  min-height: 86px;
}

.su__animation {
  opacity: 0;
}

.su__snippet_heading {
  margin: 10px 0 10px 0;
  position: relative;
}

.su__snippet_fontStyles {
  font-size: 7px;
  font-family: 'Montserrat';
  color: var(--su__searchapp-darkorange);
  position: relative;
  font-weight: 400;
  bottom: 4px;
  margin-left: 2px;
}

.su__snippet_heading_color {
  color: var(--su__searchapp-darkorange);
  font-family: 'Montserrat';
}

.typing-animation {
  overflow: hidden;
  display: inline-block;
  position: relative;
}

.typing-text {
  display: inline-block;
  overflow: hidden;
  transition: width 0.5s;
}

.bg_noresult {
  background: none;
}

.heightContainer {
  height: 305px;
}

.su__typing_annimation::after {
  content: ' ';
  animation: blink 0.7s infinite;
  top: -2px;
  position: relative;
}

.removeCursor::after {
  content: unset;
  animation: unset;
}

.su__noresult_text_color {
  color: var(--su__searchapp-deepskyblue-light);
  font-size: 13px;
}

.su__noResult_container {
  background: var(--su__searchapp-white);
  position: relative;
  border-radius: 8px;
  margin-top: -17px;
  padding: 7px 10px;
}

/* WCAG fixes */

/* Text spacing */

label[for='auto-learning'] {
  max-width: 101px;
  word-break: break-word;
}

/* Color contrast */

#search-box-search::-webkit-input-placeholder {
  /* Chrome/Opera/Safari */
  color: var(--su__searchapp-black);
  opacity: 50%;
}

#search-box-search::-moz-placeholder {
  /* Firefox 19+ */
  color: var(--su__searchapp-black);
  opacity: 50%;
}

#search-box-search:-ms-input-placeholder {
  /* IE 10+ */
  color: var(--su__searchapp-black);
  opacity: 50%;
}

#search-box-search:-moz-placeholder {
  /* Firefox 18- */
  color: var(--su__searchapp-black);
  opacity: 50%;
}

.auto-suggestion .su__bg-gray-hover:hover,
.auto-suggestion .su__bg-gray-hover:focus {
  background: var(--su__searchapp-paleblue-cool);
  border-radius: 4px;
}

.su__autosuggestion_container {
  margin: 0 24px 0 10px;
}

.su__customized_switch input + .su__customized_switch_slider {
  background: transparent;
  box-shadow: 0 0 0 1px var(--su__searchapp-darkgray) !important;
}

.su__customized_switch input + .su__customized_switch_slider::before {
  background-color: var(--su__searchapp-darkgray);
}

.su__error-msg {
  display: none;
  font-size: 12px;
  color: var(--su__searchapp-red);
  margin-top: 5px;
}

.su__formError .su__error-msg,
.su__search-facet-empty + .su__error-msg {
  display: block;
}

.su__acc-hidden {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}

/* Horizontal slider */
.su__slider-outer {
  display: flex;
}

.su__slider-button {
  flex: 0 0 25px;
  background: var(--su__searchapp-white);
  border-top: 1px solid var(--su__searchapp-lightgrey);
  border-bottom: 1px solid var(--su__searchapp-lightgrey);
  border-width: 1px 0 1px 0 !important;
  height: 42px;
  position: relative;
  cursor: pointer;
}

.su__slider-btn-left {
  position: absolute;
  left: 0;
  width: 23px;
  height: 23px;
  background: var(--su__searchapp-white) 0% 0% no-repeat padding-box;
  box-shadow: 0 3px 6px rgba(var(--su__searchapp-black-rgba), 0.16);
  border-radius: 5px;
  opacity: 1;
  top: 24px;
  z-index: 2;
  border: 1px solid var(--su__searchapp-white);
}

.su__slider-btn-right {
  position: absolute;
  right: 0;
  width: 23px;
  height: 23px;
  background: var(--su__searchapp-white) 0% 0% no-repeat padding-box;
  box-shadow: 0 3px 6px rgba(var(--su__searchapp-black-rgba), 0.16);
  border-radius: 5px;
  opacity: 5;
  top: 24px;
  z-index: 2;
  border: 1px solid var(--su__searchapp-white);
}

.su__slider_btn_position {
  top: 10px;
}

.su__slider-list {
  display: flex;
  flex: 1 1 auto;
}

.su__slider-button-right::after {
  position: absolute;
  content: '';
  width: 7px;
  height: 7px;
  border-top: 2px solid var(--su__searchapp-black);
  border-right: 2px solid var(--su__searchapp-black);
  left: 6px;
  transform: rotate(45deg);
  top: 15px;
}

.su__slider-btn-right::after,
.su__slider-btn-left::after {
  position: absolute;
  content: '';
  width: 7px;
  height: 7px;
  border-top: 2px solid var(--su__searchapp-black);
  border-right: 2px solid var(--su__searchapp-black);
  left: 3px;
  transform: rotate(45deg);
  top: 5px;
  box-sizing: content-box;
}

.su__slider-btn-left::after {
  transform: rotate(225deg);
  left: 8px;
}

.su__slider-button-left::after {
  transform: rotate(225deg);
  left: 10px;
}

.su__tabsSection .su__slider-button {
  border-top: 0;
  border-bottom: 0;
  border-radius: 3px;
}

/* waveone tool issues */

.su__bookmark-popup-title h2:focus {
  outline: 1px solid var(--su__searchapp-white);
}

#Save-bookmark.su__bookmark-input::placeholder {
  color: var(--su__searchapp-gray-standard) !important;

  /* Replace with desired color */
  font-weight: bold !important;
}

/* WCAG phase 2 fixes */

.a11y-btn {
  border: 0;
  background-color: transparent;
}

.p-0 {
  padding: 0 !important;
}

.m-auto {
  margin: auto;
}

.w-auto {
  width: auto;
}

.border-0 {
  border: 0;
}

.d-block {
  display: block !important;
}

.d-none {
  display: none !important;
}

.border-left-inherit {
  border-left: inherit;
}

.su__toggle-input:focus + label::before {
  border-color: var(--su__searchapp-deepskyblue-light) !important;
  filter: drop-shadow(0 0 2px var(--su__searchapp-deepskyblue-light));
}

.su__filter-toggle input[type='checkbox']:checked:focus + label::before {
  border-color: var(--su__searchapp-black) !important;
}

.su__refine__search:focus {
  outline: 1px solid black;
}

.su__feedback-row {
  display: flex;
  flex-direction: column;
  row-gap: 18px;
}

.su__feedradio-group .su__feedradio-row input[type='radio'] {
  display: block;
  opacity: 0;
  width: 0;
  height: 0;
  margin: 0;
}

.su__feedradio-group .su__feedradio-row input[type='radio']:not(:first-of-type) {
  height: 18px;
}

.su__feedradio-row input[type='radio']:focus + label {
  outline: 2px solid var(--su__searchapp-deepskyblue-light);
}

.apply {
  padding-left: 41px !important;
  padding-right: 42px !important;
}

.disabled-btn {
  pointer-events: none;
}

.setting-container {
  right: 0;
  bottom: 49%;
  z-index: 2;
  width: 28px;
  position: fixed;
  height: 23px;
}

.su__feedback-text {
  position: fixed;
  right: 74px;
  bottom: 150px;
}

.su__pagination.a11y-pagination li:last-child {
  display: block;
}

.order-4 {
  order: 4;
}

.su__meta-data {
  margin-top: 2px;
}

.su__viewed-results a:focus-visible {
  border: 1px solid var(--su__searchapp-deepskyblue-light);
}

.su__text_align a:focus-visible {
  border: 1px solid var(--su__searchapp-deepskyblue-light);
}

/************** Center align the react GPT widget starts ***************/
.su__fullwidth-gpt {
  max-width: 100% !important;
  flex: 0 0 100% !important;
}

.su__center-gpt-widget .su__snippets_container {
  min-height: auto;
  background: var(--su__searchapp-white);
  border: 1px solid var(--su__searchapp-lightgrey);
  border-radius: 4px;
}

.su__center-gpt-widget .su__noResult_container {
  background: transparent;
  margin-top: 0;
}

/* Loader Animation */
.loader-text {
  color: var(--su__searchapp-deepskyblue-light);
  margin-left: 6px;
}

.skeleton-box {
  display: inline-block;
  height: 10px;
  position: relative;
  overflow: hidden;
  background: transparent
    linear-gradient(
      270deg,
      rgba(var(--su__searchapp-white-rgba), 0.11) 0%,
      var(--su__searchapp-paleblue-cool) 43%,
      rgba(var(--su__searchapp-deepskyblue-light-rgba), 0.22) 100%
    )
    0% 0% no-repeat padding-box;
  width: 100%;
  border-radius: 2px;
}

.skeleton-box::after {
  position: absolute;
  top: 80px;
  right: 0;
  bottom: 0;
  left: 0;
  transform: translateX(-100%);
  background-image: linear-gradient(
    90deg,
    rgba(var(--su__searchapp-white-rgba), 0) 0,
    rgba(var(--su__searchapp-white-rgba), 0.2) 20%,
    rgba(var(--su__searchapp-white-rgba), 0.5) 60%,
    rgba(var(--su__searchapp-white-rgba), 0)
  );
  -webkit-animation: shimmer 5s infinite;
  animation: shimmer 5s infinite;
  content: '';
}

.skeleton-box:not(:first-child) {
  margin-top: 12px;
}

.skeleton-box:last-child {
  margin-bottom: 10px;
}

.skeleton-box:nth-child(1) {
  background: transparent
    linear-gradient(
      90deg,
      rgba(var(--su__searchapp-deepskyblue-light-rgba), 0.85) 0%,
      var(--su__searchapp-lightblue-alt) 43%,
      var(--su__searchapp-paleblue-cool) 100%
    )
    0% 0% no-repeat padding-box;
  margin-top: 20px;
}

.skeleton-box:nth-child(2) {
  background: transparent
    linear-gradient(
      90deg,
      var(--su__searchapp-paleblue-cool) 0%,
      var(--su__searchapp-lightgray-14) 48%,
      var(--su__searchapp-paleblue-cool) 100%
    )
    0% 0% no-repeat padding-box;
}

.skeleton-box:nth-child(3) {
  background: transparent
    linear-gradient(
      270deg,
      var(--su__searchapp-lightblue) 0%,
      var(--su__searchapp-lightblue-alt) 100%
    )
    0% 0% no-repeat padding-box;
}

.skeleton-box:nth-child(1)::after {
  animation: shimmer 4s ease-in-out infinite;
}

.skeleton-box:nth-child(2)::after {
  animation: shimmer 3s ease-in-out infinite;
}

.skeleton-box:nth-child(3)::after {
  animation: shimmer 5s ease-in-out infinite;
}

.skeleton-box:nth-child(4)::after {
  animation: shimmer 2s ease-in-out infinite;
}

@-webkit-keyframes shimmer {
  100% {
    transform: translateX(100%);
  }
}

@keyframes shimmer {
  100% {
    transform: translateX(100%);
  }
}

/* End of loading animation styles */

/******* Error logs *******/
.retry-section {
  display: flex;
  align-items: center;
  height: 100%;
  justify-content: space-between;
  margin: 2px 0;
  min-height: 26px;
}

.retry-section p {
  font-family: 'Montserrat', sans-serif;
  font-weight: 400;
  color: var(--su__searchapp-darkgray);
  margin: 0;
}

.retry-btn {
  border: 0;
  color: var(--su__searchapp-white);
  font-size: 13px;
  border-radius: 3px;
  padding: 5px 13px;
  display: block;
  min-width: 77px;
  background: linear-gradient(
    -45deg,
    var(--su__searchapp-turquoise),
    var(--su__searchapp-lightcornflowerblue),
    var(--su__searchapp-deepskyblue-light),
    var(--su__searchapp-violet),
    var(--su__searchapp-lightblue)
  );
  background-size: 600%;
  box-shadow: 2px 2px 6px var(--su__searchapp-silver-60);
  animation: bg-animate 8s linear infinite;
}
.su__summary-border-no-result .retry-btn {
  margin-left: 15px;
}

.retry-btn svg {
  margin-left: 4px;
  position: relative;
  top: 2px;
}

@keyframes bg-animate {
  0% {
    background-position: 0% 50%;
  }

  50% {
    background-position: 100% 50%;
  }

  100% {
    background-position: 0% 50%;
  }
}

.su__typing-animation.error-displayed {
  height: auto;
}

.draggableList:hover {
  background-size: 20px;
  background-position: 103% 56%;
  background-repeat: no-repeat;
  background-color: var(--su__searchapp-paleblue-cool);
  opacity: 1;
  border-color: var(--su__searchapp-lightblue-alt);
}

.su__rtl .draggableList:hover {
  background-position: left 56%;
}

.su__rtl .draggableList {
  padding: 10px 5px 10px 5px;
}

.su__fs-img-container .su__fs-redirect,
.su__fs-img-container .su__fs-feedback-btns {
  display: none;
}

.su__fs-img-container:hover .su__fs-feedback-btns,
.su__fs-img-container:focus-within .su__fs-feedback-btns,
.su__fs-img-container:hover .su__fs-redirect,
.su__fs-img-container:focus-within .su__fs-redirect {
  display: flex;
}

.su__Recommended_flex {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}

.su__Recommended_Article-section {
  min-width: 49%;
  max-width: 49%;
  flex: 1;
}

.su__similarSearches {
  min-width: 49%;
  max-width: 49%;
  flex: 1;
}

.su__fs-singleImage {
  padding: 10px 20px 10px 13px;
}

.su__fs-singleImage .su__featured-thumbnail {
  height: 120px;
  width: 234px;
  max-width: 100%;
}

.su__fs-singleImage .su__img-featured {
  height: 120px;
  width: 234px;
  max-width: 100%;
}

.su__recommendations-results {
  padding: 16px 0 16px 16px;
}

.su__rtl .su__recommendations-results {
  padding: 16px 16px 16px 0;
}

@media (min-width: 320px) {
  .su__container {
    max-width: 100%;
  }

  .su__container_custom {
    max-width: auto;
  }

  .su-customizes {
    max-width: 50%;
  }
}

@media (min-width: 576px) {
  .su__container_custom {
    max-width: 100vw;
  }
}

@media (min-width: 768px) {
  .su__feedback-modal {
    width: 395px;
  }
}

@media (min-width: 1400px) {
  .su__grid-content .su__list-items {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 48.8%;
    flex: 0 0 48.8%;
    max-width: 48.8%;
  }
}

.su__w-25 {
  width: 25%;
}

.su__w-30 {
  width: 30%;
}

.su__w-35 {
  width: 35%;
}

.su__w-45 {
  width: 45%;
}

.su__min-w-30 {
  min-width: 30%;
}

.su__w-50 {
  width: 50%;
}

.su__w-55 {
  width: 55%;
}

.su__w-70 {
  width: 70%;
}

.su__w-80px {
  width: 80px;
}

.su__w-85px {
  width: 85px;
}

.su__w-75 {
  width: 75%;
}

.su__w-100 {
  width: 100%;
}

.su__w-97 {
  width: 97%;
}

.su__w-60 {
  width: 60%;
}

.su__w-auto {
  width: auto;
}

.su__h-25 {
  height: 25%;
}

.su__h-30 {
  height: 30%;
}

.su__h-50 {
  height: 50%;
}

.su__h-75 {
  height: 75%;
}

.su__h-100 {
  height: 100%;
}

.su__h-auto {
  height: auto;
}

.su__mw-100 {
  max-width: 100%;
}

.su__mh-100 {
  max-height: 100%;
}

.su__m-1,
.su__rtl .su__rtlm-1 {
  margin: 4px;
}

.su__mt-1,
.su__my-1,
.su__rtl .su__rtlmt-1,
.su__rtl .su__rtlmy-1 {
  margin-top: 4px;
}

.su__mr-1,
.su__mx-1,
.su__rtl .su__rtlmr-1,
.su__rtl .su__rtlmx-1 {
  margin-right: 4px;
}

.su__mb-1,
.su__my-1,
.su__rtl .su__rtlmb-1,
.su__rtl .su__rtlmy-1 {
  margin-bottom: 4px;
}

.su__mt-025 {
  margin-top: 4px;
}

.su__ml-1,
.su__mx-1,
.su__rtl .su__rtlml-1,
.su__rtl .su__rtlmx-1 {
  margin-left: 4px;
}

.su__m-2,
.su__rtl .su__rtlm-2 {
  margin: 8px;
}

.su__mt-2,
.su__my-2,
.su__rtl .su__rtlmt-2,
.su__rtl .su__rtlmy-2 {
  margin-top: 8px;
}

.su__mb-15px {
  margin-bottom: 14px;
}

.su__mr-2,
.su__mx-2,
.su__rtl .su__rtlmr-2,
.su__rtl .su__rtlmx-2 {
  margin-right: 8px;
}

.su__mb-2,
.su__my-2,
.su__rtl .su__rtlmb-2,
.su__rtl .su__rtlmy-2 {
  margin-bottom: 8px;
}

.su__ml-2,
.su__mx-2,
.su__rtl .su__rtlml-2,
.su__rtl .su__rtlmx-2 {
  margin-left: 8px;
}

.su__m-3,
.su__rtl .su__rtlm-3 {
  margin: 16px;
}

.su__mt-3,
.su__my-3,
.su__rtl .su__rtlmt-3,
.su__rtl .su__rtlmy-3 {
  margin-top: 16px;
}

.su__mr-3,
.su__mx-3,
.su__rtl .su__rtlmr-3,
.su__rtl .su__rtlmx-3 {
  margin-right: 16px;
}

.su__mb-3,
.su__my-3,
.su__rtl .su__rtlmb-3,
.su__rtl .su__rtlmy-3 {
  margin-bottom: 16px;
}

.su__ml-3,
.su__mx-3,
.su__rtl .su__rtlml-3,
.su__rtl.su__rtlmx-3 {
  margin-left: 16px;
}

.su__ml-1r {
  margin-left: 16px;
}

.su__mr-1r {
  margin-right: 16px;
}

.su__m-4,
.su__rtl .su__rtlm-4 {
  margin: 24px;
}

.su__mt-4,
.su__my-4,
.su__rtl .su__rtlmt-4,
.su__rtl .su__rtlmy-4 {
  margin-top: 24px;
}

.su__mr-4,
.su__mx-4,
.su__rtl .su__rtlmr-4,
.su__rtl .su__rtlmx-4 {
  margin-right: 24px;
}

.su__mb-4,
.su__my-4,
.su__rtl .su__rtlmb-4,
.su__rtl .su__rtlmy-4 {
  margin-bottom: 24px;
}

.su__ml-4,
.su__mx-4,
.su__rtl .su__rtlml-4,
.su__rtl .su__rtlmx-4 {
  margin-left: 24px;
}

.su__m-5,
.su__rtl .su__rtlm-5 {
  margin: 48px;
}

.su__mt-5,
.su__my-5 {
  margin-top: 48px;
}

.su__mr-5,
.su__mx-5 {
  margin-right: 48px;
}

.su__mb-5,
.su__my-5 {
  margin-bottom: 48px;
}

.su__ml-5,
.su__mx-5 {
  margin-left: 48px;
}

.su__m-6 {
  margin: 64px;
}

.su__mt-6,
.su__my-6 {
  margin-top: 64px;
}

.su__mr-6,
.su__mx-6 {
  margin-right: 64px;
}

.su__mb-6,
.su__my-6 {
  margin-bottom: 64px;
}

.su__ml-6,
.su__mx-6 {
  margin-left: 64px;
}

.su__ml-il-20 {
  margin-inline-start: 20px;
}

.su__mr-il-20 {
  margin-inline-end: 20px;
}

.su__m-7 {
  margin: 96px;
}

.su__mt-7,
.su__my-7 {
  margin-top: 96px;
}

.su__mr-7,
.su__mx-7 {
  margin-right: 96px;
}

.su__mb-7,
.su__my-7 {
  margin-bottom: 96px;
}

.su__ml-7,
.su__mx-7 {
  margin-left: 96px;
}

.su__mt-0,
.su__my-0,
.su__rtl .su__rtlmt-0,
.su__rtl .su__rtlmy-0 {
  margin-top: 0;
}

.su__mr-0,
.su__mx-0,
.su__rtl .su__rtlmr-0,
.su__rtl .su__rtlmx-0 {
  margin-right: 0;
}

.su__mb-0,
.su__my-0,
.su__rtl .su__rtlmb-0,
.su__rtl .su__rtlmy-0 {
  margin-bottom: 0;
}

.su__ml-0,
.su__mx-0,
.su__rtl .su__rtlml-0,
.su__rtl .su__rtlmx-0 {
  margin-left: 0;
}

.su__p-1,
.su__rtl .su__rtlp-1 {
  padding: 4px;
}

.su__pt-1,
.su__py-1,
.su__rtl .su__rtlpt-1,
.su__rtl .su__rtlpy-1 {
  padding-top: 4px;
}

.su__pr-1,
.su__px-1,
.su__rtl .su__rtlpr-1,
.su__rtl .su__rtlpx-1 {
  padding-right: 4px;
}

.su__pr-1-inline {
  padding-inline-end: 4px;
}

.su__pb-1,
.su__py-1,
.su__rtl .su__rtlpb-1,
.su__rtl .su__rtlpy-1 {
  padding-bottom: 4px;
}

.su__pl-1,
.su__px-1,
.su__rtl .su__rtlpl-1,
.su__rtl .su__rtlpx-1 {
  padding-left: 4px;
}

.su__p-2,
.su__rtl .su__rtlp-2 {
  padding: 8px;
}

.su__pt-2,
.su__py-2,
.su__rtl .su__rtlpt-2,
.su__rtl .su__rtlpy-2 {
  padding-top: 8px;
}

.su__pr-2,
.su__px-2,
.su__rtl .su__rtlpr-2,
.su__rtl .su__rtlpx-2 {
  padding-right: 8px;
}

.su__pb-2,
.su__py-2,
.su__rtl .su__rtlpb-2,
.su__rtl .su__rtlpy-2 {
  padding-bottom: 8px;
}

.su__pl-2,
.su__px-2,
.su__rtl .su__rtlpl-2,
.su__rtl .su__rtlpx-2 {
  padding-left: 8px;
}

.su__pt-4px {
  padding-top: 4px;
}

.su__p-3,
.su__rtl .su__rtlp-3 {
  padding: 16px;
}

.su__pt-3,
.su__py-3,
.su__rtl .su__rtlpt-3,
.su__rtl .su__rtlpy-3 {
  padding-top: 16px;
}

.su__pr-3,
.su__px-3,
.su__rtl .su__rtlpr-3,
.su__rtl .su__rtlpx-3 {
  padding-right: 16px;
}

.su__pb-3,
.su__py-3,
.su__rtl .su__rtlpb-3,
.su__rtl .su__rtlpy-3 {
  padding-bottom: 16px;
}

.su__pl-3,
.su__px-3,
.su__rtl .su__rtlpl-3,
.su__rtl .su__rtlpx-3 {
  padding-left: 16px;
}

.su__px-56px {
  padding-left: 56px;
  padding-right: 56px;
}

.su__p-4,
.su__rtl .su__rtlp-4 {
  padding: 24px;
}

.su__pt-4,
.su__py-4,
.su__rtl .su__rtlpt-4,
.su__rtl .su__rtlpy-4 {
  padding-top: 24px;
}

.su__pr-4,
.su__px-4,
.su__rtl .su__rtlpr-4,
.su__rtl .su__rtlpx-4 {
  padding-right: 24px;
}

.su__pb-4,
.su__py-4,
.su__rtl .su__rtlpb-4,
.su__rtl .su__rtlpy-4 {
  padding-bottom: 24px;
}

.su__pl-4,
.su__px-4,
.su__rtl .su__rtlpl-4,
.su__rtl .su__rtlpx-4 {
  padding-left: 24px;
}

.su__p-5,
.su__rtl .su__rtlp-5 {
  padding: 48px;
}

.su__pt-5,
.su__py-5,
.su__rtl .su__rtlpt-5,
.su__rtl .su__rtlpy-5 {
  padding-top: 48px;
}

.su__pr-5,
.su__px-5,
.su__rtl .su__rtlpr-5,
.su__rtl .su__rtlpx-5 {
  padding-right: 48px;
}

.su__pb-5,
.su__py-5,
.su__rtl .su__rtlpb-5,
.su__rtl .su__rtlpy-5 {
  padding-bottom: 48px;
}

.su__pl-5,
.su__px-5,
.su__rtl .su__rtlpl-5,
.su__rtl .su__rtlpx-5 {
  padding-left: 48px;
}

.su__p-6 {
  padding: 64px;
}

.su__pt-6,
.su__py-6 {
  padding-top: 64px;
}

.su__pr-6,
.su__px-6 {
  padding-right: 64px;
}

.su__pb-6,
.su__py-6 {
  padding-bottom: 64px;
}

.su__pl-6,
.su__px-6 {
  padding-left: 64px;
}

.su__p-7 {
  padding: 64px;
}

.su__pt-7,
.su__py-7 {
  padding-top: 96px;
}

.su__pr-7,
.su__px-7 {
  padding-right: 96px;
}

.su__pb-7,
.su__py-7 {
  padding-bottom: 96px;
}

.su__pl-7,
.su__px-7 {
  padding-left: 96px;
}

.su__pt-0,
.su__py-0,
.su__rtl .su__rtlpt-0,
.su__rtl .su__rtlpy-0 {
  padding-top: 0;
}

.su__pr-0,
.su__px-0,
.su__rtl .su__rtlpr-0,
.su__rtl .su__rtlpx-0 {
  padding-right: 0;
}

.su__pb-0,
.su__py-0,
.su__rtl .su__rtlpb-0,
.su__rtl .su__rtlpy-0 {
  padding-bottom: 0;
}

.su__pl-0,
.su__px-0,
.su__rtl .su__rtlpl-0,
.su__rtl .su__rtlpx-0 {
  padding-left: 0;
}

.su__right-unset,
.su__rtl .su__right-unset-rtl {
  right: unset;
}

.su__rtl .su__cross_icon_rtl {
  right: unset;
  left: 14px;
  top: 3px;
}

.su__rtl .su__cross_icon-fb_rtl {
  right: unset;
  left: 14px;
  top: 14px;
}

.su__m-auto {
  margin: auto;
}

.su__mt-auto,
.su__my-auto {
  margin-top: auto;
}

.su__mr-auto,
.su__mx-auto {
  margin-right: auto;
}

.su__mb-auto,
.su__my-auto {
  margin-bottom: auto;
}

.su__ml-auto,
.su__mx-auto {
  margin-left: auto;
}

.su__mr-12px {
  margin-right: 12px;
}

.su__mx-12px {
  margin-left: 12px;
  margin-right: 12px;
}

.su__rtl .su__mr-auto-rtl {
  margin-right: auto;
}

.su__rtl .su__rtl-mr-unset {
  margin-right: unset;
}

.su__rtl .su__ml-unset-rtl {
  margin-left: unset;
}

.su__text-justify {
  text-align: justify;
}

.su__text-nowrap {
  white-space: nowrap;
}

.su__text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.su__center-element {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
}

.su__overflow-hide {
  overflow: hidden;
}

.su__overflow-visible {
  overflow: visible;
}

.su__overflow-auto {
  overflow: auto;
}

.su__overflowx-auto {
  overflow-x: auto;
}

.su__text-left {
  text-align: left;
}

.su__text-right {
  text-align: right;
}

.su__text-center {
  text-align: center;
}

.su__text-decoration {
  text-decoration: none;
}

.su__float-left {
  float: left;
}

.su__float-right {
  float: right;
}

.su__rtl .su__float-leftRtl {
  float: left;
}

.su__float-none {
  float: none;
}
.su__mw-300 {
  max-width: 300px;
}
.su__w-mcontent {
  width: max-content;
}
.su__container-fluid {
  width: 100%;
  padding-right: 15px;
  padding-left: 15px;
  margin-right: auto;
  margin-left: auto;
}

.su__row {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  margin-right: -15px;
  margin-left: -15px;
}

.su__gutters {
  margin-right: -15px;
  margin-left: -15px;
}

.su__no-gutters {
  margin-right: 0;
  margin-left: 0;
}

.su__col-1,
.su__col-2,
.su__col-3,
.su__col-4,
.su__col-5,
.su__col-6,
.su__col-7,
.su__col-8,
.su__col-9,
.su__col-10,
.su__col-11,
.su__col-12,
.su__col,
.su__col-auto,
.su__col-sm-1,
.su__col-sm-2,
.su__col-sm-3,
.su__col-sm-4,
.su__col-sm-5,
.su__col-sm-6,
.su__col-sm-7,
.su__col-sm-8,
.su__col-sm-9,
.su__col-sm-10,
.su__col-sm-11,
.su__col-sm-12,
.su__col-sm,
.su__col-sm-auto,
.su__col-md-1,
.su__col-md-2,
.su__col-md-3,
.su__col-md-4,
.su__col-md-5,
.su__col-md-6,
.su__col-md-7,
.su__col-md-8,
.su__col-md-9,
.su__col-md-10,
.su__col-md-11,
.su__col-md-12,
.su__col-md,
.su__col-md-auto,
.su__col-lg-1,
.su__col-lg-2,
.su__col-lg-3,
.su__col-lg-4,
.su__col-lg-5,
.su__col-lg-6,
.su__col-lg-7,
.su__col-lg-8,
.su__col-lg-9,
.su__col-lg-10,
.su__col-lg-11,
.su__col-lg-12,
.su__col-lg,
.su__col-lg-auto,
.su__col-xl-1,
.su__col-xl-2,
.su__col-xl-3,
.su__col-xl-4,
.su__col-xl-5,
.su__col-xl-6,
.su__col-xl-7,
.su__col-xl-8,
.su__col-xl-9,
.su__col-xl-10,
.su__col-xl-11,
.su__col-xl-12,
.su__col-xl,
.su__col-xl-auto {
  position: relative;
  width: 100%;
  min-height: 1px;
  padding-right: 15px;
  padding-left: 15px;
}

.su__col-md-12__mobile {
  position: relative;
  width: 100%;
  min-height: 1px;
  padding: 0;
  padding-left: 20px;
  padding-right: 20px;
  margin: 0 !important;
}

.su__col {
  -ms-flex-preferred-size: 0;
  flex-basis: 0;
  -webkit-box-flex: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  max-width: 100%;
}

.su__col-auto {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  width: auto;
  max-width: none;
}

.su__col-1 {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 8.333333%;
  flex: 0 0 8.333333%;
  max-width: 8.333333%;
}

.su__col-2 {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 16.666667%;
  flex: 0 0 16.666667%;
  max-width: 16.666667%;
}

.su__col-3 {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 25%;
  flex: 0 0 25%;
  max-width: 25%;
}

.su__col-4 {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 33.333333%;
  flex: 0 0 33.333333%;
  max-width: 33.333333%;
}

.su__col-5 {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 41.666667%;
  flex: 0 0 41.666667%;
  max-width: 41.666667%;
}

.su__col-6,
.su__meta-date {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 50%;
  flex: 0 0 50%;
  max-width: 50%;
}

.su__meta-data-tags {
  max-width: 100%;
  flex: auto;
}

.su__col-7 {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 58.333333%;
  flex: 0 0 58.333333%;
  max-width: 58.333333%;
}

.su__col-8 {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 66.666667%;
  flex: 0 0 66.666667%;
  max-width: 66.666667%;
}

.su__col-9 {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 75%;
  flex: 0 0 75%;
  max-width: 75%;
}

.su__col-10 {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 83.333333%;
  flex: 0 0 83.333333%;
  max-width: 83.333333%;
}

.su__col-11 {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 91.666667%;
  flex: 0 0 91.666667%;
  max-width: 91.666667%;
}

.su__col-12 {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 100%;
  flex: 0 0 100%;
  max-width: 100%;
}

.su__order-first {
  -webkit-box-ordinal-group: 0;
  -ms-flex-order: -1;
  order: -1;
}

.su__order-last {
  -webkit-box-ordinal-group: 14;
  -ms-flex-order: 13;
  order: 13;
}

.su__order-0 {
  -webkit-box-ordinal-group: 1;
  -ms-flex-order: 0;
  order: 0;
}

.su__order-1 {
  -webkit-box-ordinal-group: 2;
  -ms-flex-order: 1;
  order: 1;
}

.su__order-2 {
  -webkit-box-ordinal-group: 3;
  -ms-flex-order: 2;
  order: 2;
}

.su__order-3 {
  -webkit-box-ordinal-group: 4;
  -ms-flex-order: 3;
  order: 3;
}

.su__offset-1 {
  margin-left: 8.333333%;
}

.su__offset-2 {
  margin-left: 16.666667%;
}

.su__offset-3 {
  margin-left: 25%;
}

.su__offset-4 {
  margin-left: 33.333333%;
}

.su__offset-5 {
  margin-left: 41.666667%;
}

.su__offset-6 {
  margin-left: 50%;
}

.su__dropdowns-gaps {
  gap: 10px;
}

@media (min-width: 576px) {
  .su__w-sm-100 {
    width: 100%;
  }

  .su__col-sm {
    -ms-flex-preferred-size: 0;
    flex-basis: 0;
    -webkit-box-flex: 1;
    -ms-flex-positive: 1;
    flex-grow: 1;
    max-width: 100%;
  }

  .su__col-sm-auto {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: auto;
    max-width: none;
  }

  .su__col-sm-1 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 8.333333%;
    flex: 0 0 8.333333%;
    max-width: 8.333333%;
  }

  .su__col-sm-2 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 16.666667%;
    flex: 0 0 16.666667%;
    max-width: 16.666667%;
  }

  .su__col-sm-3 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 25%;
    flex: 0 0 25%;
    max-width: 25%;
  }

  .su__col-sm-4 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 33.333333%;
    flex: 0 0 33.333333%;
    max-width: 33.333333%;
  }

  .su__col-sm-5 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 41.666667%;
    flex: 0 0 41.666667%;
    max-width: 41.666667%;
  }

  .su__col-sm-6 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 50%;
    flex: 0 0 50%;
    max-width: 50%;
  }

  .su__col-sm-7 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 58.333333%;
    flex: 0 0 58.333333%;
    max-width: 58.333333%;
  }

  .su__col-sm-8 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 66.666667%;
    flex: 0 0 66.666667%;
    max-width: 66.666667%;
  }

  .su__col-sm-9 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 75%;
    flex: 0 0 75%;
    max-width: 75%;
  }

  .su__col-sm-10 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 83.333333%;
    flex: 0 0 83.333333%;
    max-width: 83.333333%;
  }

  .su__col-sm-11 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 91.666667%;
    flex: 0 0 91.666667%;
    max-width: 91.666667%;
  }

  .su__col-sm-12 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 100%;
    flex: 0 100%;
    max-width: 100%;
  }

  .su__order-sm-first {
    -webkit-box-ordinal-group: 0;
    -ms-flex-order: -1;
    order: -1;
  }

  .su__order-sm-last {
    -webkit-box-ordinal-group: 14;
    -ms-flex-order: 13;
    order: 13;
  }

  .su__order-sm-0 {
    -webkit-box-ordinal-group: 1;
    -ms-flex-order: 0;
    order: 0;
  }

  .su__order-sm-1 {
    -webkit-box-ordinal-group: 2;
    -ms-flex-order: 1;
    order: 1;
  }

  .su__order-sm-2 {
    -webkit-box-ordinal-group: 3;
    -ms-flex-order: 2;
    order: 2;
  }

  .su__order-sm-3 {
    -webkit-box-ordinal-group: 4;
    -ms-flex-order: 3;
    order: 3;
  }

  .su__offset-sm-0 {
    margin-left: 0;
  }

  .su__offset-sm-1 {
    margin-left: 8.333333%;
  }

  .su__offset-sm-2 {
    margin-left: 16.666667%;
  }

  .su__offset-sm-3 {
    margin-left: 25%;
  }
}

@media (min-width: 768px) {
  .su__w-md-100 {
    width: 100%;
  }

  .su__col-md {
    -ms-flex-preferred-size: 0;
    flex-basis: 0;
    -webkit-box-flex: 1;
    -ms-flex-positive: 1;
    flex-grow: 1;
    max-width: 100%;
  }

  .su__col-md-auto {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: auto;
    max-width: none;
  }

  .su__col-md-1 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 8.333333%;
    flex: 0 0 8.333333%;
    max-width: 8.333333%;
  }

  .su__col-md-2 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 16.666667%;
    flex: 0 0 16.666667%;
    max-width: 16.666667%;
  }

  .su__col-md-3 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 25%;
    flex: 0 0 25%;
    max-width: 25%;
  }

  .su__col-md-4 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 33.333333%;
    flex: 0 0 33.333333%;
    max-width: 33.333333%;
  }

  .su__col-md-5 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 41.666667%;
    flex: 0 0 41.666667%;
    max-width: 41.666667%;
  }

  .su__col-md-6 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 50%;
    flex: 0 0 50%;
    max-width: 50%;
  }

  .su__col-md-7 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 58.333333%;
    flex: 0 0 58.333333%;
    max-width: 58.333333%;
  }

  .su__col-md-8 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 66.666667%;
    flex: 0 0 66.666667%;
    max-width: 66.666667%;
  }

  .su__col-md-9 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 75%;
    flex: 0 0 75%;
    max-width: 75%;
  }

  .su__col-md-10 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 83.333333%;
    flex: 0 0 83.333333%;
    max-width: 83.333333%;
  }

  .su__col-md-11 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 91.666667%;
    flex: 0 0 91.666667%;
    max-width: 91.666667%;
  }

  .su__col-md-12 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 100%;
    flex: 0 0 100%;
    max-width: 100%;
  }

  .su__order-md-first {
    -webkit-box-ordinal-group: 0;
    -ms-flex-order: -1;
    order: -1;
  }

  .su__order-md-last {
    -webkit-box-ordinal-group: 14;
    -ms-flex-order: 13;
    order: 13;
  }

  .su__order-md-0 {
    -webkit-box-ordinal-group: 1;
    -ms-flex-order: 0;
    order: 0;
  }

  .su__order-md-1 {
    -webkit-box-ordinal-group: 2;
    -ms-flex-order: 1;
    order: 1;
  }

  .su__order-md-2 {
    -webkit-box-ordinal-group: 3;
    -ms-flex-order: 2;
    order: 2;
  }

  .su__order-md-3 {
    -webkit-box-ordinal-group: 4;
    -ms-flex-order: 3;
    order: 3;
  }

  .su__offset-md-0 {
    margin-left: 0;
  }

  .su__offset-md-1 {
    margin-left: 8.333333%;
  }

  .su__offset-md-2 {
    margin-left: 16.666667%;
  }

  .su__offset-md-3 {
    margin-left: 25%;
  }

  .su__meta-data {
    flex: 0 0 50%;
  }
}

@media (min-width: 992px) {
  .su__w-lg-100 {
    width: 100%;
  }

  .su__col-lg {
    -ms-flex-preferred-size: 0;
    flex-basis: 0;
    -webkit-box-flex: 1;
    -ms-flex-positive: 1;
    flex-grow: 1;
    max-width: 100%;
  }

  .su__col-lg-auto {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: auto;
    max-width: none;
  }

  .su__col-lg-1 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 8.333333%;
    flex: 0 0 8.333333%;
    max-width: 8.333333%;
  }

  .su__col-lg-2 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 16.666667%;
    flex: 0 0 16.666667%;
    max-width: 16.666667%;
  }

  .su__col-lg-3 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 25%;
    flex: 0 0 25%;
    max-width: 25%;
  }

  .su__col-lg-4 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 30%;
    flex: 0 0 30%;
    max-width: 30%;
  }

  .su__col-lg-5 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 41.666667%;
    flex: 0 0 41.666667%;
    max-width: 41.666667%;
  }

  .su__col-lg-6 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 50%;
    flex: 0 0 50%;
    max-width: 50%;
  }

  .su__col-lg-7 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 58.333333%;
    flex: 0 0 58.333333%;
    max-width: 58.333333%;
  }

  .su__col-lg-8 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 66.666667%;
    flex: 0 0 66.666667%;
    max-width: 66.666667%;
  }

  .su__col-lg-9 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 75%;
    flex: 0 0 75%;
    max-width: 75%;
  }

  .su__col-lg-10 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 83.333333%;
    flex: 0 0 83.333333%;
    max-width: 83.333333%;
  }

  .su__col-lg-11 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 91.666667%;
    flex: 0 0 91.666667%;
    max-width: 91.666667%;
  }

  .su__col-lg-12 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 100%;
    flex: 0 0 100%;
    max-width: 100%;
  }

  .su__order-lg-first {
    -webkit-box-ordinal-group: 0;
    -ms-flex-order: -1;
    order: -1;
  }

  .su__order-lg-last {
    -webkit-box-ordinal-group: 14;
    -ms-flex-order: 13;
    order: 13;
  }

  .su__order-lg-0 {
    -webkit-box-ordinal-group: 1;
    -ms-flex-order: 0;
    order: 0;
  }

  .su__order-lg-1 {
    -webkit-box-ordinal-group: 2;
    -ms-flex-order: 1;
    order: 1;
  }

  .su__order-lg-2 {
    -webkit-box-ordinal-group: 3;
    -ms-flex-order: 2;
    order: 2;
  }

  .su__order-lg-3 {
    -webkit-box-ordinal-group: 4;
    -ms-flex-order: 3;
    order: 3;
  }

  .su__offset-lg-0 {
    margin-left: 0;
  }

  .su__offset-lg-1 {
    margin-left: 8.333333%;
  }

  .su__offset-lg-2 {
    margin-left: 16.666667%;
  }

  .su__offset-lg-3 {
    margin-left: 25%;
  }
}

@media (min-width: 1200px) {
  .su__col-xl {
    -ms-flex-preferred-size: 0;
    flex-basis: 0;
    -webkit-box-flex: 1;
    -ms-flex-positive: 1;
    flex-grow: 1;
    max-width: 100%;
  }

  .su__col-xl-auto {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: auto;
    max-width: none;
  }

  .su__col-xl-1 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 8.333333%;
    flex: 0 0 8.333333%;
    max-width: 8.333333%;
  }

  .su__col-xl-2 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 16.666667%;
    flex: 0 0 16.666667%;
    max-width: 16.666667%;
  }

  .su__col-xl-3 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 24%;
    flex: 0 0 24%;
    max-width: 24%;
  }

  .su__col-xl-4 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 26%;
    flex: 0 0 26%;
    max-width: 26%;
  }

  .su__col-xl-5 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 41.666667%;
    flex: 0 0 41.666667%;
    max-width: 41.666667%;
  }

  .su__col-xl-6 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 50%;
    flex: 0 0 50%;
    max-width: 50%;
  }

  .su__col-xl-7 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 58.333333%;
    flex: 0 0 58.333333%;
    max-width: 58.333333%;
  }

  .su__col-xl-8 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 74%;
    flex: 0 0 74%;
    max-width: 74%;
  }

  .su__col-xl-9 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 76.5%;
    flex: 0 0 76.5%;
    max-width: 76.5%;
  }

  .su__col-xl-10 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 83.333333%;
    flex: 0 0 83.333333%;
    max-width: 83.333333%;
  }

  .su__col-xl-11 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 91.666667%;
    flex: 0 0 91.666667%;
    max-width: 91.666667%;
  }

  .su__col-xl-12 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 100%;
    flex: 0 0 100%;
    max-width: 100%;
  }

  .su__order-xl-first {
    -webkit-box-ordinal-group: 0;
    -ms-flex-order: -1;
    order: -1;
  }

  .su__order-xl-last {
    -webkit-box-ordinal-group: 14;
    -ms-flex-order: 13;
    order: 13;
  }

  .su__order-xl-0 {
    -webkit-box-ordinal-group: 1;
    -ms-flex-order: 0;
    order: 0;
  }

  .su__order-xl-1 {
    -webkit-box-ordinal-group: 2;
    -ms-flex-order: 1;
    order: 1;
  }

  .su__order-xl-2 {
    -webkit-box-ordinal-group: 3;
    -ms-flex-order: 2;
    order: 2;
  }

  .su__order-xl-3 {
    -webkit-box-ordinal-group: 4;
    -ms-flex-order: 3;
    order: 3;
  }

  .su__offset-xl-0 {
    margin-left: 0;
  }

  .su__offset-xl-1 {
    margin-left: 8.333333%;
  }

  .su__offset-xl-2 {
    margin-left: 16.666667%;
  }

  .su__offset-xl-3 {
    margin-left: 25%;
  }
}

.su__d-none {
  display: none;
}

.su__d-inline {
  display: inline;
}

.su__d-inline-block {
  display: inline-block;
}

.su__d-block {
  display: block;
}

.su__d-table {
  display: table;
}

.su__d-flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

.su__d-inline-flex {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
}

.su__flex-vcenter {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.su__flex-hcenter {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
}

.su__flex-start {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
}
.su__attachment_icon {
  fill: none;
  stroke: rgb(51, 51, 51);
  stroke-linecap: round;
  stroke-linejoin: round;
  stroke-width: 1;
}
.su__attachment-name {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  max-width: 230px;
}
.su__attachment-name:hover {
  text-decoration: underline;
  letter-spacing: 0;
  color: var(--su__searchapp-deepskyblue-light);
  opacity: 1;
}
.su__attachment_type {
  text-align: left;
  letter-spacing: 0;
  color: var(--su__searchapp-darkgray);
  opacity: 1;
  background: var(--su__searchapp-white);
  width: max-content;
  padding: 0 3px;
  border-radius: 2px;
}
.su__attachment-content {
  border: 1px solid var(--su__searchapp-gray);
  padding: 15px;
  color: var(--su__searchapp-gray);
  font-size: 13px;
  font-family: 'Montserrat';
  overflow-y: auto;
  overflow-x: auto;
  max-height: 540px;
  width: 100%;
  border-radius: 5px;
}
.su__attachment-content > * {
  font-weight: normal;
  font-size: 13px;
  line-height: 20px;
  letter-spacing: 0;
  color: var(--su__searchapp-gray);
}
.su__close-button {
  position: relative;
  width: 22px;
  height: 22px;
}
.su__close-button button {
  position: absolute;
  top: -10px;
  right: -16px;
}
.su__attachment_preview {
  max-width: 748px;
  max-height: 597px;
  width: 70vw;
}

@media (min-width: 320px) {
  .su__d-xs-none {
    display: none;
  }

  .su__d-xs-inline-block {
    display: inline-block;
  }

  .su__d-xs-block {
    display: block;
  }
}

@media (min-width: 576px) {
  .su__d-sm-none {
    display: none;
  }

  .su__d-sm-inline {
    display: inline;
  }

  .su__d-sm-inline-block {
    display: inline-block;
  }

  .su__d-sm-block {
    display: block;
  }

  .su__d-sm-table {
    display: table;
  }

  .su__d-sm-flex {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
  }

  .su__d-sm-inline-flex {
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
  }
}

@media (min-width: 768px) {
  .su__d-md-none {
    display: none;
  }

  .su__d-md-inline {
    display: inline;
  }

  .su__d-md-inline-block {
    display: inline-block;
  }

  .su__d-md-block {
    display: block;
  }

  .su__d-md-table {
    display: table;
  }

  .su__d-md-flex {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
  }

  .su__d-md-inline-flex {
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
  }
}

@media (min-width: 992px) {
  .su__d-lg-none {
    display: none;
  }

  .su__d-lg-inline {
    display: inline;
  }

  .su__d-lg-inline-block {
    display: inline-block;
  }

  .su__d-lg-block {
    display: block;
  }

  .su__d-lg-table {
    display: table;
  }

  .su__d-lg-flex {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
  }

  .su__d-lg-inline-flex {
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
  }
}

@media (min-width: 1200px) {
  .su__d-xl-none {
    display: none;
  }

  .su__d-xl-inline {
    display: inline;
  }

  .su__d-xl-inline-block {
    display: inline-block;
  }

  .su__d-xl-block {
    display: block;
  }

  .su__d-xl-table {
    display: table;
  }

  .su__d-xl-flex {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
  }

  .su__d-xl-inline-flex {
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
  }
}

@media (max-width: 768px) {
  .su__feedback-modal {
    width: calc(100% - 40px);
    margin: auto;
  }
  .su__attachment-content {
    height: 501px;
  }
  .su__attachment_preview {
    max-height: 590px;
    max-width: 350px;
  }
  .su__close-button button {
    top: -2px;
    right: -8px;
  }
  a.su__preview-json-title {
    margin-top: 8px;
  }
}

@media (min-width: 478px) and (max-width: 768px) {
  .su__mr_sort {
    margin-right: 5px;
  }
}

@media (max-width: 478px) {
  .su__mr_sort {
    margin-right: 0;
  }

  .su__dropdowns-gaps {
    gap: 10px;
  }
}

@media (max-width: 498px) {
  .su__flex_start {
    align-items: flex-start;
  }
}

.su__flex-row {
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
}

.su__flex-column {
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
}

.su__flex-row-reverse {
  -webkit-box-orient: horizontal;
  -webkit-box-direction: reverse;
  -ms-flex-direction: row-reverse;
  flex-direction: row-reverse;
}

.su__flex-column-reverse {
  -webkit-box-orient: vertical;
  -webkit-box-direction: reverse;
  -ms-flex-direction: column-reverse;
  flex-direction: column-reverse;
}

.su__flex-wrap {
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.su__flex-nowrap {
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
}

.su__flex-wrap-reverse {
  -ms-flex-wrap: wrap-reverse;
  flex-wrap: wrap-reverse;
}

.su__justify-content-start {
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.su__justify-content-end {
  -webkit-box-pack: end;
  -ms-flex-pack: end;
  justify-content: flex-end;
}

.su__justify-content-center {
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
}

.su__justify-content-between {
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
}

.su__justify-content-around {
  -ms-flex-pack: distribute;
  justify-content: space-around;
}

.su__align_preview {
  z-index: 9999999999;
}

.su__align-items-end {
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: flex-end;
}

.su__align-items-center {
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.su__align-items-baseline {
  -webkit-box-align: baseline;
  -ms-flex-align: baseline;
  align-items: baseline;
}

.su__align-items-stretch {
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
}

.su__align-content-start {
  -ms-flex-line-pack: start;
  align-content: flex-start;
}

.su__align-content-end {
  -ms-flex-line-pack: end;
  align-content: flex-end;
}

.su__align-content-center {
  -ms-flex-line-pack: center;
  align-content: center;
}

.su__align-content-between {
  -ms-flex-line-pack: justify;
  align-content: space-between;
}

.su__align-content-around {
  -ms-flex-line-pack: distribute;
  align-content: space-around;
}

.su__align-content-stretch {
  -ms-flex-line-pack: stretch;
  align-content: stretch;
}

.su__align-self-auto {
  -ms-flex-item-align: auto;
  align-self: auto;
}

.su__align-self-start {
  -ms-flex-item-align: start;
  align-self: flex-start;
}

.su__align-self-end {
  -ms-flex-item-align: end;
  align-self: flex-end;
}

.su__align-self-center {
  -ms-flex-item-align: center;
  align-self: center;
}

.su__align-self-baseline {
  -ms-flex-item-align: baseline;
  align-self: baseline;
}

.su__align-self-stretch {
  -ms-flex-item-align: stretch;
  align-self: stretch;
}

@media (min-width: 576px) {
  .su__text-sm-left {
    text-align: left;
  }

  .su__text-sm-right {
    text-align: right;
  }

  .su__text-sm-center {
    text-align: center;
  }
}

@media (min-width: 768px) {
  .su__text-md-left {
    text-align: left;
  }

  .su__text-md-right {
    text-align: right;
  }

  .su__text-md-center {
    text-align: center;
  }

  .su__merge-options-active {
    background: var(--su__searchapp-silver-54) 0% 0% no-repeat padding-box !important;
  }
}

@media (min-width: 992px) {
  .su__text-lg-left {
    text-align: left;
  }

  .su__text-lg-right {
    text-align: right;
  }

  .su__text-lg-center {
    text-align: center;
  }
}

@media (min-width: 1200px) {
  .su__text-xl-left {
    text-align: left;
  }

  .su__text-xl-right {
    text-align: right;
  }

  .su__text-xl-center {
    text-align: center;
  }
}

@-webkit-keyframes zoomIn {
  from {
    opacity: 0;
    -webkit-transform: scale3d(0.3, 0.3, 0.3);
    transform: scale3d(0.3, 0.3, 0.3);
  }

  50% {
    opacity: 1;
  }
}

@keyframes zoomIn {
  from {
    opacity: 0;
    -webkit-transform: scale3d(0.3, 0.3, 0.3);
    transform: scale3d(0.3, 0.3, 0.3);
  }

  50% {
    opacity: 1;
  }
}

@-webkit-keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translate3d(0, -100%, 0);
    -webkit-transform: translate3d(0, -100%, 0);
    -moz-transform: translate3d(0, -100%, 0);
    -ms-transform: translate3d(0, -100%, 0);
    -o-transform: translate3d(0, -100%, 0);
  }

  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
    -webkit-transform: translate3d(0, 0, 0);
    -moz-transform: translate3d(0, 0, 0);
    -ms-transform: translate3d(0, 0, 0);
    -o-transform: translate3d(0, 0, 0);
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translate3d(0, -100%, 0);
    -webkit-transform: translate3d(0, -100%, 0);
    -moz-transform: translate3d(0, -100%, 0);
    -ms-transform: translate3d(0, -100%, 0);
    -o-transform: translate3d(0, -100%, 0);
  }

  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
    -webkit-transform: translate3d(0, 0, 0);
    -moz-transform: translate3d(0, 0, 0);
    -ms-transform: translate3d(0, 0, 0);
    -o-transform: translate3d(0, 0, 0);
  }
}

@-webkit-keyframes fadeInRight {
  from {
    opacity: 0;
    -webkit-transform: translate3d(100%, 0, 0);
    transform: translate3d(100%, 0, 0);
  }

  to {
    opacity: 1;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    -webkit-transform: translate3d(100%, 0, 0);
    transform: translate3d(100%, 0, 0);
  }

  to {
    opacity: 1;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}

@-webkit-keyframes fadeInLeft {
  from {
    opacity: 0;
    -webkit-transform: translate3d(-100%, 0, 0);
    transform: translate3d(-100%, 0, 0);
  }

  to {
    opacity: 1;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    -webkit-transform: translate3d(-100%, 0, 0);
    transform: translate3d(-100%, 0, 0);
  }

  to {
    opacity: 1;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}

@-webkit-keyframes spin {
  from {
    -webkit-transform: rotate(0deg);
  }

  to {
    -webkit-transform: rotate(360deg);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

@-webkit-keyframes slideInUp {
  from {
    -webkit-transform: translate3d(0, 100%, 0);
    transform: translate3d(0, 100%, 0);
    visibility: visible;
  }

  to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}

@keyframes slideInUp {
  from {
    -webkit-transform: translate3d(0, 100%, 0);
    transform: translate3d(0, 100%, 0);
    visibility: visible;
  }

  to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}

@-webkit-keyframes slideInDown {
  from {
    -webkit-transform: translate3d(0, -100%, 0);
    transform: translate3d(0, -100%, 0);
    visibility: visible;
  }

  to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}

@keyframes slideInDown {
  from {
    -webkit-transform: translate3d(0, -100%, 0);
    transform: translate3d(0, -100%, 0);
    visibility: visible;
  }

  to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}

@-webkit-keyframes slideSpaceInDown {
  0% {
    opacity: 0;
    -webkit-transform-origin: 50% 100%;
    transform-origin: 50% 100%;
    transform: scale(0.2) translate(0%, 200%);
    -webkit-transform: scale(0.2) translate(0%, 200%);
    -moz-transform: scale(0.2) translate(0%, 200%);
    -ms-transform: scale(0.2) translate(0%, 200%);
    -o-transform: scale(0.2) translate(0%, 200%);
  }

  100% {
    opacity: 1;
    -webkit-transform-origin: 50% 100%;
    transform-origin: 50% 100%;
    transform: scale(1) translate(0%, 0%);
    -webkit-transform: scale(1) translate(0%, 0%);
    -moz-transform: scale(1) translate(0%, 0%);
    -ms-transform: scale(1) translate(0%, 0%);
    -o-transform: scale(1) translate(0%, 0%);
  }
}

@keyframes slideSpaceInDown {
  0% {
    opacity: 0;
    -webkit-transform-origin: 50% 100%;
    transform-origin: 50% 100%;
    transform: scale(0.2) translate(0%, 200%);
    -webkit-transform: scale(0.2) translate(0%, 200%);
    -moz-transform: scale(0.2) translate(0%, 200%);
    -ms-transform: scale(0.2) translate(0%, 200%);
    -o-transform: scale(0.2) translate(0%, 200%);
  }

  100% {
    opacity: 1;
    -webkit-transform-origin: 50% 100%;
    transform-origin: 50% 100%;
    transform: scale(1) translate(0%, 0%);
    -webkit-transform: scale(1) translate(0%, 0%);
    -moz-transform: scale(1) translate(0%, 0%);
    -ms-transform: scale(1) translate(0%, 0%);
    -o-transform: scale(1) translate(0%, 0%);
  }
}

@-webkit-keyframes fadeIntooltip {
  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

@keyframes fadeIntooltip {
  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

@-webkit-keyframes fadeOuttooltip {
  0% {
    opacity: 1;
  }

  100% {
    opacity: 0;
  }
}

@keyframes fadeOuttooltip {
  0% {
    opacity: 1;
  }

  100% {
    opacity: 0;
  }
}

@-webkit-keyframes cubeIn {
  0% {
    opacity: 0.3;
    transform: rotateY(90deg);
  }

  50% {
    animation-timing-function: ease-out;
    transform: translateZ(-200px) rotateY(45deg);
  }
}

@keyframes cubeIn {
  0% {
    opacity: 0.3;
    transform: rotateY(90deg);
  }

  50% {
    animation-timing-function: ease-out;
    transform: translateZ(-200px) rotateY(45deg);
  }
}

@keyframes tada {
  from {
    transform: scale3d(1, 1, 1);
    -webkit-transform: scale3d(1, 1, 1);
    -moz-transform: scale3d(1, 1, 1);
    -ms-transform: scale3d(1, 1, 1);
    -o-transform: scale3d(1, 1, 1);
  }

  10%,
  20% {
    transform: scale3d(0.9, 0.9, 0.9) rotate3d(0, 0, 1, -3deg);
    -webkit-transform: scale3d(0.9, 0.9, 0.9) rotate3d(0, 0, 1, -3deg);
    -moz-transform: scale3d(0.9, 0.9, 0.9) rotate3d(0, 0, 1, -3deg);
    -ms-transform: scale3d(0.9, 0.9, 0.9) rotate3d(0, 0, 1, -3deg);
    -o-transform: scale3d(0.9, 0.9, 0.9) rotate3d(0, 0, 1, -3deg);
  }

  30%,
  50%,
  70%,
  90% {
    transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg);
    -webkit-transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg);
    -moz-transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg);
    -ms-transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg);
    -o-transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg);
  }

  40%,
  60%,
  80% {
    transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, -3deg);
  }

  to {
    transform: scale3d(1, 1, 1);
  }
}

.su__animate-zoom {
  -webkit-animation: zoomin 400ms 200ms both;
  animation: zoomIn 400ms 200ms both;
}

.su__animate-fadown {
  -webkit-animation: fadeindown 300ms both;
  animation: fadeInDown 300ms both;
}

.su__animate-fadeRight {
  -webkit-animation: fadeinright 300ms both;
  animation: fadeInRight 300ms both;
}

.su__animate-fadeLeft {
  -webkit-animation: fadeinleft 300ms both;
  animation: fadeInLeft 300ms both;
}

.su__animate-slideUp {
  -webkit-animation: slideinup 600ms 100ms both;
  animation: slideInUp 600ms 100ms both;
}

.su__animate-slideDown {
  -webkit-animation: slideindown 600ms 100ms both;
  animation: slideInDown 600ms 100ms both;
}

.su__animate-slideSpDown {
  animation: slideSpaceInDown 800ms both;
  -webkit-animation: slidespaceindown 800ms both;
}

.su__minscroller::-webkit-scrollbar {
  width: 4px;
  height: 0;
  background-color: var(--su__searchapp-lightgray-14);
}

.su__minscroller::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 6px rgba(var(--su__searchapp-black-rgba), 0.3);
  box-shadow: inset 0 0 6px rgba(var(--su__searchapp-black-rgba), 0.3);
  background-color: var(--su__searchapp-lightgray-14);
  border-radius: 10px;
  -webkit-border-radius: 10px;
  -moz-border-radius: 10px;
  -ms-border-radius: 10px;
  -o-border-radius: 10px;
}

.su__minscroller::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background: var(--su__searchapp-gray);
  -webkit-border-radius: 10px;
  -moz-border-radius: 10px;
  -ms-border-radius: 10px;
  -o-border-radius: 10px;
}

.su__minscroller {
  scrollbar-width: thin;
  scrollbar-color: var(--su__searchapp-gray) transparent;
}

.su__hideScroller {
  scrollbar-width: none;
}

.su__app {
  background: var(--su__searchapp-lightgray-14);
  display: inline-block;
  width: 100%;
  position: relative;
}

.su__content-view {
  list-style: none;
}

.su__input-search {
  background: var(--su__searchapp-lightgray-14);
  padding: 0 80px 0 1.64px;
  height: 42px;
  box-shadow: 0 1px 3px 0 rgba(var(--su__searchapp-black-rgba), 0.2),
    0 1px 1px 0 rgba(var(--su__searchapp-black-rgba), 0.14),
    0 2px 1px -1px rgba(var(--su__searchapp-black-rgba), 0.12);
  font-family: 'Montserrat', sans-serif;
}

.su__input-search:focus:not(:focus-visible) {
  outline: 1px solid transparent;
}

.su__input-search-box {
  background: var(--su__searchapp-lightgray-14);
  padding: 0 48px 0 40px;
  height: 42px;
  font-family: 'Montserrat', sans-serif;
}

.su__input-search-box:focus:not(:focus-visible) {
  outline: 1px solid transparent;
}

.su__search_btn {
  right: 0;
  padding: 9px 10px 5px;
  height: 100%;
}

.su__search_box_btn {
  padding: 9px 10px 5px;
  height: 100%;
}

.su__suggestions-box {
  height: auto;
  max-height: 300px;
  overflow-x: hidden;
}

.su__suggestions-box-height {
  max-height: 416px;
}

.su__suggestions-list {
  padding: 6px 15px;
  line-height: 30px;
  height: auto;
  font-size: 12px;
  color: var(--su__searchapp-darkgray);
}

.su__suggestions-box::before,
.su__no-suggestions::before {
  content: '';
  left: 0;
  right: 0;
  bottom: 10px;
  position: absolute;
  top: 0;
  background: var(--su__searchapp-white);
  z-index: -1;
}

.su__autoSuggestion {
  border-radius: 0 0 10px 10px;
}

.su__autoSuggestion-border {
  border-radius: 6px;
  overflow: hidden;
}

.highlight {
  color: var(--su__searchapp-deepskyblue-light);
}

.su__list-item-desc .highlight {
  color: rgba(var(--su__searchapp-black-rgba), 0.87);
  border-bottom: 1px solid var(--su__searchapp-deepskyblue-light);
}

.metaResults {
  display: inline-block;
  font-size: 12px;
}

.su__tags {
  padding: 3px 6px;
  background-color: var(--su__searchapp-lightgray-14);
}

.facet-results {
  float: left;
  width: 20%;
}

.content-results {
  float: right;
  width: 80%;
}

.su__filters-button {
  border: 1px solid var(--su__searchapp-lightgrey);
  margin: 0 16px 0 0;
}

.su__align-filters-menu {
  top: 90%;
  left: 20px;
}

.su__language-filter {
  display: none;
}

.su__appearance-none {
  -moz-appearance: none;
  -webkit-appearance: none;
  appearance: none;
}

.su__select-control {
  -moz-appearance: none;
  padding-right: 15px;
  -webkit-appearance: none;
  appearance: none;
  background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' height='24' viewBox='0 0 24 24' width='24'%3E%3Cpath d='M0 0h24v24H0V0z' fill='none'/%3E%3Cpath d='M8.71 11.71l2.59 2.59c.39.39 1.02.39 1.41 0l2.59-2.59c.63-.63.18-1.71-.71-1.71H9.41c-.89 0-1.33 1.08-.7 1.71z' fill='%2357575c'/%3E%3C/svg%3E")
    no-repeat 100% 50%;
  background-position: 97%;
  background-repeat: no-repeat;
  background-size: 14px;
  background-origin: border-box;
}

.su__tabs {
  color: var(--su__searchapp-gray);
  position: relative;
  text-align: center;
  font-size: 14px;
  line-height: 24px;
  padding-top: 8px;
  padding-bottom: 8px;
  padding-right: 24px;
  padding-left: 24px;
  transition: background-color 0.35s cubic-bezier(0.35, 0, 0.25, 1);
  display: inline-block;
  cursor: pointer;
  height: 40px;
  vertical-align: top;
}

.su__pt-new {
  padding-top: 17px;
}

.su__height_57px {
  height: 57px;
}

.su__height_40px {
  height: 40px;
}

.filter-label {
  color: var(--su__searchapp-deepskyblue-light);
  cursor: pointer;
}

.su__toggle-input {
  margin: 0;
}

.label-badge {
  padding: 2px 10px;
  border-radius: 20px;
}

.su__arrow-up,
.su__arrow-left,
.su__arrow-down,
.su__arrow-right {
  position: relative;
  display: inline-block;
  width: 8px;
  height: 8px;
  border-top: 2px solid var(--su__searchapp-darkgray);
  border-right: 2px solid var(--su__searchapp-darkgray);
  right: 6px;
  -moz-transform: rotate(135deg);
  -webkit-transform: rotate(135deg);
  transform: rotate(135deg);
  border-left: none;
  border-bottom: none;
  cursor: pointer;
  -ms-transform: rotate(135deg);
  -o-transform: rotate(135deg);
  -webkit-transition: all 0.5s;
  -moz-transition: all 0.5s;
  -ms-transition: all 0.5s;
  -o-transition: all 0.5s;
  transition: all 0.5s;
}

.su__nested-arrow {
  display: flex;
  width: 18px;
  margin: auto auto auto 4px;
  height: 22px;
  align-items: center;
}

.su__nested-arrow .su__arrow-right {
  top: -1px;
  left: 0;
}

.su__nested-arrow .su__arrow-down {
  top: -1px;
  left: 1px;
}

.su__nested-filter-row {
  display: block;
}

.su__href-txt {
  padding: 8px 0 0 0;
}

.su__filter-checkbox {
  width: 18px;
  height: 18px;
  margin-right: 15px;
  margin-left: 5px;
}

.su__filter-toggle input[type='checkbox'] + label::before {
  content: '';
  display: block;
  position: absolute;
  width: 18px;
  height: 18px;
  top: 5px;
  left: 0;
  border: 1px solid var(--su__searchapp-gray);
  border-radius: 3px;
  background-color: var(--su__searchapp-white);
  box-sizing: border-box;
}

.su__filter-toggle input[type='checkbox']:checked + label::after {
  content: '';
  display: block;
  width: 4px;
  height: 8px;
  border: solid var(--su__searchapp-white);
  border-width: 0 2.5px 2.5px 0;
  -webkit-transform: rotate(37deg);
  -ms-transform: rotate(37deg);
  transform: rotate(37deg);
  position: absolute;
  top: 7px;
  left: 6px;
  overflow: hidden;
  box-sizing: unset;
}

.su__filter-toggle input[type='checkbox']:checked + label::before {
  content: '';
  background: var(--su__searchapp-deepskyblue-light);
  border-color: var(--su__searchapp-deepskyblue-light);
}

.su__search-facet-drop .su__filter-toggle input[type='checkbox'] + label::before {
  left: 6px;
  top: auto;
}

.su__search-facet-drop .su__filter-toggle input[type='checkbox'] + label::after {
  top: 10px;
  left: 11px;
}

.su__filter-toggle input[type='radio'] + label::before {
  content: '';
  display: block;
  position: absolute;
  width: 16px;
  height: 16px;
  top: 3px;
  left: 0;
  border: 1px solid var(--su__searchapp-gray);
  background-color: var(--su__searchapp-white);
  box-sizing: border-box;
  border-radius: 50%;
}

.su__filter-toggle input[type='radio']:checked + label::after {
  content: '';
  display: block;
  border: solid var(--su__searchapp-white);
  border-width: 0 2.5px 2.5px 0;
  -webkit-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  transform: rotate(45deg);
  position: absolute;
  width: 3px;
  height: 7px;
  top: 6px;
  left: 5px;
  box-sizing: unset;
}

.su__filter-toggle input[type='radio']:checked + label::before {
  content: '';
  background: var(--su__searchapp-deepskyblue-light);
  border-color: var(--su__searchapp-deepskyblue-light);
}

.su__search-facet-drop .su__filter-toggle input[type='radio'] + label::before {
  left: 6px;
}

.su__search-facet-drop .su__filter-toggle input[type='radio'] + label::after {
  top: 11px;
  left: 11px;
}

.su__active-tab {
  color: var(--su__searchapp-black) !important;
  border-bottom: 3px solid var(--su__searchapp-deepskyblue-light) !important;
  font-weight: 600;
  z-index: 1;
}

.su__featured-thumbnail {
  width: auto;
  max-width: 195px;
  min-width: 175px;
}

.su__img-featured {
  width: 100%;
  max-width: 100%;
}

.su__merged-icon {
  position: relative;
}

.su__wrapper ul {
  list-style: none;
}

.su__nested-ul {
  margin: 0 0 0 -8px;
  padding-left: 40px;
}

.su__nested-ul .su__nested-ul {
  padding-left: 32px;
  margin-left: 0;
}

.su__list-items {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
}

.su__media-body {
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

.su__showmore-icon {
  opacity: 1;
  top: 0;
  transition: 0.5s;
  cursor: default;
}

.su__showless-icon {
  opacity: 1;
  top: 0;
  transition: 0.5s;
  cursor: default;
}

.su__showmore-text:hover .su__showmore-icon {
  opacity: 0;
  left: 90px;
}

.su__showmore-text:hover .su__showless-icon {
  opacity: 0;
  left: 70px;
}
.loading_overlay-true {
  content: '';
  top: auto;
  left: 0;
  position: absolute;
  width: 100%;
  height: 100%;
  opacity: 0.5;
  z-index: 1000;
  background-color: var(--su__searchapp-white);
}

#loading::before {
  content: '';
  top: auto;
  left: 0;
  position: absolute;
  width: 100%;
  height: 100%;
  opacity: 0.5;
  z-index: 1000;
  background-color: var(--su__searchapp-white);
}

.su__facet-loading::before {
  content: '';
  top: auto;
  left: 0;
  position: absolute;
  width: 100%;
  height: 100%;
  opacity: 0.5;
  z-index: 1000;
  background-color: var(--su__searchapp-white);
}

.su__switch-view {
  min-width: 55px;
  max-width: 65px;
}

.su_thumb-no {
  min-width: 55px;
  max-width: 65px;
}

.su__view-active svg rect {
  fill: var(--su__searchapp-deepskyblue-light);
}

.su__suggestions-icon svg {
  position: relative;
  top: -2px;
}

.su__input-close {
  right: 25px;
  top: 13px;
  display: flex;
  height: 19px;
  align-items: center;
}

.su__input-cross {
  right: 0;
  z-index: 9;
}

.su__close-facet .su__filter-content-row,
.swapFilterLeft .su__searchTips {
  display: none;
}

.open-filter {
  top: -2px;
  -webkit-transition: all 0.4s;
  -o-transition: all 0.4s;
  transition: all 0.4s;
  -webkit-transform: rotate(0deg);
  -ms-transform: rotate(0deg);
  transform: rotate(0deg);
}

.close-filter {
  top: -3px;
  -webkit-transition: all 0.4s;
  -o-transition: all 0.4s;
  transition: all 0.4s;
  -webkit-transform: rotate(0deg);
  -ms-transform: rotate(0deg);
  transform: rotate(0deg);
}

.su__show-more-bg .su__arrow-down-gpt-align {
  top: 1px;
}

.su__arrow-down {
  -webkit-transition: all 0.4s;
  -o-transition: all 0.4s;
  transition: all 0.4s;
  -webkit-transform: rotate(135deg);
  -ms-transform: rotate(135deg);
  transform: rotate(135deg);
  left: 0;
  top: -1px;
}

.su__arrow-down.top-n-3px {
  top: -3px;
}

.su__knowledgeGraph {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 35%;
  flex: 0 0 35%;
  max-width: 35%;
}

.su__sm-fullwidth {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 100%;
  flex: 0 0 100%;
  max-width: 100%;
}

.su__sm-width-65 {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 100%;
  flex: 0 0 65%;
  max-width: 65%;
  width: 65%;
}

.su__knowledge-feedback {
  width: 25px;
  height: 25px;
  margin: 9px 0 1px 0;
}

.su__knowledge-boxShadow {
  -webkit-box-shadow: 0 0 6px rgba(var(--su__searchapp-black-rgba), 0.16);
  -moz-box-shadow: 0 0 6px rgba(var(--su__searchapp-black-rgba), 0.16);
  box-shadow: 0 0 6px rgba(var(--su__searchapp-black-rgba), 0.16);
}

.ft-green-tick,
.su_w-20p {
  width: 20px;
  height: 20px;
  top: 0;
  right: 0;
  left: 0;
}

.su__snippets {
  box-shadow: 0 1px 3px 0 rgba(var(--su__searchapp-black-rgba), 0.14);
}

.su__ribbon-block {
  display: block;
}

.su__ribbon-text {
  min-width: 100px;
  padding: 1px 5px;
  color: var(--su__searchapp-darkgray);
  font-weight: 500;
}

.su__p-3 {
  padding: 16px;
}

.su__pagination {
  display: -webkit-box !important;
  display: -ms-flexbox !important;
  display: flex !important;
  padding-left: 0;
  list-style: none;
  border-radius: 4px;
}

.su__pagination span.disabled {
  background: transparent;
}

.su__pagination span span,
.su__pagination span button {
  position: relative;
  display: block;
  padding: 10px 12px 9px;
  border-radius: 4px;
  margin-left: -1px;
  line-height: 10px;
  text-decoration: none;
  margin-right: 7px;
  font-size: 18px;
  text-transform: capitalize;
}

.su__pagination span span:hover,
.su__pagination span button:hover {
  color: var(--su__searchapp-deepskyblue-light);
  border-color: var(--su__searchapp-lightgrey);
}

.su__pagination span span:focus {
  z-index: 2;
  outline: 0;
}

.su__pagination span button:focus {
  z-index: 2;
  outline: 1;
}

.su__pagination span span:not(:disabled):not(.disabled),
.su__pagination span button:not(:disabled):not(.disabled) {
  cursor: pointer;
}

.su__pagination span:first-child span {
  margin-left: 0;
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
}

.su__pagination span:last-child span {
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
}

.su__pagination span span.su__pagination-active {
  color: var(--su__searchapp-white);
  background-color: var(--su__searchapp-deepskyblue-light);
  border-color: var(--su__searchapp-deepskyblue-light);
}

.su__pagination span button.su__pagination-active {
  font-weight: 500;
}

.su__pagination span span,
.su__pagination span button[disabled] {
  color: var(--su__searchapp-darkgray);
  pointer-events: none;
  cursor: auto;
}

.su__pagination-lg span {
  padding: 12px 24px;
  font-size: 20px;
  line-height: 1.5;
}

.su__pagination-lg .su__pagination span:first-child span {
  border-top-left-radius: 0.48px;
  border-bottom-left-radius: 0.48px;
}

.su__pagination-lg .su__pagination span:last-child span {
  border-top-right-radius: 0.48px;
  border-bottom-right-radius: 0.48px;
}

.su__pagination-sm span {
  padding: 4px 8px;
  font-size: 14px;
  line-height: 1.5;
}

.su__pagination-sm .su__pagination span:first-child span {
  border-top-left-radius: 3px;
  border-bottom-left-radius: 3px;
}

.su__pagination-sm .su__pagination span:last-child span {
  border-top-right-radius: 3px;
  border-bottom-right-radius: 3px;
}

.su__grid-content .su__list-items {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 49%;
  flex: 0 0 49%;
  max-width: calc(50% - 10px);
}

.su__grid-content .su__list-item-img,
.su__grid-content .su__grid-none {
  display: none;
}

.su__grid-content .su__list-item-row {
  flex-wrap: wrap;
}

.su__grid-content .su__img-featured {
  width: 150px;
  max-width: 275px;
}

.su__grid-content .su__list-item-text,
.su__grid-content .su__featured-thumbnail {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 100%;
  flex: 0 0 100%;
  max-width: 100%;
}

.su__grid-content .su__featured-thumbnail {
  margin: 0 0 15px;
}

.su__grid-content .su__ribbon-text,
.su__ribbon-text {
  min-width: 100px;
  padding: 1px 5px;
  color: var(--su__searchapp-darkgray);
  font-weight: 500;
}

.su__grid-content .su__ribbon-grid {
  display: inline-block;
}

.su__ribbon-title {
  padding-top: 1px;
  padding-bottom: 1px;
  line-height: 16px;
  overflow: hidden;
  text-overflow: ellipsis;
}

.su__grid-content .su__ribbon-list {
  display: none;
}

.su__grid-content .su__list-items .su__list-item-title {
  margin-top: 2px;
  flex-direction: column;
  align-items: stretch;
}

.su__grid-content .su__meta-date {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 100%;
  flex: 0 0 100%;
  max-width: 100%;
}

.su__grid-content,
.su__flex_box {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

.su__sticky__head {
  float: left;
  padding: 1px 5px 0 0;
}

.su__sticky__filters {
  min-width: 30px;
  padding: 0 5px;
  color: var(--su__searchapp-darkgray);
  float: left;
  white-space: normal;
  align-items: center;
  position: relative;
  display: flex;
  height: 28px;
  background: var(--su__searchapp-white) 0% 0% no-repeat padding-box;
  border: 1px solid var(--su__searchapp-lightgrey);
}

.su__sticky__filters:hover {
  box-shadow: 0 0 2px 0 var(--su__searchapp-lightgrey);
}

.su__sticky__filters__ellipses {
  white-space: nowrap;
  width: 99%;
  overflow: hidden;
  text-overflow: ellipsis;
  display: inline-block;
  padding-right: 3px;
}

.su__cancel__sticky {
  padding: 1px 4px 1px 4px;
  margin: 0 -3px 0 2px;
  float: right;
  color: var(--su__searchapp-gray);
  font-weight: 400;
  height: 100%;
  border-left: 1px solid var(--su__searchapp-lightgrey);
}

.searchTips-sidenav {
  -webkit-box-shadow: 0 2px 4px -1px rgba(var(--su__searchapp-black-rgba), 0.2),
    0 4px 5px 0 rgba(var(--su__searchapp-black-rgba), 0.14),
    0 1px 10px 0 rgba(var(--su__searchapp-black-rgba), 0.12);
  box-shadow: 0 2px 4px -1px rgba(var(--su__searchapp-black-rgba), 0.2),
    0 4px 5px 0 rgba(var(--su__searchapp-black-rgba), 0.14),
    0 1px 10px 0 rgba(var(--su__searchapp-black-rgba), 0.12);
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  overflow: auto;
  max-width: 480px;
  z-index: 9999999999;
  border-radius: 8px;
  max-height: 480px;
  height: 480px;
}

.su__searchTip-icon {
  right: 8px;
  top: 4px;
  padding: 5px;
}

.su__editClient {
  width: calc(86vw);
  height: 85vh;
  max-width: 896px;
  max-height: 430px;
  overflow: hidden auto;
  -webkit-animation: fadeindown 400ms both;
  animation: fadeInDown 400ms both;
  padding: 15px;
}

.su__full-screen {
  width: 100%;
  height: 100%;
}

.su__touch-scroll-control {
  user-select: none;
  touch-action: none;
  pointer-events: none;
  overscroll-behavior-y: none;
}

.su__no-overflow {
  overflow: hidden;
}

.sortable {
  max-height: 320px;
}

.su__sticky__top {
  border: none !important;
  padding: 0 !important;
  margin: 2px 0 2px !important;
  display: inline-flex;
}

.resultSection {
  padding: 7px 3px;
  border: 1px solid var(--su__searchapp-lightgray-14);
}

.su__border_skyblue {
  border: 1px solid var(--su__searchapp-deepskyblue-light);
}

.su-customize {
  float: right;
  width: 70%;
}

.su-display {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
}

.draggableList {
  padding: 8px 5px 8px 0;
  cursor: -webkit-grab;
  cursor: grab;
  color: var(--su__searchapp-darkgray);
}

.su__border_radius-3 {
  border-radius: 3px;
}

.su__bg-overlay {
  content: '';
  position: fixed;
  background: rgba(var(--su__searchapp-black-rgba), 0.48);
  z-index: -1;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
}

.su__overlay {
  z-index: 1;
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(var(--su__searchapp-black-rgba), 0.5);
}

.su__overlay-transparent {
  z-index: 1;
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  background-color: transparent;
}
.su__overlay.su__overlayz-9 {
  z-index: 9;
}
.su__modal-inner {
  width: 480px;
  max-width: 100%;
  background-color: var(--su__searchapp-white);
}

.su__side-Search-tips {
  right: 0;
  bottom: 50%;
  border-top-left-radius: 10px;
  border-bottom-left-radius: 10px;
  z-index: 999;
}

.draggableFilters {
  width: 30%;
}

.initialTab {
  color: var(--su__searchapp-deepskyblue-light);
  font-weight: 600;
}

.su__bookmark-save:disabled {
  background-image: -webkit-gradient(
    linear,
    right top,
    left top,
    from(var(--su__searchapp-lightgrey)),
    to(var(--su__searchapp-lightgrey))
  );
  background-image: -o-linear-gradient(
    right,
    var(--su__searchapp-lightgrey),
    var(--su__searchapp-lightgrey)
  );
  background-image: linear-gradient(
    to left,
    var(--su__searchapp-lightgrey),
    var(--su__searchapp-lightgrey)
  );
  color: var(--su__searchapp-white);
  cursor: default;
  padding: 9px 145px;
}

.su__bookmark-save:enabled {
  background-image: -webkit-gradient(
    linear,
    right top,
    left top,
    from(var(--su__searchapp-lightgrey)),
    to(var(--su__searchapp-lightgrey))
  );
  background-image: -o-linear-gradient(
    right,
    var(--su__searchapp-lightgrey),
    var(--su__searchapp-lightgrey)
  );
  background-image: linear-gradient(
    to left,
    var(--su__searchapp-lightgrey),
    var(--su__searchapp-lightgrey)
  );
  color: var(--su__searchapp-white);
  padding: 9px 145px;
}

.su__bookmark-svg-align {
  top: 8px;
  left: 7px;
  right: 8px;
}

.su__bookmark-svg-align-console {
  top: 6px;
  left: 6px;
  right: 8px;
}

.su__bookmark-active {
  background: var(--su__searchapp-deepskyblue-light);
  padding: 9px 197px;
}

.su__bookmark-ul {
  margin: 0;
  height: 260px;
  overflow-y: auto;
  margin-bottom: 0;
}

.su__back-to-top {
  bottom: 0;
  right: 10px;
  left: auto;
}

.su__btn-back-to-top svg {
  top: 6px;
}

.su__tooltip {
  position: absolute;
  top: -30px;
  padding: 4px 4px;
  font-size: 12px;
  border-radius: 2px;
  background: var(--su__searchapp-white) 0% 0% no-repeat padding-box;
  -webkit-box-shadow: 0 3px 6px rgba(var(--su__searchapp-black-rgba), 0.24);
  box-shadow: 0 3px 6px rgba(var(--su__searchapp-black-rgba), 0.24);
  -webkit-animation: fadeintooltip ease-in-out 0.5s;
  animation: fadeIntooltip ease-in-out 0.5s;
}

.su__tooltip-top {
  left: 50%;
  -webkit-transform: translateX(-50%);
  -ms-transform: translateX(-50%);
  transform: translateX(-50%);
}

.su__tooltip-bottom {
  top: calc(100% + 5px);
  left: 50%;
  -webkit-transform: translateX(-50%);
  -ms-transform: translateX(-50%);
  transform: translateX(-50%);
}
.su__tooltip-bottom-right {
  top: calc(100% + 5px);
  left: 0%;
}

.su__tooltip-left {
  top: 50%;
  right: calc(100% + 10px);
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
}

.su__tooltip-right {
  top: 50%;
  left: calc(100% + 10px);
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
}

.su__w-savebook {
  width: 25px;
}

.su__with-overlay + .su__bg-overlay {
  display: block;
  z-index: 9;
}

.su__mob-icon {
  height: 24px;
  cursor: pointer;
}

.su__mob-active svg .su__active-path {
  fill: var(--su__searchapp-deepskyblue-light);
}

.su__mob-active .su__active-text {
  color: var(--su__searchapp-deepskyblue-light);
}

.su__filter-active .su__left-sidebar {
  padding-bottom: 40px;
}

.su__knowledgeGraph-show .su__content-view {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 63%;
  flex: 0 0 63%;
  max-width: 63%;
}

.su__knowledgeGraph-show .su__knowledgeGraph-block {
  margin: 4px 16px 16px 16px;
  -webkit-box-flex: 0;
  -ms-flex: 0 0 36%;
  flex: 0 0 36%;
  max-width: 36%;
}

.su__w-155px {
  width: 155px;
}

.su__eye {
  padding: 0;
}

.su__side-customized-gear {
  right: -22px;
  bottom: calc(50% - 44px);
  border-top-left-radius: 7px;
  border-bottom-left-radius: 7px;
  padding: 10px;
}

.su__customized__settings {
  flex-direction: column;
  z-index: 999;
  background: var(--su__searchapp-white);
  width: 0;
  box-shadow: 0 3px var(--su__searchapp-lightgray-14);
  border: 1px solid var(--su__searchapp-lightgrey);
  display: none;
}

.su__customized__settings-inner {
  display: flex;
  align-items: center;
  height: 36px;
}

.su__customized_switch {
  position: relative;
  display: inline-block;
  width: 32px;
  height: 16px;
}

.su__customized_switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.su__customized_switch_slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--su__searchapp-lightgrey);
  -webkit-transition: 0.4s;
  transition: 0.4s;
}

.su__customized_switch_slider::before {
  position: absolute;
  content: '';
  height: 9px;
  width: 10px;
  left: 4px;
  bottom: 4px;
  background-color: var(--su__searchapp-white);
  -webkit-transition: 0.4s;
  transition: 0.4s;
}

.su__customized_switch input:checked + .su__customized_switch_slider {
  background-color: var(--su__searchapp-deepskyblue-light);
}

.su__customized_switch input:focus + .su__customized_switch_slider {
  box-shadow: 0 0 1px var(--su__searchapp-deepskyblue-light);
}

.su__customized_switch input:checked + .su__customized_switch_slider::before {
  -webkit-transform: translateX(13px);
  -ms-transform: translateX(13px);
  transform: translateX(13px);
  background-color: var(--su__searchapp-white);
}

.su__customized_switch_slider.round {
  border-radius: 34px;
}

.su__customized_switch_slider.round::before {
  border-radius: 50%;
}

.su__gear-min-width {
  min-width: 30px;
}

.su__tooltip-gear .su__tooltip {
  word-wrap: break-word;
  white-space: normal;
  right: 24px;
}

.su-customize-hr {
  height: 0.1px;
  width: 155px;
  margin-top: 8px;
  margin-bottom: 8px;
  margin-right: 32px;
  border-style: ridge;
}

.su__hide-gear {
  right: 178px;
}

.su__show-gear {
  width: 200px;
  display: block;
}

/* The below line is commented to stop the animation of the settings gear icon in search client */

/* #gear .gear-icon{animation-name: gear-rotation;animation-duration: 15.5s;animation-iteration-count: infinite;transform-origin: 50% 50%;display: inline-block;-webkit-animation: gear-rotation 3s linear infinite;-moz-animation: gear-rotation 3s linear infinite;-ms-animation: gear-rotation 3s linear infinite;-o-animation: gear-rotation 3s linear infinite;animation: gear-rotation 3s linear infinite;} */
@keyframes gear-rotation {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.su__product-sugt-row {
  position: absolute;
  width: 100%;
  left: 0;
  right: 0;
  overflow-y: auto;
  max-height: 250px;
}

.su__product-length-1 {
  bottom: -88px;
}

.su__product-length-2 {
  bottom: -176px;
}

.su__product-length-3 {
  bottom: -249px;
}

.su__product-length-4 {
  bottom: -352px;
}

.su__product-length-5 {
  bottom: -441px;
}

.su__product-suggestion {
  background: var(--su__searchapp-white);
  padding: 12px 15px;
  line-height: normal;
  height: auto;
  font-size: 11px;
  color: var(--su__searchapp-darkgray);
  border: 1px dotted var(--su__searchapp-gray-44);
}

.su__fill-red {
  fill: var(--su__searchapp-deepskyblue-light);
}

.su__product-sugg-category {
  background: var(--su__searchapp-white);
  border-radius: 4px;
  border: 1px solid var(--su__searchapp-gray);
  padding: 4px 15px;
  white-space: nowrap;
  margin-right: 8px;
}

.su__product-sugg-category.su__product-viewmore {
  padding-left: 22px;
}

.su__product-sugg-category.su__product-active {
  background: var(--su__searchapp-deepskyblue-light);
  color: var(--su__searchapp-white);
  border-color: var(--su__searchapp-deepskyblue-light);
}

.su__col-6.custom-metadata-width {
  padding: 0;
}

.su__fp_indicator {
  border: 2px solid var(--su__searchapp-deepskyblue-light);
}

.su__fp_indicator:hover {
  background-position-y: 10px;
}

.su__fp_indicator .disabled {
  pointer-events: none;
}

.su__rtl .su__side-customized-gear {
  right: auto;
  left: -22px;
  border-top-right-radius: 7px;
  border-bottom-right-radius: 7px;
}

.su__rtl .su-customize-hr {
  margin-right: auto;
  margin-left: 32px;
}

.su__rtl .su__hide-gear {
  left: 178px !important;
  right: auto !important;
}

.su__side-customized-gear-all-cs {
  right: -22px;
  left: auto;
  bottom: calc(50% - 10px);
  border-top-left-radius: 7px;
  border-bottom-left-radius: 7px;
  padding: 10px;
}

.su__rtl .su__side-customized-gear-all-cs {
  left: -22px;
  right: auto;
  bottom: calc(50% - 10px);
  border-top-left-radius: 7px;
  border-bottom-left-radius: 7px;
  padding: 10px;
}

.su__px-sm-0 {
  padding-right: 0;
  padding-left: 0;
}

@media (max-width: 767px) {
  .footerSection {
    text-align: center;
  }

  .su__my-sm-1 {
    margin-top: 5px;
    margin-bottom: 5px;
  }

  .su__mx-sm-1 {
    margin-right: 5px;
    margin-left: 5px;
  }
  .su__mt-sm-1 {
    margin-top: 5px;
  }

  .su__mb-sm-1 {
    margin-bottom: 5px;
  }

  .su__my-sm-0 {
    margin-top: 0;
    margin-bottom: 0;
  }

  .su__mx-sm-0 {
    margin-right: 0;
    margin-left: 0;
  }

  .su__mt-sm-0 {
    margin-top: 0;
  }

  .su__mb-sm-0 {
    margin-bottom: 0;
  }

  .su__m-sm-0 {
    padding: 0;
  }

  .su__px-sm-1 {
    padding-right: 5px;
    padding-left: 5px;
  }

  .su__py-sm-1 {
    padding-top: 5px;
    padding-bottom: 5px;
  }

  .su__pt-sm-1 {
    padding-top: 5px;
  }

  .su__p-sm-0 {
    padding: 0;
  }

  .su__pb-sm-1 {
    padding-bottom: 5px;
  }

  .su__px-sm-0 {
    padding-right: 0;
    padding-left: 0;
  }

  .su__py-sm-0 {
    padding-top: 0;
    padding-bottom: 0;
  }

  .su__pt-sm-0 {
    padding-top: 0;
  }

  .su__pb-sm-0 {
    padding-bottom: 0;
  }

  .su__pb-sm-4 {
    padding-bottom: 2.4em;
  }

  .su__form-control {
    padding: 0.475rem 0.55rem;
    height: 36px;
  }

  .su__px-sm-2 {
    padding-right: 0.5rem;
    padding-left: 0.5rem;
  }

  .su__py-sm-2 {
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
  }

  .su__advance-block .su__mob-search-iner {
    display: none;
  }

  .su__gutters {
    width: 100vw;
  }

  .su__grid-content .su__list-items:nth-child(odd),
  .su__grid-content .su__list-items:nth-child(even) {
    margin: 10px 0;
  }

  .su__grid-content .su__list-items {
    margin: 0 15px 15px 0;
  }

  .su__wsm-100 {
    width: 100%;
  }

  .su__w-sm-100,
  .su__knowledgeGraph-show .su__content-view,
  .su__knowledgeGraph-show .su__knowledgeGraph-block {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 100%;
    flex: 0 0 100%;
    max-width: 100%;
  }

  .su__knowledgeGraph-show .su__grid-content .su__list-items,
  .su__grid-content .su__list-items {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 48%;
    flex: 0 0 48.5%;
    max-width: 48.5%;
  }

  .su__knowledgeGraph-show .su__grid-content .su__list-items:nth-child(odd),
  .su__grid-content .su__list-items:nth-child(odd) {
    margin-right: 15px;
  }

  .su__knowledgeGraph-show .su__grid-content .su__list-items:nth-child,
  .su__grid-content .su__list-items:nth-child(even) {
    margin-right: 0;
  }

  .su__knowledgeGraph-show .su__knowledgeGraph-block {
    margin: 16px 0;
  }

  .su__result-per_page_btn {
    font-size: 14px;
  }

  .su__tooltip {
    z-index: 1;
  }

  .su__center-gpt-widget pre {
    padding: 15px !important;
  }
}

@media (max-width: 576px) {
  .su__result-per_page_btn {
    padding-top: 0;
    padding-bottom: 0;
    line-height: 17px;
    font-size: 14px;
  }

  .su__xs-w-100 {
    max-width: 100%;
    width: 100%;
  }

  .su__px-xs-2 {
    padding-right: 8px;
    padding-left: 8px;
  }

  .su__py-xs-2 {
    padding-top: 8px;
    padding-bottom: 8px;
  }

  .su__gutters {
    width: 100%;
    margin-right: auto;
    margin-left: auto;
  }

  .su__grid-content .su__list-items {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 100%;
    flex: 0 0 100%;
    max-width: 100%;
    margin: 0 15px 15px 0;
  }

  .su__knowledgeGraph-show .su__grid-content .su__list-items {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 100%;
    flex: 0 0 100%;
    max-width: 100%;
  }

  .su__knowledgeGraph-show .su__grid-content .su__list-items:nth-child(odd),
  .su__knowledgeGraph-show .su__grid-content .su__list-items:nth-child(even) {
    margin: 10px 0;
  }

  .su__knowledgeGraph-show .su__knowledgeGraph-block {
    margin: 8px 0 16px;
  }

  .su__pagination span span,
  .su__pagination span button {
    padding: 3px 8px;
  }

  .su__fsfeedback {
    justify-content: center;
  }
}

.su__search-facet {
  width: 100%;
}

.su__search-facet-input:not(:focus-visible) {
  outline: none;
  border: 2px solid var(--su__searchapp-deepskyblue-light);
  border-radius: 6px;
  box-shadow: 0 3px 3px 0 rgba(var(--su__searchapp-cornflowerblue-light-rgba), 0.14);
}

.su__search-facet-input {
  width: 100%;
  padding: 5px 24px 7px 34px;
  font-size: 14px !important;
}

.su__search-facet .su__search-facet-input {
  padding: 6px 24px 7px 24px !important;
  height: inherit !important;
  line-height: normal !important;
}

.su__search-facet-icon {
  margin: 6px 0;
  padding-left: 4px;
  padding-bottom: 1px;
}

.su__rtl .su__search-facet-icon {
  margin: 3px 0 3px 0;
  padding-right: 4px;
}

.su__search-head-icon {
  width: 24px;
  height: 24px;
  border-radius: 6px;
  margin-left: auto;
  box-shadow: 0 3px 3px 0 rgba(var(--su__searchapp-cornflowerblue-light-rgba), 0.14);
  background-color: var(--su__searchapp-deepskyblue-light);
}

.su__search-head-icon > svg {
  width: 25px;
  height: 24px;
  padding: 2px;
}

.su__active-search-hide {
  display: none;
}

.su__facet-main-block {
  position: absolute;
  left: 14px;
  right: 0;
  bottom: 10px;
  width: calc(100% - 28px);
}

.su__search-facet-input:active,
.su__search-facet-input:focus {
  border-color: var(--su__searchapp-deepskyblue-light);
}

.su__search-facet-items {
  background: var(--su__searchapp-white);
  border: 1px solid var(--su__searchapp-lightgrey);
  border-bottom: none;
  border-top: none;
  left: 0;
  right: 0;
}

.su__search-facet-items .su__search_facet-list {
  padding: 7px 6px;
  cursor: pointer;
}

.su__search-facet-items .su__search_facet-list:hover {
  background-color: var(--su__searchapp-lightgrey);
}

.su__search-facet-empty,
.su__search-facet-empty:focus {
  border: 2px solid var(--su__searchapp-red) !important;
  background-color: rgba(var(--su__searchapp-red-rgba), 0.09);
}

.su__search-facet-drop {
  position: absolute;
  width: 99%;
  overflow-y: auto;
  max-height: 240px;
  top: 10px;
  z-index: 22;
  background: var(--su__searchapp-white) 0% 0% no-repeat padding-box;
  box-shadow: 0 3px 6px rgba(var(--su__searchapp-black-rgba), 0.24);
  border-radius: 4px;
  left: 1px;
}

.su__facet-close-icon {
  right: 5px;
  top: 6px;
}

.su__search-facet > div {
  animation: cubeIn 300ms ease-out both;
}

.su__facet-main-block .su__toggle-label::before,
.su__facet-main-block .su__toggle-label::after {
  width: 15px;
  height: 15px;
}

.su__search-facet-drop::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background-color: var(--su__searchapp-gray-standard);
  -webkit-border-radius: 10px;
  -moz-border-radius: 10px;
  -ms-border-radius: 10px;
  -o-border-radius: 10px;
  background-image: none;
}

.su__search-facet-drop::-webkit-scrollbar {
  width: 4px;
  height: 0;
  background-color: var(--su__searchapp-white);
}

.su__see-more-less {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  padding: 1px;
}

.su__show-all .su__arrow-down {
  right: 0;
  border-top: 2px solid var(--su__searchapp-deepskyblue-light);
  border-right: 2px solid var(--su__searchapp-deepskyblue-light);
}

.su__show_all_align {
  margin-left: 10px;
}

.su__show-less .su__arrow-up {
  top: 2px;
  border-top: 2px solid var(--su__searchapp-deepskyblue-light);
  border-right: 2px solid var(--su__searchapp-deepskyblue-light);
}

.su__arrow-up {
  -moz-transform: rotate(-45deg);
  -webkit-transform: rotate(-45deg);
  transform: rotate(-45deg);
  -ms-transform: rotate(-45deg);
  -o-transform: rotate(-45deg);
  left: 5px;
  top: 6px;
  position: relative;
  display: inline-block;
  width: 8px;
  height: 8px;
}

.su__width-100 {
  width: 100%;
}

.su__search-facet-drop::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 6px rgba(var(--su__searchapp-black-rgba), 0.3);
  box-shadow: inset 0 0 6px rgba(var(--su__searchapp-black-rgba), 0.3);
  background-color: var(--su__searchapp-white);
  border-radius: 10px;
  -webkit-border-radius: 10px;
  -moz-border-radius: 10px;
  -ms-border-radius: 10px;
  -o-border-radius: 10px;
}

.su__pl-custom {
  padding-left: 5px;
}

.su__rtl {
  direction: rtl;
}

.su__ltr {
  direction: ltr;
}

.su__no-rtl {
  direction: ltr;
}

.su__rtl .su__rtlleft {
  right: auto;
  left: 0;
}

.su__rtl .su__rtlright {
  left: auto;
  right: 0;
}

.su__rtl .su__rtlright-auto {
  right: auto;
}

.su__rtl .su__rtlleft-auto {
  left: auto;
}

.su__rtl .su__select-control {
  background-position: 3%;
  padding-right: 5px;
  padding-left: 15px;
}

.su__rtl .su__input-close {
  right: auto;
  left: 46px;
  top: 13px;
}

.su__rtl .su__input-cross {
  left: 16px;
}

.su__grid-content .su__list-items:nth-child(even) {
  margin-left: 0;
}

.su__rtl .su__searchTip-icon {
  right: auto;
  left: 8px;
}

.su__rtl .su__back-to-top,
.su__rtl .su__facet-close-icon {
  left: 10px;
  right: auto;
}

.su__rtl .su__lang-icons {
  left: auto;
  right: 11px;
}

.su__rtl .su__search-facet-input {
  padding: 5px 34px 7px 24px;
}

.su__rtl .su__select-control.su__select-opt {
  padding-right: 24px;
  background: none;
}

.su__rtl .su__side-Search-tips {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-top-right-radius: 10px;
  border-bottom-right-radius: 10px;
}

.su__sort-filter.su__close-facet {
  display: none;
}

.su__filters-sort-active {
  color: var(--su__searchapp-deepskyblue-light);
  font-weight: 400;
}

.su__rtl .su__filter-toggle input[type='checkbox'] + label::before,
.su__rtl .su__filter-toggle input[type='radio'] + label::before {
  left: auto;
  right: 0;
}

.su__rtl .su__filter-toggle input[type='checkbox']:checked + label::after,
.su__rtl .su__filter-toggle input[type='radio']:checked + label::after {
  right: 5px;
}

.su__rtl .su__search-facet-drop .su__filter-toggle input[type='checkbox'] + label::before {
  left: auto;
  right: 6px;
}

.su__rtl .su__search-facet-drop .su__filter-toggle input[type='checkbox'] + label::after {
  left: auto;
  right: 11px;
}

.su__rtl .su__input-search {
  padding: 0 1.64px 0 80px;
}

.su__rtl .su__input-search-box {
  padding: 0 40 0 80px;
}

.su__rtl .su__LanguageLabel {
  background-position: right 5px center;
}

.su__rtl .su__cancel__sticky {
  padding: 1px 3px 1px 1px;
  border-right: 1px solid var(--su__searchapp-lightgrey);
  border-left: none;
  margin: 0 2px 0 -3px;
}

.su__rtl .su__sticky__filters span {
  padding: 3px 5px 3px 5px;
}

.su__rtl .su__sticky__head {
  padding: 1px 0 0 3px;
}

.su__rtl .su__facet-main-block {
  left: auto;
  right: 16px;
  bottom: 10px;
  width: calc(100% - 33px);
}

.su__rtl .su__search-head-icon {
  margin-left: unset;
}

@-webkit-keyframes colorchange {
  0% {
    background: var(--su__searchapp-lightgrey);
  }

  50% {
    background: var(--su__searchapp-lightgray-14);
  }

  100% {
    background: var(--su__searchapp-lightgrey);
  }
}

@keyframes colorchange {
  0% {
    background: var(--su__searchapp-lightgrey);
  }

  50% {
    background: var(--su__searchapp-lightgray-14);
  }

  100% {
    background: var(--su__searchapp-lightgrey);
  }
}

.su__loading1 .su__loading-view {
  position: relative;
  user-select: none;
  pointer-events: none;
}

.su__loading1 .su__loading-view::after {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  pointer-events: none;
  color: transparent;
  transition: all 0.5s ease-out;
  animation: colorchange 10s infinite;
  -webkit-animation: colorchange 10s infinite;
  cursor: wait;
  user-select: none;
  z-index: 1;
}

.su__text-bold {
  font-weight: bold;
}

.su__searchTip-list li {
  line-height: 22px;
  margin-bottom: 15px;
  position: relative;
  list-style-type: none;
  color: var(--su__searchapp-black);
}

.resultsPerPage {
  border: 1px solid var(--su__searchapp-lightgrey);
  background-color: var(--su__searchapp-white);
}

.pageSize {
  top: 40px;
  right: 0;
}

.resultsPerPage .su__arrow-down {
  bottom: 2px;
  right: 2px;
  left: 0;
  top: -3px;
}

.su__viewed-results a:visited,
.su__viewed-results a:visited h2,
.su__viewed-results a:visited .highlight {
  color: var(--su__searchapp-violet) !important;
}

.su-sourceLabel {
  max-width: calc(100% - 90px);
}

.su__iframe-modal {
  max-width: 788px;
  width: 90%;
  max-height: 542px;
  min-width: 788px;
  min-height: 520px;
  padding: 20px;
}

.su__iframe-modal .su__bookmark-inner {
  height: 440px;
}

.su__animate-fadeInRight {
  animation: fadeInRight 400ms ease-out 100ms both;
}

.su__preview-contentLabel {
  background: var(--su__searchapp-white) 0% 0% no-repeat padding-box;
  border: 1px solid var(--su__searchapp-lightgrey);
  border-radius: 5px;
  opacity: 1;
  display: block;
}

.su__f-regular {
  font-weight: 500;
}

.su__multiple-versions-dropdown {
  background: var(--su__searchapp-white) 0% 0% no-repeat padding-box;
  box-shadow: 0 0 8px rgba(var(--su__searchapp-black-rgba), 0.3);
  border-radius: 5px;
  border: unset;
  max-height: 120px;
  overflow: auto;
  top: 25px;
  z-index: 99;
  width: 100%;
  max-width: 140px;
}

.su__grid-content .su__grid-preview {
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: flex-end;
}

.su__list-inner-text {
  width: calc(100% - 175px);
}

.su__list-item-text {
  width: calc(100% - 230px);
}

.su__custom-grey-color {
  color: var(--su__searchapp-darkgray);
}

.su__w-135px {
  width: 135px;
}

.su__grid-content .su__multi-gridview {
  display: block;
}

.su__multiple-versions > a {
  padding: 5px 6px;
  display: block;
}

.su__active-Filter-color .su__arrow-position {
  top: -1px;
}

.su__grid-content .su__preview-list-block {
  display: none;
}

.su__preview-block-content:hover .su_preview-icons {
  fill: var(--su__searchapp-deepskyblue-light);
}

.su__preview-block-content:hover .su__text-preview {
  color: var(--su__searchapp-deepskyblue-light);
}

.su__parent-version {
  color: var(--su__searchapp-lightblue-alt);
  padding: 1px 5px;
  height: auto;
  background: var(--su__searchapp-lightgray-14) 0% 0% no-repeat padding-box;
  border: 1px solid var(--su__searchapp-deepskyblue-light);
  border-radius: 5px;
  max-width: 140px;
}

.su__grid-content .su__list-inner-text {
  width: calc(100%);
}

.su__multi-gridview {
  position: absolute;
  right: 15px;
  margin-top: -22px;
  text-align: right;
}

.su__content-view.list .su__multiple-versions-grid {
  display: none;
}

#auto .su__search_btn {
  height: 100%;
  left: calc(100% - 54px);
  padding: 0 13px;
}

#auto .su__search_box_btn {
  height: 100%;
  left: calc(100% - 54px);
  padding: 0 13px;
}

#auto .su__search_btn svg {
  visibility: visible;
}

#auto .su__search_box_btn svg {
  visibility: visible;
}

.su__suggesticon {
  position: relative;
  display: flex;
  align-items: center;
  margin-left: 7px;
}

.su__animate-fadow {
  transition: all 0.5s;
}

.su__feedback-modal.thxOpen {
  display: none;
}

.su__filter-appear {
  display: none;
}

button.su__feedback-btn[disabled] {
  cursor: default;
}

@media (max-width: 991px) {
  .su__multi-gridview {
    display: none;
  }

  .su__grid-content .su__list-inner-text .su__list-item-desc {
    margin-bottom: 14px;
  }
}

@media (min-width: 767px) {
  .su__display-none {
    display: none !important;
  }
}

.su__no_bookmark_min_height {
  min-height: 429px;
}

.su__no_bookmark_padding {
  padding: 0 76px 86px 76px;
}

.su__pos_relative {
  position: relative;
}

.su__pos_absolute {
  position: absolute;
}

.su__overflow-y_unset {
  overflow-y: unset;
}

.su__overflow_hidden {
  overflow: hidden;
  height: 260px;
}

.su__emoji_border {
  width: 41px;
  height: 41px;
  border: 1px solid var(--su__searchapp-lightgrey);
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 35px;
}

.su__rtl .su__emoji_border:first-child {
  margin-right: 0;
}

@media (max-width: 767px) {
  .su__display-none-767px {
    display: none !important;
  }

  .su__grid-content .su__multi-gridview {
    display: none;
  }

  .su__m-lang-block {
    padding: 5px 15px;
  }

  .su__sm-w-100,
  .su__list-item-text {
    width: 100%;
    max-width: 100%;
  }

  .su__sm-h-100 {
    height: 100%;
    max-height: 100%;
  }

  .su__iframe-modal {
    right: 0;
    top: 0;
    bottom: 0;
  }

  .su__iframe-height {
    min-height: 250px;
  }

  .su__mobile-child-block {
    width: calc(100% / 5);
    padding: 0 2px;
  }

  .su__content-gutter {
    display: block;
    width: 100%;
  }

  .su__mob-advance-text {
    width: auto;
  }

  .su__grid-content .su__img-featured {
    width: 100%;
  }

  .swapFilterLeft {
    padding-bottom: 15px;
  }

  .su__skip-link {
    font-size: 12px;
    padding: 9px 15px;
  }

  /************** Center align the react GPT widget starts ***************/

  .su__noResult_container {
    padding: 0;
  }

  .su__noresult_text_color {
    font-size: 12px;
  }

  .su__loader_snippet {
    margin: 0 !important;
  }

  .loader-text {
    font-size: 12px;
    margin-left: 6px;
  }

  .skeleton-box:last-child {
    margin-bottom: 0;
  }

  .skeleton-box {
    height: 10px;
  }

  .article-links {
    flex-direction: column;
    padding-left: 0;
    margin-top: 2px;
  }

  .article-links .links-list li {
    padding-left: 0;
  }

  .article-links > span {
    font-size: 10px;
    font-weight: 500;
  }

  .article-links .links-list a {
    padding: 4px 10px;
    font-size: 10px;
  }

  .article-links::before {
    margin-top: 0;
    width: 100%;
    left: 0;
  }

  .article-links button span {
    font-size: 10px;
  }

  .retry-section p {
    line-height: 16px;
    margin-right: 5px;
  }

  .retry-section {
    min-height: auto;
  }

  .su__sugpt_heading {
    font-size: 16px !important;
  }
}

.su__rtl .su__star-yellow:first-child {
  margin-right: 0;
}

.su__rtl .su__star-gray:first-child {
  margin-right: 0;
}

.su__bookmark-active:disabled {
  background: var(--su__searchapp-lightblue-alt);
  pointer-events: none;
  padding: 9px 197px;
}

.su__no_bookmark_svg {
  padding: 45px 0 30px 0;
}

.su__cancel {
  font-size: 18px;
}

.su__new-padding {
  font-size: 13px;
  letter-spacing: 0;
  color: var(--su__searchapp-darkgray);
  opacity: 1;
}

@media (max-width: 480px) {
  .su__search-view {
    white-space: normal;
  }

  .su__lang-icons {
    top: 5px;
  }
}

@media (max-width: 450px) {
  .su__flex_dir_column_mb {
    flex-direction: column;
  }
}

@media (max-width: 375px) {
  .su__pagination span span,
  .su__pagination span button {
    padding: 0.48px 0.64px;
    margin-right: 5px;
  }

  .su__gpt_svg {
    transform: scale(0.7) translate(-25px, -17px);
  }

  .su__rtl .su__gpt_svg {
    transform: scale(0.7) translate(45px, -21px);
  }
}

@media (max-width: 360px) {
  .su__flex_dir_col_mb {
    flex-direction: column;
  }

  .su__align-flex-mb {
    align-items: flex-start;
  }
}
@media (max-width: 345px) {
  .su__gpt-pop-up-mb {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  .su_new-btn.su__second-btn {
    margin-left: 18px;
  }
}

@media (max-width: 319px) {
  .su__results_container {
    width: 300px !important;
  }

  .su__pagination_mobile {
    overflow-x: auto;
    display: flex;
  }
}

.su__margin-right-auto {
  margin-right: auto;
}

.su__container ul {
  list-style: none;
  margin: 0;
}
.su__container .su__popup-summary ul {
  list-style: auto;
  padding: 0 0 0 15px;
}

.su__close-facet .su__arrow-down {
  -webkit-transition: all 0.4s;
  -o-transition: all 0.4s;
  transition: all 0.4s;
  -webkit-transform: rotate(135deg);
  -ms-transform: rotate(135deg);
  transform: rotate(135deg);
}

.su__select-opt.su__select-control {
  padding-right: 20px;
}

.su__results-preview {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: end;
  -ms-flex-pack: end;
  justify-content: flex-end;
}

.su__close-facet .su__multiversion-arrow .su__arrow-down {
  top: -3px;
  -webkit-transition: all 0.4s;
  -o-transition: all 0.4s;
  transition: all 0.4s;
  -webkit-transform: rotate(135deg);
  -ms-transform: rotate(135deg);
  transform: rotate(135deg);
  right: 0;
}
.su__close-facet .su__multiversion-arrow .su__arrow-down.su__versioning-console-align {
  top: -2px;
  margin-right: 2px;
}

.su__open-facet .su__multiversion-arrow .su__arrow-down {
  top: 0;
  -webkit-transition: all 0.4s;
  -o-transition: all 0.4s;
  transition: all 0.4s;
  -webkit-transform: rotate(-45deg);
  -ms-transform: rotate(-45deg);
  transform: rotate(-45deg);
  right: 0;
}

.su__multiversion-arrow {
  transform: translate(0, 1px);
}

.su__animate-tada {
  -webkit-animation: 1000ms both;
  animation: tada 1000ms both;
}

.su__linear-loader {
  position: absolute;
  margin-inline-start: 3px;
  width: calc(100% - 6px);
  bottom: 0;
}

.su__linear-loader-zendesk-full-width {
  z-index: 9;
  position: absolute;
  top: 26px;
  width: calc(100% - -113px);
  left: -2px;
}

.su__linear-loader-zendesk {
  z-index: 9;
  position: absolute;
  top: 26px;
}

.su___show-more-summary {
  cursor: pointer;
}

.su__input-search::-ms-clear {
  display: none;
}

.su__input-search-box::-ms-clear {
  display: none;
}

.su__autosuggestion-icon {
  display: flex;
}

.su__align_flex {
  display: flex;
}

.su__highlight_result {
  background-color: var(--su__searchapp-paleblue-cool);
  border-radius: 4px;
}

.su__highlight_filter_result {
  border: 2px solid var(--su__searchapp-paleblue-cool);
  background-color: var(--su__searchapp-paleblue-cool) !important;
}

.su__clearfix {
  content: '';
  display: table;
  clear: both;
}

.hover__effect:hover {
  background: rgba(var(--su__searchapp-black-rgba), 0.4);
}

.su__recent-icon {
  position: relative;
  top: 4px;
}

.su__auto-tuning {
  padding-left: 4px;
  cursor: pointer;
  padding-top: 2px;
  padding-right: 4px;
  margin: -18px 0 0 0;
}

.su_boosted-documents {
  margin-left: 4px;
}

.su__boosted-documents-positioning {
  position: relative;
}

.su__bookmarkListIconActive svg path {
  fill: var(--su__searchapp-deepskyblue-light);
}

.su__active-bookmark-list-icon svg path {
  fill: var(--su__searchapp-white);
}

.mob_bookmark_list-icon svg path {
  fill: var(--su__searchapp-black);
}

.su__cancel__sticky::before {
  content: '';
  position: absolute;
  top: 0;
  bottom: 0;
  right: 14px;
}

.su__mb__25 {
  margin-bottom: 25px;
}

.su__searchTip-list li::before {
  content: '\2022';
  position: absolute;
  left: -24px;
  font-weight: bold;
  font-size: 29px;
  top: -2px;
  color: var(--su__searchapp-lightgrey);
}

.su__indeterminate .su__nested__text {
  display: block;
  height: 2px;
  width: 13px;
  background: var(--su__searchapp-gray-standard);
  top: 13px;
  left: 2.5px;
  right: 0;
  z-index: 9;
}

.su__rtl .su__indeterminate .su__nested__text {
  right: 2.5px;
  left: 0;
}

.su__autocomplete-suggestion .su__suggesticon {
  top: 0;
}

.su__merged-icon .su__arrow-down {
  left: 0;
}

.su__bookmark-ul .su__filter-toggle input[type='checkbox'] + label::before {
  top: 0;
}

.su__bookmark-ul .su__filter-toggle input[type='checkbox']:checked + label::after {
  top: 2px;
}

.su__merged-icon > .su__arrow-right {
  top: 1px;
  left: 0;
}

.su__filter-checkboxs,
.su__toggle-input,
.su__filter-checkbox {
  opacity: 0;
}

.su__filter-label {
  visibility: visible;
}

.su__filter-label-hidden {
  visibility: hidden;
}

.open-filter .su__nested-arrow > .su__arrow-down {
  transform: rotate(46deg);
  top: -1px;
  left: 0;
}

.su__LanguageLabel {
  z-index: auto;
  height: 29px;
  background-position: 2px 5px;
  background-repeat: no-repeat;
  background-size: 20px;
  background-origin: border-box;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='20' height='20' viewBox='0 0 24 24'%3E%3Cpath d='M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zm6.93 6h-2.95c-.32-1.25-.78-2.45-1.38-3.56 1.84.63 3.37 1.91 4.33 3.56zM12 4.04c.83 1.2 1.48 2.53 1.91 3.96h-3.82c.43-1.43 1.08-2.76 1.91-3.96zM4.26 14C4.1 13.36 4 12.69 4 12s.1-1.36.26-2h3.38c-.08.66-.14 1.32-.14 2s.06 1.34.14 2H4.26zm.82 2h2.95c.32 1.25.78 2.45 1.38 3.56-1.84-.63-3.37-1.9-4.33-3.56zm2.95-8H5.08c.96-1.66 2.49-2.93 4.33-3.56C8.81 5.55 8.35 6.75 8.03 8zM12 19.96c-.83-1.2-1.48-2.53-1.91-3.96h3.82c-.43 1.43-1.08 2.76-1.91 3.96zM14.34 14H9.66c-.09-.66-.16-1.32-.16-2s.07-1.35.16-2h4.68c.09.65.16 1.32.16 2s-.07 1.34-.16 2zm.25 5.56c.6-1.11 1.06-2.31 1.38-3.56h2.95c-.96 1.65-2.49 2.93-4.33 3.56zM16.36 14c.08-.66.14-1.32.14-2s-.06-1.34-.14-2h3.38c.16.64.26 1.31.26 2s-.1 1.36-.26 2h-3.38z' fill='%2357575c'%3E%3C/path%3E%3C/svg%3E");
}

.su__bookmark-ul .su__filter-checkbox {
  opacity: 1;
}

/* only for ie11 css changes */
@media all and (-ms-high-contrast: none) {
  .su__select-control::-ms-expand {
    display: none;
  }

  .su__select-control {
    -webkit-appearance: none;
    appearance: none;
  }

  .su__lang-icons {
    top: 3px;
  }

  .su__wrapper .su__tooltip {
    display: inline;
  }

  .su__Language-boxs {
    width: 120px;
    margin: 0 10px;
  }

  .su__filter-checkboxs,
  .su__toggle-input,
  .su__filter-checkbox {
    visibility: hidden;
  }

  .su__switch-view-layout-2 {
    margin: 0 0 0 20px;
  }

  .su__fillter-sortby-layout-2 {
    margin: 0 65px 0 15px;
  }

  .su__autocomplete-suggestion .su__suggesticon {
    top: -2px;
  }

  .su__filters-button:focus,
  .su__key-focus:focus,
  .su__tabs:focus,
  .su__filter-toggle:focus {
    box-shadow: none;
  }

  .su__filter-toggle input[type='radio'] + label::before,
  .su__filter-toggle input[type='checkbox'] + label::before {
    top: 2px;
  }

  .su__filter-toggle input[type='radio']:checked + label::after {
    top: 4px;
    left: 6px;
  }

  .su__filter-toggle input[type='checkbox']:checked + label::after {
    top: 2px;
  }

  .su__nested-arrow .su__arrow-right {
    top: -1px;
    left: 0;
  }

  .swapFilterLeft .footerSection {
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }
}

/* only for safari css changes */
@media screen and (min-color-index: 0) and (-webkit-min-device-pixel-ratio: 0) {
  .su__lang-icons {
    top: 7px;
  }
}

#facets-section .facet div[id*='navigation'] .su__search-head-icon,
#facets-section .facet div[id*='nested'] .su__search-head-icon {
  display: none;
}

.su__inactive-type:hover {
  color: var(--su__searchapp-darkgray) !important;
}
.su__facet-preference .MuiPaper-root.MuiPaper-rounded.MuiMenu-paper {
  margin-top: 12px;
}

.su__merge-options .MuiList-root.MuiList-padding.MuiMenu-list {
  padding: 0;
}

.su__facet-preference .MuiList-root.MuiList-padding.MuiMenu-list {
  padding: 0;
}

.su__merge-options-item {
  font-family: 'Montserrat';
  display: flex;
  align-items: center;
  font-size: 13px;
  font-weight: 400;
  padding-left: 16px;
  padding-right: 16px;
  min-height: 34px;
  height: 34px;
  cursor: pointer;
  color: var(--su__searchapp-gray);
}

.su__merge-options-item:last-child {
  border-bottom: none;
}

.su__mergedFilter-CS-svg {
  width: fit-content;
  margin: 0;
  display: flex !important;
  text-transform: none;
}

.su__mergedFilter-CS-svg svg {
  height: 26px;
  margin-left: 10px;
}

.su-customize .initialTab .su__mergedFilter-CS-svg > svg .su__mergedFilter-CS-svg-path {
  fill: var(--su__searchapp-deepskyblue-light);
}

.su-customize
  .su__inactive-type:hover
  .su__mergedFilter-CS-svg
  > svg
  .su__mergedFilter-CS-svg-path {
  fill: var(--su__searchapp-darkgray) !important;
}

.su__active-tab .su__mergedFilter-CS-svg > svg .su__mergedFilter-CS-svg-path,
.su__inactive-type:hover .su__mergedFilter-CS-svg > svg .su__mergedFilter-CS-svg-path {
  fill: var(--su__searchapp-darkgray);
}

.su__mergedFilter-CS-tab .su__mergedFilter-CS-button {
  width: fit-content;
  padding: 0 6px;
  height: 26px;
  min-height: 26px;
  display: flex;
  align-content: center;
  align-items: center;
  margin: 0;
  line-height: 24px;
}

/* feedback css */
.su__close-svg {
  padding: 5px;
  width: 20px;
  height: 20px;
  right: 14px;
  top: 13px;
}

.su__thanks-closeIcon {
  right: 10px;
  top: 10px;
  padding: 0;
}

.su__close-svg-feedback {
  padding: 5px;
  right: 14px;
  top: 13px;
  height: 20px;
  width: 20px;
}

.su__feedtext-area {
  resize: none;
  border: 1px solid var(--su__searchapp-lightgrey);
  border-radius: 8px;
  padding: 6px 10px;
  font-family: 'Montserrat', sans-serif;
}

.su__feedtext-area:focus:not(:focus-visible),
.su__input-feedack:not(:focus-visible) {
  border: 1px solid var(--su__searchapp-lightgrey);
  outline: none;
}

.su__feedradio-row input[type='radio'] {
  display: none;
}

.su__feedradio-row input[type='radio'] + label {
  -webkit-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
}

.su__feed-labels {
  padding-left: 28px;
  padding-top: 4px;
}

.su__feedradio-row.su__feed-labels {
  padding: 9px 0 9px 0;
}

.su__feedradio-row input[type='radio'] + label::before,
.su__feedradio-row input[type='radio'] + label::after {
  content: '';
  position: absolute;
  top: 3px;
  left: 0;
  width: 18px;
  height: 18px;
  text-align: center;
  color: var(--su__searchapp-white);
  font-family: Times, serif;
  border-radius: 50%;
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
  border: 1px solid var(--su__searchapp-lightgrey);
}

.su__feedradio-row input[type='radio'] + label::before {
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
  box-shadow: inset 0 0 0 5px var(--su__searchapp-white),
    inset 0 0 0 10px var(--su__searchapp-white);
}

.su__feedradio-row input[type='radio'] + label:hover::before {
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
  box-shadow: inset 0 0 0 3px var(--su__searchapp-white),
    inset 0 0 0 10px var(--su__searchapp-gray-standard);
}

.su__feedradio-row input[type='radio']:checked + label::before {
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
  box-shadow: inset 0 0 0 3px var(--su__searchapp-white),
    inset 0 0 0 10px var(--su__searchapp-deepskyblue-light);
}

.su__feedradio-row input[type='radio']:checked + label::after {
  border: 1px solid var(--su__searchapp-deepskyblue-light);
}

.su__feed-rating .MuiBox-root {
  margin: 0;
  padding: 0;
}

.su__feed-rating {
  column-gap: 15px;
}

.su__feedback-searchsvg {
  right: 0;
  bottom: 100px;
}

.su__star,
.su__emoji {
  width: 44px;
  height: 44px;
  border-radius: 50%;
  background-repeat: no-repeat;
  background-position: center;
  background-size: 90%;
  transition: 0.3s;
}

.su__star_size {
  height: 50px;
  width: 48px;
}

.su__emoji {
  width: 25px;
  height: 25px;
  background-size: 100%;
}

.su__emoji:hover,
.su__star:hover {
  transform: scale(1.2);
}

.su__star-gray {
  background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI1MC40ODYiIGhlaWdodD0iNDguMzYxIiB2aWV3Qm94PSIwIDAgNTAuNDg2IDQ4LjM2MSI+CiAgPHBhdGggaWQ9IlBhdGhfMTgzNjUiIGRhdGEtbmFtZT0iUGF0aCAxODM2NSIgZD0iTTI1LjI0Myw2OTkuNzZhMS45NzIsMS45NzIsMCwwLDEsMS44LDEuMTJMMzIuOTMxLDcxMi44bC4zNTUuNzE5Ljc5My4xMTUsMTMuMTU5LDEuOTEyYTIuMDEsMi4wMSwwLDAsMSwxLjExNCwzLjQyOGwtOS41MjIsOS4yODItLjU3NC41NTkuMTM1Ljc5LDIuMjQ4LDEzLjEwNmExLjk2MiwxLjk2MiwwLDAsMS0uNDMxLDEuNjIzLDIuMDU3LDIuMDU3LDAsMCwxLTEuNTUuNzM0LDIsMiwwLDAsMS0uOTM2LS4yMzlsLTExLjc3LTYuMTg4LS43MDktLjM3My0uNzA5LjM3My0xMS43Nyw2LjE4OGEyLDIsMCwwLDEtLjkzNi4yMzksMi4wNTgsMi4wNTgsMCwwLDEtMS41NS0uNzM0LDEuOTYyLDEuOTYyLDAsMCwxLS40MzEtMS42MjNMMTIuMSw3MjkuNjA5bC4xMzUtLjc5LS41NzQtLjU1OS05LjUyMi05LjI4MmEyLjAxLDIuMDEsMCwwLDEsMS4xMTQtMy40MjhsMTMuMTU5LTEuOTEyLjc5My0uMTE1LjM1NS0uNzE5LDUuODg1LTExLjkyNGExLjk3MiwxLjk3MiwwLDAsMSwxLjgtMS4xMm0wLTEuNTI0YTMuNDkyLDMuNDkyLDAsMCwwLTMuMTY5LDEuOTdMMTYuMTksNzEyLjEzLDMuMDMsNzE0LjA0MmEzLjUzNCwzLjUzNCwwLDAsMC0xLjk1OCw2LjAyOGw5LjUyMiw5LjI4Mkw4LjM0Niw3NDIuNDU4YTMuNTI0LDMuNTI0LDAsMCwwLDUuMTI3LDMuNzI1TDI1LjI0Myw3NDBsMTEuNzcsNi4xODhhMy41MjQsMy41MjQsMCwwLDAsNS4xMjctMy43MjVsLTIuMjQ4LTEzLjEwNiw5LjUyMi05LjI4MmEzLjUzNCwzLjUzNCwwLDAsMC0xLjk1OC02LjAyOEwzNC4zLDcxMi4xM2wtNS44ODUtMTEuOTI0YTMuNDkyLDMuNDkyLDAsMCwwLTMuMTY5LTEuOTciIHRyYW5zZm9ybT0idHJhbnNsYXRlKDAgLTY5OC4yMzYpIiBmaWxsPSIjNTc1NzVjIi8+Cjwvc3ZnPgo=');
}

.su__star.su__star-yellow {
  background-image: url("data:image/svg+xml,%0A%3Csvg id='Group_13343' data-name='Group 13343' xmlns='http://www.w3.org/2000/svg' width='29.265' height='28.113' viewBox='0 0 29.265 28.113'%3E%3Cpath id='Path_11307' data-name='Path 11307' d='M2118.621,585.582l2.308,4.677a2.858,2.858,0,0,0,2.151,1.563l5.162.75a2.857,2.857,0,0,1,1.583,4.873l-3.735,3.641a2.858,2.858,0,0,0-.822,2.529l.882,5.141a2.857,2.857,0,0,1-4.146,3.012l-4.617-2.427a2.857,2.857,0,0,0-2.659,0l-4.616,2.427a2.857,2.857,0,0,1-4.146-3.012l.882-5.141a2.857,2.857,0,0,0-.822-2.529l-3.735-3.641a2.857,2.857,0,0,1,1.583-4.873l5.162-.75a2.857,2.857,0,0,0,2.151-1.563l2.308-4.677A2.857,2.857,0,0,1,2118.621,585.582Z' transform='translate(-2101.427 -583.99)' fill='%23fed302'/%3E%3Cpath id='Path_11308' data-name='Path 11308' d='M2194.462,725.7a2.856,2.856,0,0,0-.822,2.529l.882,5.141a2.857,2.857,0,0,1-4.146,3.012l-4.617-2.427a2.856,2.856,0,0,0-2.659,0l-4.617,2.427a2.863,2.863,0,0,1-4.069-1.7c9.417-.877,17.379-7.889,21.043-17.665l1.156.168a2.857,2.857,0,0,1,1.583,4.873Z' transform='translate(-2169.798 -708.605)' fill='%23f0f0f0' opacity='0.88' style='mix-blend-mode: multiply;isolation: isolate'/%3E%3Cg id='Group_13342' data-name='Group 13342' transform='translate(5.193 5.034)' opacity='0.2'%3E%3Cpath id='Path_11309' data-name='Path 11309' d='M2183.531,669.059a5.582,5.582,0,0,1,.6-.4c.2-.121.4-.227.607-.329a11.194,11.194,0,0,1,1.235-.513l.156-.053c.052-.018.086-.035.13-.053s.083-.031.124-.046.081-.035.122-.052c.081-.034.159-.071.238-.107l.117-.053c.039-.019.076-.04.114-.06s.075-.04.114-.057l.112-.061.111-.06.107-.067c.036-.022.073-.04.109-.062l.106-.068c.035-.023.072-.043.106-.068s.067-.05.1-.073a7.669,7.669,0,0,0,1.523-1.381c.015-.018.029-.033.045-.054s.034-.043.051-.065l.1-.129c.069-.086.138-.173.212-.257.144-.17.3-.336.453-.5s.323-.322.5-.476a5.628,5.628,0,0,1,.565-.444l.118.058-.545,1.176c-.186.391-.369.78-.559,1.17a5.3,5.3,0,0,1-1.72,1.984,5.407,5.407,0,0,1-2.461.906l-1.285.168-1.287.154Z' transform='translate(-2183.531 -663.572)' fill='%23fff'/%3E%3C/g%3E%3C/svg%3E%0A");
}

.su__emoji-icon0 {
  background-image: url('data:image/svg+xml;base64,PHN2ZyBpZD0iR3JvdXBfMTg2NzYiIGRhdGEtbmFtZT0iR3JvdXAgMTg2NzYiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgd2lkdGg9IjQwLjU4OCIgaGVpZ2h0PSI0MC41ODgiIHZpZXdCb3g9IjAgMCA0MC41ODggNDAuNTg4Ij4KICA8cGF0aCBpZD0iVmVjdG9yIiBkPSJNMjAuMjk0LDM2LjcyM0ExNi40MjksMTYuNDI5LDAsMSwwLDMuODY2LDIwLjI5NCwxNi40MjksMTYuNDI5LDAsMCwwLDIwLjI5NCwzNi43MjNaTTQwLjU4OCwyMC4yOTRBMjAuMjk0LDIwLjI5NCwwLDEsMSwyMC4yOTQsMCwyMC4yOTQsMjAuMjk0LDAsMCwxLDQwLjU4OCwyMC4yOTRaIiBmaWxsPSIjMTc3MGQ0IiBmaWxsLXJ1bGU9ImV2ZW5vZGQiLz4KICA8cGF0aCBpZD0iVmVjdG9yLTIiIGRhdGEtbmFtZT0iVmVjdG9yIiBkPSJNMTUuMSwzMC44NTZhMS4zMTksMS4zMTksMCwwLDEtMi4xNjktMS41LDguNiw4LjYsMCwwLDEsNy4zMzctNC4wMTUsNy44NCw3Ljg0LDAsMCwxLDcuMDU3LDQuMDksMS4zMTksMS4zMTksMCwxLDEtMi4yNjUsMS4zNTMsNS4yMTYsNS4yMTYsMCwwLDAtNC43OTItMi44QTUuOTgsNS45OCwwLDAsMCwxNS4xLDMwLjg1NloiIHRyYW5zZm9ybT0idHJhbnNsYXRlKDAuMTg2IDAuMzczKSIgZmlsbD0iIzE3NzBkNCIvPgogIDxwYXRoIGlkPSJWZWN0b3ItMyIgZGF0YS1uYW1lPSJWZWN0b3IiIGQ9Ik0xNC4xLDIwLjYxOGExLjA4NCwxLjA4NCwwLDAsMCwuOTItLjU1LDEuMzE5LDEuMzE5LDAsMCwxLDIuMTI0LDEuNTY1LDMuNjc2LDMuNjc2LDAsMCwxLTMsMS42MjMsMy43NDcsMy43NDcsMCwwLDEtMy4xNzQtMS42MTcsMS4zMTksMS4zMTksMCwwLDEsMi4xMTUtMS41NzcsMS4xNDMsMS4xNDMsMCwwLDAsMS4wMTkuNTU3WiIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoMC4xNTcgMC4yODcpIiBmaWxsPSIjMTc3MGQ0Ii8+CiAgPHBhdGggaWQ9IlZlY3Rvci00IiBkYXRhLW5hbWU9IlZlY3RvciIgZD0iTTI2LjEsMjAuNjE4YTEuMDg0LDEuMDg0LDAsMCwwLC45Mi0uNTUsMS4zMTksMS4zMTksMCwwLDEsMi4xMjQsMS41NjUsMy42NzYsMy42NzYsMCwwLDEtMywxLjYyMywzLjc0NywzLjc0NywwLDAsMS0zLjE3NC0xLjYxNywxLjMxOSwxLjMxOSwwLDEsMSwyLjExNS0xLjU3NywxLjE0MywxLjE0MywwLDAsMCwxLjAxOS41NTdaIiB0cmFuc2Zvcm09InRyYW5zbGF0ZSgwLjMzMyAwLjI4NykiIGZpbGw9IiMxNzcwZDQiLz4KICA8cGF0aCBpZD0iVmVjdG9yLTUiIGRhdGEtbmFtZT0iVmVjdG9yIiBkPSJNOS42LDE1LjkwNmExLjMxOSwxLjMxOSwwLDEsMSwuODM0LTIuNWw1Ljc3NiwxLjkyNWExLjMxOSwxLjMxOSwwLDAsMS0uODM0LDIuNVoiIHRyYW5zZm9ybT0idHJhbnNsYXRlKDAuMTI3IDAuMTk2KSIgZmlsbD0iIzE3NzBkNCIvPgogIDxwYXRoIGlkPSJWZWN0b3ItNiIgZGF0YS1uYW1lPSJWZWN0b3IiIGQ9Ik0yOS42OSwxMy40YTEuMzE5LDEuMzE5LDAsMCwxLC44MzQsMi41bC02LjA4OCwyLjAyOWExLjMxOSwxLjMxOSwwLDAsMS0uODM0LTIuNVoiIHRyYW5zZm9ybT0idHJhbnNsYXRlKDAuMzMzIDAuMTk2KSIgZmlsbD0iIzE3NzBkNCIvPgo8L3N2Zz4K');
}

.su__emoji-icon1 {
  background-image: url('data:image/svg+xml;base64,PHN2ZyBpZD0iR3JvdXBfMTg2NzciIGRhdGEtbmFtZT0iR3JvdXAgMTg2NzciIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgd2lkdGg9IjQwLjU4OCIgaGVpZ2h0PSI0MC41ODgiIHZpZXdCb3g9IjAgMCA0MC41ODggNDAuNTg4Ij4KICA8cGF0aCBpZD0iVmVjdG9yIiBkPSJNMjAuMjk0LDM2LjcyM0ExNi40MjksMTYuNDI5LDAsMSwwLDMuODY2LDIwLjI5NCwxNi40MjksMTYuNDI5LDAsMCwwLDIwLjI5NCwzNi43MjNaTTQwLjU4OCwyMC4yOTRBMjAuMjk0LDIwLjI5NCwwLDEsMSwyMC4yOTQsMCwyMC4yOTQsMjAuMjk0LDAsMCwxLDQwLjU4OCwyMC4yOTRaIiBmaWxsPSIjMTc3MGQ0IiBmaWxsLXJ1bGU9ImV2ZW5vZGQiLz4KICA8cGF0aCBpZD0iVmVjdG9yLTIiIGRhdGEtbmFtZT0iVmVjdG9yIiBkPSJNOC44LDE2LjcxOGExLjIxOCwxLjIxOCwwLDAsMSwyLjQzNSwwLDEuODI2LDEuODI2LDAsMCwwLDMuNjUzLDAsMS4yMTgsMS4yMTgsMCwwLDEsMi40MzUsMCw0LjI2Miw0LjI2MiwwLDEsMS04LjUyNCwwWiIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoMC4xMyAwLjIyOCkiIGZpbGw9IiMxNzcwZDQiLz4KICA8cGF0aCBpZD0iVmVjdG9yLTMiIGRhdGEtbmFtZT0iVmVjdG9yIiBkPSJNMjIuOCwxNi43MThhMS4yMTgsMS4yMTgsMCwwLDEsMi40MzUsMCwxLjgyNiwxLjgyNiwwLDEsMCwzLjY1MywwLDEuMjE4LDEuMjE4LDAsMCwxLDIuNDM1LDAsNC4yNjIsNC4yNjIsMCwwLDEtOC41MjQsMFoiIHRyYW5zZm9ybT0idHJhbnNsYXRlKDAuMzM2IDAuMjI4KSIgZmlsbD0iIzE3NzBkNCIvPgogIDxwYXRoIGlkPSJWZWN0b3ItNCIgZGF0YS1uYW1lPSJWZWN0b3IiIGQ9Ik0xNi45LDI4LjQyNmExLjMxOSwxLjMxOSwwLDAsMS0xLjc1My0xLjk3Miw3LjE1OCw3LjE1OCwwLDAsMSw1LjEtMS43NTEsNy4wMjUsNy4wMjUsMCwwLDEsNC43NjMsMS43NDgsMS4zMTksMS4zMTksMCwxLDEtMS43NDcsMS45NzcsNC40MjIsNC40MjIsMCwwLDAtMy4wNTctMS4wODhBNC41NjUsNC41NjUsMCwwLDAsMTYuOSwyOC40MjZaIiB0cmFuc2Zvcm09InRyYW5zbGF0ZSgwLjIxNiAwLjM2MykiIGZpbGw9IiMxNzcwZDQiLz4KPC9zdmc+Cg==');
}

.su__emoji-icon2 {
  background-image: url('data:image/svg+xml;base64,PHN2ZyBpZD0iR3JvdXBfMTg2NzgiIGRhdGEtbmFtZT0iR3JvdXAgMTg2NzgiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgd2lkdGg9IjQwLjU4OCIgaGVpZ2h0PSI0MC41ODgiIHZpZXdCb3g9IjAgMCA0MC41ODggNDAuNTg4Ij4KICA8cGF0aCBpZD0iVmVjdG9yIiBkPSJNMjAuMjk0LDM2LjcyM0ExNi40MjksMTYuNDI5LDAsMSwwLDMuODY2LDIwLjI5NCwxNi40MjksMTYuNDI5LDAsMCwwLDIwLjI5NCwzNi43MjNaTTQwLjU4OCwyMC4yOTRBMjAuMjk0LDIwLjI5NCwwLDEsMSwyMC4yOTQsMCwyMC4yOTQsMjAuMjk0LDAsMCwxLDQwLjU4OCwyMC4yOTRaIiBmaWxsPSIjMTc3MGQ0IiBmaWxsLXJ1bGU9ImV2ZW5vZGQiLz4KICA8cGF0aCBpZD0iVmVjdG9yLTIiIGRhdGEtbmFtZT0iVmVjdG9yIiBkPSJNMTYuMDg4LDE3LjA0NEEzLjA0NCwzLjA0NCwwLDEsMSwxMy4wNDQsMTQsMy4wNDQsMy4wNDQsMCwwLDEsMTYuMDg4LDE3LjA0NFoiIHRyYW5zZm9ybT0idHJhbnNsYXRlKDAuMTQ3IDAuMjA2KSIgZmlsbD0iIzE3NzBkNCIgZmlsbC1ydWxlPSJldmVub2RkIi8+CiAgPHBhdGggaWQ9IlZlY3Rvci0zIiBkYXRhLW5hbWU9IlZlY3RvciIgZD0iTTMwLjA4OCwxNy4wNDRBMy4wNDQsMy4wNDQsMCwxLDEsMjcuMDQ0LDE0LDMuMDQ0LDMuMDQ0LDAsMCwxLDMwLjA4OCwxNy4wNDRaIiB0cmFuc2Zvcm09InRyYW5zbGF0ZSgwLjM1MyAwLjIwNikiIGZpbGw9IiMxNzcwZDQiIGZpbGwtcnVsZT0iZXZlbm9kZCIvPgogIDxwYXRoIGlkPSJWZWN0b3ItNCIgZGF0YS1uYW1lPSJWZWN0b3IiIGQ9Ik0xMi41LDI4LjAyMkExLjUyMiwxLjUyMiwwLDAsMSwxNC4wMjIsMjYuNUgyNi4yYTEuNTIyLDEuNTIyLDAsMSwxLDAsMy4wNDRIMTQuMDIyQTEuNTIyLDEuNTIyLDAsMCwxLDEyLjUsMjguMDIyWiIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoMC4xODUgMC4zOSkiIGZpbGw9IiMxNzcwZDQiIGZpbGwtcnVsZT0iZXZlbm9kZCIvPgo8L3N2Zz4K');
}

.su__emoji-icon3 {
  background-image: url('data:image/svg+xml;base64,PHN2ZyBpZD0iR3JvdXBfMTg2NzkiIGRhdGEtbmFtZT0iR3JvdXAgMTg2NzkiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgd2lkdGg9IjQwLjU4OCIgaGVpZ2h0PSI0MC41ODgiIHZpZXdCb3g9IjAgMCA0MC41ODggNDAuNTg4Ij4KICA8cGF0aCBpZD0iVmVjdG9yIiBkPSJNMjAuMjk0LDM2LjcyM0ExNi40MjksMTYuNDI5LDAsMSwwLDMuODY2LDIwLjI5NCwxNi40MjksMTYuNDI5LDAsMCwwLDIwLjI5NCwzNi43MjNaTTQwLjU4OCwyMC4yOTRBMjAuMjk0LDIwLjI5NCwwLDEsMSwyMC4yOTQsMCwyMC4yOTQsMjAuMjk0LDAsMCwxLDQwLjU4OCwyMC4yOTRaIiBmaWxsPSIjMTc3MGQ0IiBmaWxsLXJ1bGU9ImV2ZW5vZGQiLz4KICA8cGF0aCBpZD0iVmVjdG9yLTIiIGRhdGEtbmFtZT0iVmVjdG9yIiBkPSJNMjAuMjcyLDI1Ljc0NEE1LjQwNiw1LjQwNiwwLDAsMCwyNS4xLDIzLjI4YTEuMzE5LDEuMzE5LDAsMCwxLDIuMTg1LDEuNDc4LDguMDMyLDguMDMyLDAsMCwxLTcuMDE3LDMuNjI0LDguODg0LDguODg0LDAsMCwxLTcuMjktMy41NDksMS4zMTksMS4zMTksMCwxLDEsMi4wNzYtMS42MjgsNi4yNTMsNi4yNTMsMCwwLDAsNS4yMTQsMi41MzlaIiB0cmFuc2Zvcm09InRyYW5zbGF0ZSgwLjE4NiAwLjMzNCkiIGZpbGw9IiMxNzcwZDQiLz4KICA8cGF0aCBpZD0iVmVjdG9yLTMiIGRhdGEtbmFtZT0iVmVjdG9yIiBkPSJNMjQuODMzLDE1Ljc1MkExLjExNiwxLjExNiwwLDEsMSwyMy4yLDE0LjIzYTMuNzgsMy43OCwwLDAsMSwyLjkzMS0xLjIyNEEzLjY3MywzLjY3MywwLDAsMSwyOC45LDE0LjIzNWExLjExNiwxLjExNiwwLDEsMS0xLjY0MiwxLjUxMiwxLjQ1OSwxLjQ1OSwwLDAsMC0xLjE1LS41MUExLjU2OCwxLjU2OCwwLDAsMCwyNC44MzMsMTUuNzUyWiIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoMC4zMzYgMC4xOTEpIiBmaWxsPSIjMTc3MGQ0Ii8+CiAgPHBhdGggaWQ9IlZlY3Rvci00IiBkYXRhLW5hbWU9IlZlY3RvciIgZD0iTTEyLjgzMiwxNS43NTJBMS4xMTYsMS4xMTYsMCwwLDEsMTEuMiwxNC4yM2EzLjc4LDMuNzgsMCwwLDEsMi45MzEtMS4yMjRBMy42NzMsMy42NzMsMCwwLDEsMTYuOSwxNC4yMzVhMS4xMTYsMS4xMTYsMCwxLDEtMS42NDIsMS41MTIsMS40NTksMS40NTksMCwwLDAtMS4xNS0uNTFBMS41NjgsMS41NjgsMCwwLDAsMTIuODMyLDE1Ljc1MloiIHRyYW5zZm9ybT0idHJhbnNsYXRlKDAuMTYgMC4xOTEpIiBmaWxsPSIjMTc3MGQ0Ii8+Cjwvc3ZnPgo=');
}

.su__emoji-icon4 {
  background-image: url('data:image/svg+xml;base64,PHN2ZyBpZD0iR3JvdXBfMTg2ODAiIGRhdGEtbmFtZT0iR3JvdXAgMTg2ODAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgd2lkdGg9IjQwLjU4OCIgaGVpZ2h0PSI0MC41ODgiIHZpZXdCb3g9IjAgMCA0MC41ODggNDAuNTg4Ij4KICA8cGF0aCBpZD0iVmVjdG9yIiBkPSJNMjAuMjk0LDM2LjcyM0ExNi40MjksMTYuNDI5LDAsMSwwLDMuODY2LDIwLjI5NCwxNi40MjksMTYuNDI5LDAsMCwwLDIwLjI5NCwzNi43MjNaTTQwLjU4OCwyMC4yOTRBMjAuMjk0LDIwLjI5NCwwLDEsMSwyMC4yOTQsMCwyMC4yOTQsMjAuMjk0LDAsMCwxLDQwLjU4OCwyMC4yOTRaIiBmaWxsPSIjMTc3MGQ0IiBmaWxsLXJ1bGU9ImV2ZW5vZGQiLz4KICA8cGF0aCBpZD0iVmVjdG9yLTIiIGRhdGEtbmFtZT0iVmVjdG9yIiBkPSJNMTEuMTE2LDIzYTIuMjE2LDIuMjE2LDAsMCwxLDIuMi0yLjQ3OEgyNi45NDVBMi4yMTYsMi4yMTYsMCwwLDEsMjkuMTQ1LDIzYTkuMTc3LDkuMTc3LDAsMCwxLTkuMDE1LDguMTU2QTkuMTc3LDkuMTc3LDAsMCwxLDExLjExNiwyM1oiIHRyYW5zZm9ybT0idHJhbnNsYXRlKDAuMTYzIDAuMzAyKSIgZmlsbD0iIzE3NzBkNCIvPgogIDxwYXRoIGlkPSJWZWN0b3ItMyIgZGF0YS1uYW1lPSJWZWN0b3IiIGQ9Ik0yNC45LDE1Ljk4M2ExLjExNiwxLjExNiwwLDAsMS0xLjc1OS0xLjM3NCwzLjgzNSwzLjgzNSwwLDAsMSwzLjIwNi0xLjU3LDMuNzQ2LDMuNzQ2LDAsMCwxLDMuMDIyLDEuNTc1QTEuMTE2LDEuMTE2LDAsMCwxLDI3LjYsMTUuOTc3YTEuNTQ1LDEuNTQ1LDAsMCwwLTEuMjg3LS43MDcsMS42MjYsMS42MjYsMCwwLDAtMS40MTUuNzEyWiIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoMC4zMzcgMC4xOTIpIiBmaWxsPSIjMTc3MGQ0Ii8+CiAgPHBhdGggaWQ9IlZlY3Rvci00IiBkYXRhLW5hbWU9IlZlY3RvciIgZD0iTTEyLjksMTUuOTgzYTEuMTE2LDEuMTE2LDAsMCwxLTEuNzU5LTEuMzc0LDMuODM1LDMuODM1LDAsMCwxLDMuMjA2LTEuNTcsMy43NDYsMy43NDYsMCwwLDEsMy4wMjIsMS41NzVBMS4xMTYsMS4xMTYsMCwxLDEsMTUuNiwxNS45NzdhMS41NDUsMS41NDUsMCwwLDAtMS4yODctLjcwNywxLjYyNiwxLjYyNiwwLDAsMC0xLjQxNS43MTJaIiB0cmFuc2Zvcm09InRyYW5zbGF0ZSgwLjE2IDAuMTkyKSIgZmlsbD0iIzE3NzBkNCIvPgo8L3N2Zz4K');
}

.su__emoji-white-icon0 {
  background-image: url('data:image/svg+xml;base64,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');
}

.su__emoji-white-icon1 {
  background-image: url('data:image/svg+xml;base64,PHN2ZyBpZD0iR3JvdXBfMTg2NzciIGRhdGEtbmFtZT0iR3JvdXAgMTg2NzciIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgd2lkdGg9IjQwLjU4OCIgaGVpZ2h0PSI0MC41ODgiIHZpZXdCb3g9IjAgMCA0MC41ODggNDAuNTg4Ij4KICA8cGF0aCBpZD0iVmVjdG9yIiBkPSJNMjAuMjk0LDM2LjcyM0ExNi40MjksMTYuNDI5LDAsMSwwLDMuODY2LDIwLjI5NCwxNi40MjksMTYuNDI5LDAsMCwwLDIwLjI5NCwzNi43MjNaTTQwLjU4OCwyMC4yOTRBMjAuMjk0LDIwLjI5NCwwLDEsMSwyMC4yOTQsMCwyMC4yOTQsMjAuMjk0LDAsMCwxLDQwLjU4OCwyMC4yOTRaIiBmaWxsPSIjZmZmIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiLz4KICA8cGF0aCBpZD0iVmVjdG9yLTIiIGRhdGEtbmFtZT0iVmVjdG9yIiBkPSJNOC44LDE2LjcxOGExLjIxOCwxLjIxOCwwLDAsMSwyLjQzNSwwLDEuODI2LDEuODI2LDAsMCwwLDMuNjUzLDAsMS4yMTgsMS4yMTgsMCwwLDEsMi40MzUsMCw0LjI2Miw0LjI2MiwwLDEsMS04LjUyNCwwWiIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoMC4xMyAwLjIyOCkiIGZpbGw9IiNmZmYiLz4KICA8cGF0aCBpZD0iVmVjdG9yLTMiIGRhdGEtbmFtZT0iVmVjdG9yIiBkPSJNMjIuOCwxNi43MThhMS4yMTgsMS4yMTgsMCwwLDEsMi40MzUsMCwxLjgyNiwxLjgyNiwwLDEsMCwzLjY1MywwLDEuMjE4LDEuMjE4LDAsMCwxLDIuNDM1LDAsNC4yNjIsNC4yNjIsMCwwLDEtOC41MjQsMFoiIHRyYW5zZm9ybT0idHJhbnNsYXRlKDAuMzM2IDAuMjI4KSIgZmlsbD0iI2ZmZiIvPgogIDxwYXRoIGlkPSJWZWN0b3ItNCIgZGF0YS1uYW1lPSJWZWN0b3IiIGQ9Ik0xNi45LDI4LjQyNmExLjMxOSwxLjMxOSwwLDAsMS0xLjc1My0xLjk3Miw3LjE1OCw3LjE1OCwwLDAsMSw1LjEtMS43NTEsNy4wMjUsNy4wMjUsMCwwLDEsNC43NjMsMS43NDgsMS4zMTksMS4zMTksMCwxLDEtMS43NDcsMS45NzcsNC40MjIsNC40MjIsMCwwLDAtMy4wNTctMS4wODhBNC41NjUsNC41NjUsMCwwLDAsMTYuOSwyOC40MjZaIiB0cmFuc2Zvcm09InRyYW5zbGF0ZSgwLjIxNiAwLjM2MykiIGZpbGw9IiNmZmYiLz4KPC9zdmc+Cg==');
}

.su__emoji-white-icon2 {
  background-image: url('data:image/svg+xml;base64,PHN2ZyBpZD0iR3JvdXBfMTg2NzgiIGRhdGEtbmFtZT0iR3JvdXAgMTg2NzgiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgd2lkdGg9IjQwLjU4OCIgaGVpZ2h0PSI0MC41ODgiIHZpZXdCb3g9IjAgMCA0MC41ODggNDAuNTg4Ij4KICA8cGF0aCBpZD0iVmVjdG9yIiBkPSJNMjAuMjk0LDM2LjcyM0ExNi40MjksMTYuNDI5LDAsMSwwLDMuODY2LDIwLjI5NCwxNi40MjksMTYuNDI5LDAsMCwwLDIwLjI5NCwzNi43MjNaTTQwLjU4OCwyMC4yOTRBMjAuMjk0LDIwLjI5NCwwLDEsMSwyMC4yOTQsMCwyMC4yOTQsMjAuMjk0LDAsMCwxLDQwLjU4OCwyMC4yOTRaIiBmaWxsPSIjZmZmIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiLz4KICA8cGF0aCBpZD0iVmVjdG9yLTIiIGRhdGEtbmFtZT0iVmVjdG9yIiBkPSJNMTYuMDg4LDE3LjA0NEEzLjA0NCwzLjA0NCwwLDEsMSwxMy4wNDQsMTQsMy4wNDQsMy4wNDQsMCwwLDEsMTYuMDg4LDE3LjA0NFoiIHRyYW5zZm9ybT0idHJhbnNsYXRlKDAuMTQ3IDAuMjA2KSIgZmlsbD0iI2ZmZiIgZmlsbC1ydWxlPSJldmVub2RkIi8+CiAgPHBhdGggaWQ9IlZlY3Rvci0zIiBkYXRhLW5hbWU9IlZlY3RvciIgZD0iTTMwLjA4OCwxNy4wNDRBMy4wNDQsMy4wNDQsMCwxLDEsMjcuMDQ0LDE0LDMuMDQ0LDMuMDQ0LDAsMCwxLDMwLjA4OCwxNy4wNDRaIiB0cmFuc2Zvcm09InRyYW5zbGF0ZSgwLjM1MyAwLjIwNikiIGZpbGw9IiNmZmYiIGZpbGwtcnVsZT0iZXZlbm9kZCIvPgogIDxwYXRoIGlkPSJWZWN0b3ItNCIgZGF0YS1uYW1lPSJWZWN0b3IiIGQ9Ik0xMi41LDI4LjAyMkExLjUyMiwxLjUyMiwwLDAsMSwxNC4wMjIsMjYuNUgyNi4yYTEuNTIyLDEuNTIyLDAsMSwxLDAsMy4wNDRIMTQuMDIyQTEuNTIyLDEuNTIyLDAsMCwxLDEyLjUsMjguMDIyWiIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoMC4xODUgMC4zOSkiIGZpbGw9IiNmZmYiIGZpbGwtcnVsZT0iZXZlbm9kZCIvPgo8L3N2Zz4K');
}

.su__emoji-white-icon3 {
  background-image: url('data:image/svg+xml;base64,PHN2ZyBpZD0iR3JvdXBfMTg2NzkiIGRhdGEtbmFtZT0iR3JvdXAgMTg2NzkiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgd2lkdGg9IjQwLjU4OCIgaGVpZ2h0PSI0MC41ODgiIHZpZXdCb3g9IjAgMCA0MC41ODggNDAuNTg4Ij4KICA8cGF0aCBpZD0iVmVjdG9yIiBkPSJNMjAuMjk0LDM2LjcyM0ExNi40MjksMTYuNDI5LDAsMSwwLDMuODY2LDIwLjI5NCwxNi40MjksMTYuNDI5LDAsMCwwLDIwLjI5NCwzNi43MjNaTTQwLjU4OCwyMC4yOTRBMjAuMjk0LDIwLjI5NCwwLDEsMSwyMC4yOTQsMCwyMC4yOTQsMjAuMjk0LDAsMCwxLDQwLjU4OCwyMC4yOTRaIiBmaWxsPSIjZmZmIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiLz4KICA8cGF0aCBpZD0iVmVjdG9yLTIiIGRhdGEtbmFtZT0iVmVjdG9yIiBkPSJNMjAuMjcyLDI1Ljc0NEE1LjQwNiw1LjQwNiwwLDAsMCwyNS4xLDIzLjI4YTEuMzE5LDEuMzE5LDAsMCwxLDIuMTg1LDEuNDc4LDguMDMyLDguMDMyLDAsMCwxLTcuMDE3LDMuNjI0LDguODg0LDguODg0LDAsMCwxLTcuMjktMy41NDksMS4zMTksMS4zMTksMCwxLDEsMi4wNzYtMS42MjgsNi4yNTMsNi4yNTMsMCwwLDAsNS4yMTQsMi41MzlaIiB0cmFuc2Zvcm09InRyYW5zbGF0ZSgwLjE4NiAwLjMzNCkiIGZpbGw9IiNmZmYiLz4KICA8cGF0aCBpZD0iVmVjdG9yLTMiIGRhdGEtbmFtZT0iVmVjdG9yIiBkPSJNMjQuODMzLDE1Ljc1MkExLjExNiwxLjExNiwwLDEsMSwyMy4yLDE0LjIzYTMuNzgsMy43OCwwLDAsMSwyLjkzMS0xLjIyNEEzLjY3MywzLjY3MywwLDAsMSwyOC45LDE0LjIzNWExLjExNiwxLjExNiwwLDEsMS0xLjY0MiwxLjUxMiwxLjQ1OSwxLjQ1OSwwLDAsMC0xLjE1LS41MUExLjU2OCwxLjU2OCwwLDAsMCwyNC44MzMsMTUuNzUyWiIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoMC4zMzYgMC4xOTEpIiBmaWxsPSIjZmZmIi8+CiAgPHBhdGggaWQ9IlZlY3Rvci00IiBkYXRhLW5hbWU9IlZlY3RvciIgZD0iTTEyLjgzMiwxNS43NTJBMS4xMTYsMS4xMTYsMCwwLDEsMTEuMiwxNC4yM2EzLjc4LDMuNzgsMCwwLDEsMi45MzEtMS4yMjRBMy42NzMsMy42NzMsMCwwLDEsMTYuOSwxNC4yMzVhMS4xMTYsMS4xMTYsMCwxLDEtMS42NDIsMS41MTIsMS40NTksMS40NTksMCwwLDAtMS4xNS0uNTFBMS41NjgsMS41NjgsMCwwLDAsMTIuODMyLDE1Ljc1MloiIHRyYW5zZm9ybT0idHJhbnNsYXRlKDAuMTYgMC4xOTEpIiBmaWxsPSIjZmZmIi8+Cjwvc3ZnPgo=');
}

.su__emoji-white-icon4 {
  background-image: url('data:image/svg+xml;base64,PHN2ZyBpZD0iR3JvdXBfMTg2ODAiIGRhdGEtbmFtZT0iR3JvdXAgMTg2ODAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgd2lkdGg9IjQwLjU4OCIgaGVpZ2h0PSI0MC41ODgiIHZpZXdCb3g9IjAgMCA0MC41ODggNDAuNTg4Ij4KICA8cGF0aCBpZD0iVmVjdG9yIiBkPSJNMjAuMjk0LDM2LjcyM0ExNi40MjksMTYuNDI5LDAsMSwwLDMuODY2LDIwLjI5NCwxNi40MjksMTYuNDI5LDAsMCwwLDIwLjI5NCwzNi43MjNaTTQwLjU4OCwyMC4yOTRBMjAuMjk0LDIwLjI5NCwwLDEsMSwyMC4yOTQsMCwyMC4yOTQsMjAuMjk0LDAsMCwxLDQwLjU4OCwyMC4yOTRaIiBmaWxsPSIjZmZmIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiLz4KICA8cGF0aCBpZD0iVmVjdG9yLTIiIGRhdGEtbmFtZT0iVmVjdG9yIiBkPSJNMTEuMTE2LDIzYTIuMjE2LDIuMjE2LDAsMCwxLDIuMi0yLjQ3OEgyNi45NDVBMi4yMTYsMi4yMTYsMCwwLDEsMjkuMTQ1LDIzYTkuMTc3LDkuMTc3LDAsMCwxLTkuMDE1LDguMTU2QTkuMTc3LDkuMTc3LDAsMCwxLDExLjExNiwyM1oiIHRyYW5zZm9ybT0idHJhbnNsYXRlKDAuMTYzIDAuMzAyKSIgZmlsbD0iI2ZmZiIvPgogIDxwYXRoIGlkPSJWZWN0b3ItMyIgZGF0YS1uYW1lPSJWZWN0b3IiIGQ9Ik0yNC45LDE1Ljk4M2ExLjExNiwxLjExNiwwLDAsMS0xLjc1OS0xLjM3NCwzLjgzNSwzLjgzNSwwLDAsMSwzLjIwNi0xLjU3LDMuNzQ2LDMuNzQ2LDAsMCwxLDMuMDIyLDEuNTc1QTEuMTE2LDEuMTE2LDAsMCwxLDI3LjYsMTUuOTc3YTEuNTQ1LDEuNTQ1LDAsMCwwLTEuMjg3LS43MDcsMS42MjYsMS42MjYsMCwwLDAtMS40MTUuNzEyWiIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoMC4zMzcgMC4xOTIpIiBmaWxsPSIjZmZmIi8+CiAgPHBhdGggaWQ9IlZlY3Rvci00IiBkYXRhLW5hbWU9IlZlY3RvciIgZD0iTTEyLjksMTUuOTgzYTEuMTE2LDEuMTE2LDAsMCwxLTEuNzU5LTEuMzc0LDMuODM1LDMuODM1LDAsMCwxLDMuMjA2LTEuNTcsMy43NDYsMy43NDYsMCwwLDEsMy4wMjIsMS41NzVBMS4xMTYsMS4xMTYsMCwxLDEsMTUuNiwxNS45NzdhMS41NDUsMS41NDUsMCwwLDAtMS4yODctLjcwNywxLjYyNiwxLjYyNiwwLDAsMC0xLjQxNS43MTJaIiB0cmFuc2Zvcm09InRyYW5zbGF0ZSgwLjE2IDAuMTkyKSIgZmlsbD0iI2ZmZiIvPgo8L3N2Zz4K');
}

.su__emoji-active {
  filter: none;
}

.su__formError #su__feedback-email {
  border: 1px solid var(--su__searchapp-red);
}

.su__feedshow-overlay {
  content: '';
  position: fixed;
  background: rgba(var(--su__searchapp-black-rgba), 0.48);
  z-index: 1;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
}

.su__feedshow-center {
  width: 367px;
  position: fixed;
  z-index: 99;
  left: 0;
  right: 0;
  margin: auto;
  top: 50%;
  transform: translateY(-50%);
  border-radius: 6px;
}

.su__feedshow-center .su_feedback_form {
  margin: auto;
}

.su__fs-search-result {
  display: none;
}

.su__pagerating-row {
  background: var(--su__searchapp-white);
  box-shadow: 0 1px 7px rgba(var(--su__searchapp-black-rgba), 0.16);
}

.su__pagerating-row,
.su_feedback_form {
  max-width: 350px;
}

.su__thanks_modal_padding {
  padding: 0 42px 0 42px;
  max-width: 367px;
}

.su__feedshow-bottom {
  position: relative;
  width: fit-content;
}

.su__left-sidebar {
  padding-bottom: 20px;
}

.su__click-title.su__fs-search-result {
  display: block;
}

.list .su__auto-tuning {
  order: 2;
}

.su__grid-content .su__fs-search-result {
  width: 13px;
}

/* feature snippets */
.su__fs-media {
  margin-right: 16px;
  margin-bottom: 15px;
  border-radius: 6px;
  overflow: hidden;
  height: 133px;
}

.su__fs-media .su__featured-thumbnail > .su__mr-2 {
  margin: 0;
}

.su__fs-media .su__featured-thumbnail .su__img-featured {
  border-radius: 4px;
  max-height: 120px;
  height: 120px;
}

.su__fs-media .su__featured-thumbnail {
  width: 100%;
  height: 90%;
  max-width: 100%;
  min-width: 100%;
}

.su__fs-media .su__href-txt {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  width: 100%;
}

.su__fs-inner-media .su__featured-thumbnail {
  width: 100%;
  max-width: 100%;
}

.su__fs-inner-media .su__featured-thumbnail img {
  border-radius: 5px;
}

.su__pagerating-lay .su__close-svg {
  width: 12px;
  height: 12px;
}

.su__FeaturedSnippet .su__featureSnippet-left .su__href-txt {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.su__d-contents {
  display: contents;
}

.su__knowledge_feedback {
  max-width: 272px;
  background-color: var(--su__searchapp-paleblue-cool);
  padding: 0 10px 0 10px;
}

.su__knowledge-feedback-row {
  margin-left: auto;
}

.su__feedback-icon-above .su__feedback-row {
  margin-left: auto;
  bottom: 0;
}

.su__feedback_icon_align {
  position: absolute;
  bottom: 0;
}

.su__featured-feedback {
  width: 26px;
  height: 26px;
  background: var(--su__searchapp-white);
  border-radius: 4px;
  justify-content: center;
  display: flex;
  align-items: center;
}

.su__knowledge_downlikeicon {
  border-radius: 50px;
  opacity: 0.9;
  display: flex;
  align-items: center;
  padding-top: 6px;
}

.su__featured-thankyou {
  height: 35px;
  top: 5px;
}

.su__feedback-icon-above .su__thankyou-text {
  background: var(--su__searchapp-vividgreen);
  color: var(--su__searchapp-white);
  padding: 5px 10px;
  border-radius: 5px;
}

.su__featureSnippet-response {
  height: 40px;
}

.su__fs-singleImage .su__featureSnippet-response {
  height: 35px;
}

.su__snippet-none {
  display: none;
}

.su__feedback-btn[disabled] {
  background: var(--su__searchapp-deepskyblue-light);
  color: var(--su__searchapp-white);
  opacity: 0.5;
}

.su__allcontent-active.su__content-gutter {
  margin: 0 auto;
  padding: 0;
}

.su__allSelected-show > .su__mob-facetshow {
  display: none;
}

.su__FeaturedSnippet a span span.highlight,
.su__fs-media-content a span span.highlight {
  color: rgba(var(--su__searchapp-jetgray-rgba), 0.8);
  font-style: normal;
  padding: 0;
}

.su__steps-bullets {
  display: list-item;
  list-style-type: disc;
  list-style-position: inside;
}

.su__feedback-modal {
  overflow: hidden;
  word-break: break-all;
  border-radius: 6px;
  background: var(--su__searchapp-white);
  box-shadow: 0 0 20px 0 rgba(var(--su__searchapp-sapphire-rgba), 0.3);
  bottom: 1rem;
  right: 1rem;
  z-index: 2;
}

.su__feedback-thankyou {
  padding: 5px;
}

.su__fs-media .su__feedback-thankyou {
  padding: 0;
  margin-bottom: 2px;
}

.su__feedback-steps {
  top: 10px;
  left: 5px;
}

.su__thankyou-text,
.su__feedback-steps .su__thankyou-text,
#single_hiddenSnippet .su__thankyou-text {
  background: var(--su__searchapp-vividgreen) 0% 0% no-repeat padding-box;
  border-radius: 3px;
  color: var(--su__searchapp-white);
  padding: 5px 10px;
}

.su__listed-item .su__fs-search-result .su__tooltip-left {
  top: calc(100% + 10px);
  left: -100%;
  right: auto;
  -webkit-transform: translateX(-50%);
  -ms-transform: translateX(-50%);
  transform: translateX(-50%);
}

.su__listed-item .su__fs-search-result .su__tringle-left {
  top: -11px;
  left: calc(50% - 10px);
  right: auto;
  border-right: 6px solid transparent;
  border-bottom: 6px solid var(--su__searchapp-darkgray);
  border-left: 6px solid transparent;
}

.su__rtl .su__listed-item .su__fs-search-result .su__tooltip-left {
  left: 140%;
}

.su__duplicate-bookmark {
  padding: 10px 0;
  margin: 0;
  color: var(--su__searchapp-red);
  font-weight: 400;
}

.su__fs-search-result.active .su__tooltip {
  z-index: 1;
}

.su__listed-item .su__list-item-title {
  max-width: calc(100% - 20px);
}

.su__grid-content .su__auto-tuning + .su_preview-startblock {
  margin-right: 14px;
  margin-left: 5px;
}

@media (max-width: 480px) {
  .su__pagination span span {
    padding: 2px 7px !important;
    line-height: normal;
    margin-right: 4px;
    -moz-appearance: none;
    -webkit-appearance: none;
    appearance: none;
  }

  .su__pagination span button[type='button'] {
    padding: 2px 7px !important;
    line-height: normal;
    margin-right: 4px;
    -moz-appearance: none;
    -webkit-appearance: none;
    appearance: none;
    color: var(--su__searchapp-darkgray);
  }

  .resultsPerPage {
    border: 1px solid var(--su__searchapp-lightgrey);
    padding-right: 14px !important;
  }

  .footerSection {
    text-align: left !important;
  }
}

/* classes for reuseable pop up component starts here */
.su__modal-inner.su__new-modal {
  width: 480px;
  border-radius: 10px;
  height: 383px;
}

.su__modal-inner.su__mobile-modal {
  width: calc(100% - 20px);
  border-radius: 10px;
}

.su_new-btn {
  border: 1px solid var(--su__searchapp-gray);
  border-radius: 14px;
  margin-left: 18px;
  background-color: var(--su__searchapp-white);
  color: var(--su__searchapp-black);
  font-size: 12px;
  width: 120px;
  height: 28px;
  font-weight: 700;
}

.su__ph-gray::placeholder {
  color: var(--su__searchapp-rosybrown);
}

.su__ph-gray::-webkit-input-placeholder {
  color: var(--su__searchapp-rosybrown);
}

.su__ph-gray::-moz-placeholder {
  color: var(--su__searchapp-rosybrown);
}

.su__ph-gray:-ms-input-placeholder {
  color: var(--su__searchapp-rosybrown);
}

.su__ph-gray::-ms-input-placeholder {
  color: var(--su__searchapp-rosybrown);
}

.su__add-click {
  background-color: var(--su__searchapp-gray);
  color: var(--su__searchapp-white);
}

.su_new-cross {
  padding-top: 24px;
}

.su__bottom-padding {
  padding-bottom: 15px;
}

.su__bottom-padding-button {
  padding-bottom: 0;
  padding-top: 13px;
}
.su__cross-icon {
  top: 5px;
  position: absolute;
  right: 11px;
}

.su__saved-search {
  display: flex;
  position: relative;
  bottom: 17px;
  margin-left: auto;
  align-self: stretch;
}

.su__popup-text.su__flex-edit {
  flex-direction: row;
}

.su__new-setting {
  margin-left: 10px;
  font-size: 14px;
  width: 115px;
}

.su__tabs.su__edit-tabs {
  padding: 8px 9px;
}

.su__edit-disabled {
  pointer-events: none;
  opacity: 0.4;
}

/* classes for toggle switch starts here */
.toggle {
  --width: 30px;
  --height: calc(var(--width) / 2);

  position: relative;
  display: inline-block;
  width: var(--width);
  height: var(--height);
  box-shadow: 0 1px 3px rgba(var(--su__searchapp-black-rgba), 0.3);
  border-radius: var(--height);
  cursor: pointer;
}

.toggle input {
  display: none;
}

.toggle .slider {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: var(--height);
  background-color: var(--su__searchapp-white);
  transition: all 0.4s ease-in-out;
  border: 1px solid var(--su__searchapp-deepskyblue-light);
}

.toggle .slider::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: calc(var(--height));
  height: calc(var(--height));
  border-radius: calc(var(--height) / 2);
  background-color: var(--su__searchapp-deepskyblue-light);
  box-shadow: 0 1px 3px rgba(var(--su__searchapp-black-rgba), 0.3);
  transition: all 0.4s ease-in-out;
}

.toggle input:checked + .slider {
  background-color: var(--su__searchapp-white);
}

.toggle input:checked + .slider::before {
  transform: translateX(calc(var(--width) - var(--height)));
}

/* classes for toggle switch ends here */

/* Only for VF community styles */
.su__btn,
.su__filters-button,
.su__btn-block {
  background-image: none !important;
}

ul.su__searchTip-list li {
  margin-left: 0;
}

/* Higher Logic serach client style */
.HtmlContent .su__ScrollToTop {
  position: relative;
  z-index: 999;
}

.HtmlContent select#select-lng {
  padding: 0 24px 0 24px;
}

.su__AllContentsGridView-child .su__list-item-title {
  margin-top: 2px;
  flex-direction: row !important;
  align-items: center !important;
}

.su__search-tuning {
  width: 10px;
}

.su__grid-content .su__search-tuning {
  width: 34px;
}

.su__Recommended_Article-max-width {
  max-width: 100%;
  margin-right: 18px;
  word-break: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
}

.su__Recommended_Article {
  font-size: 17px;
  color: var(--su__searchapp-darkgray);
  font-weight: 500;
}

.su__Recommended_Articles-R {
  border-radius: 4px;
}

.su__recommendations-ViewMore {
  width: 100%;
  text-align: center;
  padding: 15px 0;
}

.su__recommendations-ViewMore-text {
  color: var(--su__searchapp-deepskyblue-light);
  font-size: 14px;
}

.su__recommendations-ViewMore-text:hover {
  text-decoration: underline;
  cursor: pointer;
}

.su__Recomended_border-t {
  border-top: 2px solid var(--su__searchapp-lightgrey);
}

.su__recommendations-tag-content {
  display: inline-block;
}

.su__recommendation-label {
  padding: 5px 6px;
  font-size: 11px;
  background: var(--su__searchapp-white) 0% 0% no-repeat padding-box;
  border: 1px solid var(--su__searchapp-lightgrey);
  border-radius: 5px;
  opacity: 1;
  display: inline-block;
  color: var(--su__searchapp-gray);
}

.su__recommendations-content {
  font-weight: 400;
  font-size: 15px;
  letter-spacing: 0;
  color: var(--su__searchapp-darkgray);
}

.su__border-b:hover .su__recommendations-content {
  color: var(--su__searchapp-deepskyblue-light);
}

.su__Recomended_border-b {
  border-bottom: 1px solid var(--su__searchapp-lightgrey);
}

/* .su__recommendations-title:hover{line-height: 16px !important;} */
.su__recommendations-title {
  line-height: 14px;
  font-weight: normal;
}

.su__Recomended_border-b:last-child {
  border: none;
}

.su_no-filters-mobile {
  font-size: 14px;
  font-family: 'Montserrat', sans-serif;
  margin-top: 33px;
}

.no_filter-msg {
  display: block;
  text-align: center;
}

.su__description-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: inline-block;
  padding-left: 5px;
}

.multiVersionGridView {
  display: flex;
  justify-content: space-between;
  width: 100%;
  margin-left: auto;
}

.su__filter-icon {
  text-align: left;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  font-size: 13px;
  line-height: 17px;
  letter-spacing: 0;
  color: var(--su__searchapp-deepskyblue-light);
  opacity: 1;
  cursor: pointer;
  height: 27px;
  background: var(--su__searchapp-paleblue-cool) 0% 0% no-repeat padding-box;
  border: 1px solid var(--su__searchapp-deepskyblue-light);
  border-radius: 4px;
}

.su_background_grey {
  background-color: var(--su__searchapp-lightgrey);
  display: flex;
  justify-content: center;
  align-items: center;
}

.su__align_url {
  position: absolute;
  bottom: 0;
  left: 0;
}

.su_background_grey:hover .su__align_icon_play_grey path {
  fill: var(--su__searchapp-black);
}

.su__align_grey_icon {
  margin: auto;
  margin-top: 27px;
}

.su__grey_bg {
  background-color: var(--su__searchapp-lightgrey);
}

.su__bg_grey_container {
  background-color: var(--su__searchapp-lightgrey);
  max-height: 175px;
  border-radius: 10px;
}

.su__bg_grey_container:hover .su__align_icon_play_grey path {
  fill: var(--su__searchapp-black);
}

.su__grey_bg:hover .su__align_icon_play_grey path {
  fill: var(--su__searchapp-black);
}

.su__thumbs_icon_align {
  margin-top: 8px;
}

.su__icon_align {
  margin-right: 8px;
}

.d-flex {
  display: flex;
}

.su__snippet_container-width-mobile {
  width: 100%;
}

.su__grey-container {
  width: 30%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--su__searchapp-lightgrey);
}

.su__snippets_mobile {
  display: flex;
  margin: auto;
  justify-content: flex-end;
}

.su__grey-container:hover .su__align_icon_play_grey {
  fill: var(--su__searchapp-black);
}

.su__hide_snippets {
  display: none !important;
}

.su__padding_10 {
  padding: 10px;
}

.su__padding_7 {
  padding-top: 7px;
  padding-left: 7px;
}

.su__resultSaved {
  position: absolute;
  right: 26px;
  top: -25px;
  background-color: var(--su__searchapp-darkorange);
  color: var(--su__searchapp-white);
  font-size: 12px;
  border-radius: 3px 3px 0 0;
  padding: 5px 7px 2px;
  height: 24px;
  padding-top: 0;
  padding-bottom: 0;
  display: flex;
  align-items: center;
  z-index: 99;
}

.su__bookmarkSavedResultText {
  color: var(--su__searchapp-deepskyblue-light);
  text-decoration: underline;
}

.su__bookmarkSavedResultText:hover {
  color: var(--su__searchapp-deepskyblue-light) !important;
  text-decoration: underline;
}

.su__viewed-results .su__bookmarkSavedResultText:visited {
  color: var(--su__searchapp-deepskyblue-light) !important;
  text-decoration: none !important;
}

.su__viewed-results .su__deletedSavedResultText {
  color: var(--su__searchapp-deepskyblue-light) !important;
  text-decoration: none !important;
}

.su__viewed-results .su__deletedSavedResultText:hover {
  color: var(--su__searchapp-deepskyblue-light) !important;
  text-decoration: none !important;
}

.su__viewed-results .su__deletedSavedResultText:visited {
  color: var(--su__searchapp-deepskyblue-light) !important;
  text-decoration: none !important;
}

.su__BookmarkActive-tab {
  color: var(--su__searchapp-black);
  border-bottom: 2px solid var(--su__searchapp-deepskyblue-light) !important;
  font-weight: 700;
}

.su__savedResultLimitReached {
  font-size: 12px;
  color: var(--su__searchapp-red);
  margin-top: 12px;
  margin-left: 16px;
  font-weight: 600;
}

.sc__tips_arabicBox {
  padding-left: 30px !important;
}

.su__savedResultTooltip {
  visibility: visible;
  color: var(--su__searchapp-deepskyblue-light);
  position: absolute !important;
  z-index: 99999 !important;
  padding: 4px 1px 4px 8px;
  font-size: 12px !important;
  border-radius: 3px !important;
  background: var(--su__searchapp-paleblue-cool);
  -webkit-box-shadow: 0 0 0.48px rgba(var(--su__searchapp-black-rgba), 0.16) !important;
  box-shadow: 0 0 0.48px rgba(var(--su__searchapp-black-rgba), 0.16) !important;
  -webkit-animation: fadeintooltip ease-in-out 0.5s;
  animation: fadeIntooltip ease-in-out 0.5s;
  display: inline-block;
  left: 7px;
  top: 28px;
  max-width: 93%;
  white-space: inherit !important;
}

.savedResultDontExist {
  visibility: visible;
  color: var(--su__searchapp-white);
  position: absolute !important;
  z-index: 99999 !important;
  padding: 5px 5px 5px 5px;
  font-size: 12px !important;
  border-radius: 3px !important;
  background: var(--su__searchapp-deepskyblue-light);
  -webkit-box-shadow: 0 0 0.48px rgba(var(--su__searchapp-black-rgba), 0.16) !important;
  box-shadow: 0 0 0.48px rgba(var(--su__searchapp-black-rgba), 0.16) !important;
  -webkit-animation: fadeintooltip ease-in-out 0.5s;
  animation: fadeIntooltip ease-in-out 0.5s;
  display: inline-block;
  left: 7px;
  top: 28px;
  max-width: 93%;
  white-space: inherit !important;
}

.su__sourceLabelSectionW-80 {
  width: 80%;
}

.su__IconsSectionW-20 {
  width: 20%;
  justify-content: end;
}

.su__savedResultBookmarkIcon svg path:hover {
  fill: var(--su__searchapp-deepskyblue-light);
}

.su__tooltipIconsOutline:focus-visible {
  outline: none;
}

.su__tabs_bookmarkList:focus {
  border-radius: 0;
  box-shadow: none;
}

.su__filters-button:focus,
.su__key-focus:focus,
.su__tabs:focus,
.su__filter-toggle:focus {
  border-radius: 0;
  box-shadow: none;
  border: none;
}

.su__rtl .su__resultSavedArabic {
  left: 34px !important;
  right: unset !important;
}

.su__rtl .su__bookmark_SavedResult_RTL {
  text-align: right !important;
}

.su__rtl .su__savedResultTooltipArabic {
  left: unset !important;
  right: 7px !important;
}

.su__rtl .su__filter-checkbox {
  margin-right: 0 !important;
}

.su__rtl .facet .su__arrow-down {
  margin-left: 9px;
}

.su__bookmark_SavedResult_opacity {
  opacity: 0.5;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.loading .dot {
  position: relative;
  width: 0.6em;
  height: 0.6em;
  margin: 0.5em;
  border-radius: 50%;
}

.loading .dot::before {
  position: absolute;
  content: '';
  width: 100%;
  height: 100%;
  background: inherit;
  border-radius: inherit;
  animation: wave 0.5s ease-out infinite;
}

.loading .dot:nth-child(1) {
  background: var(--su__searchapp-electricblue);
}

.loading .dot:nth-child(1)::before {
  animation-delay: 0.05s;
}

.loading .dot:nth-child(2) {
  background: var(--su__searchapp-paleblue);
}

.loading .dot:nth-child(2)::before {
  animation-delay: 0.1s;
}

.loading .dot:nth-child(3) {
  background: var(--su__searchapp-deepskyblue-light);
}

.loading .dot:nth-child(3)::before {
  animation-delay: 0.15s;
}

.loading .dot:nth-child(4) {
  background: var(--su__searchapp-deepskyblue-light);
}

.loading .dot:nth-child(4)::before {
  animation-delay: 0.2s;
}

.loading .dot:nth-child(5) {
  background: var(--su__searchapp-deepskyblue-light);
}

.loading .dot:nth-child(5)::before {
  animation-delay: 0.25s;
}

@keyframes wave {
  50%,
  75% {
    transform: scale(2.5);
  }

  80%,
  100% {
    opacity: 0;
  }
}

.su__gpt-preview-article-link {
  width: 346px;
  height: auto;
  background: var(--su__searchapp-white) 0% 0% no-repeat padding-box;
  box-shadow: 0 3px 30px var(--su__searchapp-silver-30);
  border-radius: 3px;
  opacity: 0;
  animation: rt-fade-in-down 0.1s ease-in-out forwards;
}

.su__citation_modal_container {
  padding: 15px;
  line-height: 20px;
}

.su__padding_bottom_17 {
  padding-bottom: 17px;
}

.su__article_href_dimension {
  width: auto;
  height: auto;
  color: var(--su__searchapp-deepskyblue-light);
  font-size: 14px;
  font-family: 'Montserrat';
  font-weight: 600;
  display: inline !important;
  word-break: break-word;
  overflow-wrap: break-word;
}

.su__open_href_svg {
  margin: 0;
  padding: 0;
}

.su__article_href_container {
  display: flex;
  margin-top: 8px;
}

.su__article_desc_dimensions {
  margin-top: 6px;
  width: auto;
  height: auto;
}

.su__fix_preview_bottom {
  bottom: 0;
  width: calc(100% - 10px);
  margin-left: 5px;
}

.su__transition_citation {
  transition: transform 0.5s;
  transform: translate(0, 100%);
}

.su__invisible_div {
  width: 346px;
  padding: 10px 0 10px 0;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 5px;
  z-index: 1;
}

.su__fadeIn_animation {
  animation: fadeIntooltip linear 0.2s;
}

.su__fadeOut_animation {
  animation: fadeOuttooltip linear 0.5s;
}

.su__href_mobile {
  color: var(--su__searchapp-deepskyblue-light);
  font-size: 14px;
  font-family: 'Montserrat';
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  font-weight: 600;
  text-decoration: underline;
  word-break: break-word;
  overflow-wrap: break-word;
}

.su__title_mobile {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  margin-top: 6px;
}

.su__citation_desc {
  font-size: 13px;
  font-family: 'Montserrat';
  word-break: break-word;
  overflow-wrap: break-word;
}

.su__heading_citation {
  font-size: 14px;
  font-family: 'Montserrat';
  font-weight: 600;
  color: var(--su__searchapp-deepskyblue-light);
}

.su__heading_container_citation {
  display: flex;
  justify-content: space-between;
  height: 15px;
  margin-bottom: 18px;
}

.su__article_links_mobile_view {
  height: auto;
  border-radius: 6px 6px 3px 3px;
}

.su__cross_svg {
  margin: 0;
}

.su__margin_top_15 {
  margin-top: 15px;
}

.su__padding-top-3px {
  padding-top: 3px;
}

.su__transition_in {
  transform: translate(0, 3%);
}

.su__line_clamp_2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.su__line_clamp_3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.su__cursor_pointer {
  cursor: pointer;
}

.su__citation_desc .highlight {
  color: rgba(var(--su__searchapp-black-rgba), 0.87);
  border-bottom: 1px solid var(--su__searchapp-deepskyblue-light);
}

.su__open_article_icon {
  margin-left: auto;
  line-height: 20px;
}

.su__right_pos_diamond {
  left: 21px;
}

.su__left_pos_diamond {
  right: 17px;
}

.su__ml-33 {
  margin-left: 33px;
}

.su_citation {
  color: var(--su__searchapp-darkgray);
  font-weight: 500;
  margin-left: 3px;
  position: relative;
  top: 6px;
  font-size: 13px;
  background: var(--su__searchapp-paleblue-cool) 0% 0% no-repeat padding-box;
  padding: 1px 6px 1px 6px;
  border-radius: 2px;
  cursor: pointer;
}

@keyframes rt-fade-in-down {
  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

.su__top_pos_diamond {
  top: -8px;
}

.su__align-items-start {
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
}

@media (max-width: 1023px) {
  .su__snippets_container {
    -webkit-text-size-adjust: 100%;
    -moz-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
    font-size: 13px !important;
  }
}

/* Css for pop ups -  */
.su__pr_14px {
  padding-right: 14px;
}

.su__pl_14px {
  padding-left: 14px;
}

.su__pt_14px {
  padding-top: 14px;
}

.su__pt_12px {
  padding-top: 12px;
}

.su__pb_14px {
  padding-bottom: 14px;
}

.su__p-ie-20px {
  padding-inline-end: 20px;
}

.su__padding-vertical-12px {
  padding-top: 12px;
  padding-bottom: 12px;
}

.su__mr-5px {
  margin-right: 5px;
}

.su__ml-5px {
  margin-left: 5px;
}

.su__mt-10px {
  margin-right: 10px;
}
.su__article-preview-modal {
  position: fixed;
  transform: translate(-50%, -50%);
  top: 50%;
  left: 50%;
  width: 633px;
}

.su-tr-0 {
  top: 0;
  right: 0;
}
.su__sso-loader-autocomplete {
  margin: auto;
  width: 30px;
  height: 30px;
  border: 5px solid #f3f3f3;
  border-top: 5px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.su__pr-5px {
  padding-right: 5px;
}
.su__modal-content {
  background: white;
  box-shadow: 0 14px 30px rgba(var(--su__searchapp-deepskyblue-light-rgba), 0.129);
  border-radius: 8px;
  overflow: auto;
  max-height: 550px;
  height: 420px;
}
.su__modal-header {
  display: flex;
  justify-content: space-between;
  background: var(--su__searchapp-lightgrey) 0% 0% no-repeat padding-box;
}
.su__modal-body {
  margin-top: 5px;
  margin-bottom: 15px;
  background: var(--su__searchapp-white) 0% 0% no-repeat padding-box;
  border-radius: 0 0 6px 6px;
}
.su__article-header {
  margin-top: 6px;
  margin-bottom: 6px;
  margin-left: 15px;
}
.su__article-title {
  margin-left: 10px !important;
  margin-right: 15px !important;
  padding-right: 7px !important;
  text-align: left;
  font: normal normal bold 16px / 22px Montserrat !important;
  letter-spacing: 0 !important;
  color: var(--su__searchapp-darkgray) !important;
  opacity: 1 !important;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: normal;
}
.su__mr-15 {
  margin-right: 15px;
}

.su__d-grid {
  display: grid;
}
.su__span-article {
  font: normal normal 600 14px / 18px Montserrat;
  letter-spacing: 0;
  color: var(--su__searchapp-darkgray);
}
.su__span-article-value {
  font: normal normal normal 12px/18px Montserrat !important ;
  letter-spacing: 0;
  color: var(--su__searchapp-black);
}
.su__mr-20 {
  margin-right: 20px;
}
.su__padding-article-preview {
  padding: 14px 10px 6px 0;
}

.su__m-b-5 {
  margin-bottom: 5px;
}

.su__border-bottom-grey {
  border-bottom: 1px solid rgba(var(--su__searchapp-deepskyblue-light-rgba), 0.129);
}

.su__loading-internal {
  transform: translate(-50%, -50%);
  top: 50%;
  left: 50%;
  position: fixed;
  z-index: 10000;
  display: flex;
}
.su__loading-internal .dot {
  position: relative;
  width: 1em;
  height: 1em;
  margin: 0.6em;
  border-radius: 50%;
}
.su__loading-internal .dot::before {
  position: absolute;
  content: '';
  width: 100%;
  height: 100%;
  background: inherit;
  border-radius: inherit;
  animation: wave 0.5s ease-out infinite;
}
.su__loading-internal .dot:nth-child(1) {
  background: var(--su__searchapp-rosybrown);
}
.su__loading-internal .dot:nth-child(1)::before {
  animation-delay: 0.05s;
}
.su__loading-internal .dot:nth-child(2) {
  background: var(--su__searchapp-lightblue-alt);
}
.su__loading-internal .dot:nth-child(2)::before {
  animation-delay: 0.1s;
}
.su__loading-internal .dot:nth-child(3) {
  background: var(--su__searchapp-turquoise);
}
.su__loading-internal .dot:nth-child(3)::before {
  animation-delay: 0.15s;
}
.su__loading-internal .dot:nth-child(4) {
  background: var(--su__searchapp-lightblue);
}
.su__loading-internal .dot:nth-child(4)::before {
  animation-delay: 0.2s;
}
.su__loading-internal .dot:nth-child(5) {
  background: var(--su__searchapp-deepskyblue-light);
}
.su__loading-internal .dot:nth-child(5)::before {
  animation-delay: 0.25s;
}
.su__h-100vh {
  height: 100vh;
}

@keyframes wave {
  50%,
  75% {
    transform: scale(2.5);
  }
  80%,
  100% {
    opacity: 0;
  }
}
.su__no-content-preview {
  position: fixed;
  transform: translate(-50%, -50%);
  top: 50%;
  left: 50%;
  width: 400px;
  height: 180px;
  background: white;
  border-radius: 8px;
  font-size: 20px;
  font-weight: 500;
  color: var(--su__searchapp-darkgray);
}
.su__mx-32px,
.su__rtl .su__rtlmx-32px {
  margin-left: 32px;
  margin-right: 32px;
}

.su__p_14px {
  padding: 14px !important;
}

.su__pb_22px {
  padding-bottom: 22px;
}

.su__mb-10px {
  margin-bottom: 10px;
}

.su__pt_22px {
  padding-top: 22px;
}

.su__pt_5px {
  padding-top: 5px;
}

#setFocusOnGPTResponse p {
  margin-top: 5px;
  margin-bottom: 5px;
}

.su__pt_8px {
  padding-top: 8px;
}

.su__pb_8px {
  padding-bottom: 8px;
}

#setFocusOnGPTResponse ul {
  all: revert;
  padding-inline-start: 1px;
  margin-top: 5px;
  margin-bottom: 5px;
  list-style-position: outside;
}

#setFocusOnGPTResponse ul li {
  line-height: 23px;
  margin-top: 6px;
}

#setFocusOnGPTResponse ul ol {
  margin-inline-start: 15px;
}

#setFocusOnGPTResponse ol ul {
  margin-inline-start: 15px;
}

.su__pt_10px {
  padding-top: 10px;
}

.su__pt_15px {
  padding-top: 15px;
}

#setFocusOnGPTResponse ol {
  all: revert;
  padding-inline-start: 0;
  margin-top: 5px;
  margin-bottom: 5px;
  list-style-position: outside;
}

#setFocusOnGPTResponse ol li {
  margin-inline-start: 1px;
  line-height: 23px;
  margin-top: 6px;
}

.su__pb_10px {
  padding-bottom: 10px;
}

.su__mr_20px {
  margin-right: 20px;
}

.su__mr_4px {
  margin-right: 4px;
}

.su__mr_10px {
  margin-inline-end: 10px;
}

.su__lh-24px {
  line-height: 24px;
}

.su__lh-28px {
  line-height: 28px;
}

.su__right_20px {
  right: 20px;
}

.su__mt-50px {
  margin-top: 50px;
}

.su__fontFamily_Montserrat {
  font-family: 'Montserrat';
}

.su__form_styles_disable {
  border: 1px solid var(--su__searchapp-gray);
  background: var(--su__searchapp-lightgray-14);
}

.su__form_styles_enable {
  border: 1px solid var(--su__searchapp-gray);
  background: var(--su__searchapp-white);
}

.su__opacity_pointThree {
  opacity: 0.3;
}

.su__opacity_one {
  opacity: 1;
}

.su__line-height_24px {
  line-height: 24px;
}

.su__feedback_pop_up_height {
  height: 130px !important;
}

.su__padding_top_28 {
  padding-top: 28px;
}

.su__advance_search_btn {
  width: 42px;
  height: 42px;
  border-radius: 6px;
  background-color: rgba(var(--su__searchapp-white-rgba), 0.2);
  border: none;
  cursor: pointer;
}

.su__advance_search_btn-web {
  /* width: 42px;
  height: 42px; */
  border-radius: 6px;
  background-color: rgba(var(--su__searchapp-white-rgba), 0.2);
  border: none;
  cursor: pointer;
}

.su__save-bookmarks {
  width: 42px;
  height: 42px;
  background-color: rgba(var(--su__searchapp-white-rgba), 0.2);
  border-radius: 6px;
}
.su__save-bookmarks-zendesk {
  width: 24px;
  height: 42px;
  background-color: transparent;
  border-radius: 6px;
}

.su_bookmarked_list {
  margin-left: 10px;
}

.su__form-block {
  max-width: 1162px;
}

.su_searchbar_box {
  width: 100%;
}

.su_searchbar_box_clicked {
  width: 90%;
  position: relative;
}

.su__bookmark_text {
  width: 66px;
  white-space: pre-wrap;
}

.su__mobile_setting_icon {
  width: 35px;
  height: 42px;
  background-color: rgba(var(--su__searchapp-white-rgba), 0.2);
  border-radius: 6px;
  margin-right: 12px;
  margin-left: 3px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.su__recent_search_text {
  font-size: 13px;
  color: var(--su__searchapp-gray);
  margin-top: 10px;
  margin-left: 10px;
  margin-bottom: 5px;
}

.su__recentSearch_result {
  margin-inline-start: 7px;
  font-size: 16px;
  color: var(--su__searchapp-darkgray);
}

.su__suggested_text {
  font-size: 13px;
  color: var(--su__searchapp-gray);
  margin-top: 12px;
  margin-left: 10px;
  margin-bottom: 6px;
}

.su__zendesk_wd_ht {
  width: 30px;
  height: 30px;
  border-radius: 6px;
  background-color: rgba(var(--su__searchapp-white-rgba), 0.2);
  border: none;
  margin-inline-end: 7px;
  margin-inline-start: 9px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.su__zendesk_bookmark_wd_ht {
  width: 30px;
  height: 30px;
  background-color: rgba(var(--su__searchapp-white-rgba), 0.2);
  border-radius: 6px;
  margin-inline-end: 7px;
}

.su__zendesk_searchbox {
  height: 30px;
  position: absolute;
  z-index: 9;
}

.su__search_elements {
  width: 100%;
  transition: width 0.2s ease;
}

.su__sb_clicked {
  position: absolute;
  width: calc(100% - -115px);
}

.su__sb_clicked_autocomplete {
  position: absolute;
  width: calc(100% - -130px);
}

.su__input-close-console {
  top: 5px;
  right: -5px;
}

.su__zendeskBox {
  position: absolute;
  z-index: 10;
  padding: 5px 10px 2px;
}

.su__grid_box_shadow {
  -webkit-box-shadow: 0 0 6px rgba(var(--su__searchapp-black-rgba), 0.16);
  -moz-box-shadow: 0 0 6px rgba(var(--su__searchapp-black-rgba), 0.16);
  box-shadow: 0 0 6px rgba(var(--su__searchapp-black-rgba), 0.16);
}

/* THIS CSS IS RESPONSIBLE FOR RESULT BOX DESIGN IN MOBILE VIEW */
.su__mobile_resultbox {
  top: 59px;
  width: 100vw;
  border-radius: 0;
  overflow-y: scroll;

  /* Allow vertical scrolling */
  -ms-overflow-style: none;

  /* Hide scrollbar on IE and Edge */

  /* scrollbar-width: none; */
  position: fixed;
  left: 0;
  bottom: 0;
}

/* Hide scrollbar for WebKit browsers (Chrome, Safari) */
.su__mobile_resultbox::-webkit-scrollbar {
  display: none;
}

.su__mobile_zendesk_resultbox {
  position: fixed;
  left: 0;
  top: 47px;
  width: 100vw;
  height: 100vh;
  overflow: scroll;
}

.su__suggestions-box_mobile {
  height: auto;
  overflow-x: hidden;
}

.su__close_sb {
  display: flex;
  align-items: center;
  margin-right: 15px;
}

/* Filter/facet section UI revamp */
.su__h-44px {
  height: 44px;
}

.su__line-height-19 {
  line-height: 19px;
}

.su__line-height-22 {
  line-height: 22px;
}

.su__w-5px {
  width: 5px;
}

.su__text-underline {
  text-decoration: underline;
}

.su__px-3_25 {
  padding-left: 20px;
  padding-right: 20px;
}

.su__pt-20px {
  padding-top: 20px;
}

.su__p-10 {
  padding: 10px;
}

.su__open-facet .su__arrow-down {
  top: 3px;
  -webkit-transition: all 0.4s;
  -o-transition: all 0.4s;
  transition: all 0.4s;
  -webkit-transform: rotate(-45deg);
  -ms-transform: rotate(-45deg);
  transform: rotate(-45deg);
}

.su__clear-filter {
  color: var(--su__searchapp-black);
  opacity: 0.14;
}

.su__bottom-buttons {
  background: var(--su__searchapp-white) 0% 0% no-repeat padding-box;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  font-size: 15px;
  line-height: 19px;
}

.su__console-btns {
  border: 1px solid var(--su__searchapp-deepskyblue-light);
  border-radius: 5px;
}

.su__apply-btn {
  background: var(--su__searchapp-deepskyblue-light);
  color: var(--su__searchapp-white);
  box-shadow: 0 0 7px var(--su__searchapp-deepskyblue-light);
}

.su__console-btn-active {
  background-color: var(--su__searchapp-deepskyblue-light);
  color: var(--su__searchapp-white);
  box-shadow: 0 0 7px var(--su__searchapp-deepskyblue-light);
}

.su__vertical-line {
  height: 34px;
  border: 1px solid var(--su__searchapp-gray);
  opacity: 0.51;
  width: 0;
}

/* .........edit-page-layout-css......... */
.su__border-top {
  border-top: 1px solid rgba(var(--su__searchapp-dimgray-alt-rgba), 0.46);
}

.su__border-bottom {
  border-bottom: 1px solid rgba(var(--su__searchapp-dimgray-alt-rgba), 0.46);
}

.su__border-light-gray {
  border: 1px solid var(--su__searchapp-lightgrey);
}

.su__opacity-46 {
  opacity: 0.46;
}

.su__editl-active-tab {
  color: var(--su__searchapp-deepskyblue-light) !important;
  border-bottom: 2px solid var(--su__searchapp-deepskyblue-light) !important;
  font-weight: 500;
}

.su__dim-gray {
  color: var(--su__searchapp-gray) !important;
}

.su__dark-gray {
  color: var(--su__searchapp-darkgray) !important;
}

.su__line-height-20 {
  line-height: 20px;
}

.su__flex-shrink-1 {
  -webkit-box-flex: -1;
  -ms-flex-shrink: -1;
  flex-shrink: 1;
}

.su__bright-blue {
  color: var(--su__searchapp-deepskyblue-light);
}

.su__sky-blue {
  color: var(--su__searchapp-deepskyblue-light);
}

.su__cb-blue {
  color: var(--su__searchapp-deepskyblue-light);
}

.su__royal-blue {
  color: var(--su__searchapp-deepskyblue-light);
}

.su__bg-bright-white {
  background: var(--su__searchapp-white) 0% 0% no-repeat padding-box;
}

.su__bg-p-gray {
  background: var(--su__searchapp-lightgray-14) 0% 0% no-repeat padding-box;
}

.su__box-shadow {
  box-shadow: 0 0 20px rgba(var(--su__searchapp-black-rgba), 0.16);
}

.su__box-shadow-bl-6 {
  box-shadow: 0 0 6px rgba(var(--su__searchapp-black-rgba), 0.16);
}

.su__radius-5p {
  border-radius: 5px;
}

.su__slide-line {
  border: 1px solid var(--su__searchapp-lightgrey);
  height: 2px;
  width: 100%;
  position: relative;
  bottom: 3px;
  background: var(--su__searchapp-lightgrey);
}

.su__bullet-none {
  list-style-type: none;
}
.su__summary-height-feed {
  height: 38px;
}
.su__editPglayout {
  width: calc(100% - 40px);
  height: 90vh;
  max-height: 695px;
  -webkit-animation: fadeindown 400ms both;
  animation: fadeInDown 400ms both;
}

.su__lr-arrows div {
  width: 23px;
  height: 23px;
  box-shadow: 0 3px 6px rgba(var(--su__searchapp-black-rgba), 0.16);
  background: var(--su__searchapp-white) 0% 0% no-repeat padding-box;
  z-index: 1;
  border-radius: 5px;
  border-top: 0;
  border-bottom: 0;
}

.su__lr-arrows.arrow-left div {
  box-shadow: 1px -6px 15px 7px rgba(var(--su__searchapp-black-rgba), 0.16);
}

.su__lr-arrows.arrow-right div {
  box-shadow: 1px 6px 15px 7px rgba(var(--su__searchapp-black-rgba), 0.16);
}

.su__lr-arrows {
  background: transparent;
}

.su__d-none-imp {
  display: none !important;
}

.su__radio-btn {
  width: 20px;
  height: 20px;
}

.radio-button-label input[type='radio'] {
  opacity: 0;
  position: absolute;
  right: 10px;
}

.su__radio-button-label {
  display: block;
  align-items: center;
  cursor: pointer;
  width: 20px;
  height: 20px;
  border-radius: 100%;
  position: relative;
}

.su__radio-button-label::before {
  content: '';
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid var(--su__searchapp-black);
  border-radius: 50%;
  transition: all 0.2s ease;
  position: absolute;
  top: 0;
  box-sizing: content-box;
}

input[type='radio']:checked + .su__radio-button-label::before {
  border-color: var(--su__searchapp-deepskyblue-light);
  background-color: var(--su__searchapp-white);
}

input[type='radio']:checked + .su__radio-button-label::after {
  content: '';
  width: 10px;
  height: 10px;
  background-color: var(--su__searchapp-deepskyblue-light);
  border-radius: 50%;
  position: absolute;
  inset: 13px auto auto 29px;
  top: 5px;
}

.su__rtl input[type='radio']:checked + .su__radio-button-label::after {
  inset: 13px 29px auto auto;
  top: 5px;
}

input[type='radio']:focus + .su__radio-button-label {
  box-shadow: 0 0 0 var(--su__searchapp-deepskyblue-light);
}

.su__mobile-facet {
  max-width: 100%;
  background-color: var(--su__searchapp-white);
}

.su__box-shadow-bl-2 {
  box-shadow: 0 0 2px rgba(var(--su__searchapp-black-rgba), 0.16);
}

.su__line-height-16 {
  line-height: 16px;
}

.su__padding-t-015 {
  padding-top: 2px;
}

.scrollinnerDiv::-webkit-scrollbar,
.filterlistscroll::-webkit-scrollbar,
.su__preselectedTab::-webkit-scrollbar,
.su__tagBox_collapse::-webkit-scrollbar,
.su__content-tab::-webkit-scrollbar {
  display: none;
}

.su__opacity-0 {
  opacity: 0;
}

/* gpt pop up css */
.su__showLeft-arrow::after {
  position: absolute;
  left: 0;
  top: 10px;
  width: 50px;
  height: 85%;
  pointer-events: none;
  box-shadow: inset 35px 35px 0 7px var(--su__searchapp-white-16);
  content: '';
  background: -webkit-linear-gradient(180deg, hsl(0deg 0% 83.86%) 0%, hsl(0deg 0% 100%) 100%);
  filter: blur(14px);
  z-index: 1;
}

.su__showRight-arrow::before {
  z-index: 2;
  pointer-events: none;
  box-shadow: inset 35px 35px 0 7px var(--su__searchapp-white-16);
  position: absolute;
  right: 0;
  top: 18px;
  width: 50px;
  height: 35px;
  content: '';
  background: -webkit-linear-gradient(180deg, hsl(0deg 0% 83.86%) 0%, hsl(0deg 0% 100%) 100%);
  filter: blur(10px);
}

.su__dimensions-check-box {
  height: 18px;
  width: 18px;
}

.advertisement-box-border {
  box-shadow: 0 0 6px rgba(var(--su__searchapp-black-rgba), 0.16);
  border-radius: 4px;
  overflow: hidden;
}

.su__tooltip_wrap {
  padding-left: 5px;
  padding-right: 5px;
  overflow-wrap: break-word;
  word-wrap: break-word;

  /* For older browser compatibility */
  display: flex;
  justify-content: center;
  align-items: center;
  white-space: nowrap;
}

.su__relate_header {
  font-size: 16px;
  line-height: 26px;
  font-weight: 500;
  color: var(--su__searchapp-darkgray);
}

.su__cs_grid_bg {
  background: var(--su__searchapp-lightgray-14);
}

/* custom dropdown css starts here  */

.su__custom-dropdown-container {
  display: flex;
  width: max-content;
  position: relative;
}

.su__dropdown_modal_content {
  border: 1px solid var(--su__searchapp-white);
  box-shadow: 0 0 8px rgba(var(--su__searchapp-black-rgba), 0.3);
  border-radius: 5px;
  position: absolute;
  background: var(--su__searchapp-white);
  top: 32px;
  max-height: 274px;
  padding: 6px;
  max-width: 161px;
  overflow-wrap: break-word;
  overflow-y: auto;
}

.su__dropdown-button {
  display: flex;
  padding: 5px 40px 4px 6px;
  border: 1px solid var(--su__searchapp-lightgrey);
  border-radius: 4px;
  background: var(--su__searchapp-lightgray-14);
}

.su__sorting-btn-min-width {
  min-width: 161px;
}

.su__dropdown-text-trucation {
  max-width: 87px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.su__rtl .su__dropdown-padding {
  padding: 5px 6px 4px 40px;
}

.su__arrow_position {
  position: absolute;
  top: 10px;
  right: 10px;
  left: unset;
}

.su__rtl .su__arrow-align-rtl {
  left: 8px;
  right: unset;
}

.su__dropdown-items {
  font: normal normal normal 11px/14px Montserrat;
  padding: 3px;
  width: 100%;
  margin: 2px 0;
}

.su__dropdown-items:hover,
.su__dropdown-items:focus {
  background: var(--su__searchapp-deepskyblue-light);
  color: var(--su__searchapp-white);
  outline: none;
}

.su__language-dropdown-min-width {
  min-width: 161px;
}

.su__sorting-dropdown-min-width {
  min-width: 161px;
}

.su__min_max_width-16px {
  min-width: 16px;
  max-width: 16px;
}

.su__noresult-btn {
  background: var(--su__searchapp-deepskyblue-light);
  color: var(--su__searchapp-white);
  padding: 9px 78px;
  border-radius: 4px;
}

.su__no_result_container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 80vh;
  flex-direction: column;
}

.su__counter-transform {
  transform: matrix(-1, 0, 0, -1, 0, 0) !important;
}

.su__counter_transform-down-arrow {
  right: 100px;
  transform: rotate(315deg);
}

.su__merge_versioning ::-webkit-scrollbar {
  width: 2px;

  /* Vertical scrollbar width */
  background: var(--su__searchapp-lightpink);
}

.su__merge_versioning ::-webkit-scrollbar-track {
  background: var(--su__searchapp-white);

  /* Set the background of the scrollbar track */
}

.su__merge_versioning ::-webkit-scrollbar-thumb {
  background: rgba(var(--su__searchapp-black-rgba), 0.16);

  /* Set the color of the scrollbar thumb */
  border-radius: 10px;

  /* Optionally add rounded corners to the thumb */
  scrollbar-width: thin;
}

/* Webkit browsers */

.su__custom-dropdown-container ::-webkit-scrollbar {
  width: 2px;

  /* Vertical scrollbar width */
  background: var(--su__searchapp-lightpink);
}

.su__custom-dropdown-container ::-webkit-scrollbar-track {
  background: var(--su__searchapp-white);

  /* Set the background of the scrollbar track */
}

.su__custom-dropdown-container ::-webkit-scrollbar-thumb {
  background: rgba(var(--su__searchapp-black-rgba), 0.16);

  /* Set the color of the scrollbar thumb */
  border-radius: 10px;

  /* Optionally add rounded corners to the thumb */
  scrollbar-width: thin;
}

.su__popup_input_text {
  font-size: 14px;
  line-height: 20px;
  font-weight: 600;
  color: var(--su__searchapp-darkgray);
}

.su__popup_placeholder::placeholder {
  color: var(--su__searchapp-darkgray);
  opacity: 0.39;
}
.su__summarization {
  width: 22px;
  height: 22px;
  box-shadow: inset 0 0 6px rgba(var(--su__searchapp-black-rgba), 0.16);
  border-radius: 2px;
  opacity: 1;
  align-items: center;
  display: flex;
  justify-content: center;
  cursor: pointer;
  bottom: 2px;
  position: relative;
  padding: 0;
}

.su__summarization_hover-bg:hover {
  background: #1770d4 0% 0% no-repeat padding-box;
}
.su__popup {
  position: fixed;
  top: 0;
  right: 0;
  width: 30%; /* Cover 40% of the screen */
  height: 100%;
  background-color: var(--su__searchapp-white);
  box-shadow: 0 3px 20px rgba(var(--su__searchapp-black-rgba), 0.16);
  z-index: 11;
  transition: transform 0.3s ease-in-out;
  transform: translateX(100%); /* Start off-screen */
}

.su__popup.open {
  transform: translateX(0); /* Slide in from the right */
}
.su__popup-header {
  padding: 15px 15px 0 15px;
  text-align: left;
  display: flex;
  justify-content: space-between;
}
.su__popup-source-label {
  color: var(--su__searchapp-white);
  background-color: var(--su__searchapp-deepskyblue-light);
  border-radius: 5px;
  padding: 5px;
  font-weight: 500;
}
.su__popup-content {
  background: var(--su__searchapp-lightgray-14) 0% 0% no-repeat padding-box;
  padding-bottom: 12px;
  padding-left: 10px;
  padding-right: 7px;

  /* overflow: auto;
  max-height: 250px; */
}
.su__text-popup-title {
  padding: 5px 15px 0 15px;
  font-size: 15px;
  font-weight: 500;
  color: var(--su__searchapp-darkgray);
}
.su__href-txt-popup {
  padding: 0 110px 0 15px;
  font-size: 12px;
}
.su__popup-metadata {
  padding: 0 15px 0 15px;
}
.su__popup-content-body {
  padding: 15px;
  background: var(--su__searchapp-white) 0% 0% no-repeat padding-box;
  border-radius: 4px;

  /* max-height: calc(100vh - 35px); */
  opacity: 1;
  overflow-y: auto;
  scrollbar-color: var(--su__searchapp-lightgrey) transparent;
  scrollbar-width: thin;
}
.su__summary-border-no-result {
  background: var(--su__searchapp-white) 0% 0% no-repeat padding-box;
  box-shadow: inset 0 0 20px rgba(var(--su__searchapp-deepskyblue-light-rgba), 0.2),
    0 1px 4px rgba(var(--su__searchapp-black-rgba), 0.14);
  border: 1px solid var(--su__searchapp-paleblue-cool);
  border-radius: 4px;
  opacity: 1;
  color: var(--su__searchapp-darkgray);
  font-size: 13px;
  padding-bottom: 12px;
  padding-left: 10px;
  padding-top: 12px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 20px;
  text-align: left !important;
}
.su__summary-border {
  border: 1px solid var(--su__searchapp-lightgrey);
}
.su__summary-popup {
  margin-left: 7px;
  letter-spacing: 0;
  font-size: 16px;
  font-family: 'Montserrat';
  font-weight: 600;
}
.su__popup-summary {
  margin-top: 20px;
  margin-bottom: 25px;
  text-align: left;
  line-height: 24px;
  color: var(--su__searchapp-darkgray);
  font-size: 14px;

  /* padding-left: 15px; */
  padding-right: 3px;
}
.su__popup-summary-positive {
  margin-left: 11px;
  margin-right: 11px;
}
.su__mb-12 {
  margin-bottom: 12px;
}
.su__thanku-feed {
  display: flex;
  align-items: center;
  background: var(--su__searchapp-white) 0% 0% no-repeat padding-box;
  box-shadow: 0 0 20px rgba(var(--su__searchapp-black-rgba), 0.16);
  border-radius: 4px;
  padding: 7px;
}
.su__summary-thank-text {
  font-size: 12px;
  letter-spacing: 0;
  color: var(--su__searchapp-darkgray);
  margin-left: 5px;
  margin-right: 10px;
}

.su__popup-unable {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
  color: var(--su__searchapp-darkgray);
  font-weight: 500;
  font-size: 13px;
  background: var(--su__searchapp-white) 0% 0% no-repeat padding-box;
  box-shadow: inset 0 0 20px rgba(var(--su__searchapp-deepskyblue-light-rgba), 0.2),
    0 1px 4px rgba(var(--su__searchapp-black-rgba), 0.14);
  border: 1px solid var(--su__searchapp-paleblue-cool);
  border-radius: 4px;
  padding: 12px 15px 12px 10px;
  align-items: center;
}
.su__margin-10px {
  margin: 10px;
}
.su__margin-20px {
  margin: 20px;
}

/* custom dropdown css ends here */
.su__kh_scTips_head {
  text-align: right;
}

.su__khTips {
  padding-left: 35px !important;
}

.su__kh_st-padding-right {
  padding-right: 96px;
}

/* ................Feature Snippet UI.................... */
.su__bg-softblack {
  background: rgba(var(--su__searchapp-black-rgba), 0.78) 0% 0% no-repeat padding-box;
}

.su__featured-btn:hover svg path,
.su__fs-redirect:hover path {
  fill: var(--su__searchapp-darkorange);
}

.su__fs-img-container {
  border-radius: 4px;
}

.su__fs-img-container .su__img-featured {
  transition: transform 0.3s ease;
}

.su__fs-img-container:hover img,
.su__fs-img-container:focus-within img {
  transform: scale(1.1);
}

.su__fs-redirect {
  position: absolute;
  left: 0;
  top: 0;
  background: rgba(var(--su__searchapp-black-rgba), 0.78) 0% 0% no-repeat padding-box;
  z-index: 1;
  border-radius: 4px;
}

.su__fs-play-btn {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  margin: auto;
  z-index: 1;
  padding: 0;
  fill: var(--su__searchapp-white);
}

.su__fs-multimedia-main .su__fs-media {
  width: 234px;
  height: 150px;
  padding-bottom: 16px;
  flex: 0 0 auto;
}

.su__fs-media-box {
  overflow-x: auto;
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: none;

  /* Firefox */
  -ms-overflow-style: none;

  /* IE and Edge */
}

.su__fs-media-box::-webkit-scrollbar {
  display: none;

  /* Chrome, Safari, and Opera */
}

.snippetArrows {
  position: absolute;
  top: 43%;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.3s ease;
  background: var(--su__searchapp-white) 0% 0% no-repeat padding-box;
  box-shadow: 0 3px 6px rgba(var(--su__searchapp-black-rgba), 0.16);
  border-radius: 5px;
  opacity: 1;
  z-index: 2;
  border: 1px solid var(--su__searchapp-white);
  right: 1px;
}

.snippetArrows:last-child {
  right: -14px;
}

.su__blur-effect-btn {
  position: absolute;
  top: 40%;
  transform: translateY(-50%);
  width: 50px;
  height: 120px;
  background-color: var(--su__searchapp-lightgrey);
  filter: blur(8px);
  z-index: 1;
}

.su__blur-effect-btn-left {
  left: -15px;
}

.su__blur-effect-btn-right {
  right: -15px;
}

.su__bottom-1px {
  bottom: 1px;
}

.su__top-1px {
  top: 1px;
}

.su__top-2px {
  top: 2px;
}

.su__top-3px {
  top: 3px;
}

.su__fs-show-steps {
  display: flex;
  align-items: center;
  color: var(--su__searchapp-deepskyblue-light);
}

.su__fs-show-steps :nth-of-type(2) {
  border-right: 2px solid var(--su__searchapp-deepskyblue-light);
  border-top: 2px solid var(--su__searchapp-deepskyblue-light);
}

.su__fs-steps {
  padding: 10px 20px 10px 13px;
}

.su__fs-steps-align-icon {
  position: absolute;
  bottom: 0;
  right: 0;
}

.su__fs-steps .su__featured-thumbnail {
  max-width: 234px;
  min-width: 200px;
  height: 173px;
}

.su__fs-steps-media .su__img-featured {
  width: 234px;
  height: 173px;
  max-width: 234px;
  border-radius: 4px;
}

.su__fs-stepsmobile-media {
  border-radius: 4px;
  width: 346px;
  height: 196px;
}

.su__fs-stepsmobile-media .su__img-featured {
  width: 346px;
  height: 196px;
  border-radius: 4px;
  align-self: flex-end;
}

.su__fs-stepsmobile .su__fs-show-steps {
  top: unset;
}

.su__fs-QA {
  box-shadow: inset 0 0 40px var(--su__searchapp-silver-16),
    0 0 6px rgba(var(--su__searchapp-black-rgba), 0.16);
  border: 1px solid var(--su__searchapp-lightblue-alt);
  border-radius: 4px;
}

.su__wrapping-data {
  display: inline-block;
  max-width: 100%;

  /* Adjust this value as needed */
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
}

.su__fs-QA .su__featured-thumbnail {
  max-width: 234px;
  height: 146px;
}

.su__fs-QA .su__img-featured {
  width: 234px;
  height: 146px;
  border-radius: 4px;
}

.su__fs-QA .su__featured-thumbnail .su__feedback-icon-above {
  display: flex;
  align-items: center;
  position: absolute;
  bottom: 0;
  padding: 15px;
  margin: 8px;
}

.su__fs-QA .su__href-txt {
  padding: 1px 0 10px 0;
}

.su__fsQA-mobile-media .su__img-featured {
  width: 346px;
  height: 198px;
  border-radius: 4px;
}

.su__fsQA-icons {
  position: absolute;
  bottom: 15px;
  right: 15px;
}

.su__fs-mediaicons {
  position: absolute;
  bottom: 0;
}

.su__more-results {
  border: 1px solid var(--su__searchapp-deepskyblue-light);
  border-radius: 5px;
  padding: 3px 9px;
  color: var(--su__searchapp-deepskyblue-light);
}

.su__source-label {
  background: var(--su__searchapp-white) 0% 0% no-repeat padding-box;
  border: 1px solid var(--su__searchapp-lightgrey);
  border-radius: 5px;
  color: var(--su__searchapp-gray);
  padding: 2px 2px 2px 4px;
}

.su__result-title {
  font: normal normal 500 17px / 22px Montserrat;
}

.su__color-grey {
  color: var(--su__searchapp-gray);
}

.su__result-tile-padding {
  padding-left: 41px;
  padding-right: 36px;
}

.su__rtl .su__mr-mt-10 {
  margin-right: 45px;
  margin-left: 11px;
}

.su__mr-18px {
  margin-right: 18px;
}

.su__PreviewTile {
  margin-top: 28px;
  padding: 12px;
}

.su__rtl .su__PreviewTile {
  margin-top: 18px;
}

.thxClose .multiform-step-two {
  margin-top: 10px;
}

.su__padding-modal-singleStep {
  padding: 20px 14px 20px 14px;
}

.su__feedback-modal.su__padding-modal-multiStep {
  padding: 10px 14px 20px 14px;
}

.su__thankspopUpPadding {
  padding: 10px 0 10px 0;
}

.su__feedback-radio-btns:focus + label::before {
  border-color: var(--su__searchapp-deepskyblue-light) !important;
  filter: drop-shadow(0 0 2px var(--su__searchapp-deepskyblue-light));
}

@media (min-width: 767px) and (max-width: 1140px) {
  .su__white-space-unset {
    white-space: unset;
  }
}

@media (max-width: 1200px) {
  .su__result-tile-padding {
    padding-left: 36px;
    padding-right: 26px;
  }
}

@media (max-width: 1024px) {
  .su__sort-filter-postion {
    top: calc(50%);
  }

  .su__close-icon-filter {
    position: absolute;
    right: 10px;
    top: 10px;
  }

  /* .su__popup-summary-width{
    width: 50% !important;
    
  } */
  .su__popup {
    width: 50%;
  }

  /* .su__popup-summary-content-max-height {
    max-height: calc(100vh - 15%);
  } */
  .su__result-per_page_btn {
    font-size: 14px;
  }

  .su__allSelected-notshow.su__ipadview-block {
    padding-left: 0;
  }

  .su__filter-appear {
    display: block;
  }

  .su__result-tile-padding {
    padding-left: 26px;
    padding-right: 26px;
  }

  .su__filter-appear .su__clear-filters {
    margin: auto 0;
  }

  .su__PreviewTile {
    margin-top: 15px;
  }

  .su__search-facet-icon {
    margin: 5px 0 0;
  }

  .su__search-facet .su__search-facet-input {
    padding: 5px 24px 5px 24px !important;
  }
}

.su__rtl .su__close_sb {
  transform: rotate(180deg);
  margin-right: 0;
  margin-left: 15px;
}

.su__scTips-header-color {
  color: var(--su__searchapp-deepskyblue-light);
}

.su__scTips-closeBtn-section {
  display: flex;
  align-items: center;
  justify-content: center;
}

.su__scTips-closeBtn {
  background: var(--su__searchapp-deepskyblue-light) 0% 0% no-repeat padding-box;
  width: calc(100% - 40px);
  height: 36px;
}

.su__border-r-4 {
  border-radius: 4px;
}

.su__setting-br {
  border-radius: 4px 0 0 4px;
}

.su__rtl .su__setting-br {
  border-radius: 0 4px 4px 0;
}

.su__setting-bs {
  box-shadow: 0 0 20px rgba(var(--su__searchapp-deepskyblue-light-rgba), 0.48);
}

.su__page_label {
  font-size: 12px;
  font-weight: 400;
  color: var(--su__searchapp-darkgray);
}

.su__rtl .su__nested-ul {
  padding-left: 0;
  padding-right: 40px;
  margin: 0 0 0 0;
}

.su-draggableFilters {
  padding-left: 0;
  padding-right: 40px;
}

.su__rtl .su-draggableFilters {
  padding-left: 40px;
  padding-right: 0;
}

.su__mt-24 {
  margin-top: 24px;
}

.su__searchPageFeedback {
  background: var(--su__searchapp-deepskyblue-light);
  height: 54px;
  width: 54px;
  border-radius: 100%;
  box-shadow: 0 0 20px rgba(var(--su__searchapp-sapphire-rgba), 0.69);
  margin-right: 32px;
  margin-bottom: -32px;
}

.su__max-width-preview-title {
  max-width: calc(100% - 200px);
}

@media (max-width: 767px) {
  .su__searchPageFeedback {
    margin-bottom: 0;
  }
  .su__max-width-preview-title {
    max-width: calc(100% - 20px);
  }

  .su__mobile_resultbox::-webkit-scrollbar {
    width: 4px;
    background: var(--su__searchapp-red);
    height: 164px;
    border-left: 5px solid var(--su__searchapp-white);
    display: block;
  }

  .su__mobile_resultbox::-webkit-scrollbar-thumb {
    background: var(--su__searchapp-gray);
    border-radius: 5px;
  }
  .su__popup {
    width: 100%;
  }
  .su__mobile_resultbox::-webkit-scrollbar-track {
    background: var(--su__searchapp-white);
    border-radius: 5px;
  }

  .su__col-rev-mb {
    flex-direction: column-reverse;
  }

  .su__padding_15px-mb {
    padding: 15px;
  }

  .su__pt-unset-mb {
    padding-top: unset;
  }

  .su__ml-unset-mb {
    margin-left: unset;
  }

  .su__pb-12px-mb {
    padding-bottom: 12px;
  }

  .su__mb-8px-mb {
    margin-bottom: 8px;
  }

  .su__iframe-modal .su__bookmark-inner {
    height: 230px;
  }

  .su__pagerating-row,
  .su_feedback_form {
    width: calc(100% - 20px);
  }

  .su__modal-inner.su__thanks-modal {
    width: calc(100% - 20px);
    height: 168px;
    border-radius: 4px;
  }

  .did-you-mean {
    padding: 0 13px 0 13px;
  }

  .su__slider_btn_position {
    top: 6px;
  }

  .su__showLeft-arrow::after {
    position: absolute;
    left: 0;
    top: 1px;
    width: 50px;
    height: 85%;
    pointer-events: none;
    box-shadow: inset 35px 35px 0 7px var(--su__searchapp-white-16);
    content: '';
    background: -webkit-linear-gradient(180deg, hsl(0deg 0% 83.86%) 0%, hsl(0deg 0% 100%) 100%);
    filter: blur(14px);
    z-index: 1;
  }

  /* .su__popup-summary-content-max-height {
    max-height: calc(100vh - 25%);
  } */

  .su__showRight-arrow::before {
    z-index: 2;
    pointer-events: none;
    box-shadow: inset 35px 35px 0 7px var(--su__searchapp-white-16);
    position: absolute;
    right: 0;
    top: 1px;
    width: 50px;
    height: 35px;
    content: '';
    background: -webkit-linear-gradient(180deg, hsl(0deg 0% 83.86%) 0%, hsl(0deg 0% 100%) 100%);
    filter: blur(10px);
  }

  .su__tabs-mobile {
    margin-inline-end: 24px;
    padding-left: 0;
    padding-right: 0;
  }

  .su__mobile_component_none {
    display: none;
  }

  .su__mobile_component_show {
    display: block;
  }

  .su__mobile_component_flex {
    display: flex;
  }

  .su__search_section {
    padding-top: 8px;
    padding-bottom: 8px;
  }

  .su__search-forms {
    padding-inline-end: 0;
  }

  .su__input-search {
    padding: 0 48px 0 40px;
  }

  .su__input-search-box {
    padding: 0 48px 0 40px;
  }

  .su__mobile_head_sb {
    display: flex;
    width: 100%;
  }

  .su__head_searchbox {
    width: 100%;
  }

  .su__head_setting {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-inline-start: -1px;
  }

  .footerSection {
    text-align: center;
  }

  .su__my-sm-1 {
    margin-top: 5px;
    margin-bottom: 5px;
  }

  .su__mx-sm-1 {
    margin-right: 5px;
    margin-left: 5px;
  }

  .su__mt-sm-1 {
    margin-top: 5px;
  }

  .su__mb-sm-1 {
    margin-bottom: 5px;
  }

  .su__mb-sm-2 {
    margin-bottom: 10px;
  }

  .su__my-sm-0 {
    margin-top: 0;
    margin-bottom: 0;
  }

  .su__mx-sm-0 {
    margin-right: 0;
    margin-left: 0;
  }

  .su__mt-sm-0 {
    margin-top: 0;
  }

  .su__mb-sm-0 {
    margin-bottom: 0;
  }

  .su__pb-sm-20 {
    padding-bottom: 20px;
  }

  .su__m-sm-0 {
    padding: 0;
  }

  .su__px-sm-1 {
    padding-right: 5px;
    padding-left: 5px;
  }

  .su__py-sm-1 {
    padding-top: 5px;
    padding-bottom: 5px;
  }

  .su__pt-sm-1 {
    padding-top: 5px;
  }

  .su__p-sm-0 {
    padding: 0;
  }

  .su__pb-sm-1 {
    padding-bottom: 5px;
  }

  .su__px-sm-0 {
    padding-right: 0;
    padding-left: 0;
  }

  .su__py-sm-0 {
    padding-top: 0;
    padding-bottom: 0;
  }

  .su__pt-sm-0 {
    padding-top: 0;
  }

  .su__pb-sm-0 {
    padding-bottom: 0;
  }

  .su__pb-sm-4 {
    padding-bottom: 2.4em;
  }

  .su__form-control {
    padding: 7px 9px;
    height: 36px;
  }

  .su__px-sm-2 {
    padding-right: 8px;
    padding-left: 8px;
  }

  .su__py-sm-2 {
    padding-top: 8px;
    padding-bottom: 8px;
  }

  .su__advance-block .su__mob-search-iner {
    display: none;
  }

  .su__gutters {
    width: 100vw;
  }

  .su__grid-content .su__list-items:nth-child(odd),
  .su__grid-content .su__list-items:nth-child(even) {
    margin: 10px 0;
  }

  .su__wsm-100 {
    width: 100%;
  }

  .su__w-sm-100,
  .su__knowledgeGraph-show .su__content-view,
  .su__knowledgeGraph-show .su__knowledgeGraph-block {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 100%;
    flex: 0 0 100%;
    max-width: 100%;
  }

  .su__knowledgeGraph-show .su__grid-content .su__list-items,
  .su__grid-content .su__list-items {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 100%;
    flex: 0 0 100%;
    max-width: 100%;
  }

  .su__knowledgeGraph-show .su__grid-content .su__list-items:nth-child,
  .su__grid-content .su__list-items:nth-child(even) {
    margin-right: 0;
  }

  .su__result-per_page_btn {
    font-size: 14px;
  }

  .su__tooltip {
    z-index: 9;
  }

  .su__sm-w-100 {
    width: 100%;
    max-width: 100%;
  }

  .su__display-none-767px {
    display: none !important;
  }

  .su__grid-content .su__multi-gridview {
    display: none;
  }

  .su__m-lang-block {
    padding: 14px 20px 0 20px;
  }

  .su__sm-w-100,
  .su__list-item-text {
    width: 100%;
    max-width: 100%;
  }

  .su__sm-h-100 {
    max-height: 100%;
    max-width: unset;
    min-width: unset;
    min-height: unset;
  }

  .su__iframe-modal {
    width: calc(100% - 40px);
    max-height: 341px;
  }

  .su__mobile-child-block {
    width: calc(100% / 4);
    padding: 0 2px;
  }

  .su__content-gutter {
    display: block;
    width: 100%;
  }

  .su__grid-content .su__img-featured {
    width: 100%;
  }

  .swapFilterLeft {
    padding-bottom: 15px;
  }

  .su__skip-link {
    font-size: 12px;
    padding: 9px 15px;
  }

  /************** Center align the react GPT widget starts ***************/
  .su__typing-animation {
    padding: 7px 10px;
  }

  .su__noresult_text_color {
    font-size: 12px;
  }

  .su__loader_snippet {
    margin: 0 !important;
  }

  .loader-text {
    font-size: 12px;
    margin: 0 !important;
  }

  .skeleton-box:last-child {
    margin-bottom: 0;
  }

  .skeleton-box {
    height: 8px;
  }

  .article-links {
    flex-direction: column;
    padding-left: 0;
    margin-top: 2px;
  }

  .article-links .links-list li {
    padding-left: 0;
  }

  .article-links > span {
    font-size: 10px;
    font-weight: 500;
  }

  .article-links .links-list a {
    padding: 4px 10px;
    font-size: 10px;
  }

  .article-links::before {
    margin-top: 0;
    width: 100%;
    left: 0;
  }

  .article-links button span {
    font-size: 10px;
  }

  .retry-section p {
    line-height: 16px;
    margin-right: 5px;
  }

  .retry-section {
    min-height: auto;
  }

  .su__search_feedback_modal {
    width: calc(100% - 40px);
    z-index: 10;
  }

  .su__emoji_border {
    width: 41px;
    height: 41px;
    border: 1px solid var(--su__searchapp-lightgrey);
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 35px;
  }

  .su__emoji {
    width: 25px;
    height: 25px;
    background-size: 100%;
  }

  .su__modal-inner {
    width: calc(100% - 20px);
    max-width: 100%;
    background-color: var(--su__searchapp-white);
    border-radius: 8px;
  }

  .searchTips-sidenav {
    max-height: 560px;
    height: 100%;
    width: calc(100% - 40px);
    max-width: unset;
  }

  .su__bookmark-save:disabled {
    background-image: -webkit-gradient(
      linear,
      right top,
      left top,
      from(var(--su__searchapp-lightgrey)),
      to(var(--su__searchapp-lightgrey))
    );
    background-image: -o-linear-gradient(
      right,
      var(--su__searchapp-lightgrey),
      var(--su__searchapp-lightgrey)
    );
    background-image: linear-gradient(
      to left,
      var(--su__searchapp-lightgrey),
      var(--su__searchapp-lightgrey)
    );
    color: var(--su__searchapp-white);
    cursor: default;
    padding: 9px 0;
  }

  .su__bookmark-save:enabled {
    background-image: -webkit-gradient(
      linear,
      right top,
      left top,
      from(var(--su__searchapp-lightgrey)),
      to(var(--su__searchapp-lightgrey))
    );
    background-image: -o-linear-gradient(
      right,
      var(--su__searchapp-lightgrey),
      var(--su__searchapp-lightgrey)
    );
    background-image: linear-gradient(
      to left,
      var(--su__searchapp-lightgrey),
      var(--su__searchapp-lightgrey)
    );
    color: var(--su__searchapp-white);
    cursor: default;
    padding: 9px 0;
  }

  .su__no_bookmark_min_height {
    min-height: 336px;
  }

  .su__no_bookmark_padding {
    padding: 0 14px 30px 14px;
  }

  .su__bookmark-active {
    background: var(--su__searchapp-deepskyblue-light);
    padding: 9px 0;
  }

  .su__bookmark-active:disabled {
    background: var(--su__searchapp-lightblue-alt);
    pointer-events: none;
    padding: 9px 0;
  }

  .su__no_bookmark_svg {
    padding: 10px 0 30px 0;
  }

  .su__overflow_hidden {
    overflow: hidden;
    height: 230px;
  }

  .su__bookmark-ul {
    margin: 0;
    height: auto;
    overflow-y: auto;
    margin-bottom: 0;
    max-height: 260px;
    min-height: 260px;
  }

  .su__sm-h-336 {
    height: 336px;
  }

  .su__sm-mt-0 {
    margin-top: 0;
  }

  .su__feedtext-area-gpt {
    border: 1px solid var(--su__searchapp-gray);
    border-radius: 5px;
    width: calc(100% - 29px) !important;
    height: 177px !important;
    font-size: 12px;
    padding: 10px;
    resize: none;
    margin-left: 14px;
    margin-right: 14px;
  }

  .su__btn-gpt {
    color: var(--su__searchapp-white);
    border: 1px solid var(--su__searchapp-deepskyblue-light);
    border-radius: 3px;
    width: calc(100% - 29px);
    height: 36px;
    font-size: 14px;
    background: var(--su__searchapp-deepskyblue-light) 0% 0% no-repeat padding-box;
  }

  .su__thanks_min_width {
    min-width: max-content;
  }

  .su__mr-mt-10 {
    margin-right: 0;
    margin-left: 0;
    margin-top: unset;
  }

  .su__rtl .su__mr-mt-10 {
    margin-right: 0;
  }

  .su__mb-pr-14 {
    padding-right: 14px;
  }

  .su__mob-mr-0 {
    margin-right: 0;
  }

  .su__rtl .su__mob-ar-mr-16 {
    margin-right: 16px;
  }

  .su__rtl .su__mob-ar-pr-0 {
    padding-right: 0;
  }

  .su__rtl .su__mob-ar-pl-14 {
    padding-left: 14px;
  }

  .su__cancel {
    font-size: 17px;
  }

  .su__new-padding {
    font-size: 12px;
  }

  .su__padding-rl-0 {
    padding-left: 0;
    padding-right: 0;
  }

  .su__result_tile_mobile {
    flex: 0 0 100%;
    max-width: 100%;
  }

  .su__px-7px {
    padding-left: 7px;
    padding-right: 7px;
  }

  .su__fs-img-container .su__fs-feedback-btns {
    display: flex;
  }

  .su__fs-img-container .su__fs-redirect,
  .su__fs-img-container:hover .su__fs-redirect {
    display: none;
  }

  .su__mob-advance-text {
    width: 80px;
    margin: auto;
  }

  .su__ribbon-title {
    width: fit-content;
  }

  .su__advance-blocks {
    width: auto;
  }

  .su-editMode .su__mob-txt {
    width: 100%;
    text-align: center;
  }

  .su__grid-content .su__list-items {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 48.6%;
    flex: 0 0 48.6%;
    max-width: 48.6%;
    margin: 0 15px 15px 0;
  }

  .su__knowledgeGraph-show .su__content-view {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 100%;
    flex: 0 0 100%;
    max-width: 100%;
  }

  .su__knowledgeGraph-show .su__knowledgeGraph-block {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 100%;
    flex: 0 0 100;
    max-width: 100%;
  }

  .su__grid-content .su__list-items .su__list-item-title {
    margin-top: 2px;
    flex-direction: column;
    align-items: stretch;
  }

  .su__FeaturedSnippet .su__video-thumbnail-play {
    width: 25px;
    height: 25px;
  }

  .su__mr-0-mobile {
    margin-right: 0;
  }

  .su__pl-20px {
    padding-left: 20px;
  }

  .su__pl-10px {
    padding-left: 10px;
  }

  .su__rtl .su__pl-20px {
    padding-left: 0;
    padding-right: 20px;
  }

  .su__px-20px {
    padding-left: 20px;
  }

  .su__Recommended_flex {
    display: flex;
    flex-direction: column;
    justify-content: unset;
    margin-bottom: 15px;
  }

  .su__Recommended_Article-section {
    max-width: 100% !important;
    min-width: 100% !important;
    margin-right: 0 !important;
    margin-top: 15px !important;
    margin-bottom: 15px !important;
  }

  .su__similarSearches {
    min-width: 100%;
    max-width: 100%;
    margin-top: 20px !important;
    margin-bottom: 40px;
  }

  .su__mergedFilter-CS-tab .su__mergedFilter-CS-button {
    padding: 0;
    width: unset;
  }

  .su__merged_tabs svg {
    margin: 0;
  }

  .su__mergedtab-items {
    padding-right: 0;
    padding-left: 0;
  }

  .su__showinPage_text_dimension {
    margin: 0;
    margin-left: 12px;
    margin-right: 12px;
  }

  .su__page_label {
    font-size: 11px;
    font-weight: 400;
    color: var(--su__searchapp-gray);
  }

  .su__fs-singleImage .su__featured-thumbnail {
    width: 346px;
    height: 198px;
    max-width: 100%;
  }

  .su__fs-singleImage .su__img-featured {
    width: 346px;
    height: 198px;
    max-width: 100%;
  }

  .su__mobile-pagination-mt-30px {
    margin-top: 30px;
  }

  .su__rtl .su__nested-ul {
    padding-left: 0;
    padding-right: 40px;
    margin: 2px -10px 0 0;
  }

  .draggableList:hover {
    background-image: none;
  }

  .su__rtl .draggableList {
    padding: 15px 14px 5px 14px;
  }

  .su__rtl .su__recommendations-results,
  .su__recommendations-results {
    padding: 0;
  }

  .su__rtl .su-draggableFilters,
  .su-draggableFilters {
    padding-right: 0;
    padding-left: 0;
  }

  .su__sm_mt-14 {
    margin-top: 14px;
  }

  .su__sm_mb-10 {
    margin-bottom: 10px;
  }

  .su__sm_mt-10 {
    margin-top: 10px;
  }

  .su__sm-flex-1 {
    flex: 1;
  }

  .su__sm_pr-7 {
    padding-right: 7px;
  }

  .su__sm_pl-7 {
    padding-left: 7px;
  }

  .su__sm-br-none {
    border: none;
  }

  .su__sm-mb-1 {
    margin-bottom: 16px;
  }

  .su__feedback-text {
    position: fixed;
    right: 74px;
    bottom: 190px;
  }

  .su__side-customized-gear {
    bottom: calc(50% - 17px);
    border-top-left-radius: 7px;
    border-bottom-left-radius: 7px;
    padding: 10px;
  }

  .su__sm-mb-80 {
    margin-bottom: 80px;
  }

  .su__sm-m-is-18 {
    margin-inline-start: 18px;
  }

  .su__sm_mr-il-0 {
    margin-inline-end: 0;
  }

  .su__show-more-bg .su__arrow-down-gpt-align {
    top: 3px;
  }
}

@media (max-width: 385px) {
  /* WCAG phase 2 fixes */
  .mobile-column-alignment {
    flex-wrap: wrap;
  }

  .mobile-column-alignment .su__mob-facetshow {
    flex: 1 0 100%;
  }

  .mobile-column-alignment .su__fillter-sortby-layout-2 {
    margin-top: 10px;
  }

  .su__ml-0 {
    margin-left: 0;
  }

  .su__mr-12-mobile {
    margin-inline-end: 12px;
  }

  .su__mobile-mr-0 {
    margin-right: 0;
  }

  .su__editPglayout {
    max-width: 90vw;
  }
}

.su__bs-cyan-blue {
  box-shadow: 0 0 20px rgba(var(--su__searchapp-sapphire-rgba), 0.69);
}

/* ......UI first unit testing...... */
.Knowledge_graph_feedback :hover svg path {
  fill: var(--su__searchapp-deepskyblue-light);
}

.su__y-scroll {
  overflow-y: auto;
  overflow-x: hidden;
  height: 100%;
  width: 100%;
}

.su__rtl .su__searchTip-header {
  padding-right: 24px;
}

.su__justify-sa {
  justify-content: space-between;
}

.su__color-gray-url {
  color: var(--su__searchapp-gray);
}

.su__color-black {
  color: var(--su__searchapp-darkgray);
}

.su__search-facet-input.filter-search {
  outline: none;
  border: 1px solid var(--su__searchapp-gray-standard);
  border-radius: 3px;
  box-shadow: none;
}

.su__fill-deepblue {
  fill: var(--su__searchapp-deepskyblue-light);
}

.su__transfrom-180 {
  transform: translate(4px, 2px) rotate(180deg);
}

.su__merged_tabs svg path {
  transform: translate(1px, 0) scale(0.8);
}

.su__recommendation-inner-div,
.su__sim_s_inner-div {
  background: var(--su__searchapp-white) 0% 0% no-repeat padding-box;
  box-shadow: 0 0 6px rgba(var(--su__searchapp-black-rgba), 0.16);
  border-radius: 4px;
}

.su__border-b-lg {
  border-bottom: 1px solid var(--su__searchapp-lightgrey);
}

.su__sim_s_inner-div .su__sim_s_item:last-child {
  border: none;
}

.su__mergedtbmobile {
  transform: translate(-6px, 0);
}

.su__facet_space {
  margin-inline-end: 12px;
}

.su__sticky_facet-container {
  position: relative;
}

.su__pb-20 {
  padding-bottom: 20px;
}

.su__sticky_expend_btn {
  position: absolute;
  bottom: 0;
  background: var(--su__searchapp-lightgray-14) 0% 0% no-repeat padding-box;
}

.su__facet_top_blur::before {
  position: absolute;
  content: '';
  width: 100%;
  height: 7px;
  border-top: 19px solid var(--su__searchapp-lightgray-14);
  top: -22px;
  filter: blur(6px);
  transform: rotate(180deg);
}

.su__stickyfacet_height {
  max-height: 115px;
  overflow: hidden;
}

.su__aboveSourceLabel_Items {
  display: flex;
  padding-bottom: 10px;
  min-width: 10px;
}

.su__padding-12px {
  padding: 12px;
}

.su__search_tuning_icon {
  height: 24px;
  width: 24px;
}

.su__align_fb_icons {
  position: absolute;
  right: -10px;
}

.su__w-30px {
  width: 30px;
}

.su__left-item-img {
  height: 16px;
  width: 16px;
  margin-top: 5px;
}

.su__centre-align-dots {
  position: absolute;
  justify-content: center;
  align-items: center;
  width: 100%;
}

.su__previousBtn {
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px solid var(--su__searchapp-gray-standard);
  border-radius: 30px 5px 5px 30px;
  width: 38px;
  height: 27px;
}

.su__nextBtn {
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px solid var(--su__searchapp-gray-standard);
  border-radius: 5px 30px 30px 5px;
  width: 38px;
  height: 27px;
}

.su__w_62 {
  width: 62px;
}

.su_h_38 {
  height: 38px;
}

.resultPerPageBox button {
  width: 100%;
  text-align: left;
}

.resultPerPageBox button:hover {
  color: var(--su__searchapp-white);
  background-color: var(--su__searchapp-deepskyblue-light);
}

.su__pl_6 {
  padding-left: 6px;
}

.su__min-width-article-btn {
  min-width: calc(100% - 50px);
}

.su__autocomplete_alignment {
  margin-left: 7px;
  line-height: 5px;
  gap: 5px;
}

.su__h_80 {
  height: 80%;
}

.su__view_all_tag {
  font-size: 11px;
  text-align: center;
  position: relative;
}

.su__tagBox_collapse {
  height: 62px;
  overflow-y: scroll;
}

.su__view_all_tag_btn {
  background: transparent;
  border: none;
  color: var(--su__searchapp-deepskyblue-light);
  cursor: pointer;
  padding-top: 8px;
}

.su__view_all_tag::before {
  position: absolute;
  content: '';
  width: 100%;
  height: 4px;
  border-top: 19px solid var(--su__searchapp-lightgray-14);
  top: -15px;
  filter: blur(5px);
  transform: rotate(180deg);
  left: -15px;
  right: 0;
}

.su__rtl .su__view_all_tag::before {
  right: -10px;
}

.su__width-tile {
  max-width: calc(100% - 50px);
  width: 93%;
}

.su__ml-30px {
  margin-left: 30px;
}

.su__cs_border_bottom {
  width: 100%;
}

.su__rtl .su__mobile_arabic_header {
  margin-left: 20px;
  margin-right: 5px;
}

.su__rtl .su__rtlleft-10px {
  left: 10px;
  right: unset;
}

.su__right-10px {
  right: 10px;
}

.su__feedback-response .su__feedback-icon-above .su__feedbtn {
  position: relative;
  bottom: 0;
  padding-right: 0;
}

.su__feedback-response .su__feedback-icon-above {
  justify-content: flex-end;
}

.su__rtl .su__rtl-l-0 {
  left: 0;
}

.su__rtl .su__rtl-r-auto {
  right: auto;
}

.su__rtl .su__mr-rtl-20 {
  margin-right: 20px;
}

.su__left-0 {
  left: 0;
}

.su__left-m14 {
  left: -10px;
}

.su__w-35px {
  width: 35px;
}

.su__rtl .su__previousBtn {
  transform: rotate(180deg);
}

.su__sildeBox .arrow-left {
  position: absolute;
  left: 10px;
  z-index: 15;
}

.su__sildeBox .arrow-right {
  position: absolute;
  right: 10px;
  z-index: 15;
}

.su__eyes-btn .su__eye {
  padding-left: 9px;
}

.su__rtl .su__eyes-btn .su__eye {
  padding-left: 0;
  padding-right: 9px;
}

.su__rtl .su__text-alignment {
  text-align: right;
}

.su__rtl .su__mirror-h {
  transform: scaleX(-1);
}

.su__rtl .su__searchTip-list li::before {
  position: absolute;
  right: -24px;
}

.su__scale_1 {
  transform: scale(1.1);
}

.su__w-175px {
  width: 175px;
}

.su__prtl-2px {
  padding-left: 2px;
}

.su__rtl .su__prtl-2px {
  padding-right: 2px;
}

.su__allSelected-pref-no {
  padding-left: 0;
  padding-right: 0;
}

.su__background-Oceanblue {
  background: var(--su__searchapp-teal-13) 0% 0% no-repeat padding-box;
}

.su__p-2px {
  padding-left: 2px;
  padding-right: 2px;
}

.su__messagepreview {
  height: 20px;
  width: 20px;
  border-radius: 2px;
}

.su__messagepreview path {
  fill: var(--su__searchapp-deepskyblue-light);
}

.su__rearrange_facet-eye-btn,
.DragItem {
  transform: translate(1px, 4px);
}

.su__overflow-x {
  overflow-x: auto;
  overflow-y: hidden;
}

.su__rtl .su__r-auto {
  right: auto;
}

.su__min-width-article-btn_img {
  min-width: calc(100% - 245px);
}

.su__p-0,
.su__rtl .su__rtlp-0 {
  padding: 0;
}

.su__m-0,
.su__rtl .su__rtlm-0 {
  margin: 0;
}

.su__sso-loader {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  margin: auto;
  width: 50px;
  height: 50px;
  border: 5px solid var(--su__searchapp-lightgray-14);
  border-top: 5px solid var(--su__searchapp-deepskyblue-light);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.su__chrome-extension .su__searchBox-header {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.su__chrome-extension .su__searchBox-header .su__search-forms {
  width: 95%;
}

.su__mt-7px {
  margin-top: 7px;
}

.su__mb-20px {
  margin-bottom: 20px;
}

.su__mb-16px {
  margin-bottom: 16px;
}

.su__center-gpt-widget pre {
  background: var(--su__searchapp-lightgrey) 0% 0% no-repeat padding-box;
  padding: 20px;
  border-radius: 12px;
}

#setFocusOnGPTResponse ul ul {
  margin-inline-start: 15px;
}

#setFocusOnGPTResponse ol ol {
  margin-inline-start: 15px;
}

.su__gpt_padding {
  padding: 15px 20px;
}

.su__gpt_mobile_padding {
  padding: 15px 15px;
}

#setFocusOnGPTResponse pre ul {
  white-space: initial;
}

#setFocusOnGPTResponse pre {
  white-space: normal;
}

#setFocusOnGPTResponse pre code {
  white-space: pre-wrap;
  text-wrap: auto;
}

#setFocusOnGPTResponse pre code.language-sh,
#setFocusOnGPTResponse pre code.language-shell {
  white-space: pre-line;
}

#setFocusOnGPTResponse pre ol {
  white-space: initial;
}

#setFocusOnGPTResponse table {
  border-collapse: collapse;
  margin-top: 15px;
}

#setFocusOnGPTResponse th,
#setFocusOnGPTResponse td {
  border: 1px solid var(--su__searchapp-black);
  padding: 8px;
}

.su__dislike_hov_fill:hover {
  fill: var(--su__searchapp-red);
}

.su__like_hov_fill:hover {
  fill: var(--su__searchapp-vividgreen);
}

.su__copyToBoard_hov_fill:hover {
  fill: var(--su__searchapp-deepskyblue-light);
}

.su__gap-5 {
  gap: 5px;
}

.su__citation_link {
  color: var(--su__searchapp-darkgray);
}

.su__citation_link:hover {
  text-decoration: underline;
  color: var(--su__searchapp-deepskyblue-light);
}

.su__citation-cs {
  background: var(--su__searchapp-white) 0% 0% no-repeat padding-box;
  border: 1px solid var(--su__searchapp-lightgrey);
  border-radius: 5px;
  color: var(--su__searchapp-gray);
  font-size: 11px;
  line-height: 14px;
  padding: 2px 5px 2px 5px;
  width: fit-content;
  max-width: 300px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.su__pb-30px {
  padding-bottom: 30px;
}

.su__p-10px {
  padding: 10px;
}

.su__show-more-before::before {
  content: '';
  position: absolute;
  left: -5px;
  right: 0;
  height: 30px;
  pointer-events: none;
  filter: blur(3px);
  margin-bottom: 69px;
  background: linear-gradient(
    to bottom,
    rgba(var(--su__searchapp-white-rgba), 0.1),
    rgba(var(--su__searchapp-white-rgba), 0.9),
    rgba(var(--su__searchapp-white-rgba), 3)
  );
}

.su__showMore-btn-wrap {
  padding: 7px 45px 7px 45px;
  border: 1px solid var(--su__searchapp-deepskyblue-light);
  border-radius: 18px;
}

.su__showMore-btn-wrap:hover {
  background: var(--su__searchapp-paleblue-cool) 0% 0% no-repeat padding-box;
}

.su__mr-10px {
  margin-right: 10px;
}

.su__gap-7px {
  gap: 7px;
}

.su__ml-auto {
  margin-left: auto;
}

.su__p-20px {
  padding: 20px;
}

.su__SUgpt-tooltip {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.su__SUgpt-tooltip .su__SUgpt-tooltiptext {
  visibility: hidden;
  width: 71px;
  background: rgba(var(--su__searchapp-black-rgba), 0.8);
  color: var(--su__searchapp-white);
  text-align: center;
  border-radius: 2px;
  position: absolute;
  z-index: 1;
  bottom: -170%;
  left: 0;
  transform: translateX(-70%);
  opacity: 0;
  transition: opacity 0.3s;
  font-size: 10px;
  font-weight: normal;
  height: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.su__SUgpt-tooltip:hover .su__SUgpt-tooltiptext {
  visibility: visible;
  opacity: 1;
}

.su__font-14px {
  font-size: 14px;
}

.su__sandboxgpt-spinner {
  position: absolute;
  left: 49%;
  top: 42%;
  z-index: 100000;
}

.su__sandboxgpt-feedback-blur {
  opacity: 0.2;
}

.su__bngpt-h {
  cursor: pointer;
  height: 34px;
}

.su__top__19px {
  top: -19px;
}

.su__tooltip-positions {
  right: -58px;
  left: unset;
}
.su__tooltip-summerize-positions {
  right: -44px;
  left: unset;
}

.su__rtl .su__tooltip-positions {
  right: unset;
  left: 58px;
}

.su__content-s-border {
  border-bottom: 3px solid var(--su__searchapp-lightgrey);
  width: 100%;
  height: 3px;
}

.su__text-wrap {
  text-wrap: wrap;
}

.su__top-n-4 {
  top: -24px;
}

.su__nested-list {
  max-height: 0;
  opacity: 0;
  overflow: hidden;
  transition: max-height 0.3s ease, opacity 0.6s ease;
}

.su__open-nested-list {
  max-height: 100%;
  opacity: 1;
}

.su__right-14px {
  right: 14px;
}

.su__arrow-icon {
  position: relative;
  z-index: 1;
  transition: transform 0.6s ease;
  transform-origin: center;
}

.su__arrow-icon.su__transfrom-180 {
  transform: rotateX(180deg);

  /* Rotate along X-axis to create flip */
}

.su__arrow-icon:not(.su__transfrom-180) {
  transform: rotateX(0deg) translate(-5px, -5px);

  /* Initial state */
}

.su__min-height-30px {
  min-height: 30px;
}

.su__min-height-60px {
  min-height: 60px;
}

.su__min-w-50px {
  min-width: 50px;
}

.su__multiple-version-label {
  padding-right: 2px;
  padding-left: 2px;
  min-width: 35px;
  text-align: center;
}

.su__bottom-unset {
  bottom: unset;
}

.su__mt-30 {
  margin-top: 30px;
}

.su__rtl .su__mr-ar-10 {
  margin-right: 10px;
}

.su__ml-12px {
  margin-left: 12px;
}

.su__m-is-p6 {
  margin-inline-start: 10px;
}

.su__m-ie-1 {
  margin-inline-end: 16px;
}

.su__h-36 {
  height: 36px;
}

.su__w-322 {
  width: 322px;
}

.su__bg-transparent,
.su__btn-transparent {
  background-color: transparent;
}

.su__mb-8 {
  margin-bottom: 8px;
}

.su__max-height {
  max-height: 25px;
}

.su__mergedFilter-CS-svg .su__arrow-down {
  top: 7px;
  right: 5px;
}

.su__mergedFilter-CS-svg .su__arrow-up {
  top: 10px;
  right: 6px;
}

.su__max-height_20per {
  max-height: 20%;
}

.su__pt-9px {
  padding-top: 9px;
}

.su__pb-9px {
  padding-bottom: 9px;
}

.su__hover-color-black:hover {
  color: var(--su__searchapp-darkgray);
}

.su__recommendation-inner-div .su__recommendations-tag-content .su__recommendation-label {
  margin-block-start: 5px !important;
  margin-block-end: 0 !important;
}

.su__filter-content-row .su__filter-toggle .su__filter-label {
  font-weight: normal;
  margin-bottom: 0;
}

.su__list-items .su__list-item-row .su__list-item-title a {
  text-decoration: none;
}

.su__customized__settings .su__customized__settings-inner .su___autolearning {
  margin-bottom: 0;
  font-weight: normal;
  cursor: pointer;
}

.su__customized__settings .su__customized__settings-inner .su___show-more-summary {
  margin-top: 13px;
  margin-bottom: 13px;
  cursor: pointer;
}

.su__grid-tiles {
  margin-right: 20px;
  margin-bottom: 20px;
}

.su__grid-tiles:nth-child(2n) {
  margin-right: 0;
}

.su__rtl .su__grid-tiles {
  margin-left: 20px;
  margin-right: 0;
}

.su__rtl .su__grid-tiles:nth-child(2n) {
  margin-left: 0;
}

.su__rtl .su__languages-dropdown {
  direction: ltr;
  unicode-bidi: isolate;
}

.su__pb-5px {
  padding-bottom: 5px;
}

.su__min-w20 {
  min-width: 20%;
}

#MergedDataHead .highlight {
  color: var(--su__searchapp-deepskyblue-light);
}

.su__mx-w90 {
  max-width: 90%;
}

.u__bookmark-inner label {
  font-weight: 500;
  font-size: 12px;
}

.su__advanceLabel input {
  font-size: 12px !important;
}

.su__save_bookmark_unq label {
  margin-bottom: 4px;
  font-weight: 500;
  font-size: 12px;
  line-height: 19px;
}

.su__word-normal-kh button {
  word-break: normal;
  word-wrap: normal;
}

.su__save_bookmark_unq input {
  font-size: 14px !important;
}

.su__search-box-unq button {
  line-height: 0;
}

.su__kh-btn-lh-0 button {
  line-height: 0;
}

.su__kh-div-lh-0 div:first-child {
  line-height: 0;
}

.su__Feedback-res label {
  font-weight: normal;
  font-size: 12px;
  line-height: 19px;
}

.su__feedback-email-label label {
  font-weight: 400;
}

.su__kh-btn-fw-n button {
  font-weight: normal;
}

.su__kh-input-br-8 input {
  border-radius: 8px;
}

.su__kh-textarea-br-8 textarea {
  border-radius: 8px;
}

.su__kh-placeholder-clr input::-webkit-input-placeholder {
  color: var(--su__searchapp-darkgray) !important;
  opacity: 1 !important;
  font-weight: 600 !important;
}

.su__kh-placeholder-clr input::-moz-placeholder {
  color: var(--su__searchapp-darkgray) !important;
  opacity: 1 !important;
  font-weight: 600 !important;
}

.su__kh-placeholder-clr input::-ms-input-placeholder {
  color: var(--su__searchapp-darkgray) !important;
  opacity: 1 !important;
  font-weight: 600 !important;
}

.su__kh-placeholder-clr input::placeholder {
  color: var(--su__searchapp-darkgray) !important;
  opacity: 1 !important;
  font-weight: 600 !important;
}

.su__kh_mx-18px h2 {
  margin-left: 18px;
  margin-right: 18px;
}

.su-tabsSection .su__kh_label label {
  margin-bottom: unset !important;
}

.su__mb-38px {
  margin-bottom: 38px;
}

.su__p-1px {
  padding: 1px;
}

.su__min-w-80 {
  min-width: 80px;
}

.su__t-m2 {
  margin-top: -2px;
}

.su__min-w-70 {
  min-width: 70px;
}

.su__custom-width-facets {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 28%;
  flex: 0 0 28%;
  max-width: 28%;
}

.su__custom-width-facet-results {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 72%;
  flex: 0 0 72%;
  max-width: 72%;
}

.hideFacets .su__d-inline-block .su__margin-8px {
  margin-right: 8px;
}

.su__d-inline-block:last-child .su__margin-8px {
  margin-right: 0;
}

.su__rtl .hideFacets .su__d-inline-block:last-child .su__margin-8px {
  margin-right: 8px;
}

.su__rtl .hideFacets .su__d-inline-block:nth-child(2) .su__margin-8px {
  margin-right: 0;
}

.su__heightFacet-26px {
  height: 26px;
}

.su__heightRadio-22px {
  height: 22px;
}

.su__mt-100 {
  margin-top: 100px;
}

/* CSS styles for the feedback form designs */

.su__feedback-textarea {
  position: relative;
}

.su__feedback-charlimit {
  position: absolute;
  color: rgba(var(--su__searchapp-steelblue-rgba), 0.6);
  font: normal 400 10px / normal 'Montserrat';
  bottom: 10px;
  right: 10px;
}

.su__feedback-modal .su__feedback-label {
  color: var(--su__searchapp-darkgray);
  font: normal normal 600 13px / normal Montserrat;
  margin-bottom: 10px;
  line-height: 20px;
}

.su__feedradio-group.su__feed-labels {
  color: var(--su__searchapp-darkgray);
  font: 14px normal 400 normal Montserrat;
  line-height: normal;
}

.feedback-question-three {
  width: 85%;
  word-break: break-word;
}

.su__feedback-modal textarea,
.su__feedback-modal .su__feedtext-area:focus:not(:focus-visible),
.su__input-feedack:not(:focus-visible) {
  border-radius: 4px;
  border: 1px solid var(--su__searchapp-lightgrey);
}

.su__feedback-modal .su__btn:not(:focus-visible) {
  width: 100%;
  border-radius: 4px;
  background: var(--su__searchapp-deepskyblue-light);
  color: var(--su__searchapp-white);
}

.su__feedradio-row {
  display: flex;
  flex-direction: column;
  place-items: flex-start;
}

.su__feed-title {
  color: var(--su__searchapp-darkgray);
  font: normal normal 500 14px / normal Montserrat;
}

.su__feed-title.su__fb_thanks {
  margin-top: 5px;
}

.ft-green-tick {
  width: 38px;
  height: 38px;
}

.su__feedback-btn {
  width: 100%;
  height: 36px;
  background: var(--su__searchapp-deepskyblue-light);
  color: var(--su__searchapp-white);
}

.multiform-step-two {
  display: flex;
  flex-direction: column;
  row-gap: 10px;
}

.su__feedback-btn.disabled-btn {
  border-radius: 4px;
  background: rgba(var(--su__searchapp-deepskyblue-light-rgba), 0.3) !important;
}

.su__feedback-modal.thanksModal {
  width: 350px;
  right: 50%;
  bottom: 50%;
  transform: translate(50%, 50%);
}

.thanksModal .su__feedback-row {
  row-gap: 12px;
}

.thanksModal .feedback-submit-content {
  padding: 0 0 11px 0;
}

.su__height_email {
  height: 40px;
}

.su__feedback-row.feedback-submit-content {
  gap: 0;
}

@media (max-width: 500px) {
  .su__feedback-modal,
  .su__feedback-modal.thanksModal {
    top: 50%;
    left: 50%;
    bottom: unset;
    right: unset;
    transform: translate(-50%, -50%);
  }
}

.su__profile-section {
  border-radius: 50%;
  background: var(--su__searchapp-white);
  color: var(--su__searchapp-deepskyblue-light);
  font-weight: 600;
  font-size: 24px;
  width: 42px;
  height: 42px;
  justify-content: center;
  display: flex;
  align-items: center;
  margin-left: 10px;
  cursor: pointer;
}

#su__loggedInPopup {
  position: absolute;
  top: 0;
  right: 0;
  width: 325px;
  box-shadow: 0 3px 30px var(--su__searchapp-gray-19);
  background: transparent
    linear-gradient(
      90deg,
      var(--su__searchapp-pastelblue) 0%,
      var(--su__searchapp-lightblue-alt) 37%,
      var(--su__searchapp-palecyan) 64%,
      var(--su__searchapp-skyblue-light) 83%,
      var(--su__searchapp-paleblue-light) 100%
    )
    0% 0% no-repeat padding-box;
  padding-top: 8px;
  border-radius: 4px;
  z-index: 1;
}

#su__loggedInPopup > div {
  background: var(--su__searchapp-white);
  color: var(--su__searchapp-black);
  flex-direction: column;
  border-radius: 4px;
  padding: 5px;
  padding-bottom: 25px;
}

.su__logged-in-charAt0 {
  width: 75px;
  height: 75px;
  border-radius: 50%;
  background: var(--su__searchapp-lightgrey);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 44px;
  font-weight: bold;
  margin-bottom: 8px;
}

.su__sign-out-btn {
  margin-top: 15px;
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 14px;
  border: none;
  background: transparent
    linear-gradient(
      278deg,
      var(--su__searchapp-orange-dark) 0%,
      var(--su__searchapp-darkorange) 100%
    )
    0% 0% no-repeat padding-box;
  padding: 8px 40px;
  border-radius: 25px;
  color: var(--su__searchapp-white);
  cursor: pointer;
}

.su__margin-left-auto {
  margin-left: auto;
}

.su__font-color-000 {
  color: var(--su__searchapp-black);
}

.chat-container {
  display: flex;
  justify-content: center;
  margin-top: 15px;
  background: var(--su__searchapp-white) 0% 0% no-repeat padding-box;
  border: 1px solid var(--su__searchapp-lightgrey);
  width: 100%;
  border-radius: 8px;
}

.chat-container .input-box {
  display: flex;
  align-items: center;
  width: 100%;
  border-radius: 8px;
  padding: 0 10px;
}

.chat-container .input-box input {
  border: none;
  outline: none;
  background: none;
  width: 100%;
  padding: 14px 8px;
  font-size: 14px;
  color: var(--su__searchapp-black);
}

.chat-container .input-box button {
  border: none;
  padding: 4px 10px;
  cursor: pointer;
  margin-left: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
  background: var(--su__searchapp-deepskyblue-light);
  border-radius: 3px;
  color: var(--su__searchapp-white);
}

.chat-container .input-box button:disabled {
  background: var(--su__searchapp-gray-standard);
  cursor: not-allowed;
}

.su__multiple-versions-block {
  margin-left: auto;
}
.su_preview-startblock svg path:hover {
  stroke: none;
}
.su__loading_true .su__sc-loading {
  position: relative;
  user-select: none;
  pointer-events: none;
  overflow: hidden;
  display: inherit;
  border: none !important;
}

.su__loading_true .su__sc-loading::after {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  pointer-events: none;
  color: transparent;
  transition: all 0.5s ease-out;
  animation: loadereffect 1.5s infinite;
  -webkit-animation: loadereffect 1.5s infinite;
  cursor: wait;
  user-select: none;
  z-index: 2;
  height: 100%;
  width: 15%;
  background: linear-gradient(
    to right,
    var(--su__searchapp-lightgrey) 25%,
    var(--su__searchapp-lightgrey) 50%,
    var(--su__searchapp-lightgrey) 100%
  );
  filter: blur(20px);
}
.su__loading_true .su__sc-loading::before {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  pointer-events: none;
  color: transparent;
  cursor: wait;
  user-select: none;
  z-index: 2;
  background: var(--su__searchapp-lightgrey);
}

.su__loading_true .su__loading-Dnone {
  display: none;
}

@keyframes loadereffect {
  0% {
    left: 0;
  }

  100% {
    left: 120%;
  }
}

.su__facet-skeleton-loader {
  position: absolute !important;
  width: 94%;
  height: 100%;
}
.su-preview-json {
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  position: fixed;
}

.su__w-70dvw {
  width: 70vw;
}
.su__w-85dvw {
  width: 85vw;
}
.su-preview-json .su__preview-type-two {
  line-height: 2;
}
.su__preview-type-two {
  border: 1px solid var(--su__searchapp-gray-standard);
  padding: 13px;
  color: var(--su__searchapp-gray);
  font-size: 13px;
  font-family: 'Montserrat';
  overflow-y: auto;
  overflow-x: auto;
  max-height: 75vh;
  border-radius: 5px;
}

/* .su-preview-json .divider {
  border-top: 1px solid #e0e0e0;
  margin: 16px 0;
} */
.su__preview-type-two.su__thin-scrollbar > div {
  border-top: 1px solid var(--su__searchapp-gray-standard);
  padding: 15px 0 15px 0;
}
.su__preview-type-two.su__thin-scrollbar > div:first-child {
  border: 0;
  padding: 0 0 15px 0;
}

/* .su-preview-json::-webkit-scrollbar{
  width: 12px;
} */

/* .scroll-container::-webkit-scrollbar-track {
  background: #D1D1D1;
} */

.su-preview-json .su__close-icon {
  position: fixed;
  top: 0;
  right: -28px;
  background: var(--su__searchapp-white);
  border: none;
  border-radius: 4px;
  height: 22px;
  width: 22px;
  cursor: pointer;
  padding: 0;
}

.su-preview-json .su__close-icon svg {
  fill: var(--su__searchapp-gray);
}
.su__preview-type-two.su__thin-scrollbar {
  scrollbar-color: var(--su__searchapp-lightgrey) transparent;
}
.su__p-20 {
  padding: 20px;
}
a.su__preview-json-title {
  all: unset;
  display: inline-block;
  margin-bottom: 15px;
  font-size: 17px;
  letter-spacing: 0;
  color: var(--su__searchapp-darkgray);
  font-weight: 600;
  cursor: pointer;
}

.su-preview-json .divider.su__main {
  margin-right: -20px;
  margin-left: -20px;
  border-top: 1px solid var(--su__searchapp-gray);
}
.su__preview-type-two li p {
  display: inline;
}

.su__preview-type-two .su__d-flex:last-child {
  padding-bottom: 0;
}

.su__preview-type-two ul {
  list-style: auto;
}
.su__preview-type-two pre {
  white-space: pre-wrap;
}

/* uncomment this to hide keys in preview */
.su__preview-type-two table,
.su__preview-type-two table tr,
.su__preview-type-two table tr td {
  margin: 0;
  padding: 0;
  border-spacing: 0;
}
.su__no-preview {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
}
.su__preview-type-two table {
  all: unset;
  display: table;
  border-collapse: separate;
  box-sizing: border-box;
  text-indent: initial;
  unicode-bidi: isolate;
  border-spacing: 2px;
  border-color: gray;
  border: 1px solid var(--su__searchapp-lightgrey);
}
.su__preview-type-two thead th {
  font-weight: bold;
  padding: 10px;
  border: 1px solid var(--su__searchapp-lightgrey);
  text-transform: uppercase;
}
.su__preview-type-two table tbody td {
  padding: 10px !important;
  border: 1px solid var(--su__searchapp-lightgrey);
}
.su__preview-type-two .su__preview-value * {
  font-size: min(0.85rem, 100%) !important;
}

.su__preview-type-two .su__preview-value hr {
  border: none;
  border-top: 1px solid var(--su__searchapp-lightgray-14) !important;
  height: 1px;
  background-color: var(--su__searchapp-lightgray-14) !important;
}

.su__preview-type-two .su__preview-value ul br,
.su__preview-type-two .su__preview-value ol br,
.su__preview-type-two .su__preview-value h1 + br,
.su__preview-type-two .su__preview-value h2 + br,
.su__preview-type-two .su__preview-value h3 + br,
.su__preview-type-two .su__preview-value h4 + br,
.su__preview-type-two .su__preview-value h5 + br,
.su__preview-type-two .su__preview-value h6 + br,
.su__preview-type-two .su__preview-value ol + br,
.su__preview-type-two .su__preview-value ul + br {
  display: none;
}

h3.su__d-key {
  margin: 0 5px 0 0;
  padding: unset;
  display: inline-block;
  color: var(--su__searchapp-darkgray);
}

@keyframes wave {
  50%,
  75% {
    transform: scale(2.5);
  }

  80%,
  100% {
    opacity: 0;
  }
}
.su__attachment-tooltip {
  width: 100%;
  bottom: 120%;
  top: unset;
}
.su__attachment-tooltip > div {
  white-space: normal;
}
.su__thin-scrollbar {
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: var(--su__searchapp-gray) transparent;
}

.su__thin-scrollbar::-webkit-scrollbar {
  width: 3px;
}

.su__thin-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.su__thin-scrollbar::-webkit-scrollbar-thumb {
  background-color: var(--su__searchapp-lightgrey);
  border-radius: 4px;
  border: 1px solid transparent;
}

.su__loading-card-width {
  flex: 0 0 100% !important;
  max-width: 100% !important;
}

.su__ml-50 {
  margin-left: 50px;
}

.facet.su__open-facet:has(.MuiSlider-root) .su__sort-filter-icon {display: none}
.su__filter-toggle:has(.MuiSlider-thumb) {padding-left: 0}

.su-preview-json .su__tooltip-container {
  height: max-content;
}
.su__summary-result-tile {
  font-size: 15px;
  color: var(--su__searchapp-darkgray);
  line-height: 20px;
  font-weight: 500;
}
.su__tags-summary {
  padding: 3px 3px;
  background: var(--su__searchapp-white) 0% 0% no-repeat padding-box;
  border: 1px solid var(--su__searchapp-lightgrey);
  border-radius: 5px;
  opacity: 1;
}
.su__mr-2px {
  margin-right: 2px;
}
.su__bg-overlay.su__summary-index {
  z-index: 1;
}
.su__ml-1px {
  margin-left: 1px;
}

.su__advance-search-button {
  padding: 0 10px;
  min-width: 160px;
  border-radius: 5px;
  height: 27px;
  border: 1px solid var(--su__searchapp-deepskyblue-light);
  background-color: var(--su__searchapp-white);
}
.advance-search-txt {
  text-align: left;
  font: normal normal bold 13px / 16px Montserrat;
  letter-spacing: 0;
  opacity: 1;
  font-weight: 500;
}

.su__savebookmark-icon-color svg path {
  fill: var(--su__searchapp-gray);
}
.su__active-bookmark-list-icon-web svg path {
  fill: var(--su__searchapp-deepskyblue-light);
}

.su__advance-icon-color {
  fill: var(--su__searchapp-deepskyblue-light);
}

.su__advance-active-background {
  background-color: var(--su__searchapp-deepskyblue-light);
  border-color: var(--su__searchapp-deepskyblue-light);
}

.su__advance-icon-svg-fill-white {
  fill: var(--su__searchapp-white);
}

.facet.su__close-facet:has(.MuiSlider-root) .su__sort-filter-icon,
.facet.su__open-facet:has(.MuiSlider-root) .su__sort-filter-icon {display: none}
.su__filter-toggle:has(.MuiSlider-thumb) {padding-left: 0}