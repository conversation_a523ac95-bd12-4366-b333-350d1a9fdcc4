/* global jest, it, expect, describe, beforeEach, afterEach */
import React from 'react';
import { mount } from 'enzyme';
import Icon from './svg';

describe('SVG Component', () => {
  const props = {
    className: '',
    IconName: '',
    IconClass: '',
    color: '',
    width: '',
    height: '',
    widthInner: '',
    heightInner: '',
    strokeWidthp: '',
    strokeLinecapp: '',
    strokeLinejoinp: '',
    strokeMiterlimitp: '',
    rx: '',
    transform: '',
    fill: '',
    cx: '',
    cy: '',
    r: '',
    onClick: jest.fn()
  };
  beforeEach(() => {});
  afterEach(() => {
    props.IconName = '';
    props.className = '';
  });

  const icons = [
    { IconName: 'Close', className: 'close_icon' },
    { IconName: 'Ok', className: 'ok_icon' },
    { IconName: 'Advancesearch', className: 'advance_search_icon' }
  ];

  it('should mount clickable svg with available props for icon with path, circle or rect properties', () => {
    icons.forEach((icon) => {
      props.IconName = icon.IconName;
      props.className = icon.className;
      props.onClick.mockClear();
      const wrapper = mount(<Icon {...props} />);
      wrapper.update();
      const svg = wrapper.find(`svg.${icon.className}`);
      expect(svg).toHaveLength(1);
      svg.simulate('click', {});
      expect(props.onClick).toHaveBeenCalledTimes(1);
    });
  });
});
