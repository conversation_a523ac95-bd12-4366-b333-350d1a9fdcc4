import React from 'react';

const sparkle = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    xmlnsXlink="http://www.w3.org/1999/xlink"
    width="20"
    height="21"
    viewBox="0 0 22 23"
  >
    <defs>
      <clipPath id="clip-Generating_icon">
        <rect width="22" height="23" />
      </clipPath>
    </defs>
    <g id="Generating_icon" data-name="Generating icon" clipPath="url(#clip-Generating_icon)">
      <g id="Group_19724" data-name="Group 19724" transform="translate(1 1)">
        <g id="Group_18524" data-name="Group 18524">
          <path
            id="Path_18304"
            data-name="Path 18304"
            d="M19.565,8.127l.795-1.785,1.761-.806a.513.513,0,0,0,0-.928L20.36,3.8l-.795-1.8a.5.5,0,0,0-.916,0l-.795,1.785L16.084,4.6a.513.513,0,0,0,0,.928l1.761.806.795,1.8A.508.508,0,0,0,19.565,8.127Zm-8.009,1.53-1.6-3.571a1,1,0,0,0-1.831,0l-1.6,3.571L3,11.279a1.026,1.026,0,0,0,0,1.857l3.522,1.622,1.6,3.571a1,1,0,0,0,1.831,0l1.6-3.571,3.522-1.622a1.026,1.026,0,0,0,0-1.857Zm7.083,6.631-.795,1.785-1.761.806a.513.513,0,0,0,0,.928l1.761.806.795,1.8a.5.5,0,0,0,.916,0l.795-1.785,1.771-.806a.513.513,0,0,0,0-.928l-1.761-.806-.795-1.8A.508.508,0,0,0,18.639,16.288Z"
            transform="translate(-2.415 -1.707)"
            fill="#1770d4"
          />
        </g>
      </g>
    </g>
  </svg>
);
export default sparkle;
