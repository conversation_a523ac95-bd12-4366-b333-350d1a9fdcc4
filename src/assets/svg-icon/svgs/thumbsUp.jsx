import React from 'react';

const thumb = ({ position, fill }) =>
  position === 'up' ? (
    <svg
      className="su__like_hov_fill"
      id="Component_49_10"
      data-name="Component 49 – 10"
      xmlns="http://www.w3.org/2000/svg"
      fill="#707070"
      xmlnsXlink="http://www.w3.org/1999/xlink"
      width="24.43"
      height="24.43"
      viewBox="0 0 24.43 24.43"
    >
      <defs>
        <clipPath id="clip-path">
          <rect
            id="Rectangle_11427"
            data-name="Rectangle 11427"
            width="24.43"
            height="24.43"
            transform="translate(24.43 24.43) rotate(180)"
            fill={fill}
          />
        </clipPath>
      </defs>
      <g id="Group_19616" data-name="Group 19616" clipPath="url(#clip-path)">
        <path
          id="Path_18332"
          data-name="Path 18332"
          d="M72.366,9.418a2.851,2.851,0,0,0-2.788-3.449,1.9,1.9,0,0,0-1.612.893l-4.531,7.25H60.382a2.036,2.036,0,0,0-2.036,2.036v8.143a2.036,2.036,0,0,0,2.036,2.036h14.4a3.054,3.054,0,0,0,2.731-1.688l1.78-3.56a4.071,4.071,0,0,0,.43-1.821v-3.11a3.054,3.054,0,0,0-3.054-3.054h-5.09ZM63.435,24.291V16.148H60.382v8.143ZM69.651,8.008,65.471,14.7v9.6h9.31a1.018,1.018,0,0,0,.911-.563l1.78-3.56a2.036,2.036,0,0,0,.215-.911v-3.11a1.018,1.018,0,0,0-1.018-1.018h-5.09a2.036,2.036,0,0,1-1.991-2.462l.788-3.677A.816.816,0,0,0,69.651,8.008Z"
          transform="translate(-56.31 -4.951)"
          fillRule="evenodd"
          fill={fill}
        />
      </g>
    </svg>
  ) : (
    <svg
      id="Component_50_24"
      className="su__dislike_hov_fill"
      data-name="Component 50 – 24"
      xmlns="http://www.w3.org/2000/svg"
      xmlnsXlink="http://www.w3.org/1999/xlink"
      width="24.43"
      fill="#707070"
      height="24.43"
      viewBox="0 0 24.43 24.43"
    >
      <defs>
        <clipPath id="clip-path">
          <rect id="Rectangle_11428" data-name="Rectangle 11428" width="24.43" height="24.43" />
        </clipPath>
      </defs>
      <g id="Group_19617" data-name="Group 19617" clipPath="url(#clip-path)">
        <path
          id="Path_18333"
          data-name="Path 18333"
          d="M93.021,24.44a2.851,2.851,0,0,0,2.788,3.449,1.9,1.9,0,0,0,1.612-.893l4.531-7.25h3.054a2.036,2.036,0,0,0,2.036-2.036V9.566a2.036,2.036,0,0,0-2.036-2.036h-14.4a3.053,3.053,0,0,0-2.731,1.688l-1.78,3.56a4.073,4.073,0,0,0-.43,1.821v3.111a3.054,3.054,0,0,0,3.054,3.054h5.089Zm8.931-14.874v8.143h3.054V9.566ZM95.736,25.849l4.18-6.688v-9.6h-9.31a1.018,1.018,0,0,0-.91.563l-1.78,3.56a2.037,2.037,0,0,0-.215.91v3.111a1.018,1.018,0,0,0,1.018,1.018h5.089A2.036,2.036,0,0,1,95.8,21.19l-.788,3.676A.816.816,0,0,0,95.736,25.849Z"
          transform="translate(-84.647 -4.476)"
          fill={fill}
          fillRule="evenodd"
        />
      </g>
    </svg>
  );

export default thumb;
