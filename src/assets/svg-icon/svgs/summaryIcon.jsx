/* eslint-disable react/prop-types */
import React from 'react';
const SummaryIcon = ({ fillColour }) => (
  <>
    <svg
      xmlns="http://www.w3.org/2000/svg"
      id="Group_40322"
      data-name="Group 40322"
      width="13.333"
      height="14"
      viewBox="0 0 13.333 14"
      aria-hidden="true"
      focusable="false"
      fill={fillColour}
    >
      <g id="Group_18524" data-name="Group 18524">
        <path
          id="Path_18304"
          data-name="Path 18304"
          d="M13.848,5.987l.53-1.19,1.174-.537a.342.342,0,0,0,0-.619L14.378,3.1l-.53-1.2a.334.334,0,0,0-.61,0l-.53,1.19-1.181.537a.342.342,0,0,0,0,.619L12.7,4.79l.53,1.2A.339.339,0,0,0,13.848,5.987ZM8.509,7.007L7.442,4.627a.667.667,0,0,0-1.221,0L5.155,7.007,2.807,8.089a.684.684,0,0,0,0,1.238l2.348,1.081,1.067,2.38a.667.667,0,0,0,1.221,0l1.067-2.38,2.348-1.081a.684.684,0,0,0,0-1.238Zm4.722,4.421-.53,1.19-1.174.537a.342.342,0,0,0,0,.619l1.174.537.53,1.2a.334.334,0,0,0,.61,0l.53-1.19,1.181-.537a.342.342,0,0,0,0-.619l-1.174-.537-.53-1.2A.339.339,0,0,0,13.231,11.428Z"
          transform="translate(-2.415 -1.707)"
        />
      </g>
    </svg>
  </>
);

export default SummaryIcon;
