import React from 'react';

const copy = ({ copied }) =>
  !copied ? (
    <svg
      className="su__copyToBoard_hov_fill"
      fill="#707070"
      id="Component_51_26"
      data-name="Component 51 – 26"
      xmlns="http://www.w3.org/2000/svg"
      width="18.322"
      height="18.322"
      viewBox="0 0 18.322 18.322"
    >
      <path
        id="Path_18329"
        data-name="Path 18329"
        d="M19.214,9.566H9.035V11.6h6.107a2.036,2.036,0,0,1,2.036,2.036V23.817a2.036,2.036,0,0,1-2.036,2.036H4.963a2.036,2.036,0,0,1-2.036-2.036V13.638A2.036,2.036,0,0,1,4.963,11.6H7V9.566A2.036,2.036,0,0,1,9.035,7.53H19.214A2.036,2.036,0,0,1,21.25,9.566V19.745a2.036,2.036,0,0,1-2.036,2.036H17.178V19.745h2.036ZM7,13.638H4.963V23.817H15.142V13.638H7v0Z"
        transform="translate(-2.927 -7.53)"
        fillRule="evenodd"
      />
    </svg>
  ) : (
    <svg xmlns="http://www.w3.org/2000/svg" width="12" height="9.189" viewBox="0 0 12 9.189">
      <path
        id="Icon_ionic-ios-checkmark"
        data-name="Icon ionic-ios-checkmark"
        d="M22.371,13.282l-.981-1.009a.211.211,0,0,0-.156-.067h0a.2.2,0,0,0-.156.067l-6.8,6.849-2.474-2.474a.215.215,0,0,0-.312,0l-.992.992a.222.222,0,0,0,0,.318l3.121,3.121a.987.987,0,0,0,.652.318,1.034,1.034,0,0,0,.646-.306h.006l7.451-7.49A.238.238,0,0,0,22.371,13.282Z"
        transform="translate(-10.434 -12.206)"
        fill="#fff"
      />
    </svg>
  );

export default copy;
