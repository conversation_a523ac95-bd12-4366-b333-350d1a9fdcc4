import React from 'react';
const thanksCheck = () => (
  <>
    <svg
      className="su_thanks-check"
      xmlns="http://www.w3.org/2000/svg"
      width="106"
      height="106"
      viewBox="0 0 106 106"
    >
      <defs>
        <linearGradient
          id="linear-gradient"
          x1="0.814"
          x2="0.186"
          y2="1.36"
          gradientUnits="objectBoundingBox"
        >
          <stop offset="0" stopColor="#1770d4" />
          <stop offset="0.467" stopColor="#1a4ad1" />
          <stop offset="1" stopColor="#1a4ad1" />
        </linearGradient>
        <filter id="Vector" x="0" y="0" width="106" height="106" filterUnits="userSpaceOnUse">
          <feOffset />
          <feGaussianBlur stdDeviation="6" result="blur" />
          <feFlood floodColor="#003dff" floodOpacity="0.122" />
          <feComposite operator="in" in2="blur" />
          <feComposite in="SourceGraphic" />
        </filter>
      </defs>
      <g transform="matrix(1, 0, 0, 1, 0, 0)" filter="url(#Vector)">
        <path
          id="Vector-2"
          data-name="Vector"
          d="M29.837,42.963l-8.575-8.575a2.619,2.619,0,0,0-1.925-.787,2.766,2.766,0,0,0-2.013.875,2.747,2.747,0,0,0,0,3.85L28,49.088a2.538,2.538,0,0,0,3.675,0L52.588,28.175a2.752,2.752,0,0,0,0-4.025,2.964,2.964,0,0,0-4.025.087ZM35,70a33.866,33.866,0,0,1-13.562-2.756A35.263,35.263,0,0,1,2.756,48.563,33.866,33.866,0,0,1,0,35,34.081,34.081,0,0,1,2.756,21.35a34.978,34.978,0,0,1,7.525-11.113A35.878,35.878,0,0,1,21.438,2.756,33.866,33.866,0,0,1,35,0,34.081,34.081,0,0,1,48.65,2.756,34.945,34.945,0,0,1,67.244,21.35,34.081,34.081,0,0,1,70,35a33.866,33.866,0,0,1-2.756,13.563,35.878,35.878,0,0,1-7.481,11.156A34.978,34.978,0,0,1,48.65,67.244,34.081,34.081,0,0,1,35,70Z"
          transform="translate(18 18)"
          fill="url(#linear-gradient)"
        />
      </g>
    </svg>
  </>
);
export default thanksCheck;
