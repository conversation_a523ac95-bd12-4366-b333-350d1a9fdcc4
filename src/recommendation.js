const React = require('react');
const { Provider } = require('react-redux');
const { waitForWindowVariable, mounter, MOUNT_POINTS } = require('constants/constants');
require('isomorphic-fetch');
require('../src/assets/css/recommendation.css');

const loader = () => mounter(<></>, MOUNT_POINTS.recommendationApp);
loader();

waitForWindowVariable('scConfiguration', 15000)
  .then(() => {
    const configureStore = require('./redux/configureStore').default;
    const Recommendations = require('./App-recommendation').default;

    const store = configureStore();
    mounter(
      <Provider store={store}>
        <Recommendations />
      </Provider>,
      MOUNT_POINTS.recommendationApp
    );
  })

  .catch((err) => {
    console.log(err);
  });
