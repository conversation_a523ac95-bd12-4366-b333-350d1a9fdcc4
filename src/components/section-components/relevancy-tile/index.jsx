import React, { Fragment } from 'react';
import { tabIndexes } from 'constants/a11y';
import StaticStrings from 'StaticStrings';
import { useTranslation } from 'react-i18next';

const RelevancyTile = (props) => {
  const { t } = useTranslation();

  const getConfidenceClass = (confidence) => {
    switch (confidence) {
      case 'High':
        return 'su__confidence-high su__f-bold';
      case 'Medium':
        return 'su__confidence-medium su__f-bold';
      case 'Low':
        return 'su__confidence-low su__f-bold';
      default:
        return '';
    }
  };

  try {
    let { item } = props;
    if (item.relevanceBand || item.relevancePercentile) {
      return (
        <Fragment>
          <div className="su__flex-vcenter">
            <div className="su__href-txt su__text-decoration su__color-lgray su__font-12 su_letter_space su__sc-loading">
              <span tabIndex={tabIndexes.tabIndex_0}>
                {t(StaticStrings.relevance_score)}:{' '}
                <span className="su__f-bold">{item.relevancePercentile}</span>
              </span>{' '}
              |{' '}
              <span tabIndex={tabIndexes.tabIndex_0}>
                {t(StaticStrings.confidence)}:{' '}
                <span className={getConfidenceClass(item.relevanceBand)}>{item.relevanceBand}</span>
              </span>
            </div>
          </div>
        </Fragment>
      );
    } else {
      return null;
    }
  } catch (e) {
    console.log('Error in Relevancy component', e);
    return <div></div>;
  }
};

export default React.memo(RelevancyTile);
