/* global jest, it, expect, describe */
import React from 'react';
import { shallow } from 'enzyme';
import RelevancyTile from './index';

jest.mock('react-i18next', () => ({
  useTranslation: () => ({ t: (key) => key })
}));

describe('RelevancyTile', () => {
  const propsWithRelevance = {
    item: {
      relevanceBand: 'High',
      relevancePercentile: 90
    }
  };

  const propsWithoutRelevance = {
    item: {}
  };

  it('renders correctly with relevance data', () => {
    const wrapper = shallow(<RelevancyTile {...propsWithRelevance} />);
    expect(wrapper.find('.su__flex-vcenter').exists()).toBe(true);
    expect(wrapper.find('.su__f-bold').at(0).text()).toBe('90');
    expect(wrapper.find('.su__confidence-high').text()).toBe('High');
  });

  it('renders null when there is no relevance data', () => {
    const wrapper = shallow(<RelevancyTile {...propsWithoutRelevance} />);
    expect(wrapper.type()).toBe(null);
  });

  it('catches errors and renders an empty div', () => {
    const propsWithError = {
      item: null
    };
    const wrapper = shallow(<RelevancyTile {...propsWithError} />);
    expect(wrapper.type()).toBe('div');
    expect(wrapper.children().length).toBe(0);
  });
});
