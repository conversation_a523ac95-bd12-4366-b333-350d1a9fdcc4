import React from 'react';
import { bool, func, object } from 'prop-types';
import { string } from '../../../../node_modules/i/lib/util';
import { RECORD_TYPES } from 'constants/constants';
const ArticlePreviewModal = ({ isOpen, onClose, articleData, recordType }) => {
  if (!isOpen || !recordType) return null;

  return (
    <>
      {articleData && Object.keys(articleData).length !== 0 ? (
        <div className="su__article-preview-modal">
          <div className="su__modal-content">
            <div className="su__modal-header">
              <div className="su__d-flex su__article-header su__align-items-center">
                {recordType === RECORD_TYPES.ARTICLE ? (
                  <div className="su__d-flex ">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="28"
                      height="28"
                      viewBox="0 0 28 28"
                    >
                      <defs>
                        <clipPath id="clip-path">
                          <rect
                            id="Rectangle_13340"
                            data-name="Rectangle 13340"
                            width="19.596"
                            height="14.184"
                            fill="#fff"
                          />
                        </clipPath>
                      </defs>
                      <g id="Group_20523" data-name="Group 20523" transform="translate(-2126 -268)">
                        <rect
                          id="Rectangle_12829"
                          data-name="Rectangle 12829"
                          width="28"
                          height="28"
                          rx="3"
                          transform="translate(2126 268)"
                          fill="#f48b00"
                        />
                        <g id="Group_20503" data-name="Group 20503" transform="translate(2130 275)">
                          <g id="Group_20503-2" data-name="Group 20503" clipPath="url(#clip-path)">
                            <path
                              id="Path_18338"
                              data-name="Path 18338"
                              d="M185.6,44.086q-.015.956-.029,1.912c0,.081,0,.163,0,.266h-.246c-1.972,0-3.944,0-5.917.006a6.491,6.491,0,0,0-2.11.3c-.351.121-.686.292-1.022.438a1.489,1.489,0,0,1,1.189-1.775,6.89,6.89,0,0,1,1.84-.19c1.538-.007,3.076,0,4.615,0,.437,0,.588-.149.588-.585q0-4.663,0-9.327v-.281H185.6Z"
                              transform="translate(-166.001 -32.829)"
                              fill="#fff"
                            />
                            <path
                              id="Path_18339"
                              data-name="Path 18339"
                              d="M41.576,12.055a3.49,3.49,0,0,1-.42-.18,2.679,2.679,0,0,0-1.125-.239,14.394,14.394,0,0,0-1.584-.053c-1.321-.006-2.642,0-3.963,0h-.241V.027h.185c1.366,0,2.731,0,4.1,0a2.955,2.955,0,0,1,2.889,2.008,2.9,2.9,0,0,1,.162.916c.012,3,.008,6,.008,9,0,.031,0,.062-.007.1"
                              transform="translate(-32.253 -0.025)"
                              fill="#fff"
                            />
                            <path
                              id="Path_18340"
                              data-name="Path 18340"
                              d="M183.872,11.34h-.24c-1.59,0-3.179-.015-4.768.008a10.771,10.771,0,0,0-1.628.186,2.989,2.989,0,0,0-.657.285c0-.048-.011-.095-.011-.141q-.02-4.319-.038-8.638A2.927,2.927,0,0,1,178.543.177a3.224,3.224,0,0,1,.974-.166C180.928-.007,182.339,0,183.749,0c.037,0,.074,0,.123.008Z"
                              transform="translate(-166.277 0)"
                              fill="#fff"
                            />
                            <path
                              id="Path_18341"
                              data-name="Path 18341"
                              d="M0,34.972H1.079v.264q0,4.654,0,9.308a1.166,1.166,0,0,0,.037.34c.063.2.259.308.54.308,1.417,0,2.834-.011,4.251.006.636.008,1.272.062,1.907.118a2.042,2.042,0,0,1,.584.168,1.444,1.444,0,0,1,.922,1.623c-.386-.159-.774-.35-1.18-.48a6.248,6.248,0,0,0-1.887-.259q-3-.016-5.993-.021H0Z"
                              transform="translate(0 -32.941)"
                              fill="#fff"
                            />
                          </g>
                        </g>
                      </g>
                    </svg>
                  </div>
                ) : (
                  <div className="su__d-flex ">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="28"
                      height="28"
                      viewBox="0 0 28 28"
                    >
                      <defs>
                        <clipPath id="clip-path">
                          <rect
                            id="Rectangle_12828"
                            data-name="Rectangle 12828"
                            width="18.617"
                            height="15.523"
                            fill="#fff"
                          />
                        </clipPath>
                      </defs>
                      <g id="Group_20527" data-name="Group 20527" transform="translate(-2126 -268)">
                        <g id="Group_20522" data-name="Group 20522">
                          <rect
                            id="Rectangle_12829"
                            data-name="Rectangle 12829"
                            width="28"
                            height="28"
                            rx="3"
                            transform="translate(2126 268)"
                            fill="#1770d4"
                          />
                          <g
                            id="Group_19803"
                            data-name="Group 19803"
                            transform="translate(2131 274)"
                          >
                            <g id="Group_19802" data-name="Group 19802" clipPath="url(#clip-path)">
                              <path
                                id="Path_7846"
                                data-name="Path 7846"
                                d="M0,173.456V167.6c.234-.27.4-.318.628-.157a1.763,1.763,0,0,0,1.058.311c1.945-.007,3.89,0,5.835,0H7.75c0,.**************.213,0,.3,0,.606,0,.909a.393.393,0,0,0,.441.436q1.109,0,2.218,0a.389.389,0,0,0,.433-.426c.008-.309,0-.618,0-.927,0-.064.008-.128.013-.205H11.1c1.951,0,3.9,0,5.853,0a1.675,1.675,0,0,0,1.023-.307.378.378,0,0,1,.441-.037.4.4,0,0,1,.2.381q0,2.708,0,5.417a1.471,1.471,0,0,1-.289.882,1.6,1.6,0,0,1-1.4.655q-7.616-.006-15.233,0c-.085,0-.17,0-.254-.01a1.538,1.538,0,0,1-1.373-1.095c-.019-.062-.044-.122-.066-.183"
                                transform="translate(0 -159.223)"
                                fill="#fff"
                              />
                              <path
                                id="Path_7847"
                                data-name="Path 7847"
                                d="M0,6.452V3.616C.008,3.607.021,3.6.024,3.59A1.561,1.561,0,0,1,1.71,2.328H5.423c0-.251-.007-.475,0-.7a2.228,2.228,0,0,1,.058-.466A1.547,1.547,0,0,1,7.08,0q2.226,0,4.453,0a1.985,1.985,0,0,1,.433.038,1.552,1.552,0,0,1,1.227,1.513c.005.252,0,.5,0,.774h.257c1.23,0,2.46-.017,3.689.014a1.508,1.508,0,0,1,1.477,1.57q0,1.118,0,2.235A1.542,1.542,0,0,1,17.007,7.76H10.856c0-.131,0-.24,0-.348a.382.382,0,0,0-.434-.419q-1.118,0-2.235,0a.377.377,0,0,0-.424.41c-.005.113,0,.226,0,.347-.062,0-.1.01-.145.01q-3.053,0-6.107,0a1.532,1.532,0,0,1-1.4-.981C.067,6.671.036,6.56,0,6.452M6.988,2.317h4.636V1.571H6.988Z"
                                transform="translate(0 0)"
                                fill="#fff"
                              />
                            </g>
                          </g>
                        </g>
                      </g>
                    </svg>
                  </div>
                )}
                <div className="su__d-flex su__align-items-center su__justify-content-center">
                  <h2 className="su__article-title su__text-left ">
                    {recordType === RECORD_TYPES.CASE
                      ? articleData.Subject || 'Untitled Subject'
                      : articleData.Title || 'Untitled Article'}
                  </h2>
                </div>
              </div>
              <div className="su__mr-20 su__mt-20 su__cursor_pointer" onClick={onClose}>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="13.236"
                  height="13.236"
                  viewBox="0 0 13.236 13.236"
                >
                  <path
                    id="svgexport-9"
                    d="M7.835,6.759l5.154-5.154A.857.857,0,0,0,11.776.393L6.622,5.546,1.468.393A.857.857,0,0,0,.256,1.605L5.41,6.759.256,11.913a.857.857,0,1,0,1.213,1.213L6.622,7.972l5.154,5.154a.857.857,0,0,0,1.213-1.213Z"
                    transform="translate(-0.004 -0.141)"
                    fill="#43425d"
                  />
                </svg>
              </div>
            </div>
            <div className="su__modal-body su__text-left su__ml-15  su__mr-15 ">
              <div className="su__d-grid su__border-bottom-grey su__padding-article-preview ">
                <span className="su__span-article su__m-b-5">
                  {recordType == RECORD_TYPES.CASE ? 'Subject' : 'Title'}
                </span>
                <span className="su__span-article-value su__pr-5px su__m-b-5">
                  {recordType === RECORD_TYPES.CASE
                    ? articleData.Subject || 'N/A'
                    : articleData.Title || 'Untitled Article'}
                </span>
              </div>
              <div className="su__justify-content-between su__d-flex su__padding-article-preview">
                <div className="su__d-grid su__w-45 su__border-bottom-grey ">
                  <span className="su__span-article su__m-b-5">Record Type</span>
                  <span className="su__span-article-value su__m-b-5 su__pr-5px">
                    {articleData.attributes.type || 'N/A'}
                  </span>
                </div>
                <div className="su__w-45 su__border-bottom-grey su__d-grid">
                  <span className="su__span-article su__m-b-5 ">
                    {recordType == RECORD_TYPES.CASE ? 'Case Number' : 'Article Number'}
                  </span>
                  <span className="su__span-article-value su__m-b-5 su__pr-5px">
                    {recordType === RECORD_TYPES.CASE
                      ? articleData.CaseNumber || 'N/A'
                      : articleData.ArticleNumber || 'N/A'}
                  </span>
                </div>
              </div>
              <div className="su__justify-content-between su__d-flex su__padding-article-preview">
                <div className="su__d-grid su__border-bottom-grey  su__w-45 ">
                  <span className="su__span-article su__m-b-5">
                    {recordType === RECORD_TYPES.CASE ? 'Priority' : 'Publish Status'}
                  </span>
                  <span className="su__span-article-value su__m-b-5 su__pr-5px">
                    {recordType == RECORD_TYPES.CASE
                      ? articleData.Priority || 'N/A'
                      : articleData.PublishStatus || 'N/A'}
                  </span>
                </div>
                <div className="su__w-45 su__border-bottom-grey su__d-grid">
                  <span className="su__span-article su__m-b-5">Last Modified</span>
                  <span className="su__span-article-value su__m-b-5 su__pr-5px">
                    {articleData.LastModifiedDate || 'N/A'}
                  </span>
                </div>
              </div>

              <div className="su__d-flex  su__border-bottom-grey su__d-grid su__padding-article-preview ">
                <span className="su__span-article su__m-b-5">
                  {recordType == RECORD_TYPES.CASE ? 'Description' : 'URL Name'}
                </span>
                <span className="su__span-article-value su__m-b-5 su__pr-5px">
                  {recordType == RECORD_TYPES.CASE
                    ? articleData.Description || 'N/A'
                    : articleData.UrlName || 'N/A'}
                </span>
              </div>
              <div className="su__w-45 su__border-bottom-grey su__d-grid su__padding-article-preview">
                <span className="su__span-article su__m-b-5">
                  {recordType === RECORD_TYPES.CASE ? 'Status' : 'Version Number'}
                </span>
                <span className="su__span-article-value su__m-b-5 su__pr-5px">
                  {recordType === RECORD_TYPES.CASE
                    ? articleData.Status || '  N/A'
                    : articleData.VersionNumber || 'N/A'}
                </span>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div className="su__no-content-preview su__w-100 su__h-100  su__flex-hcenter su__font-24 su__f-bold su__color-lgray su__position-relative">
          <div
            className="su__position-absolute su__top-0  su__right-0  su__m-3 su__cursor_pointer"
            onClick={onClose}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="13.236"
              height="13.236"
              viewBox="0 0 13.236 13.236"
            >
              <path
                id="svgexport-9"
                d="M7.835,6.759l5.154-5.154A.857.857,0,0,0,11.776.393L6.622,5.546,1.468.393A.857.857,0,0,0,.256,1.605L5.41,6.759.256,11.913a.857.857,0,1,0,1.213,1.213L6.622,7.972l5.154,5.154a.857.857,0,0,0,1.213-1.213Z"
                transform="translate(-0.004 -0.141)"
                fill="#43425d"
              />
            </svg>
          </div>
          {'Nothing to preview here'}
        </div>
      )}
    </>
  );
};

ArticlePreviewModal.propTypes = {
  isOpen: bool.isRequired,
  onClose: func.isRequired,
  articleData: object.isRequired,
  recordType: string
};

export default ArticlePreviewModal;
