import React, { Fragment } from 'react';
import variables from '../../../redux/variables';
const Href = (props) => {
  try {
    let { item, from } = props;
    return (
      <Fragment>
        <div className="su__flex-vcenter">
          {!variables.toggleDisplayKeys[2].hideEye && (
            <div
              className={`su__href-txt su__text-decoration su__color-gray-url   su_letter_space su__line-height-22 su__text-truncate su__font-13 su__sc-loading ${
                from === 'popup' ? 'su__href-txt-popup' : ''
              }`}
            >
              {' '}
              {item.href}
            </div>
          )}
        </div>
      </Fragment>
    );
  } catch (e) {
    console.log('Error in Href component', e);
    return <div></div>;
  }
};

export default React.memo(Href);
