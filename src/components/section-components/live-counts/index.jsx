import React from 'react';
const LiveCounts = (props) => {
  try {
    let { item } = props;
    return (
      // need boardName for aurora results as well
      item.boardName && (
        <div className="su__color-black su__mr-1 su__rtlmr-0 su__rtlml-2 su__line-height-n su__font-12 su__sc-loading">
          {[
            <span key="kudos">Kudos {item.liveCounts?.kudos}</span>,
            <span key="replies">Replies {item.liveCounts?.replies}</span>,
            <span key="views">Views {item.liveCounts?.views}</span>
          ].reduce((prev, curr) => [prev, ' | ', curr])}
        </div>
      )
    );
  } catch (e) {
    console.log('Error in Live Counts component', e);
    return <div></div>;
  }
};

export default React.memo(LiveCounts);
