/* eslint-disable react/prop-types */
import React from 'react';
import Tooltip from 'components/feature-components/tooltip/index.jsx';
import { useTranslation } from 'react-i18next';
import StaticStrings from '../../../StaticStrings';
import IconColors from '../../../IconColors';

const SearchTuning = (props) => {
  try {
    const { t } = useTranslation();
    let { item } = props;
    return (
      (item.bypass_filter || item.autotuned) && (
        <div className="su__flex-vcenter  su__search_tuning_icon">
          <div className="su__boosted-documents-positioning su__sc-loading">
            {item.bypass_filter ? (
              <div>
                <Tooltip text={t(StaticStrings.BOOSTED)} position="bottom">
                  <svg
                    id="trending-up"
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                  >
                    <g id="trending-up-2" data-name="trending-up">
                      <g id="trending-up-3" data-name="trending-up">
                        <rect
                          id="Rectangle_35"
                          data-name="Rectangle 35"
                          width="24"
                          height="24"
                          transform="translate(0 24) rotate(-90)"
                          fill="#1770d4"
                          opacity="0"
                        />
                        <path
                          id="Path_28"
                          data-name="Path 28"
                          d="M21,7a.78.78,0,0,0,0-.21.64.64,0,0,0-.05-.17,1.1,1.1,0,0,0-.09-.14.75.75,0,0,0-.14-.17l-.12-.07a.69.69,0,0,0-.19-.1h-.2A.7.7,0,0,0,20,6H15a1,1,0,0,0,0,2h2.83l-4,4.71L9.51,10.14a1,1,0,0,0-1.28.22l-5,6a1,1,0,0,0,1.54,1.28L9.22,12.3l4.27,2.56a1,1,0,0,0,1.27-.21L19,9.7V12a1,1,0,0,0,2,0Z"
                          fill="#1770d4"
                        />
                      </g>
                    </g>
                  </svg>
                </Tooltip>
              </div>
            ) : (
              <div>
                {item.autotuned ? (
                  <Tooltip text={t(StaticStrings.AUTOTUNED)} position="bottom">
                    <div className="su__flex-vcenter su__mx-2">
                      <svg
                        title="autotuning"
                        width="16"
                        height="16"
                        viewBox="0 0 16 16"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <defs>
                          <clipPath id="clip-Tunning-11">
                            <rect width="16" height="16" />
                          </clipPath>
                        </defs>
                        <g id="Tunning" clipPath="url(#clip-Tunning-11)">
                          <path
                            id="Path_1567"
                            data-name="Path 1567"
                            d="M3,15.444v1.778H8.333V15.444ZM3,4.778V6.556h8.889V4.778ZM11.889,19V17.222H19V15.444H11.889V13.667H10.111V19ZM6.556,8.333v1.778H3v1.778H6.556v1.778H8.333V8.333ZM19,11.889V10.111H10.111v1.778ZM13.667,8.333h1.778V6.556H19V4.778H15.444V3H13.667Z"
                            transform="translate(-3 -3)"
                            fill={IconColors.AutoTunesvgFill}
                          />
                        </g>
                      </svg>
                    </div>
                  </Tooltip>
                ) : null}
              </div>
            )}
          </div>
        </div>
      )
    );
  } catch (e) {
    console.log('Error in Solved component', e);
    return <div></div>;
  }
};

export default React.memo(SearchTuning);
