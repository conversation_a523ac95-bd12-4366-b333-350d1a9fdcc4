import React, { useState, Fragment, useRef, useEffect } from 'react';
import SavedResult from 'components/section-components/saved-result/index.jsx';
import SaveUnsaveText from 'components/section-components/saveUnsaveText/index.jsx';
import Summarization from '../summarization/index';

const GridView = (props) => {
  try {
    let {
      item,
      child,
      savedResultBookmarkClicked,
      savedResultLimitReachedFunc,
      bookmarkListIconFunc,
      isGridView,
      isDeviceMobile
    } = props;

    const componentTobeRendered = child.props.children;

    const [showText, setShowText] = useState(false);
    const [saved, setSaved] = useState(false);

    const handleButtonClick = () => {
      setSaved(!saved);
      setShowText(true);
      setTimeout(() => {
        setShowText(false);
      }, 1000);
    };
    const [hasContent, setHasContent] = useState(false);
    const containerRef = useRef(null);
    useEffect(() => {
      if (containerRef.current) {
        const belowDiscreption = containerRef.current.children.length > 0;
        setHasContent(belowDiscreption);
      }
    }, [componentTobeRendered]);

    return (
      <div className="su__list-item-row su__d-md-flex su__w-100">
        <div
          className={`${
            item.imageSource ? 'su__list-item-text' : 'su__w-100'
          } su__sm-w-100 su__media-body  su__word-break`}
        >
          <div className={`su__list-item-title su__flex-vcenter su__mw-100`}>
            <Fragment>
              <div className="su__d-flex su__justify-content-between su__align-items-center">
                <div className="su__aboveSourceLabel_Items">
                  {React.Children.map(componentTobeRendered, (child) => {
                    if (!child || !child.props || child.props['position'] != 'above-soucelabel')
                      return false;
                    return React.cloneElement(child, props, null);
                  })}
                </div>
                <div
                  className={`su__d-flex su__align-items-baseline ${
                    isDeviceMobile || isGridView ? '' : 'su__IconsSectionW-20'
                  }`}
                >
                  {window.scConfiguration && window.scConfiguration.summarizationEnabled && (
                    <Summarization item={item} index={props.index} />
                  )}
                  <div className={`${isDeviceMobile || isGridView ? 'su__w-30px' : 'su__w-50'}`}>
                    <SavedResult
                      bookmarkListIconFunc={bookmarkListIconFunc}
                      savedResultLimitReachedFunc={savedResultLimitReachedFunc}
                      savedResultBookmarkClicked={savedResultBookmarkClicked}
                      btnclick={handleButtonClick}
                      item={item}
                    />
                  </div>
                  <SaveUnsaveText
                    savedResultBookmarkClicked={savedResultBookmarkClicked}
                    saved={saved}
                    setSaved={setSaved}
                    showText={showText}
                    item={item}
                  />
                  {React.Children.map(componentTobeRendered, (child) => {
                    if (!child || !child.props || child.props['position'] != 'after-sourceLabel')
                      return false;
                    return React.cloneElement(child, props, null);
                  })}
                </div>
              </div>

              <div className="su__flex-vcenter su__flex-gap su__justify-content-between su__w-100 su__mt-1">
                {React.Children.map(componentTobeRendered, (child) => {
                  if (!child || !child.props || child.props['position'] != 'above-title')
                    return false;
                  return React.cloneElement(child, props, null);
                })}
                <div className="su__flex-vcenter su__sourceLabelSectionW-80">
                  {React.Children.map(componentTobeRendered, (child) => {
                    if (!child || !child.props || child.props['position'] != 'title-first')
                      return false;
                    return React.cloneElement(child, props, null);
                  })}
                </div>
              </div>
              <div className="su__d-flex">
                {React.Children.map(componentTobeRendered, (child) => {
                  if (!child || !child.props || child.props['position'] != 'title-second')
                    return false;
                  return React.cloneElement(child, props, null);
                })}
              </div>
            </Fragment>
          </div>

          {React.Children.map(componentTobeRendered, (child) => {
            if (!child || !child.props || child.props['position'] != 'below-title') return false;
            return React.cloneElement(child, props, null);
          })}

          <div
            className={`su__d-flex su__justify-content-between  ${
              hasContent ? 'su__min-height-30px' : ''
            } `}
          >
            {React.Children.map(componentTobeRendered, (child) => {
              if (!child || !child.props || child.props['position'] != 'below-description')
                return false;
              return React.cloneElement(child, props, null);
            })}
          </div>
        </div>
      </div>
    );
  } catch (e) {
    console.log('Error in GridView component', e);
    return <div></div>;
  }
};

export default React.memo(GridView);
