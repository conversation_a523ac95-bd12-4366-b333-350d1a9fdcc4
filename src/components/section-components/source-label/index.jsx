import React, { Fragment } from 'react';
import variables from '../../../redux/variables';
const SourceLabel = (props) => {
  try {
    let { item, from } = props;
    return (
      <Fragment>
        {!variables.toggleDisplayKeys[5].hideEye &&
          variables.searchCallVariables.showContentTag && (
            <span
              title={item.contentTag}
              className={`su__ribbon-title su__font-11 su__px-2 su__rtlml-0 su__d-inline-block su__radius-3 su_source_label_font su__f-normal su__sc-loading ${
                from === 'popup' ? 'su__popup-source-label' : 'su__source-label'
              }`}
            >
              {' '}
              {item.contentTag}
            </span>
          )}
      </Fragment>
    );
  } catch (e) {
    console.log('Error in SourceLabel component', e);
    return <div></div>;
  }
};

export default React.memo(SourceLabel);
