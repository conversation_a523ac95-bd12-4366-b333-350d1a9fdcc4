import React, { Fragment, useState, useRef, useEffect } from 'react';
import SavedResult from 'components/section-components/saved-result/index.jsx';
import SaveUnsaveText from 'components/section-components/saveUnsaveText/index.jsx';
import variables from 'redux/variables';
import { useSelector } from 'react-redux';
import Summarization from '../summarization/index';

const ListView = (props) => {
  try {
    let {
      item,
      child,
      savedResultBookmarkClicked,
      savedResultLimitReachedFunc,
      bookmarkListIconFunc
    } = props;
    const componentTobeRendered = child.props.children;
    const [showText, setShowText] = useState(false);
    const [saved, setSaved] = useState(false);
    let searchResult = useSelector((state) => state.searchResult);
    let metaStatus = Boolean(
      searchResult.metaGraph && Object.keys(searchResult.metaGraph).length > 0
    );
    let relatedTilesStatus = Boolean(
      searchResult.relatedTiles && searchResult.relatedTiles.length > 0
    );

    const handleButtonClick = () => {
      setSaved(!saved);
      setShowText(true);
      setTimeout(() => {
        setShowText(false);
      }, 1000);
    };
    const [hasContent, setHasContent] = useState({ below: false, end: false });
    const containerRef = useRef([]);
    useEffect(() => {
      if (containerRef.current) {
        const belowDiscreption = containerRef.current[0]?.children.length > 0;
        const Endposition = containerRef.current[1]?.children.length > 0;
        setHasContent({ below: belowDiscreption, end: Endposition });
      }
    }, [componentTobeRendered]);
    return (
      <Fragment>
        <div className="su__list-item-row su__w-100 su__d-flex su__justify-content-between">
          {React.Children.map(componentTobeRendered, (child) => {
            if (!child || !child.props || child.props['position'] != 'left') return false;
            return React.cloneElement(child, props, null);
          })}
          <div
            className={`${
              item.imageSource ? 'su__list-item-text' : 'su__w-100'
            } su__media-body su__word-break su__mx-w90 ${
              ((metaStatus || relatedTilesStatus) && !variables.mergeResults) ||
              (variables.mergeResults &&
                !variables.resultsInAllContentSources &&
                (metaStatus || relatedTilesStatus))
                ? 'su__min-width-article-btn'
                : null
            } ${item.imageSource ? 'su__min-width-article-btn_img' : null}`}
          >
            <div className="su__flex-vcenter">
              <Fragment>
                {React.Children.map(componentTobeRendered, (child) => {
                  if (!child || !child.props || child.props.position !== 'indextitle') return null;
                  return React.cloneElement(child, props);
                })}
              </Fragment>
            </div>
            <div className={`su__list-item-title su__flex-vcenter su__mw-100`}>
              <Fragment>
                {React.Children.map(componentTobeRendered, (child) => {
                  if (!child || !child.props || child.props['position'] != 'title') return false;
                  return React.cloneElement(child, props, null);
                })}
              </Fragment>
            </div>
            {React.Children.map(componentTobeRendered, (child) => {
              if (!child || !child.props || child.props['position'] != 'center') return false;
              return React.cloneElement(child, props, null);
            })}
            <div
              ref={(ele) => {
                containerRef.current[0] = ele;
              }}
              className={`su__d-flex su__justify-content-between  ${
                hasContent.below ? 'su__min-height-30px' : ''
              } su__flex-column `}
            >
              {React.Children.map(componentTobeRendered, (child) => {
                if (!child || !child.props || child.props['position'] != 'below-description')
                  return false;
                return React.cloneElement(child, props, null);
              })}
            </div>
          </div>
          <div className="su__ml-2 su__text-right su__loading-view su__d-flex su__align-items-baseline">
            {window.scConfiguration && window.scConfiguration.summarizationEnabled && (
              <Summarization item={item} index={props.index} />
            )}
            <SavedResult
              bookmarkListIconFunc={bookmarkListIconFunc}
              savedResultLimitReachedFunc={savedResultLimitReachedFunc}
              savedResultBookmarkClicked={savedResultBookmarkClicked}
              btnclick={handleButtonClick}
              item={item}
            />
            <SaveUnsaveText
              savedResultBookmarkClicked={savedResultBookmarkClicked}
              saved={saved}
              setSaved={setSaved}
              showText={showText}
              item={item}
            />
            {React.Children.map(componentTobeRendered, (child) => {
              if (!child || !child.props || child.props['position'] != 'right') return false;
              return React.cloneElement(child, props, null);
            })}
          </div>
        </div>
        <div
          className={
            item.relevanceBand || item.relevancePercentile
              ? 'su__d-flex su__justify-content-between su__w-100 multiVersionGridView su__zendesk-flex-wrap-reverse'
              : 'multiVersionGridView'
          }
        >
          {React.Children.map(componentTobeRendered, (child) => {
            if (!child || !child.props || child.props['position'] != 'end') return false;

            return React.cloneElement(child, props, null);
          })}
        </div>
      </Fragment>
    );
  } catch (e) {
    console.log('Error in ListView component', e);
    return <div></div>;
  }
};

export default React.memo(ListView);
