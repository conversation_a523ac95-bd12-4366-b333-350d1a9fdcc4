/* eslint-disable no-undef */
import React, { useEffect, useState, useRef } from 'react';
import variables from '../../../redux/variables';
import Tooltip from 'components/feature-components/tooltip/index';
import StaticStrings from 'StaticStrings';
import { useTranslation } from 'react-i18next';
const AttachToTicketIcon = (props) => {
  try {
    let { item, index, showAttachToTicket, setActiveAttachIndex, isListView } = props;
    const attachToTicketIconRef = useRef(null);
    const attachToTicketRef = useRef(null);
    const [Ticket, isTicket] = useState(false);
    const { t } = useTranslation();
    const outsideClickHandler = (event) => {
      if (attachToTicketRef.current && attachToTicketIconRef.current) {
        if (
          !attachToTicketRef.current.contains(event.target) &&
          !attachToTicketIconRef.current.contains(event.target)
        ) {
          setActiveAttachIndex(null);
          isTicket((Ticket) => Ticket === false);
        }
      }
    };

    useEffect(() => {
      document.removeEventListener('click', outsideClickHandler);
      document.addEventListener('click', outsideClickHandler);
      return () => document.removeEventListener('click', outsideClickHandler);
    }, []);

    const openTicket = (item, id) => {
      setActiveAttachIndex(id);
      isTicket(!Ticket);
    };

    const clickToAttachTicket = (result) => {
      client.get('ticket').then(function (data) {
        client.get('currentUser').then(function (user) {
          let agentEmail = user.currentUser.email;
          client
            .invoke('ticket.comment.appendText', 'Hi. Check out this article: ' + result.href)
            .then(function () {});
          gza('attachToCaseComment', {
            searchString: variables.searchCallVariables.searchString,
            id: result['_id'],
            url: result.href,
            t: result.highlight.TitleToDisplayString,
            subject: data.ticket.subject,
            caseNumber: data.ticket.id,
            author: agentEmail,
            index: result.sourceName,
            type: result.objName
          });
        });
      });
      isTicket(false);
    };

    return (
      <>
        <div>
          <Tooltip
            text={t(StaticStrings.ATTACHTOTICKET)}
            position="bottom"
            className="position-relative"
            tipClassName={
              'su__position-absolute  su__zindex-1  su__tooltip-positions su__sc-loading'
            }
          >
            <div className="su__attachTicket">
              <span
                ref={attachToTicketIconRef}
                className="su__attachIcon"
                onClick={() => {
                  openTicket(item, index);
                }}
              >
                {' '}
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="22"
                  height="22"
                  viewBox="0 0 16.908 16.908"
                >
                  <path
                    id="attach_file_24dp_5F6368_FILL0_wght400_GRAD0_opsz24"
                    d="M229.2-869.884a4.433,4.433,0,0,1-1.343,3.256,4.433,4.433,0,0,1-3.256,1.343,4.433,4.433,0,0,1-3.256-1.343A4.433,4.433,0,0,1,220-869.884v-6.805a3.193,3.193,0,0,1,.966-2.345,3.193,3.193,0,0,1,2.345-.966,3.192,3.192,0,0,1,2.345.966,3.193,3.193,0,0,1,.966,2.345v6.437a1.952,1.952,0,0,1-.589,1.435,1.952,1.952,0,0,1-1.435.589,1.952,1.952,0,0,1-1.435-.589,1.952,1.952,0,0,1-.589-1.435v-6.07a.712.712,0,0,1,.211-.524.712.712,0,0,1,.524-.211.712.712,0,0,1,.524.211.712.712,0,0,1,.212.524v6.07a.536.536,0,0,0,.156.4.536.536,0,0,0,.4.156.536.536,0,0,0,.4-.156.536.536,0,0,0,.156-.4v-6.437a1.86,1.86,0,0,0-.543-1.306,1.746,1.746,0,0,0-1.3-.533A1.776,1.776,0,0,0,222-878a1.777,1.777,0,0,0-.533,1.306v6.805a2.939,2.939,0,0,0,.9,2.216,3.047,3.047,0,0,0,2.226.91,2.964,2.964,0,0,0,2.189-.91,3.167,3.167,0,0,0,.938-2.216v-6.437a.712.712,0,0,1,.212-.524.712.712,0,0,1,.524-.211.712.712,0,0,1,.524.211.712.712,0,0,1,.212.524Z"
                    transform="translate(-767.413 466.691) rotate(45)"
                    fill="#464646"
                  />
                </svg>
              </span>
            </div>
          </Tooltip>
          <span>
            {showAttachToTicket && Ticket && (
              <div
                ref={attachToTicketRef}
                className={`su__ticketIcon su__p-1px ${isListView ? 'su__right-0' : ''}`}
                onClick={() => {
                  clickToAttachTicket(item);
                }}
              >
                <ul className="su__ticket">
                  <li className="su-other-elements-section su-attach-case su_ticket_attach">
                    <a className="su-case-attach su__cursor">Attach To Ticket</a>
                  </li>
                </ul>
              </div>
            )}
          </span>
        </div>
      </>
    );
  } catch (e) {
    console.log('Error in SourceLabel component', e);
    return <div></div>;
  }
};

export default React.memo(AttachToTicketIcon);
