/* global jest, it, expect, describe, beforeEach */
import React from 'react';
import { shallow } from 'enzyme';
import FeedbackSearch from './index';
import { Rating } from 'components/section-components/rating/index.jsx';
jest.mock('../../../redux/variables', () => require('__mocks__/variables'));
describe('FeedbackSearch Component', () => {
  let props;
  let wrapper;

  beforeEach(() => {
    props = {
      textAreaFeedback: '',
      settextAreaFeedback: jest.fn(),
      selectedTextFeedback: 'Selected feedback text',
      questionOne: 'How would you rate your experience?',
      questionTwo: 'Any additional feedback?',
      selectedSearchTemplate: 'feedback-template',
      rating: 3,
      hoverRating: 0,
      hanleMouseEnter: jest.fn(),
      handleMouseLeave: jest.fn(),
      handleSaveRating: jest.fn(),
      huddleOnChange: jest.fn(),
      isValid: false,
      isEmailPrefilled: '',
      isSupportPageUrl: false,
      followupEmail: '',
      setFollowupEmail: jest.fn()
    };

    wrapper = shallow(<FeedbackSearch {...props} />);
  });

  it('should render without crashing', () => {
    expect(wrapper.exists()).toBe(true);
  });

  it('should display the rating question (questionOne)', () => {
    expect(wrapper.find('.su__feedback-label').at(0).text()).toEqual(props.questionOne);
  });

  it('should render the Rating component with correct props', () => {
    const ratingComponent = wrapper.find(Rating);
    expect(ratingComponent.exists()).toBe(true);
    expect(ratingComponent.prop('feedbackType')).toBe(props.selectedSearchTemplate);
    expect(ratingComponent.prop('rating')).toBe(props.rating);
    expect(ratingComponent.prop('onMouseEnter')).toBe(props.hanleMouseEnter);
    expect(ratingComponent.prop('onMouseLeave')).toBe(props.handleMouseLeave);
    expect(ratingComponent.prop('onClick')).toBe(props.handleSaveRating);
  });

  it('should render textarea and handle input correctly', () => {
    const textarea = wrapper.find('textarea');
    expect(textarea.exists()).toBe(true);
    textarea.simulate('change', { target: { value: 'New feedback' } });
    expect(props.settextAreaFeedback).toHaveBeenCalledWith('New feedback');
  });

  it('should limit the textarea input to maxCharacters', () => {
    const maxCharacters = 300;
    const textarea = wrapper.find('textarea');

    textarea.simulate('change', { target: { value: 'a'.repeat(maxCharacters) } });
    expect(props.settextAreaFeedback).toHaveBeenCalledWith('a'.repeat(maxCharacters));
  });

  it('should display character count for textarea', () => {
    props.textAreaFeedback = 'Some feedback';
    wrapper.setProps(props);
    const charLimit = wrapper.find('.su__feedback-charlimit');
    expect(charLimit.text().trim()).toBe('13/300'); // Trimmed text comparison
  });

  it('should not display the second question if isSupportPageUrl is true', () => {
    props.isSupportPageUrl = true;
    wrapper.setProps(props);
    expect(wrapper.find('.su__feed-txtarea').exists()).toBe(false);
    expect(wrapper.find('.su__feed-email-box').exists()).toBe(false);
  });

  it('should handle rating interactions correctly', () => {
    const ratingComponent = wrapper.find(Rating);
    ratingComponent.simulate('mouseEnter', 4);
    expect(props.hanleMouseEnter).toHaveBeenCalledWith(4);
    ratingComponent.simulate('mouseLeave');
    expect(props.handleMouseLeave).toHaveBeenCalled();
    ratingComponent.simulate('click', 5);
    expect(props.handleSaveRating).toHaveBeenCalledWith(5);
  });
});
