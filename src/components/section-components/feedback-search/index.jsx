/* eslint-disable react/prop-types */
import React from 'react';
import { Rating } from 'components/section-components/rating/index.jsx';
import { useTranslation } from 'react-i18next';
import StaticStrings from 'StaticStrings';
import variables from '../../../redux/variables';
import EmailInput from '../email-input/index';
import { tabIndexes } from 'constants/a11y';
const FeedbackSearch = (props) => {
  try {
    const { t } = useTranslation();
    let {
      textAreaFeedback,
      settextAreaFeedback,
      selectedTextFeedback,
      questionOne,
      questionTwo,
      selectedSearchTemplate,
      rating,
      hoverRating,
      hanleMouseEnter,
      handleMouseLeave,
      handleSaveRating,
      huddleOnChange,
      isSupportPageUrl,
      followupEmail,
      setFollowupEmail,
      isValid
    } = props;
    const maxCharacters = 300;

    return (
      <>
        <div className="su__feed-rating su__font-10">
          {/* Question 1 + Rating component needed for in case of SupportURL (true/false both) */}
          <div className="su__feedback-label su__font-11 su__color-lgray">{t(questionOne)}</div>
          <Rating
            feedbackType={selectedSearchTemplate}
            size={40}
            stars={5}
            rating={rating}
            hoverRating={hoverRating}
            onMouseEnter={hanleMouseEnter}
            onMouseLeave={handleMouseLeave}
            onClick={handleSaveRating}
          />
        </div>

        {/* In case of SupportURL: false -> Question 2 (Text feedback + email input)  */}
        {!isSupportPageUrl && (
          <>
            <div className="su__feed-txtarea">
              <div
                lang={variables.searchCallVariables.langAttr}
                aria-label={t(selectedTextFeedback)}
                className="su__feedback-label su__font-11 su__color-lgray"
              >
                {t(questionTwo)}
              </div>
              <div className="su__w-100 su__feedback-textarea">
                <textarea
                  lang={variables.searchCallVariables.langAttr}
                  name="feeback-txtarea"
                  maxLength={maxCharacters}
                  className="su__feedtext-area su__w-100 su__box-sizing"
                  id="su__feedtext-area"
                  rows="4"
                  value={textAreaFeedback}
                  onChange={(e) => {
                    const value = e.target.value;
                    settextAreaFeedback(value.trim() === '' ? '' : value);
                  }}
                  aria-label={t(StaticStrings.say_more)}
                ></textarea>
                <div className="su__feedback-charlimit">
                  {textAreaFeedback.length}/{maxCharacters}{' '}
                </div>
              </div>
            </div>
            <EmailInput
              setFollowupEmail={setFollowupEmail}
              huddleOnChange={huddleOnChange}
              followupEmail={followupEmail}
              tabIndex={tabIndexes.tabIndex_0}
              isValid={isValid}
            />
          </>
        )}
      </>
    );
  } catch (e) {
    console.log('Error in FeedbackSearch component', e);
    return <div></div>;
  }
};

export default React.memo(FeedbackSearch);
