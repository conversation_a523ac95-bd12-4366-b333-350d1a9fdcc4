/* global jest, it, expect, describe, beforeEach, afterEach */
import React from 'react';
import { mount } from 'enzyme';
import { useSelector } from 'react-redux';
import state from '../../../../__mocks__/state';
import Title from './index';

jest.mock('react-redux', () => ({
  useSelector: jest.fn()
}));
jest.mock('react-i18next', () => ({
  useTranslation: () => ({ t: (key) => key })
}));
global.client = {
  invoke: jest.fn()
};

// Mock the gza function
global.gza = jest.fn();

jest.mock('../../../redux/variables', () => require('__mocks__/variables'));

describe('Title Component', () => {
  let props = {};

  beforeEach(() => {
    // Mock useSelector to return both searchResult and firstLoad
    useSelector.mockImplementation((selectorFn) =>
      selectorFn({
        ...state,
        firstLoad: false
      })
    );

    props = {
      item: {
        objName: 'tickets',
        highlight: { TitleToDisplay: ['Test Title'] },
        href: 'https://example.com',
        _id: '123',
        allowlinkopennewtab: true
      },
      index: 0,
      linkOpened: jest.fn(),
      setTitleClicked: jest.fn(),
      urlOpensInNewTab: true,
      viewedResult: false
    };
    jest.resetModules(); // Ensures a fresh import of mocked modules

    jest.doMock('../../../redux/variables', () => ({
      controllingVariables: {
        firstTimeLoad: true,
        processing: false,
        urlState: 0,
        currentUrlState: 0
      },
      toggleDisplayKeys: [
        { key: 'Title', hideEye: true }, // Ensure `hideEye: true` for Title
        { key: 'Summary', hideEye: false },
        { key: 'Url', hideEye: false },
        { key: 'Metadata', hideEye: false },
        { key: 'Icon', hideEye: false },
        { key: 'Tag', hideEye: false }
      ]
    }));
  });

  afterEach(() => {
    jest.clearAllTimers();
    jest.clearAllMocks();
    jest.resetModules();
  });
  it('renders without crashing', () => {
    const wrapper = mount(<Title {...props} />);
    expect(wrapper.exists()).toBe(true);
  });
  it('renders the correct title', () => {
    const wrapper = mount(<Title {...props} />);
    expect(wrapper.find('h2').text()).toBe('Test Title');
  });

  it('calls `linkOpened` when clicked (new tab)', () => {
    const wrapper = mount(<Title {...props} />);

    wrapper.find('a').simulate('click');
    expect(props.linkOpened).toHaveBeenCalledWith(props.item, 1);
  });

  it('calls `ticketClicked` when clicked (without new tab)', (done) => {
    props.item.allowlinkopennewtab = false;
    props.urlOpensInNewTab = false;
    const wrapper = mount(<Title {...props} />);
    wrapper.find('a').simulate('click');

    setImmediate(() => {
      console.log(props.linkOpened.mock.calls, 'check console');
      expect(props.linkOpened).toHaveBeenCalledWith(props.item, 2);
      done();
    });
  });

  it('calls `linkOpened` on middle-click', () => {
    const wrapper = mount(<Title {...props} />);

    wrapper.find('a').simulate('mousedown', { button: 1 });
    expect(props.linkOpened).toHaveBeenCalledWith(props.item, 1);
  });

  it('calls `ticketClicked` on right-click', () => {
    props.item.allowlinkopennewtab = false;
    const wrapper = mount(<Title {...props} />);

    wrapper.find('a').simulate('contextmenu');
    expect(props.linkOpened).toHaveBeenCalledWith(props.item, 2);
    //expect(client.invoke).toHaveBeenCalledWith('routeTo', 'ticket', props.item._id);
  });

  //     jest.resetModules();

  //     jest.doMock('../../../redux/variables', () => ({
  //       searchClientType: 16,
  //       toggleDisplayKeys: [{ hideEye: true }] // Ensure it's mocked properly
  //     }));

  //     // Re-import the mocked module
  //     const variables = require('../../../redux/variables');
  //     console.log('hideEye in test:', variables.toggleDisplayKeys[0].hideEye);

  //     const wrapper = mount(<Title {...props} />);

  //     console.log('Anchor exists:', wrapper.find('a').exists()); // Debugging output
  //     expect(wrapper.find('a').exists()).toBe(false);
  //   });
  //   it('does not render if `hideEye` is true', () => {
  //     const wrapper = mount(<Title {...props} />);

  //     console.log('Anchor exists:', wrapper.find('a').exists()); // Debugging output
  //     expect(wrapper.find('a').exists()).toBe(false);
  //   });
});
