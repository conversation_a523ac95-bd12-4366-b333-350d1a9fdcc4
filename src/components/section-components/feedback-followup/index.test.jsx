/* global jest, it, expect, describe */
import React from 'react';
import { shallow } from 'enzyme';
import FeedbackFollowUP from './index';

// Mock the required modules
jest.mock('react-redux', () => ({
  useSelector: jest.fn()
}));

jest.mock('react-i18next', () => ({
  useTranslation: () => ({ t: (key) => key })
}));

jest.mock('../../../redux/variables', () => require('__mocks__/variables'));

jest.mock('react-redux', () => ({
  useSelector: jest.fn()
}));

// Mock sessionStorage and localStorage
const mockSessionStorage = {};
const mockLocalStorage = {};
Object.defineProperty(window, 'sessionStorage', {
  value: {
    getItem: jest.fn((key) => mockSessionStorage[key]),
    setItem: jest.fn((key, value) => {
      mockSessionStorage[key] = value;
    }),
    removeItem: jest.fn((key) => {
      delete mockSessionStorage[key];
    })
  }
});

Object.defineProperty(window, 'localStorage', {
  value: {
    getItem: jest.fn((key) => mockLocalStorage[key]),
    setItem: jest.fn((key, value) => {
      mockLocalStorage[key] = value;
    }),
    removeItem: jest.fn((key) => {
      delete mockLocalStorage[key];
    })
  }
});

// Mock utility functions
global._gr_utility_functions = {
  getCookie: jest.fn(() => 'mock-session-id')
};

describe('FeedbackFollowUP Component', () => {
  const mockProps = {
    questionTwo: 'Question Two Text',
    questionFour: 'Question Four Text',
    selectedSearchFollowUp: 'Search Follow-Up Text',
    isSupportPageUrl: true,
    onSelectionChange: jest.fn(),
    selectedOption: 'Yes',
    selectedTextFeedback: 'Text Feedback Placeholder',
    textAreaFeedback: '',
    settextAreaFeedback: jest.fn(),
    followupEmail: '',
    huddleOnChange: jest.fn(),
    setFollowupEmail: jest.fn(),
    isTooEarlyFeedback: false,
    isMultiStepQuestions: { step: 0 }
  };

  it('should render without crashing', () => {
    const wrapper = shallow(<FeedbackFollowUP {...mockProps} />);
    expect(wrapper.exists()).toBe(true);
  });

  it('should display radio buttons when on step 0', () => {
    const wrapper = shallow(<FeedbackFollowUP {...mockProps} />);
    expect(wrapper.find('input[type="radio"]').length).toBeGreaterThan(0);
  });

  it('should call onSelectionChange when a radio button is selected', () => {
    const wrapper = shallow(<FeedbackFollowUP {...mockProps} />);
    wrapper.find('#su__feed-yes').simulate('change', { target: { value: 'Yes' } });
    expect(mockProps.onSelectionChange).toHaveBeenCalledWith('Yes');
  });

  it('should display textarea when selectedOption is "No" and on step 0', () => {
    const updatedProps = {
      ...mockProps,
      selectedOption: 'No'
    };
    const wrapper = shallow(<FeedbackFollowUP {...updatedProps} />);
    expect(wrapper.find('textarea').exists()).toBe(true);
  });

  it('should call settextAreaFeedback when textarea value changes', () => {
    const updatedProps = {
      ...mockProps,
      selectedOption: 'No'
    };
    const wrapper = shallow(<FeedbackFollowUP {...updatedProps} />);
    wrapper.find('textarea').simulate('change', { target: { value: 'Feedback Text' } });
    expect(mockProps.settextAreaFeedback).toHaveBeenCalledWith('Feedback Text');
  });
});
