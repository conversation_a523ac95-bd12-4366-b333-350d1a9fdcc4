import React from 'react';
import { useTranslation } from 'react-i18next';
import StaticStrings from 'StaticStrings';
import { a11y, tabIndexes } from '../../../constants/a11y';
import variables from '../../../redux/variables';
import EmailInput from '../email-input/index';

const _YES = 'Yes';
const _NO = 'No';

const FeedbackFollowUP = (props) => {
  try {
    const { t } = useTranslation();
    let {
      questionTwo,
      questionFour,
      selectedSearchFollowUp,
      isSupportPageUrl,
      onSelectionChange,
      selectedOption,
      selectedTextFeedback,
      textAreaFeedback,
      settextAreaFeedback,
      followupEmail,
      huddleOnChange,
      setFollowupEmail,
      isTooEarlyFeedback,
      isMultiStepQuestions,
      isValid
    } = props;

    const handleRadioChange = (event) => {
      onSelectionChange(event.target.value);
    };
    const maxCharacters = 300;

    return (
      <>
        {isSupportPageUrl && isMultiStepQuestions.step == 0 && (
          <div className="su__feed-radio">
            {' '}
            <div
              lang={variables.searchCallVariables.langAttr}
              aria-label={t(selectedSearchFollowUp)}
              className="su__feedback-label su__font-11 su__color-lgray"
            >
              {t(questionTwo)}
            </div>
            <div className="su__feedradio-group">
              <div role="radiogroup" className="su__feedradio-row su__flex-vcenter su__font-14">
                <input
                  lang={variables.searchCallVariables.langAttr}
                  aria-label={t(StaticStrings.su_yes)}
                  tabIndex={tabIndexes.tabIndex_0}
                  type="radio"
                  id="su__feed-yes"
                  name="su__feedback-followone"
                  checked={selectedOption === _YES}
                  value="Yes"
                  onChange={handleRadioChange}
                  className="su__feedback-radio-btns"
                />
                <label
                  lang={variables.searchCallVariables.langAttr}
                  aria-label={t(StaticStrings.su_yes)}
                  role={a11y.ROLES.RADIO}
                  className="su__feed-labels su__cursor su__font-12 su__position-relative su__mr-2"
                  htmlFor="su__feed-yes"
                >
                  {t(StaticStrings.su_yes)}
                </label>
                <input
                  lang={variables.searchCallVariables.langAttr}
                  aria-label={t(StaticStrings.su_no)}
                  tabIndex={tabIndexes.tabIndex_0}
                  type="radio"
                  id="su__feed-no"
                  name="su__feedback-followtwo"
                  checked={selectedOption === 'No'}
                  value="No"
                  onChange={handleRadioChange}
                  className="su__feedback-radio-btns"
                />
                <label
                  lang={variables.searchCallVariables.langAttr}
                  aria-label={t(StaticStrings.su_no)}
                  role={a11y.ROLES.RADIO}
                  className="su__feed-labels su__cursor su__font-12 su__position-relative su__mr-2"
                  htmlFor="su__feed-no"
                >
                  {t(StaticStrings.su_no)}
                </label>
                {!isTooEarlyFeedback && (
                  <>
                    <input
                      lang={variables.searchCallVariables.langAttr}
                      aria-label={t(StaticStrings.su_too_early_to_comment)}
                      tabIndex={tabIndexes.tabIndex_0}
                      type="radio"
                      id="su__feed-too-early"
                      name="su__feedback-followthree"
                      checked={selectedOption === 'Early'}
                      value="Early"
                      onChange={handleRadioChange}
                      className="su__feedback-radio-btns"
                    />
                    <label
                      lang={variables.searchCallVariables.langAttr}
                      aria-label={t(StaticStrings.su_too_early_to_comment)}
                      role={a11y.ROLES.RADIO}
                      className="su__feed-labels su__cursor su__font-12 su__position-relative su__mr-2"
                      htmlFor="su__feed-too-early"
                    >
                      {t(StaticStrings.su_too_early_to_comment)}
                    </label>
                  </>
                )}
              </div>
            </div>
          </div>
        )}

        {isSupportPageUrl &&
          selectedOption === _NO &&
          isMultiStepQuestions.step == 0 &&
          !isTooEarlyFeedback && (
            <>
              <div className="su__feed-txtarea">
                <>
                  <div
                    lang={variables.searchCallVariables.langAttr}
                    aria-label={t(selectedTextFeedback)}
                    className="su__feedback-label su__font-11 su__color-lgray"
                  >
                    {t(questionFour)}
                  </div>
                  <div className="su__w-100 su__feedback-textarea">
                    <textarea
                      lang={variables.searchCallVariables.langAttr}
                      name="feeback-txtarea"
                      className="su__feedtext-area su__w-100 su__box-sizing"
                      id="su__feedtext-area"
                      rows="4"
                      maxLength={maxCharacters}
                      value={textAreaFeedback}
                      onChange={(e) => {
                        const value = !e.target.value.trim();
                        settextAreaFeedback(value ? '' : e.target.value);
                      }}
                      aria-label={t(StaticStrings.say_more)}
                    ></textarea>
                    <div className="su__feedback-charlimit">
                      {textAreaFeedback.length}/{maxCharacters}{' '}
                    </div>
                  </div>
                </>
              </div>
              <EmailInput
                setFollowupEmail={setFollowupEmail}
                huddleOnChange={huddleOnChange}
                followupEmail={followupEmail}
                tabIndex={tabIndexes.tabIndex_0}
                isValid={isValid}
              />
            </>
          )}
      </>
    );
  } catch (e) {
    console.log('Error in FeedbackFollowUP component', e);
    return <div></div>;
  }
};

export default React.memo(FeedbackFollowUP);
