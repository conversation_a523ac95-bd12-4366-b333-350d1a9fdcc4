import React, { useEffect, useState, useRef } from 'react';
import variables from '../../../redux/variables';
import { v4 as uuid } from 'uuid';
import StaticStrings from 'StaticStrings';
import { useDevice } from 'function-library/hooks';
import { useTranslation } from 'react-i18next';

const KUDOS = 'Kudos';
const VIEWS = 'Views';
const REPLY_COUNT = 'Reply Count';

const Metadata = (props) => {
  try {
    let { item, from } = props;
    item.metadata.sort((a, b) => {
      return a.value.join('').length - b.value.join('').length;
    });

    const [showTagBtn, setShowTagBtn] = useState(false);
    const metaBoxRef = useRef(null);
    const { isDeviceMobile } = useDevice();
    const { t } = useTranslation();
    useEffect(() => {
      if (isDeviceMobile) {
        const tagBoxHeight = metaBoxRef.current;
        tagBoxHeight && tagBoxHeight.clientHeight > 50 && setShowTagBtn(true);
      }
    }, [metaBoxRef.current]);

    const expandTagBox = () => {
      setShowTagBtn(false);
    };

    return (
      <>
        {item.metadata && item.metadata.length > 0 && (
          <div className="su__d-flex su__flex-column su__w-100 su__sc-loading">
            <div className={`${showTagBtn && isDeviceMobile ? 'su__tagBox_collapse' : null}`}>
              <div
                ref={metaBoxRef}
                className={`su__tag_box su__flex-wrap su__align-content-around font-12 su__flex-vcenter ${
                  from === 'popup' ? '' : ' su__mt-1'
                }`}
              >
                {React.Children.toArray(
                  item.metadata.map((data) => (
                    <>
                      {!variables.toggleDisplayKeys[3].hideEye &&
                      data.value[0]?.toString?.().length &&
                      data.value[0] !== '' &&
                      data.key != KUDOS &&
                      data.key != VIEWS &&
                      data.key != REPLY_COUNT ? (
                        <div className={`su__meta-data `} key={uuid()}>
                          <div className="su__d-flex su__flex-wrap su__flex-vcenter su__mb-1">
                            <span
                              className={`metaDataKey  su__rtlmr-0 su__rtlml-2 su__font-11 su__color-grey su__f-medium ${
                                from ? 'su__mr-5px ' : 'su__mr-2'
                              }`}
                            >
                              {' '}
                              {data.key}
                              {': '}
                            </span>
                            {data.value.length && (
                              <span className="su__flex-1 su__flex-vcenter su__flex-wrap">
                                {React.Children.toArray(
                                  data.value.map((dataItem) => (
                                    <span
                                      key={uuid()}
                                      className={`su__color_grey su__text-center  su__radius-2 su__mr-1 su__rtlmr-0 su__rtlml-2 su__line-height-n su__font-10 su__f-normal  su__segoe-ui ${
                                        from ? 'su__tags-summary' : 'su__tags'
                                      }`}
                                      dangerouslySetInnerHTML={{ __html: dataItem }}
                                    ></span>
                                  ))
                                )}
                              </span>
                            )}
                          </div>
                        </div>
                      ) : null}
                    </>
                  ))
                )}
              </div>
            </div>
            {showTagBtn && isDeviceMobile && (
              <div className="su__view_all_tag ">
                <button onClick={expandTagBox} className="su__view_all_tag_btn ">
                  {`${t(StaticStrings.VIEWALLTAGS)}`}
                </button>
              </div>
            )}
          </div>
        )}
      </>
    );
  } catch (e) {
    console.log('Error in Metadata component', e);
    return <div></div>;
  }
};

export default React.memo(Metadata);
