import React from 'react';
import { useTranslation } from 'react-i18next';

const FeedbackTextual = (props) => {
  try {
    const { t } = useTranslation();
    let { selectedPageTextFeedback } = props;
    const maxCharacters = 300;

    return (
      <>
        <div className="su__feed-desc  su__mt-2 su__color-lgray su__font-13 su__f-bold su__word-break">
          {t(selectedPageTextFeedback)}
        </div>
        <div className="su__pagerating-box su__my-3 su__position-relative">
          <textarea
            className="su__input-feedack su__form-control su__w-100 su__su__font-14 su__text-black su__border su__radius-2 su__input-feedback-height"
            type="text"
            name="su__pagerating-input"
            id="su__pagerating-input"
            value={props.textInputFeedback}
            onChange={(e) => props.settextInputFeedback(e.target.value)}
            maxLength={maxCharacters}
          />
          <div className="su__feedback-charlimit">
            {props.textInputFeedback.length}/{maxCharacters}{' '}
          </div>
        </div>
      </>
    );
  } catch (e) {
    console.log('Error in FeedbackTextual component', e);
    return <div></div>;
  }
};

export default React.memo(FeedbackTextual);
