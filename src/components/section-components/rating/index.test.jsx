/* global jest, it, expect, describe, beforeEach, afterEach, beforeAll */
import React from 'react';
import { mount } from 'enzyme';
import { Rating } from './index';

jest.mock('react-redux', () => ({
  useSelector: jest.fn()
}));
jest.mock('react-i18next', () => ({
  useTranslation: () => ({ t: (key) => key })
}));
jest.mock('function-library/hooks/use-device/use-device', () => () => ({
  isDeviceDesktop: true
}));
// Mock the gza function
global.gza = jest.fn();

jest.mock('../../../redux/variables', () => require('__mocks__/variables'));
describe('<Rating />', () => {
  let wrapper;
  const mockOnClick = jest.fn();
  const mockOnMouseEnter = jest.fn();
  const mockOnMouseLeave = jest.fn();

  beforeAll(() => {
    jest.setTimeout(10000);
  });
  beforeEach(() => {
    wrapper = mount(
      <Rating
        feedbackType="Stars"
        rating={3}
        hoverRating={0}
        onClick={mockOnClick}
        onMouseEnter={mockOnMouseEnter}
        onMouseLeave={mockOnMouseLeave}
        stars={5}
        pageRateFeed={3}
      />
    );
  });

  afterEach(() => {
    jest.clearAllTimers();
    jest.clearAllMocks();
    jest.resetModules();
  });
  it('should render without crashing', () => {
    expect(wrapper.exists()).toBe(true);
  });

  it('should render the correct number of stars', () => {
    expect(wrapper.find('.su__star')).toHaveLength(5);
  });

  it('should apply correct class for selected rating', () => {
    expect(wrapper.find('.su__star-yellow')).toHaveLength(3);
    expect(wrapper.find('.su__star-gray')).toHaveLength(2);
  });

  it('should call onClick when a star is clicked', () => {
    wrapper.find('button').at(2).simulate('click');
    expect(mockOnClick).toHaveBeenCalledWith(3);
  });

  it('should call onMouseEnter when hovering over a star', () => {
    wrapper.find('button').at(2).simulate('mouseenter');
    expect(mockOnMouseEnter).toHaveBeenCalledWith(3);
  });

  it('should call onMouseLeave when leaving a star', () => {
    wrapper.find('button').at(2).simulate('mouseleave');
    expect(mockOnMouseLeave).toHaveBeenCalled();
  });

  it('should render emoticons when feedbackType is Emoticons', () => {
    wrapper.setProps({ feedbackType: 'Emoticons' });
    expect(wrapper.find('#emoticons').exists()).toBe(true);
    expect(wrapper.find('.su__emoji')).toHaveLength(5);
  });

  it('should correctly highlight selected emoticon', () => {
    wrapper.setProps({ feedbackType: 'Emoticons', rating: 2 });
    expect(wrapper.find('.su__emoji-active')).toHaveLength(2);
  });
});
