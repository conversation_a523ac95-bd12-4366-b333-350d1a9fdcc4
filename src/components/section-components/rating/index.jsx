import React from 'react';
import { a11y, tabIndexes } from '../../../constants/a11y';
import StaticStrings from 'StaticStrings';
import { useTranslation } from 'react-i18next';
import variables from '../../../redux/variables';
import { v4 as uuid } from 'uuid';
import { func, number, string } from 'prop-types';
import useDevice from 'function-library/hooks/use-device/use-device';

const STARS = 'Stars';
const EMOTICONS = 'Emoticons';

export function Rating({
  feedbackType,
  rating,
  hoverRating,
  onClick,
  onMouseEnter,
  onMouseLeave,
  stars = 5,
  pageRateFeed
}) {
  const { t } = useTranslation();
  const { isDeviceDesktop } = useDevice();
  return (
    <div className="su__star-wrapper">
      {feedbackType == STARS && (
        <div className="su__feed-rating su__flex-vcenter su__font-10 ">
          {[...Array(stars)].map((_, index) => (
            <button
              lang={variables.searchCallVariables.langAttr}
              aria-label={`${t(StaticStrings.rating)} ${index + 1}`}
              role={a11y.ROLES.RADIO}
              tabIndex={tabIndexes.tabIndex_0}
              className={`${
                (hoverRating || rating || pageRateFeed) &&
                (hoverRating || rating || pageRateFeed) > index
                  ? 'su__star-yellow'
                  : 'su__star-gray'
              }  su__star su__mr-2 su__cursor a11y-btn`}
              key={uuid()}
              onMouseEnter={isDeviceDesktop ? () => onMouseEnter(index + 1) : null}
              onMouseLeave={isDeviceDesktop ? () => onMouseLeave() : null}
              onClick={() => onClick(index + 1)}
            ></button>
          ))}
        </div>
      )}
      {feedbackType == EMOTICONS && (
        <div className="su__feed-rating su__flex-vcenter su__font-10" id="emoticons">
          {[...Array(stars)].map((_, index) => (
            <div
              key={index}
              className={`su__emoji_border ${rating === index + 1 ? 'su__bg_theme_blue' : ''}`}
            >
              <button
                lang={variables.searchCallVariables.langAttr}
                aria-label={`${t(StaticStrings.rating)} ${index + 1}`}
                role={a11y.ROLES.RADIO}
                key={uuid()}
                tabIndex={tabIndexes.tabIndex_0}
                className={`${
                  (rating || pageRateFeed) && (rating || pageRateFeed) > index
                    ? 'su__emoji-active'
                    : ''
                } a11y-btn su__emoji su__cursor ${
                  rating === index + 1
                    ? `su__emoji-white-icon${rating - 1}`
                    : `su__emoji-icon${index}`
                }`}
                onMouseEnter={isDeviceDesktop ? () => onMouseEnter(index + 1) : null}
                onMouseLeave={isDeviceDesktop ? () => onMouseLeave() : null}
                onClick={(e) => onClick(index + 1, e)}
              ></button>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}

Rating.propTypes = {
  feedbackType: string,
  rating: number,
  hoverRating: number,
  onClick: func,
  onMouseEnter: func,
  onMouseLeave: func,
  stars: number,
  pageRateFeed: string
};

Rating.defaultProps = {
  feedbackType: '',
  rating: 0,
  hoverRating: '',
  onClick: () => {},
  onMouseEnter: () => {},
  onMouseLeave: () => {},
  stars: 1,
  pageRateFeed: ''
};
