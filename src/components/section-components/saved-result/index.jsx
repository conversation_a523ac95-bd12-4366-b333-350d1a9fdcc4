import React, { useState, useEffect, Fragment } from 'react';
import Tooltip from 'components/feature-components/tooltip/index.jsx';
import { useTranslation } from 'react-i18next';
import { a11y } from '../../../constants/a11y';
import variables from '../../../redux/variables';
import IconColors from '../../../IconColors';
import StaticStrings from 'StaticStrings';

const SavedResult = (props) => {
  let { btnclick, item, savedResultBookmarkClicked, savedResultLimitReachedFunc } = props;

  try {
    const [isSaved, setIsSaved] = useState(false);
    const [savedResultsKey] = useState(
      localStorage.getItem('savedResult_' + variables.searchCallVariables.uid) || '[]'
    );
    const savedResultsKey1 = 'savedResult_' + variables.searchCallVariables.uid;
    useEffect(() => {
      const savedResults = JSON.parse(localStorage.getItem(savedResultsKey1) || '[]');
      setIsSaved(
        savedResults && savedResults.some((result) => result.uniqueField === item.uniqueField)
      );
    }, [
      item.href,
      savedResultsKey1,
      savedResultsKey,
      savedResultBookmarkClicked,
      item.uniqueField
    ]);

    let saveUnsaveResult = () => {
      props.bookmarkListIconFunc();
      let data = JSON.parse(localStorage.getItem(savedResultsKey1) || '[]');
      if (data && data.length >= 50) {
        const isAlreadySaved =
          data && data.some((result) => result.uniqueField === item.uniqueField);
        if (isAlreadySaved) {
          data = data.filter((result) => result.uniqueField !== item.uniqueField);
          localStorage.setItem(savedResultsKey1, JSON.stringify(data));
          setIsSaved(false);
          btnclick();
          return;
        } else {
          savedResultLimitReachedFunc();
          return;
        }
      }
      const isAlreadySaved = data
        ? data.some((result) => result.uniqueField === item.uniqueField)
        : null;
      if (isAlreadySaved) {
        data = data.filter((result) => result.uniqueField !== item.uniqueField);
      } else {
        data.push({
          title: item.highlight.TitleToDisplay[0] || item.href,
          href: item.href,
          savedResult: false,
          csIndexName: item.sourceName,
          savedResultId: item._id,
          objName: item.objName,
          uniqueField: item.uniqueField ? item.uniqueField : '',
          savedResultExists: 0
        });
      }
      localStorage.setItem(savedResultsKey1, JSON.stringify(data));
      setIsSaved(!isAlreadySaved);
      btnclick();
    };

    const { t } = useTranslation();
    return (
      <Fragment>
        <Tooltip
          text={t(isSaved ? StaticStrings.Unsave : StaticStrings.Save_for_Later)}
          position="bottom"
          className="position-relative"
          tipClassName={'su__position-absolute  su__zindex-1  su__tooltip-positions '}
        >
          <button
            lang={variables.searchCallVariables.langAttr}
            aria-label={t(isSaved ? StaticStrings.Unsave : StaticStrings.Save_for_Later)}
            role={a11y.ROLES.BTN}
            className="su_preview-startblock su__cursor a11y-btn su__savedResultBookmarkIcon su__sc-loading su__position-relative su__top-2px su__ml-1px"
            onClick={saveUnsaveResult}
          >
            <svg
              id="bookmark"
              xmlns="http://www.w3.org/2000/svg"
              width="22"
              height="22"
              viewBox="0 0 22 22"
            >
              <g id="bookmark-2" data-name="bookmark">
                <rect
                  id="Rectangle_29"
                  data-name="Rectangle 29"
                  width="22"
                  height="22"
                  opacity="0"
                />
                <path
                  id="Path_24"
                  data-name="Path 24"
                  d="M5.932,19.776A.936.936,0,0,1,5,18.844V5.172A2.125,2.125,0,0,1,7.05,3H16a2.125,2.125,0,0,1,2.05,2.172V18.844a.932.932,0,0,1-1.4.8l-5.284-2.992L6.4,19.636A.932.932,0,0,1,5.932,19.776Z"
                  transform="translate(-0.524 -0.388)"
                  fill={isSaved ? IconColors.saved_result_fill : IconColors.saveArticleIconFill}
                  stroke={isSaved ? 'none' : '#000'}
                  strokeWidth="1"
                />
              </g>
            </svg>
          </button>
        </Tooltip>
      </Fragment>
    );
  } catch (e) {
    console.log('Error in SourceLabel component', e);
    return <div></div>;
  }
};

export default SavedResult;
