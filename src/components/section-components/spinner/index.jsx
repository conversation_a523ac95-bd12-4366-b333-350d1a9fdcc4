import React from 'react';
import { bool } from 'prop-types';
import { SVGS } from '../../../constants/svgs';
import StaticStrings from 'StaticStrings';

const Spinner = ({ isError, isAutoComplete }) => {
  let emptyAppSvg = SVGS.emptyFolder('248.788', '185.401');
  return isError && !isAutoComplete ? (
    <div className="su__center-element">
      <div>
        <div
          dangerouslySetInnerHTML={{
            __html: emptyAppSvg
          }}
        ></div>
        <span className="su__padding_10">{StaticStrings.Empty_App}</span>
      </div>
    </div>
  ) : isError && isAutoComplete ? (
    <div className="su__flex-hcenter">
      <span className="su__padding_10">{StaticStrings.Empty_Autocomplete}</span>
    </div>
  ) : (
    <div className={isAutoComplete ? 'su__sso-loader-autocomplete' : 'su__sso-loader'}></div>
  );
};

Spinner.propTypes = {
  isError: bool,
  isAutoComplete: bool
};
export default Spinner;
