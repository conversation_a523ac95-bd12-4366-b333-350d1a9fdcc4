/* global jest, it, expect, describe, beforeEach, afterEach, beforeAll */
import React from 'react';
import { mount } from 'enzyme';
import { act } from 'react-dom/test-utils';
import { SVGS } from 'assets/svg-icon/index';
import FeedbackConfirmation from './index';

jest.mock('react-redux', () => ({
  useSelector: jest.fn()
}));
jest.mock('react-i18next', () => ({
  useTranslation: () => ({ t: (key) => key })
}));
jest.mock('function-library/hooks/use-device/use-device', () => () => ({
  isDeviceDesktop: true
}));
// Mock the gza function
global.gza = jest.fn();

jest.mock('../../../redux/variables', () => require('__mocks__/variables'));
describe('FeedbackConfirmation Component', () => {
  let props = {};

  beforeAll(() => {
    jest.setTimeout(10000);
  });
  beforeEach(() => {
    props = {
      selectedAck: 'Thank you!',
      searchToggle: false,
      selectedSearchAcknowledgement: 'Search feedback received',
      isSearchFeedback: false,
      isThanksModel: { isOpen: true, message: '' },
      setThanksModel: jest.fn(),
      isPageRating: false
    };
  });

  afterEach(() => {
    jest.clearAllTimers();
    jest.clearAllMocks();
    jest.resetModules();
  });
  it('should render without crashing', () => {
    const wrapper = mount(<FeedbackConfirmation {...props} />);
    expect(wrapper.exists()).toBe(true);
  });

  it('should display the correct message when searchToggle is false', async () => {
    const props = {
      selectedAck: 'Thank you for your feedback!',
      searchToggle: false,
      selectedSearchAcknowledgement: '',
      isSearchFeedback: false,
      isThanksModel: { isOpen: true, message: '' }, // Initially empty
      setThanksModel: jest.fn(),
      isPageRating: false
    };

    const wrapper = mount(<FeedbackConfirmation {...props} />);

    await act(async () => {
      wrapper.setProps({ isThanksModel: { isOpen: true, message: props.selectedAck } });
      wrapper.update();
    });

    expect(wrapper.find('.su__feed-title').text()).toBe(props.selectedAck);
  });

  it('should display the correct message when searchToggle is true', async () => {
    props.searchToggle = true;
    const wrapper = mount(<FeedbackConfirmation {...props} />);
    await act(async () => {
      // Simulate the effect of useEffect by updating the prop
      wrapper.setProps({
        isThanksModel: { isOpen: true, message: props.selectedSearchAcknowledgement }
      });
      wrapper.update();
    });
    expect(wrapper.find('.su__feed-title').text()).toBe(props.selectedSearchAcknowledgement);
  });

  it('should handle errors gracefully and not crash', () => {
    const consoleSpy = jest.spyOn(console, 'log').mockImplementation(() => {});
    mount(<FeedbackConfirmation {...props} isThanksModel={null} />);
    expect(consoleSpy).toHaveBeenCalledWith(
      'Error in FeedbackConfirmation component',
      expect.anything()
    );
    consoleSpy.mockRestore();
  });

  it('should render the checkmark SVG', () => {
    const wrapper = mount(<FeedbackConfirmation {...props} />);
    expect(wrapper.find(SVGS.thanksCheck).exists()).toBe(true);
  });
  it('should return previous state if message is already set', async () => {
    props.isThanksModel.message = 'Existing message'; // Pre-set message

    const wrapper = mount(<FeedbackConfirmation {...props} />);

    await act(async () => {
      wrapper.update();
    });

    expect(wrapper.find('.su__feed-title').text()).toBe('Existing message');
  });

  it('should set message when searchToggle and isSearchFeedback are false', async () => {
    props.searchToggle = false;
    props.isSearchFeedback = false;
    props.selectedAck = 'General feedback received';

    const wrapper = mount(<FeedbackConfirmation {...props} />);

    await act(async () => {
      wrapper.setProps({
        isThanksModel: { isOpen: true, message: 'General feedback received' }
      });
      wrapper.update();
    });

    expect(wrapper.find('.su__feed-title').text()).toBe('General feedback received');
  });

  it('should set message when searchToggle is true', async () => {
    props.searchToggle = true;

    const wrapper = mount(<FeedbackConfirmation {...props} />);

    await act(async () => {
      wrapper.setProps({
        isThanksModel: { isOpen: true, message: props.selectedSearchAcknowledgement }
      });
      wrapper.update();
    });

    expect(wrapper.find('.su__feed-title').text()).toBe(props.selectedSearchAcknowledgement);
  });

  it('should set message when isSearchFeedback is true', async () => {
    props.isSearchFeedback = true;

    const wrapper = mount(<FeedbackConfirmation {...props} />);

    await act(async () => {
      wrapper.setProps({
        isThanksModel: { isOpen: true, message: props.selectedSearchAcknowledgement }
      });
      wrapper.update();
    });

    expect(wrapper.find('.su__feed-title').text()).toBe(props.selectedSearchAcknowledgement);
  });
});
