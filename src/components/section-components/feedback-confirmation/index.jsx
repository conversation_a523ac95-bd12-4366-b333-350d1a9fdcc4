import React, { useEffect } from 'react';
import { a11y } from '../../../constants/a11y';
import { useTranslation } from 'react-i18next';
import { SVGS } from 'assets/svg-icon/index';

const FeedbackConfirmation = (props) => {
  try {
    const { t } = useTranslation();
    let {
      selectedAck,
      searchToggle,
      selectedSearchAcknowledgement,
      isSearchFeedback,
      isThanksModel,
      setThanksModel,
      isPageRating
    } = props;

    useEffect(() => {
      setThanksModel((prev) => {
        if (prev.isOpen && prev.message) {
          return prev;
        }
        // Determine the message based on the conditions
        let message = '';
        if (!searchToggle && !isSearchFeedback) {
          message = t(selectedAck); // Use the translation of selectedAck
        } else if (searchToggle || isSearchFeedback) {
          message = selectedSearchAcknowledgement; // Use the selectedSearchAcknowledgement directly
        }

        // Update the state with the determined message
        return { ...prev, message };
      });
    }, [
      isThanksModel.isOpen,
      searchToggle,
      isSearchFeedback,
      selectedAck,
      selectedSearchAcknowledgement
    ]);

    return (
      <div role={a11y.ROLES.ALERT} className={`${!isPageRating ? 'su_feedback_form' : null}`}>
        <div className="su__feedback-row feedback-submit-content su__text-center su__thankspopUpPadding">
          <div className="su__svg-ok">
            <div className="su__flex-hcenter">
              <SVGS.thanksCheck />
            </div>
          </div>
          <div className="su__feed-title su__font-14 su__text-black su__word-break">
            {isThanksModel.message}
          </div>
        </div>
      </div>
    );
  } catch (e) {
    console.log('Error in FeedbackConfirmation component', e);
    return <div></div>;
  }
};

export default React.memo(FeedbackConfirmation);
