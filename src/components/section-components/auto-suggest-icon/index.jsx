import React, { Fragment } from 'react';

const SOLVED = 'Solved';
const UNSOLVED = 'Unsolved';
const SEARCHSTRING = 'searchString';
const RECENTSEARCH = 'recentSearch';
const AUTOSUGGESTION = 'autoSuggestion';

const AutoCompleteResultIcon = (props) => {
  try {
    let { item } = props;
    return (
      <Fragment>
        <div className="su__suggesticon">
          {item.solved == SOLVED ? (
            <svg
              className="su__mr-2"
              xmlns="http://www.w3.org/2000/svg"
              height="20"
              viewBox="0 0 24 24"
              width="20"
            >
              <path d="M0 0h24v24H0z" fill="none" />
              <path
                fill="#5bca51"
                d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"
              />
            </svg>
          ) : null}
          {item.solved == UNSOLVED ? (
            <svg
              id="checkmark-circle-2"
              xmlns="http://www.w3.org/2000/svg"
              width="18"
              height="18"
              viewBox="0 0 18 18"
            >
              <g id="checkmark-circle-2-2" data-name="checkmark-circle-2">
                <rect
                  id="Rectangle_39"
                  data-name="Rectangle 39"
                  width="18"
                  height="18"
                  fill="#57575c"
                  opacity="0"
                />
                <path
                  id="Path_32"
                  data-name="Path 32"
                  d="M9.5,2A7.5,7.5,0,1,0,17,9.5,7.5,7.5,0,0,0,9.5,2Zm3.225,5.708L9.3,12.208a.752.752,0,0,1-1.185.007L6.282,9.882A.751.751,0,0,1,7.467,8.96L8.69,10.52l2.835-3.75a.755.755,0,0,1,1.2.915Z"
                  transform="translate(-0.5 -0.5)"
                  fill="#57575c"
                />
              </g>
            </svg>
          ) : null}
        </div>
        <div className="su__autosuggestion-icon">
          {item.type === SEARCHSTRING ? (
            <svg
              className="su__mr-2"
              xmlns="http://www.w3.org/2000/svg"
              width="20"
              height="20"
              viewBox="0 0 24 24"
            >
              <path d="M20 5H4c-1.1 0-1.99.9-1.99 2L2 17c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm-9 3h2v2h-2V8zm0 3h2v2h-2v-2zM8 8h2v2H8V8zm0 3h2v2H8v-2zm-1 2H5v-2h2v2zm0-3H5V8h2v2zm9 7H8v-2h8v2zm0-4h-2v-2h2v2zm0-3h-2V8h2v2zm3 3h-2v-2h2v2zm0-3h-2V8h2v2z" />
              <path d="M0 0h24v24H0zm0 0h24v24H0z" fill="none" />
            </svg>
          ) : null}
          {item.type === RECENTSEARCH ? (
            <svg
              id="schedule_black_24dp"
              xmlns="http://www.w3.org/2000/svg"
              width="18"
              height="18"
              viewBox="0 0 18 18"
            >
              <path id="Path_36" data-name="Path 36" d="M0,0H18V18H0Z" fill="none" />
              <path
                id="Path_37"
                data-name="Path 37"
                d="M8.993,2A7,7,0,1,0,16,9,7,7,0,0,0,8.993,2ZM9,14.6A5.6,5.6,0,1,1,14.6,9,5.6,5.6,0,0,1,9,14.6ZM8.846,5.5H8.8a.5.5,0,0,0-.5.5v3.3a.693.693,0,0,0,.343.6l2.9,1.743a.5.5,0,1,0,.511-.861L9.35,9.182V6A.5.5,0,0,0,8.846,5.5Z"
                fill="#57575c"
              />
            </svg>
          ) : null}
          {item.type === AUTOSUGGESTION ? (
            <svg
              className="su__mr-2"
              xmlns="http://www.w3.org/2000/svg"
              width="20"
              height="20"
              viewBox="0 0 24 24"
            >
              <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z" />
              <path d="M0 0h24v24H0z" fill="none" />
            </svg>
          ) : null}
        </div>
      </Fragment>
    );
  } catch (e) {
    console.log('Error in AutoCompleteResultIcon component', e);
    return <div></div>;
  }
};

export default React.memo(AutoCompleteResultIcon);
