import React, { useState, Fragment, useEffect, useRef } from 'react';
import Tooltip from 'components/feature-components/tooltip/index.jsx';
import { useTranslation } from 'react-i18next';
import variables from '../../../redux/variables';
import IconColors from '../../../IconColors';
import StaticStrings from 'StaticStrings';
import { A11Y_IDS } from 'constants/a11y';
import { a11y, tabIndexes } from '../../../constants/a11y';
import Icons from '../../../assets/svg-icon/svg';
import ArticlePreviewModal from 'components/section-components/article-preview/index.jsx';
import { SC_IDS, STRING_PARTIALS, RECORD_TYPES } from 'constants/constants';
import { useDevice } from 'function-library/hooks';
import { useDispatch, useSelector } from 'react-redux';
import { previewContentSearch } from '../../../redux/ducks';
/**
 * Preview Component - Renders a preview modal for search results
 * @param {Object} props - Component props
 * @param {Object} props.item - The search result item to preview
 * @param {Object} props.searchResult - The complete search result object
 * @param {Function} props.linkOpened - Callback function when a link is opened
 * @param {number} props.index - Index of the current item in search results
 */
const Preview = (props) => {
  const dispatch = useDispatch();
  const previewContentData = useSelector((state) => state.previewContentData);
  const previewContentError = useSelector((state) => state.previewContentError);
  try {
    const { t } = useTranslation();
    const { item, searchResult, linkOpened, index } = props;
    // State hooks for managing preview modal
    const [titleForModal, setTitleForModal] = useState('');
    const [sourceLabelForModal, setSourceLabelForModal] = useState('');
    const [openPreviewModalUrl, setOpenPreviewModalUrl] = useState('');
    const [openPreviewModal, setOpenPreviewModal] = useState(false);
    const [previewJson, setPreviewJson] = useState(false);
    const [previewFields, setPreviewFields] = useState([]);
    const [showOverlay, setShowOverlay] = useState(false);
    const [itemIndex, setItemIndex] = useState(null);
    const [sortedPreviewFields, setSortedPreviewFields] = useState([]);
    const { isDeviceDesktop, isDeviceMobile } = useDevice();
    const modalRef = useRef(null);
    /**
     * Opens the preview modal and handles different preview types
     * @param {Object} item - The item to preview
     */
    const [recordType, setRecordType] = useState('');
    const [articleData, setArticleData] = useState({});
    const [showLoader, setShowLoader] = useState(false);
    const isPreviewJson = item.previewType == 3;
    const desktopcondition = isDeviceDesktop ? 'su__w-70dvw' : 'su__w-85dvw';
    const previewClass = isPreviewJson
      ? `su-preview-json ${desktopcondition}`
      : 'su__position-fixed su__trbl-0';

    const guideClass =
      variables.searchClientType == SC_IDS.ZENDESK_GUIDE ? 'su__align_preview' : '';
    const openPreview = (item) => {
      const isYouTube = item.href.toLowerCase().includes('youtube.com');
      const isVimeo =
        item.href.toLowerCase().includes('vimeo.com') && /^\d+$/.test(item.href.split('.com/')[1]);

      setShowLoader(!(isYouTube || isVimeo));
      setShowOverlay(true);
      const currentRecordType = item.Id.startsWith('ka') ? RECORD_TYPES.ARTICLE : RECORD_TYPES.CASE;
      setRecordType(currentRecordType);
      let newarr;
      let articleArr = [
        'ArticleNumber',
        'PublishStatus',
        'LastModifiedDate',
        'VersionNumber',
        'Title',
        'UrlName',
        'summary'
      ];
      let caseArr = [
        'CaseNumber',
        'Description',
        'LastModifiedDate',
        'Type',
        'Subject',
        'Status',
        'Priority'
      ];
      newarr = currentRecordType == RECORD_TYPES.ARTICLE ? articleArr : caseArr;
      const message = {
        RecordId: item.Id,
        RecordType: currentRecordType,
        apiFieldName: newarr.join(', ')
      };

      window.postMessage(message, '*');
      setTimeout(() => {
        setArticleData(window.articleData);
        setShowLoader(false);
      }, 3000);
      document.body.style.otitleForModalerflow = 'hidden';
      document.body.style.overflow = 'hidden';
      setSourceLabelForModal(item.sourceLabel);
      setTitleForModal(item.highlight?.TitleToDisplay?.[0] || item.href);

      if (item.previewType === 1) {
        setOpenPreviewModalUrl(modifyUrlForPreview(item.href));
        setOpenPreviewModal(true);
        setItemIndex(index);
      } else if (item.previewType === 3) {
        setItemIndex(index);
        setShowLoader(true);
        fetchPreviewContent(item);
      }
    };

    /**
     * Closes the preview modal and resets state
     */
    const closePreview = () => {
      document.body.style.overflow = 'auto';
      setOpenPreviewModal(false);
      setShowLoader(false);
      setOpenPreviewModalUrl('');
      setShowOverlay(false);
      setItemIndex(null);
      setPreviewFields([]);

      document.getElementsByClassName('su__iframe-src')[0].setAttribute('src', '');
    };

    /**
     * Fetches preview content from the server for preview type 3
     * @param {Object} item - The item to fetch preview content for
     */
    const fetchPreviewContent = (item) => {
      dispatch(previewContentSearch.start(item));
    };

    useEffect(() => {
      if (previewContentData && previewContentData?.statusCode === 200) {
        setPreviewFields(previewContentData.result?.previewFields);
        setPreviewJson(true);
        setShowLoader(false);
        setOpenPreviewModal(true);
      }
      if (previewContentError || previewContentData?.result?.previewFields?.length === 0) {
        setShowLoader(false);
        setPreviewFields([]);
        setPreviewJson(true);
        console.error('Error fetching preview content:', previewContentError);
      }
    }, [previewContentData, previewContentError]);

    /**
     * Sanitizes HTML content by removing SVG, images and fixing opacity styles
     * @param {string} htmlString - The HTML string to sanitize
     * @returns {string} Sanitized HTML string
     */
    const sanitizeHtml = (htmlString) => {
      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = htmlString;
      tempDiv.querySelectorAll('svg, img').forEach((el) => el.remove());
      // all the style attributes in the response html will be removed from backend code
      const elementsWithStyle = tempDiv.querySelectorAll('[style]');
      elementsWithStyle.forEach((el) => {
        const style = el.getAttribute('style');
        if (style.includes('opacity: 0')) {
          el.setAttribute('style', style.replace('opacity: 0', 'opacity: 1'));
        }
      });
      return tempDiv.innerHTML;
    };
    const capitalizeFirstLetter = (string) => {
      if (!string) return '';
      return string.charAt(0).toUpperCase() + string.slice(1);
    };

    /**
     * Modifies URLs for video previews (YouTube and Vimeo)
     * @param {string} url - The URL to modify
     * @returns {string} Modified URL suitable for embedding
     */
    const modifyUrlForPreview = (url) => {
      if (url.toLowerCase().includes('youtube.com')) {
        return url.replace('watch?v=', 'embed/');
      } else if (url.toLowerCase().includes('vimeo.com')) {
        return 'https://player.vimeo.com/video/' + url.split('.com/')[1];
      } else {
        return url;
      }
    };

    /**
     * Extracts plain text title from HTML content
     * @param {string} value - HTML string containing the title
     * @returns {string} Plain text title
     */
    const getTitle = (value) => {
      const div = document.createElement('div');
      div.innerHTML = sanitizeHtml(value);
      return div.textContent || div.innerText || value;
    };

    /**
     * Effect hook to handle focus trap when preview modal is open
     */
    useEffect(() => {
      const handleMessage = (event) => {
        if (event.data && event.data.articleData) {
          setArticleData(event.data.articleData);
          setShowLoader(false);
        }
      };
      window.addEventListener('message', handleMessage);
      return () => {
        window.removeEventListener('message', handleMessage);
        setShowLoader(false);
      };
    }, []);
    useEffect(() => {
      if (!showLoader) {
        setArticleData(window.articleData || {});
      }
    }, [openPreviewModal, showLoader, window.articleData]);
    useEffect(() => {
      const sortedPreviewFields = [...previewFields].sort(
        (a, b) => a.previewOrder - b.previewOrder
      );
      setSortedPreviewFields(sortedPreviewFields);
    }, [previewFields]);

    const internalPreview =
      variables.searchClientType === SC_IDS.SALESFORCE_VISUALFORCE_INTERNAL &&
      (item.objName.toLowerCase().slice(-5) === STRING_PARTIALS.OBJECT ||
        item.objName.toLowerCase() === RECORD_TYPES.CASE);
    const showPreview = internalPreview || item.href.includes(window.location.origin);
    return (
      <Fragment>
        {showLoader && (
          <div className="su__loading-internal">
            <div className="dot"></div>
            <div className="dot"></div>
            <div className="dot"></div>
            <div className="dot"></div>
            <div className="dot"></div>
          </div>
        )}
        {((item.previewType === 1 &&
          ((typeof item?.href === 'string' && item.href.toLowerCase().includes('youtube.com')) ||
            (typeof item?.href === 'string' &&
              item.href.toLowerCase().includes('vimeo.com') &&
              /^\d+$/.test(item.href.split('.com/')[1])) ||
            showPreview)) ||
          item.previewType === 3) &&
          searchResult &&
          searchResult.searchClientSettings &&
          searchResult.searchClientSettings.preview &&
          !variables.toggleDisplayKeys[2].hideEye && (
            <Tooltip text={t(StaticStrings.preview)} position="top" className="position-relative">
              <button
                type="button"
                lang={variables.searchCallVariables.langAttr}
                aria-label={t(StaticStrings.preview)}
                role={a11y.ROLES.BTN}
                className="su_preview-startblock a11y-btn su__sc-loading"
                onClick={() => {
                  openPreview(item);
                  linkOpened(item, index + 1);
                }}
              >
                <div className="su__results-preview">
                  <div className="su__preview-block-content su__d-inline-block su__cursor">
                    <div
                      aria-label="Preview"
                      role={a11y.ROLES.BTN}
                      tabIndex={tabIndexes.tabIndex_0}
                      className="su__flex-vcenter su__justify-content-end"
                    >
                      <svg
                        id="eye"
                        xmlns="http://www.w3.org/2000/svg"
                        width="20"
                        height="20"
                        viewBox="0 0 20 20"
                      >
                        <g id="eye-2" data-name="eye">
                          <g id="eye-3" data-name="eye">
                            <rect
                              id="Rectangle_33"
                              data-name="Rectangle 33"
                              width="20"
                              height="20"
                              fill={IconColors.PreviewIconFill}
                              opacity="0"
                            />
                            <circle
                              id="Ellipse_10"
                              data-name="Ellipse 10"
                              cx="1.25"
                              cy="1.25"
                              r="1.25"
                              transform="translate(8.75 8.75)"
                              fill={IconColors.PreviewIconFill}
                            />
                            <path
                              id="Path_26"
                              data-name="Path 26"
                              d="M18.558,10.416c-.533-.925-3.467-5.567-8.45-5.417-4.608.117-7.275,4.167-8,5.417a.833.833,0,0,0,0,.833c.525.908,3.333,5.417,8.242,5.417h.208c4.608-.117,7.283-4.167,8-5.417A.833.833,0,0,0,18.558,10.416Zm-8.225,3.333a2.917,2.917,0,1,1,2.917-2.917,2.917,2.917,0,0,1-2.917,2.917Z"
                              transform="translate(-0.333 -0.833)"
                              fill={IconColors.PreviewIconFill}
                            />
                          </g>
                        </g>
                      </svg>
                    </div>
                  </div>
                </div>
              </button>
            </Tooltip>
          )}

        {openPreviewModal && itemIndex === index && !showLoader && (
          <div
            id={A11Y_IDS.trap}
            ref={modalRef}
            className={`su__flex-hcenter su__bookmark-row su__zindex-3 su__sc-loading ${previewClass} ${guideClass}`}
          >
            {internalPreview && item.previewType == 1 ? (
              <ArticlePreviewModal
                isOpen={!showLoader}
                onClose={closePreview}
                articleData={articleData}
                recordType={recordType}
              />
            ) : (
              <div
                className={`${
                  item.previewType == 3
                    ? ' '
                    : 'su__iframe-modal su__animate-fadeInRight su__position-fixed su__py-2 su__sm-h-100 su__radius-1 su__mx-auto'
                } su__shadow-lg su__bg-white  su__zindex-2 su__radius-1`}
              >
                {item.previewType == 3 && previewJson && (
                  <>
                    <div className="su__overflow-auto su__text-left su__p-20">
                      <div className="su__d-flex su__align-items-start su__mb-15 ">
                        <div
                          className={`su__text-left su__d-flex su__justify-content-between su__flex-1 su__font-14 su__color-gray su__f-normal ${
                            isDeviceMobile ? 'su__flex-column-reverse' : ''
                          }`}
                        >
                          <span
                            className={`su__word-break su__overflow-hide  ${
                              isDeviceMobile ? 'su__pt-2' : ''
                            }`}
                          >
                            {sortedPreviewFields.map((preview, index) => {
                              const isFirstItem = index === 0;
                              return (
                                <React.Fragment key={`${preview.previewOrder}-${preview.label}`}>
                                  {Object.keys(preview).some((key) => key.includes('_html'))
                                    ? // If '_html' key exists, render its value
                                      Object.entries(preview)
                                        .filter(([key]) => key.includes('_html') && key !== 'label')
                                        .map(([key, value]) => (
                                          <>
                                            {isFirstItem ? (
                                              <a
                                                role={a11y.ROLES.LNK}
                                                aria-label={`Title: ${getTitle(value)}`}
                                                className="su__preview-json-title "
                                                target="_blank"
                                                rel="noopener noreferrer"
                                                href={item.href}
                                                key={key}
                                              >
                                                <span
                                                  className={`su__line-clamp  su__text-underline  ${
                                                    isDeviceDesktop ? 'hover-color-dblue' : ''
                                                  }`}
                                                  dangerouslySetInnerHTML={{
                                                    __html: sanitizeHtml(value || item.href)
                                                  }}
                                                ></span>
                                              </a>
                                            ) : null}
                                          </>
                                        ))
                                    : // If no '_html' key exists, render other key-value pairs
                                      Object.entries(preview).map(([key, value]) => {
                                        if (key !== 'previewOrder' && key !== 'label') {
                                          return (
                                            <>
                                              {isFirstItem ? (
                                                <a
                                                  role={a11y.ROLES.LNK}
                                                  aria-label={`Title: ${getTitle(value)}`}
                                                  className="su__preview-json-title"
                                                  target="_blank"
                                                  rel="noopener noreferrer"
                                                  href={item.href}
                                                >
                                                  <div
                                                    className={`su__line-clamp su__text-underline  ${
                                                      isDeviceDesktop ? 'hover-color-dblue' : ''
                                                    }`}
                                                  >
                                                    {value || item.href}
                                                  </div>
                                                </a>
                                              ) : null}
                                            </>
                                          );
                                        }
                                        return null;
                                      })}
                                </React.Fragment>
                              );
                            })}
                          </span>
                          {!variables.toggleDisplayKeys[5].hideEye &&
                            variables.searchCallVariables.showContentTag && (
                              <Tooltip
                                text={item.sourceLabel}
                                position="bottom"
                                className="position-relative su__f-normal"
                              >
                                <span className="su__mw-300 su__w-mcontent">
                                  <span
                                    tabIndex={tabIndexes.tabIndex_0}
                                    className={`su__px-2 su__f-normal su__color_grey su__preview-contentLabel su__text-truncate su__font-14 ${
                                      isDeviceMobile ? '' : 'su__ml-2 '
                                    } `}
                                  >
                                    {item.sourceLabel}
                                  </span>
                                </span>
                              </Tooltip>
                            )}
                        </div>
                        <div className="su__close-button">
                          <button
                            type="button"
                            className="a11y-btn "
                            aria-label="Close modal"
                            onClick={closePreview}
                          >
                            <Icons
                              className="su__cursor su__p-1"
                              IconName="Close"
                              width="20"
                              height="20"
                              color="#57575c"
                            />
                          </button>
                        </div>
                      </div>
                      {/* Render the wrapper for items with previewOrder > 1 */}
                      <div className="su__preview-type-two su__thin-scrollbar">
                        {previewFields?.length === 0 ||
                        previewFields
                          ?.filter((preview) => preview.previewOrder > 1)
                          .every((preview) =>
                            Object.entries(preview).every(([key, value]) => {
                              if (key === 'previewOrder' || key === 'label') return true; // Skip these keys
                              if (Array.isArray(value)) {
                                return value.every((v) => v === null || v === ''); // Check if array values are empty
                              }
                              return value === ''; // Check if string values are empty
                            })
                          ) ? (
                          <div className="su__no-preview">
                            <h3>Nothing to Preview</h3>
                          </div>
                        ) : (
                          sortedPreviewFields
                            .filter((preview) => preview.previewOrder > 1)
                            .map((preview) => {
                              const hasValidData = Object.entries(preview).some(([key, value]) => {
                                if (Array.isArray(value)) {
                                  return value.some((v) => v !== null && v !== '');
                                }
                                return key !== 'previewOrder' && key !== 'label' && value !== '';
                              });
                              if (!hasValidData) {
                                return null; // Skip rendering if no valid data
                              }
                              return (
                                <React.Fragment key={`${preview.previewOrder}-${preview.label}`}>
                                  {Object.keys(preview).some((key) => key.includes('_html'))
                                    ? // If '_html' key exists, render only '_html' related keys
                                      Object.entries(preview)
                                        .filter(
                                          ([key]) =>
                                            key.includes('_html') &&
                                            preview[key] !== '' &&
                                            preview[key].length !== 0
                                        )
                                        .map(([key, value]) => (
                                          <React.Fragment key={key}>
                                            <div className="su__d-flex" key={key}>
                                              <span>
                                                <h3 className={`su__${preview.label} su__d-key`}>
                                                  {capitalizeFirstLetter(preview.label || key)}:
                                                </h3>
                                                <span
                                                  className="su__preview-value"
                                                  dangerouslySetInnerHTML={{
                                                    __html: Array.isArray(value)
                                                      ? sanitizeHtml(
                                                          value
                                                            .filter((v) => v !== null && v !== '')
                                                            .join('<br />')
                                                        )
                                                      : sanitizeHtml(value)
                                                  }}
                                                ></span>
                                              </span>
                                            </div>
                                          </React.Fragment>
                                        ))
                                    : Object.entries(preview)
                                        .filter(
                                          ([key, value]) =>
                                            key !== 'previewOrder' &&
                                            key !== 'label' &&
                                            value !== '' &&
                                            value.length !== 0
                                        )
                                        .map(([key, value]) => (
                                          <React.Fragment key={key}>
                                            <div className="su__d-flex">
                                              <span>
                                                <h3 className={`su__${preview.label} su__d-key`}>
                                                  {capitalizeFirstLetter(preview.label || key)}:
                                                </h3>
                                                {Array.isArray(value) ? (
                                                  <span
                                                    dangerouslySetInnerHTML={{
                                                      __html: sanitizeHtml(
                                                        value
                                                          .filter((v) => v !== null && v !== '')
                                                          .join('<br />')
                                                      )
                                                    }}
                                                  ></span>
                                                ) : (
                                                  <span
                                                    dangerouslySetInnerHTML={{
                                                      __html: sanitizeHtml(value)
                                                    }}
                                                  ></span>
                                                )}
                                              </span>
                                            </div>
                                          </React.Fragment>
                                        ))}
                                </React.Fragment>
                              );
                            })
                        )}
                      </div>
                    </div>
                  </>
                )}

                {item.previewType == 1 && (
                  <div className="su__close-icon su__d-flex su__align-items-start su__py-2 su__px-3">
                    <div className="su__text-left su__d-flex su__align-items-start su__flex-1 su__font-14 su__color-gray su__f-regular su__overflow-hide  su__d-flex su__justify-content-between">
                      <span className="su__max-width-preview-title su__word-break">
                        <h2
                          tabIndex={tabIndexes.tabIndex_0}
                          className="su__line-clamp su__font-14 su__f-regular su__my-0"
                          dangerouslySetInnerHTML={{ __html: titleForModal }}
                        ></h2>
                      </span>
                      {!variables.toggleDisplayKeys[5].hideEye &&
                        variables.searchCallVariables.showContentTag && (
                          <span
                            tabIndex={tabIndexes.tabIndex_0}
                            className="su__px-3 su__ml-2 su__f-regular su__color-black su__preview-contentLabel su__text-truncate  su__mx-32px su__font-14 su__font-bold"
                          >
                            {sourceLabelForModal}
                          </span>
                        )}
                    </div>
                    <button
                      type="button"
                      lang={variables.searchCallVariables.langAttr}
                      className="a11y-btn p-0 su__position-absolute su__top-10px su__right-10px su__rtlleft-10px"
                      aria-label={t(StaticStrings.close_popup)}
                      role={a11y.ROLES.BTN}
                      tabIndex={tabIndexes.tabIndex_0}
                      onClick={closePreview}
                    >
                      <svg
                        className="su__cursor"
                        width="24"
                        height="24"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          className="su__fill-black"
                          d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"
                        />
                        <path d="M0 0h24v24H0z" fill="none" />
                      </svg>
                    </button>
                  </div>
                )}
                <div
                  tabIndex={tabIndexes.tabIndex_0}
                  className={`${previewJson ? '' : 'su__bookmark-inner su__p-3'}`}
                >
                  {item.previewType == 1 && (
                    <iframe
                      tabIndex={tabIndexes.tabIndex_0}
                      className="su__iframe-height su__iframe-src"
                      width="100%"
                      height="100%"
                      target="parent"
                      src={openPreviewModalUrl}
                      title={titleForModal}
                    ></iframe>
                  )}
                </div>
              </div>
            )}
          </div>
        )}
        {showOverlay && (
          <div
            tabIndex={tabIndexes.tabIndex_0}
            className="su__overlay su__overlayz-9"
            onClick={closePreview}
          ></div>
        )}
      </Fragment>
    );
  } catch (e) {
    console.log('Error in preview component', e);
    return <div></div>;
  }
};

export default Preview;
