/* global gza */
import React, { useEffect, useRef, useState } from 'react';
import PropTypes from 'prop-types';
import a11y, { A11Y_IDS, tabIndexes } from '../../../constants/a11y';
import StaticStrings from '../../../StaticStrings';
import { useSelector, useDispatch } from 'react-redux';
import SourceLabel from '../source-label/index';
import variables from '../../../redux/variables';
import Icons from '../../../assets/svg-icon/svg';
import { useTranslation } from 'react-i18next';
import Title from '../../section-components/title/index';
import Href from '../../section-components/href/index';
import Tooltip from 'components/feature-components/tooltip/index';
import { summarization } from 'redux/ducks';
import { SVGS } from 'assets/svg-icon/index';
import {
  animateText,
  readStream,
  copyToClipboard,
  showAndCloseToast
} from '../../../function-library/commonFunctions';

const SummarizationPopup = ({
  item,
  onClose,
  isOpen,
  onResponseChange,
  setResponseStatus,
  triggerRef
}) => {
  const { t } = useTranslation();
  const [showLikeHighlight, setShowLikeHighlight] = useState(false);
  const [showDislikeHighlight, setShowDislikeHighlight] = useState(false);
  const [showToast, setShowToast] = useState(false);
  const [showCopy, setShowCopy] = useState(false);
  const [suGPTSumms, setSuGPTSumms] = useState('');
  const closeButtonRef = useRef(null);
  const summarizationrespone = t(suGPTSumms?.trim?.(), { defaultValue: suGPTSumms?.trim?.() });

  // Keyboard event handling for the popup
  const handleKeyDown = (e) => {
    if (e.key === 'Escape') {
      onClose();
    }
  };

  // Set focus to close button when popup opens
  useEffect(() => {
    if (isOpen && closeButtonRef.current) {
      closeButtonRef.current.focus();
    }
  }, [isOpen]);

  // Return focus to the trigger button when popup closes
  useEffect(() => {
    if (!isOpen && triggerRef && triggerRef.current) {
      triggerRef.current.focus();
    }
  }, [isOpen, triggerRef]);

  // Add keyboard event listener for Escape key
  useEffect(() => {
    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown);
    }
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [isOpen]);

  const FAIL_TEXT = 'Unable to generate the summary.';
  const [summaryClassList, setSummaryClassList] = useState('');
  const [noAnswerArray, setnoAnswerArray] = useState([]);
  const latestEffectId = useRef(null);
  let animatorRefs = [];
  variables.summaryStreaming = true;
  const [{ hasError, loader, printing }, setTriggers] = useState({
    loader: true,
    hasError: false,
    printing: false
  });
  const [errorObject, setErrorObject] = useState({
    show: false,
    statusCode: '',
    showRetry: false,
    message: ''
  });
  const summaryContent = useRef('');
  useEffect(() => {
    if (!printing) {
      onResponseChange(summarizationrespone);
    }
  }, [suGPTSumms, printing]);

  const summarizationResult = useSelector((state) => state.summarizationResult);
  const dispatch = useDispatch();

  const handlePositive = () => {
    setShowLikeHighlight(true);
    gza('summarize_interaction', {
      uid: variables.searchCallVariables.uid,
      docTitle: item.highlight.TitleToDisplay[0],
      interactionType: 'feedback',
      reaction: true,
      responseGenerated: summarizationrespone,
      analyticsId: window._gza_analytics_id
    });
    showAndCloseToast(setShowToast, setShowCopy);
  };

  const handleNegative = () => {
    setShowDislikeHighlight(true);
    gza('summarize_interaction', {
      uid: variables.searchCallVariables.uid,
      docTitle: item.highlight.TitleToDisplay[0],
      interactionType: 'feedback',
      reaction: false,
      responseGenerated: summarizationrespone,
      analyticsId: window._gza_analytics_id
    });
    showAndCloseToast(setShowToast, setShowCopy);
  };
  const handleCheckForError = (inputString, errorArray) => {
    if (errorArray) {
      let filteredArray = errorArray.filter((x) => x.startsWith(inputString));
      if (filteredArray && filteredArray.length) {
        setTriggers((prevState) => ({ ...prevState, hasError: true }));
      } else {
        setTriggers((prevState) => ({ ...prevState, hasError: false }));
      }
    }
  };

  const handleCopyToClipboard = () => {
    copyToClipboard(summaryContent, setShowCopy, setShowToast);
    gza('summarize_interaction', {
      uid: variables.searchCallVariables.uid,
      docTitle: item.highlight.TitleToDisplay[0],
      interactionType: 'copyToClipBoard',
      responseGenerated: summarizationrespone,
      analyticsId: window._gza_analytics_id
    });
  };

  useEffect(() => {
    if (hasError) {
      setSummaryClassList(() => 'su__noResult_container su__summary-border-no-result');
    } else {
      setSummaryClassList(() =>
        printing ? 'su__typing_annimation su__popup-unable' : 'su__position-relative'
      );
    }
  }, [hasError, printing]);

  useEffect(() => {
    if (!summarizationResult) return;

    const effectId = Math.random();
    latestEffectId.current = effectId;

    if (summarizationResult.includes?.('ERROR_GENERATING')) {
      setTriggers((prev) => ({ ...prev, loader: false, hasError: true }));
      animateText(
        FAIL_TEXT,
        effectId,
        latestEffectId,
        animatorRefs,
        setTriggers,
        setSuGPTSumms,
        FAIL_TEXT,
        noAnswerArray
      );
      return;
    }

    try {
      // Clear old animator refs from memory
      animatorRefs.forEach((ref) => clearTimeout(ref));
      // Add fresh entry of animator ref to memory
      animatorRefs.push(
        setTimeout(() =>
          readStream(
            summarizationResult,
            effectId,
            latestEffectId,
            animatorRefs,
            setTriggers,
            setSuGPTSumms,
            FAIL_TEXT,
            handleCheckForError,
            setResponseStatus,
            null,
            setErrorObject,
            setnoAnswerArray
          )
        )
      );
    } catch (e) {
      console.log(e);
    }
  }, [summarizationResult]);

  useEffect(() => {
    if (!summarizationResult) return;
  }, [summarizationResult]);

  return (
    <>
      <div
        id={A11Y_IDS.trap}
        className={`su__popup su__popup-width-100 su__flex-column su__d-flex su__popup-summary-width ${
          isOpen ? 'open ' : ''
        }`}
        role={a11y.ROLES.DIALOG}
        aria-modal="true"
        aria-label={t(StaticStrings.summarization)}
        aria-labelledby="summarization-title"
      >
        <div className="su__popup-content">
          <div className="su__popup-header">
            <SourceLabel from="popup" item={item} />
            <div className="su__popup-close-icon">
              <button
                type="button"
                lang={variables.searchCallVariables.langAttr}
                className="a11y-btn su__close-btn"
                aria-label={t(StaticStrings.close_popup)}
                role={a11y.ROLES.BTN}
                tabIndex={tabIndexes.tabIndex_0}
                onClick={onClose}
                ref={closeButtonRef}
              >
                <Icons
                  className="su__cursor su__p-1"
                  IconName="Close"
                  width="20"
                  height="20"
                  color="#57575c"
                />
              </button>
            </div>
          </div>
          <h2
            id="summarization-title"
            className="su__text-truncate su__my-0 su__text-left su__text-popup-title su__truncate-two-lines su__text-wrap"
          >
            <Title item={item} from="popup" />
          </h2>
          <Href from="popup" item={item} />
          {/* <div className="su__popup-metadata">
            <Metadata from="popup" item={item} />
          </div> */}
        </div>
        <div
          className={`su__popup-content-body  ${
            hasError ? 'su__margin-10px ' : 'su__summary-border  su__margin-20px'
          }`}
        >
          <>
            <div className="su__d-flex su__gap-5">
              <SVGS.SummarizationSvg />
              <p className="su__font-14 su__line-height-15 loader-text su__mt-0 su__mb-0 su__f-regular su__summary-popup">
                {loader ? 'Summarizing...' : 'Summary'}
              </p>
            </div>
            {loader && !printing && (
              <ul className="su__loader_snippet su__pl-0">
                <li className="">
                  <div className="skeleton-box"></div>
                  <div className="skeleton-box"></div>
                  <div className="skeleton-box"></div>
                </li>
              </ul>
            )}
            {!loader && errorObject.show && (
              <div className={summaryClassList}>
                {errorObject.message}
                {errorObject.showRetry && (
                  <button
                    onClick={() =>
                      dispatch(
                        summarization.start({
                          objName: item.objName,
                          sourceName: item.sourceName,
                          docId: item.uniqueField
                        })
                      )
                    }
                    lang={variables.searchCallVariables.langAttr}
                    role={a11y.ROLES.BTN}
                    tabIndex={tabIndexes.tabIndex_0}
                    type="button"
                    className="retry-btn su__cursor"
                  >
                    {t(StaticStrings.retry)}
                    <SVGS.retry />
                  </button>
                )}
              </div>
            )}
            {!!suGPTSumms && !loader && !errorObject.show && (
              <div
                ref={summaryContent}
                lang={variables.searchCallVariables.langAttr}
                className={
                  hasError
                    ? 'su__noresult_text_color su__summary-border-no-result'
                    : 'su__popup-summary'
                }
                dangerouslySetInnerHTML={{ __html: summarizationrespone }}
              ></div>
            )}
          </>

          {!loader && !hasError && !printing && (
            <div className="su__d-flex su__align-items-center su__mb-12 su__summary-height-feed">
              <Tooltip text={t(StaticStrings.COPYCLIPBOARD)} position="bottom-right" from="summary">
                <button
                  type="button"
                  className="a11y-btn su__padding-0 su__cursor_pointer su__copyToBoard"
                  lang={variables.searchCallVariables.langAttr}
                  role={a11y.ROLES.BTN}
                  aria-label={t(StaticStrings.COPYCLIPBOARD)}
                  onClick={handleCopyToClipboard}
                >
                  <SVGS.copy />
                </button>
              </Tooltip>
              {!showLikeHighlight && !showDislikeHighlight && (
                <div className="su__popup-summary-positive su__position-relative">
                  <button
                    type="button"
                    className="a11y-btn su__padding-0 su__cursor_pointer su__positive"
                    tabIndex={tabIndexes.tabIndex_0}
                    role={a11y.ROLES.BTN}
                    aria-label={t(StaticStrings.positive_feedback_summary)}
                    lang={variables.searchCallVariables.langAttr}
                    onClick={handlePositive}
                  >
                    <SVGS.thumb position={'up'} />
                  </button>
                </div>
              )}
              {showLikeHighlight && !showDislikeHighlight && (
                <>
                  <button
                    role={a11y.ROLES.BTN}
                    type="button"
                    className="a11y-btn su__padding-0 su__cursor_pointer su__position-relative su__popup-summary-positive"
                    aria-live="assertive"
                    aria-atomic="true"
                    lang={variables.searchCallVariables.langAttr}
                    aria-label={t(StaticStrings.positive_given)}
                  >
                    <SVGS.thumb position={'up'} fill="#00b029" />
                  </button>
                </>
              )}
              {/* dislike button success  */}
              {showDislikeHighlight && (
                <div>
                  <button
                    type="button"
                    lang={variables.searchCallVariables.langAttr}
                    role={a11y.ROLES.BTN}
                    className="a11y-btn su__padding-0 su__cursor_pointer su__dislike_hov_fill su__popup-summary-positive "
                    aria-live="assertive"
                    aria-atomic="true"
                    aria-label={t(StaticStrings.negative_given)}
                  >
                    <SVGS.thumb position={'down'} fill={'#ff1616'} />
                  </button>
                </div>
              )}
              {/* dislike button  */}
              {!showLikeHighlight && !showDislikeHighlight && (
                <div>
                  <button
                    type="button"
                    className="a11y-btn su__padding-0 su__cursor_pointer su__dislike_hov_fill su__mr-10 su__negative "
                    tabIndex={tabIndexes.tabIndex_0}
                    lang={variables.searchCallVariables.langAttr}
                    role={a11y.ROLES.BTN}
                    aria-label={t(StaticStrings.negative_feedback_summary)}
                    onClick={handleNegative}
                  >
                    <SVGS.thumb position={'down'} />
                  </button>
                </div>
              )}
              {showToast && (
                <div className="su__thanku-feed">
                  <SVGS.ThankyouSvg />
                  <span className="su__summary-thank-text">Thank you for the feedback!</span>
                </div>
              )}
              {showCopy && (
                <span className="su__copied-span su__d-flex su__gap-5">
                  <SVGS.copy copied={showCopy} />
                  <span>Copied</span>
                </span>
              )}
            </div>
          )}
        </div>
      </div>
      <div
        tabIndex={tabIndexes.tabIndex_0}
        className="su__bg-overlay su__summary-index"
        onClick={onClose}
      ></div>
    </>
  );
};

SummarizationPopup.propTypes = {
  item: PropTypes.object.isRequired,
  onClose: PropTypes.func.isRequired,
  isOpen: PropTypes.bool.isRequired,
  onResponseChange: PropTypes.func.isRequired,
  setResponseStatus: PropTypes.func.isRequired,
  triggerRef: PropTypes.object
};

export default SummarizationPopup;
