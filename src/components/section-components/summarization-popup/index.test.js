/* global jest, it, expect, describe, beforeEach, afterEach */

// Mock variables module before any imports
jest.mock('../../../redux/variables', () => {
  return {
    searchClientType: 6,
    searchCallVariables: {
      uid: 'test-uid',
      langAttr: 'en'
    },
    summaryStreaming: true
  };
});

import React from 'react';
import { mount } from 'enzyme';
import { act } from 'react-dom/test-utils';
import SummarizationPopup from './index';
import { useDispatch, useSelector } from 'react-redux';
import variables from '../../../../__mocks__/variables';
import state, { createMockStream } from '../../../../__mocks__/state';

// Mock the redux hooks
jest.mock('react-redux', () => ({
  useSelector: jest.fn(),
  useDispatch: jest.fn()
}));

jest.setTimeout(10000);
jest.useFakeTimers();

jest.mock('react-i18next', () => ({
  useTranslation: jest.fn().mockReturnValue({ t: (key) => key })
}));

window.gza = jest.fn();
jest.mock('../../../function-library/commonFunctions', () => ({
  animateText: jest.fn(),
  readStream: jest.fn(
    (
      response,
      effectId,
      latestEffectId,
      animatorRefs,
      setTriggers,
      setSuGPTSumms,
      setErrorObject
    ) => {
      // For error testing
      if (response === 'ERROR_GENERATING') {
        setTriggers({ loader: false, hasError: true, printing: false });
        setErrorObject({
          show: true,
          message: 'Error message',
          showRetry: true
        });
        return;
      }
      // For normal flow
      setTriggers({ loader: false, hasError: false, printing: false });
      setSuGPTSumms('Summary text content');
    }
  ),
  copyToClipboard: jest.fn((content, setShowCopy, setShowToast) => {
    setShowCopy(true);
    setTimeout(() => setShowToast(true), 100);
  }),
  showAndCloseToast: jest.fn((setShowToast, setShowCopy) => {
    setShowToast(true);
    setTimeout(() => {
      setShowToast(false);
      setShowCopy(false);
    }, 2000);
  })
}));

// Mock redux ducks
jest.mock('redux/ducks', () => ({
  summarization: {
    start: jest.fn()
  }
}));

// Mock other components used within SummarizationPopup
jest.mock('../../section-components/source-label/index', () => {
  const SourceLabel = () => <div>SourceLabel</div>;
  return SourceLabel;
});

jest.mock('../../section-components/title/index', () => {
  const Title = () => <div>Title</div>;
  return Title;
});

jest.mock('../../section-components/href/index', () => {
  const Href = () => <div>Href</div>;
  return Href;
});

jest.mock('../../section-components/meta-data/index', () => {
  const Metadata = () => <div>Metadata</div>;
  return Metadata;
});
describe('SummarizationPopup Component', () => {
  let mockItem;
  let mockProps;
  const mockDispatch = jest.fn();
  const setTriggersMock = jest.fn();
  const mockHandleCheckForError = (inputString, errorArray) => {
    if (errorArray) {
      const filteredArray = errorArray.filter((item) => item.startsWith(inputString));
      if (filteredArray && filteredArray.length) {
        setTriggersMock({ hasError: true });
      } else {
        setTriggersMock({ hasError: false });
      }
    }
  };

  beforeEach(() => {
    // Reset the mock before each test
    useDispatch.mockReturnValue(mockDispatch);

    // Setup mock item
    mockItem = {
      highlight: {
        TitleToDisplay: ['Test Title'],
        SummaryToDisplay: ['Test Summary']
      },
      sourceName: 'Test Source',
      objName: 'TestObj',
      uniqueField: 'test123'
    };

    // Setup props
    mockProps = {
      item: mockItem,
      onClose: jest.fn(),
      isOpen: true,
      onResponseChange: jest.fn(),
      setResponseStatus: jest.fn()
    };

    // Set searchCallVariables in variables
    variables.searchCallVariables = {
      uid: 'test-uid',
      langAttr: 'en'
    };

    // Set analytics ID
    window._gza_analytics_id = '12345';

    // Mock useSelector to return summarization result
    useSelector.mockImplementation(() => 'Test summarization content');
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  /**
   * Helper function to test interactions with buttons
   * @param {string} buttonSelector - CSS selector for the button to test
   * @param {Object} gzaParams - Parameters expected in the gza call
   * @returns {Promise<void>}
   */
  const testInteraction = async (buttonSelector, gzaParams) => {
    jest.useFakeTimers();

    state.summarizationResult = createMockStream();
    useSelector.mockImplementation((selector) => selector(state));

    let wrapper;
    await act(async () => {
      wrapper = mount(<SummarizationPopup {...mockProps} />);
    });

    jest.runAllTimers();

    wrapper.update();

    // Find and directly interact with the button
    const button = wrapper.find(buttonSelector);
    expect(button.exists()).toBe(true);

    await act(async () => {
      button.simulate('click');
    });

    expect(window.gza).toHaveBeenCalledWith('summarize_interaction', {
      uid: 'test-uid',
      docTitle: 'Test Title',
      responseGenerated: 'Summary text content',
      analyticsId: window._gza_analytics_id,
      ...gzaParams
    });

    wrapper.unmount();
  };

  it('renders without crashing', () => {
    const wrapper = mount(<SummarizationPopup {...mockProps} />);
    expect(wrapper.exists()).toBe(true);
  });

  it('shows loading state initially', () => {
    const wrapper = mount(<SummarizationPopup {...mockProps} />);
    expect(wrapper.find('.loader-text').text()).toContain('Summarizing...');
    expect(wrapper.find('.su__loader_snippet').exists()).toBe(true);
  });

  it('calls onResponseChange when summary content changes', () => {
    mount(<SummarizationPopup {...mockProps} />);
    expect(mockProps.onResponseChange).toHaveBeenCalled();
  });

  it('handles positive feedback click', async () => {
    await testInteraction('.su__positive', {
      interactionType: 'feedback',
      reaction: true
    });
  });

  it('handles negative feedback click', async () => {
    await testInteraction('.su__negative', {
      interactionType: 'feedback',
      reaction: false
    });
  });

  it('handles copy to clipboard click', async () => {
    await testInteraction('.su__copyToBoard', {
      interactionType: 'copyToClipBoard'
    });
  });

  it('tests handleCheckForError when error is found', async () => {
    // Test with error condition
    const mockErrorArray = ['Error: test message', 'Another error'];
    const inputString = 'Error';

    mockHandleCheckForError(inputString, mockErrorArray);
    expect(setTriggersMock).toHaveBeenCalledWith({ hasError: true });
  });

  it('tests handleCheckForError when no error is found', async () => {
    const mockErrorArray = ['Some error'];
    const inputString = 'Different string';

    mockHandleCheckForError(inputString, mockErrorArray);
    expect(setTriggersMock).toHaveBeenCalledWith({ hasError: false });
  });

  it('displays loader when waiting for summary', () => {
    const wrapper = mount(<SummarizationPopup {...mockProps} />);

    expect(wrapper.find('.loader-text').text()).toContain('Summarizing...');
    expect(wrapper.find('.su__loader_snippet').exists()).toBe(true);

    expect(wrapper.find('.su__popup-summary').exists()).toBe(false);

    wrapper.unmount();
  });

  it('should display error message when summarizationResult has an error', async () => {
    useSelector.mockReturnValue('ERROR_GENERATING');

    let wrapper;
    await act(async () => {
      wrapper = mount(<SummarizationPopup {...mockProps} />);
    });
    await act(async () => {
      jest.runAllTimers();
      wrapper.update();
    });
    expect(wrapper.find('.loader-text').text()).toBe('Summary');
    const { animateText } = require('../../../function-library/commonFunctions');
    expect(animateText).toHaveBeenCalledWith(
      expect.stringContaining('Unable to generate'),
      expect.anything(),
      expect.anything(),
      expect.anything(),
      expect.anything(),
      expect.anything(),
      expect.stringContaining('Unable to generate'),
      expect.anything()
    );

    wrapper.unmount();
  });

  it('should update error state properly when handling errors', () => {
    const { readStream } = require('../../../function-library/commonFunctions');
    const setTriggersMock = jest.fn();
    const setErrorObjectMock = jest.fn();
    readStream(
      'ERROR_GENERATING',
      'testEffectId',
      { current: 'testEffectId' },
      [],
      setTriggersMock,
      jest.fn(),
      setErrorObjectMock
    );
    expect(setTriggersMock).toHaveBeenCalledWith({
      loader: false,
      hasError: true,
      printing: false
    });

    // Verify that errorObject was set correctly
    expect(setErrorObjectMock).toHaveBeenCalledWith({
      show: true,
      message: expect.any(String),
      showRetry: expect.any(Boolean)
    });
  });
});
