import React, { useState, useEffect } from 'react';
import Tooltip from 'components/feature-components/tooltip/index.jsx';
import variables from '../../../redux/variables';
import { A11Y_IDS } from 'constants/a11y';
import { tabIndexes } from '../../../constants/a11y';
import Icons from '../../../assets/svg-icon/svg';
import { useDevice } from 'function-library/hooks';
import StaticStrings from 'StaticStrings';
import { useTranslation } from 'react-i18next';
import { attachmentPreviewSearch } from '../../../redux/ducks';
import { useDispatch, useSelector } from 'react-redux';
const AttachmentPreview = ({ item, attachmentPreview, linkOpened, index }) => {
  const dispatch = useDispatch();
  const attachmentPreviewData = useSelector((state) => state.attachmentPreviewData);
  const attachmentPreviewError = useSelector((state) => state.attachmentPreviewError);
  try {
    const AttachmentToDisplay = item.highlight.AttachmentToDisplay;
    const { isDeviceMobile, isDeviceDesktop } = useDevice();
    const [openAttachmentPreview, setOpenAttachmentPreview] = useState(false);
    const [previewData, setPreviewData] = useState(null);
    const [attachmentName, setAttachmentName] = useState(null);
    const [attachmentUrl, setAttachmentUrl] = useState(null);
    const [showOverlay, setShowOverlay] = useState(false);
    const [showLoader, setShowLoader] = useState(false);
    const [itemId, setItemId] = useState(null);
    const { t } = useTranslation();

    const handlePlainText = (text) => {
      if (!text) return;
      let normalizedText = text.replace(/\n{3,}/g, '\n\n');
      return normalizedText
        .split('\n')
        .map((line) => {
          let linedata = line.trim();
          return linedata ? `<p>${linedata}</p>` : '';
        })
        .filter(Boolean)
        .join('');
    };

    const handleAttachmentPreview = (name) => {
      setShowLoader(true);
      const queryPassed = {
        uniqueField: item.uniqueField,
        sourceName: item.sourceName,
        objName: item.objName,
        attachmentName: name
      };
      dispatch(attachmentPreviewSearch.start(queryPassed));
    };

    const openUrl = (url) => {
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('target', '_blank');
      link.style.display = 'none';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    };
    const handleAttachmentclick = async (url, name) => {
      try {
        sendAttachmentevent(item, index + 1, attachmentPreview, name, url);
        if (openAttachmentPreview) {
          setPreviewData(null);
        }
        setAttachmentName(name);
        setAttachmentUrl(url);
        setItemId(index);
        if (attachmentPreview == 1) {
          openUrl(url);
        } else {
          setShowLoader(true);
          setShowOverlay(true);
          handleAttachmentPreview(name);
        }
      } catch (error) {
        console.log('error while downloading or previewing the attachment file', error);
        setShowLoader(false);
        setShowOverlay(false);
      }
    };
    const closePreview = () => {
      setOpenAttachmentPreview(false);
      setShowOverlay(false);
      setPreviewData(null);
      setAttachmentName(null);
      setAttachmentUrl(null);
      setShowLoader(false);
      setItemId(null);
    };
    const sanitizeHtml = (htmlString) => {
      if (!htmlString) return;
      const tempDiv = document.createElement('div');
      const regex = /<\/?[a-z][\s\S]*>/i;
      const content = regex.test(htmlString);
      tempDiv.innerHTML = htmlString;
      let contentdiv;
      if (content) {
        const parser = new DOMParser();
        const parsedHTML = parser.parseFromString(htmlString, 'text/html');
        const pages = parsedHTML.querySelectorAll('.page');
        const joinedHTML = Array?.from(pages)
          .map((page) => page?.innerHTML)
          .join('');
        contentdiv = joinedHTML || parsedHTML.body.innerHTML;
      } else {
        contentdiv = handlePlainText(htmlString);
      }
      return contentdiv;
    };

    const sendAttachmentevent = (result, rank, type, name, url) => {
      linkOpened(result, rank);
      const event = {
        index: result['sourceName'],
        type: result['objName'],
        id: result['_id'],
        relevance_score: result['_score'],
        rank: rank,
        convUrl: result['href'],
        convSub: result['highlight']['TitleToDisplayString'][0],
        pageSize: variables.searchCallVariables.pageSize,
        page_no: variables.searchCallVariables.pageNo,
        sc_analytics_fields: result['trackAnalytics'],
        attachmentPreviewType: type,
        attachmentName: name,
        attachmentUrl: url
      };
      /* eslint-disable no-undef */
      gza('attachment_preview', event);
    };

    useEffect(() => {
      if (attachmentPreviewData && !previewData && attachmentPreviewData?.statusCode === 200) {
        try {
          setPreviewData(attachmentPreviewData.result?.attachmentPreview);
          setOpenAttachmentPreview(true);
          setShowLoader(false);
        } catch (error) {
          setShowLoader(false);
          console.error('Error parsing attachment preview data:', error);
        }
      }
      if (attachmentPreviewError) {
        setShowLoader(false);
        setShowOverlay(false);
        setPreviewData(null);
        setAttachmentName(null);
        setAttachmentUrl(null);
        console.error('Error fetching attachment preview:', attachmentPreviewError);
      }
    }, [attachmentPreviewData, attachmentPreviewError]);

    return (
      <>
        {showLoader && (
          <div className="su__loading-internal">
            <div className="dot"></div>
            <div className="dot"></div>
            <div className="dot"></div>
            <div className="dot"></div>
            <div className="dot"></div>
          </div>
        )}
        {attachmentPreview && AttachmentToDisplay && AttachmentToDisplay.length !== 0 ? (
          <div className="su__d-flex su__flex-row su__flex-wrap su__flex-vcenter su__mt-1">
            <span className="su__mr-2 su__color-grey  su__rtlmr-0 su__rtlml-2 su__font-11 su__f-medium su__mb-1">
              {t(StaticStrings.Attachments)}
            </span>
            {[...AttachmentToDisplay].map((attachment) => {
              const extenstion = attachment.name.split('.').pop();
              return (
                <Tooltip
                  key={`${attachment.url}`}
                  text={attachment.name}
                  position="top"
                  className="position-relative"
                  tipClassName={'su__attachment-tooltip'}
                >
                  <span className="su__d-flex su__flex-vcenter su__text-center su__tags su__radius-2 su__mr-1 su__rtlmr-0 su__rtlml-2 su__line-height-n su__font-11 su__f-normal  su__segoe-ui su__mb-1">
                    <Icons
                      className="su__attachment_icon"
                      IconName="Attachments"
                      width="16"
                      height="16"
                      fill="none"
                      transform="translate(8.982 -1.191) rotate(41)"
                    />
                    <span
                      className="su__p-2px su__attachment-name su__cursor_pointer"
                      onClick={() => {
                        handleAttachmentclick(attachment.url, attachment.name);
                      }}
                    >
                      {attachment.name.slice(0, -extenstion.length - 1)}
                    </span>
                    <span className="su__attachment_type">{extenstion.toUpperCase()}</span>
                  </span>{' '}
                </Tooltip>
              );
            })}
          </div>
        ) : null}
        {openAttachmentPreview &&
          !showLoader &&
          itemId === index &&
          previewData &&
          previewData.length && (
            <div
              id={A11Y_IDS.trap}
              className={`su__flex-hcenter su__bookmark-row su__zindex-3 su__sc-loading su-preview-json su__bg-white su__p-20 su__radius-1 `}
            >
              <div className="su__attachment_preview">
                {previewData && previewData.length && (
                  <>
                    <div className="su__d-flex su__align-items-start su__mb-15">
                      <div
                        className={`su__text-left su__d-flex su__justify-content-between su__flex-1 su__font-14 su__color-gray su__f-normal ${
                          isDeviceMobile ? 'su__flex-column-reverse' : ''
                        }`}
                      >
                        <span className="su__word-break su__overflow-hide ">
                          <h2
                            tabIndex={tabIndexes.tabIndex_0}
                            className={`su__line-clamp su__font-17 su__f-regular su__cursor_pointer su__text-underline ${
                              isDeviceMobile ? 'su__m-0 su__pt-2' : 'su__my-0 '
                            } ${isDeviceDesktop ? 'hover-color-dblue' : ''}`}
                            onClick={() => openUrl(attachmentUrl)}
                            dangerouslySetInnerHTML={{ __html: attachmentName }}
                          ></h2>
                        </span>
                        {!variables.toggleDisplayKeys[5].hideEye &&
                          variables.searchCallVariables.showContentTag && (
                            <Tooltip
                              text={item.sourceLabel}
                              position="bottom"
                              className="position-relative su__f-normal"
                            >
                              <span className="su__mw-300 su__w-mcontent su__pointer">
                                <span
                                  tabIndex={tabIndexes.tabIndex_0}
                                  className={`su__px-2 su__f-normal su__color_grey su__preview-contentLabel su__text-truncate su__font-14 ${
                                    isDeviceMobile ? '' : 'su__ml-2 '
                                  } `}
                                >
                                  {item.sourceLabel}
                                </span>
                              </span>
                            </Tooltip>
                          )}
                      </div>
                      <div className="su__close-button">
                        <button
                          type="button"
                          className="a11y-btn "
                          aria-label="Close modal"
                          onClick={closePreview}
                        >
                          <Icons
                            className="su__cursor su__p-1"
                            IconName="Close"
                            width="20"
                            height="20"
                            color="#57575c"
                          />
                        </button>
                      </div>
                    </div>
                    <div className="su__overflow-auto su__text-left ">
                      {/* Render the wrapper for items with previewOrder  2  */}
                      <div className="su__attachment-content su__thin-scrollbar">
                        <div
                          dangerouslySetInnerHTML={{
                            __html: sanitizeHtml(previewData[0][attachmentName])
                          }}
                        ></div>
                      </div>
                    </div>
                  </>
                )}
              </div>
            </div>
          )}
        {showOverlay && (
          <div
            tabIndex={tabIndexes.tabIndex_0}
            className="su__overlay su__overlayz-9"
            onClick={closePreview}
          ></div>
        )}
      </>
    );
  } catch (error) {
    return null;
  }
};

export default React.memo(AttachmentPreview);
