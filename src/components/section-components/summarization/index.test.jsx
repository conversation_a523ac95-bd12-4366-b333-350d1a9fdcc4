/* eslint-disable react/prop-types */
/* global */
/* global jest, it, expect, describe, beforeEach afterEach */

import React from 'react';
import { mount } from 'enzyme';
import Summarization from './index';
import { useDispatch } from 'react-redux';
import { summarization } from 'redux/ducks';
import variables from '../../../redux/variables';

// Mock the redux hooks
jest.mock('react-redux', () => ({
  ...jest.requireActual('react-redux'),
  useDispatch: jest.fn()
}));

// Mock the summarization action
jest.mock('redux/ducks', () => ({
  summarization: {
    start: jest.fn()
  }
}));

// Mock variables
jest.mock('../../../redux/variables', () => ({
  searchCallVariables: {
    searchString: 'test search',
    pageSize: 10,
    pageNo: 1,
    uid: 'test-uid'
  }
}));

// Mock the global gza function
global.gza = jest.fn();

// Mock the SummarizationPopup component
const SummarizationPopup = ({ onClose, onResponseChange, setResponseStatus }) => {
  return (
    <div data-testid="summarization-popup">
      <button data-testid="close-popup" onClick={onClose}>
        Close
      </button>
      <button
        data-testid="set-response"
        onClick={() => {
          onResponseChange('Sample summarization response');
          setResponseStatus('success');
        }}
      >
        Set Response
      </button>
    </div>
  );
};

jest.mock('../summarization-popup/index', () => SummarizationPopup);

describe('Summarization Component', () => {
  let useDispatchMock;
  let mockItem;
  beforeEach(() => {
    mockItem = {
      objName: 'TestObject',
      sourceName: 'TestSource',
      uniqueField: 'test-id-123',
      _id: 'test-id-123',
      href: 'https://test.com/item',
      highlight: {
        TitleToDisplayString: ['Test Title']
      },
      trackAnalytics: { key1: 'value1' }
    };
    useDispatchMock = jest.fn();
    useDispatch.mockReturnValue(useDispatchMock);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });
  it('renders without crashing', () => {
    const wrapper = mount(<Summarization item={mockItem} isGridView={false} index={0} />);
    expect(wrapper.exists()).toBe(true);
  });

  it('renders the summarization icon SVG inside the summarization button', () => {
    const wrapper = mount(<Summarization item={mockItem} isGridView={false} index={0} />);
    // Find the button with the su__summarization class
    const summaryButton = wrapper.find('button.su__summarization');
    // Verify the button exists
    expect(summaryButton.exists()).toBe(true);
    // Check if there's an SVG element inside this specific button
    expect(summaryButton.find('svg').exists()).toBe(true);
  });

  it('does not render the SummarizationPopup by default', () => {
    const wrapper = mount(<Summarization item={mockItem} isGridView={false} index={0} />);
    expect(wrapper.find('[data-testid="summarization-popup"]').exists()).toBe(false);
  });

  it('shows the SummarizationPopup when handleSummarization is called', () => {
    const wrapper = mount(<Summarization item={mockItem} isGridView={false} index={0} />);

    wrapper.find('.su__summarization').simulate('click');
    // SummarizationPopup should be visible
    expect(wrapper.find('[data-testid="summarization-popup"]').exists()).toBe(true);
  });

  it('dispatches summarization action when handleSummarization is called', () => {
    const wrapper = mount(<Summarization item={mockItem} isGridView={false} index={0} />);

    wrapper.find('.su__summarization').simulate('click');

    expect(useDispatchMock).toHaveBeenCalledWith(
      summarization.start({
        objName: mockItem.objName,
        sourceName: mockItem.sourceName,
        docId: mockItem.uniqueField
      })
    );
  });

  it('tracks conversion analytics when handleSummarization is called with non-empty search string', () => {
    const wrapper = mount(<Summarization item={mockItem} isGridView={false} index={1} />);

    // Click on the summarization icon to trigger handleSummarization
    wrapper.find('.su__summarization').simulate('click');

    // Check if gza was called with the correct parameters
    expect(global.gza).toHaveBeenCalledWith('conversion', {
      index: mockItem.sourceName,
      type: mockItem.objName,
      id: mockItem._id,
      rank: 2, // index + 1
      convUrl: mockItem.href,
      convSub: mockItem.highlight.TitleToDisplayString[0],
      pageSize: variables.searchCallVariables.pageSize,
      page_no: variables.searchCallVariables.pageNo,
      sc_analytics_fields: mockItem.trackAnalytics
    });
  });

  it('should not track conversion if search string is empty', () => {
    // Set empty search string
    const originalSearchString = variables.searchCallVariables.searchString;
    variables.searchCallVariables.searchString = '   ';

    const wrapper = mount(<Summarization item={mockItem} isGridView={false} index={0} />);

    // Click on the summarization icon to trigger handleSummarization
    wrapper.find('.su__summarization').simulate('click');

    // Check that gza was not called
    expect(global.gza).not.toHaveBeenCalledWith('conversion', expect.anything());

    // Restore original search string
    variables.searchCallVariables.searchString = originalSearchString;
  });

  it('closes the popup when onClose is called', () => {
    const wrapper = mount(<Summarization item={mockItem} isGridView={false} index={0} />);

    // Open the popup
    wrapper.find('.su__summarization').simulate('click');
    expect(wrapper.find('[data-testid="summarization-popup"]').exists()).toBe(true);

    // Close the popup
    wrapper.find('[data-testid="close-popup"]').simulate('click');
    wrapper.update();

    // SummarizationPopup should not be visible
    expect(wrapper.find('[data-testid="summarization-popup"]').exists()).toBe(false);
  });

  it('tracks summarize_search_result analytics when summarizationrespone changes', () => {
    const wrapper = mount(<Summarization item={mockItem} isGridView={false} index={0} />);

    // Set up window analytics ID
    window._gza_analytics_id = 'test-analytics-id';

    // Open the popup
    wrapper.find('.su__summarization').simulate('click');

    // Set response using the mock button
    wrapper.find('[data-testid="set-response"]').simulate('click');

    // Check if gza was called with summarize_search_result
    expect(global.gza).toHaveBeenCalledWith('summarize_search_result', {
      uid: variables.searchCallVariables.uid,
      textEntered: variables.searchCallVariables.searchString,
      responseGenerated: 'Sample summarization response',
      responseStatus: 'success',
      analyticsId: 'test-analytics-id'
    });
  });
});
