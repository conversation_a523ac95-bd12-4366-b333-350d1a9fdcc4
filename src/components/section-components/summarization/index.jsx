/* global gza */
import React, { useState, useEffect, useRef } from 'react';
import PropTypes from 'prop-types';
import SummarizationPopup from '../summarization-popup/index';
import { useDispatch } from 'react-redux';
import { summarization } from 'redux/ducks';
import variables from '../../../redux/variables';
import { a11y, A11Y_IDS, tabIndexes, useFocusTrap } from '../../../constants/a11y';
import { useTranslation } from 'react-i18next';
import StaticStrings from 'StaticStrings';
import { SVGS } from 'assets/svg-icon/index';
import Tooltip from 'components/feature-components/tooltip/index';

const Summarization = ({ item, index }) => {
  const [focusTrap] = useFocusTrap();
  const { t } = useTranslation();
  const [isPopupVisible, setPopupVisible] = useState(false);
  const [summarizationrespone, setSummarizationrespone] = useState('');
  const [responseStatus, setResponseStatus] = useState('');
  const [fillColour, setFillColour] = useState();
  const buttonRef = useRef(null);
  const dispatch = useDispatch();

  const handleSummarization = () => {
    setPopupVisible(true);
    setFillColour('#000');
    dispatch(
      summarization.start({
        objName: item.objName,
        sourceName: item.sourceName,
        docId: item.uniqueField
      })
    );
    const rank = index + 1;
    if (
      variables.searchCallVariables.searchString.trim() &&
      variables.searchCallVariables.searchString.trim().length
    ) {
      gza('conversion', {
        index: item.sourceName,
        type: item.objName,
        id: item._id,
        rank: rank,
        convUrl: item.href,
        convSub: item.highlight.TitleToDisplayString[0],
        pageSize: variables.searchCallVariables.pageSize,
        page_no: variables.searchCallVariables.pageNo,
        sc_analytics_fields: item.trackAnalytics
      });
    }
  };

  const handleClose = () => {
    setPopupVisible(false);
  };

  const handleKeyDown = (e) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      handleSummarization();
    }
  };

  useEffect(() => {
    if (summarizationrespone) {
      gza('summarize_search_result', {
        uid: variables.searchCallVariables.uid,
        textEntered: variables.searchCallVariables.searchString,
        responseGenerated: summarizationrespone,
        responseStatus: responseStatus,
        analyticsId: window._gza_analytics_id
      });
    }
  }, [summarizationrespone]);

  useEffect(() => {
    focusTrap(isPopupVisible);
  }, [isPopupVisible]);

  return (
    <>
      <div>
        <Tooltip
          text={t(StaticStrings.Summarize)}
          position="bottom"
          tipClassName="su__tooltip-summerize-positions"
        >
          <button
            onMouseOver={() => setFillColour('#fff')}
            onMouseOut={() => setFillColour('#000')}
            ref={buttonRef}
            onClick={handleSummarization}
            onKeyDown={handleKeyDown}
            className="su__summarization su__mr-2px  a11y-btn su__summarization_hover-bg"
            aria-label={t(StaticStrings.summarize_content || 'Summarize content')}
            aria-expanded={isPopupVisible}
            role={a11y.ROLES.BTN}
            tabIndex={tabIndexes.tabIndex_0}
            lang={variables.searchCallVariables.langAttr}
            type="button"
            data-trigger-a11y={A11Y_IDS.trap}
            data-name-a11y="summarize_content"
            data-timeout-a11y="5000"
          >
            <SVGS.SummaryIcon fillColour={fillColour} />
          </button>
        </Tooltip>
        {isPopupVisible && (
          <SummarizationPopup
            item={item}
            onClose={handleClose}
            isOpen={isPopupVisible}
            onResponseChange={setSummarizationrespone}
            setResponseStatus={setResponseStatus}
            triggerRef={buttonRef}
          />
        )}
      </div>
    </>
  );
};

Summarization.propTypes = {
  item: PropTypes.object.isRequired,
  isGridView: PropTypes.bool,
  index: PropTypes.number
};

export default Summarization;
