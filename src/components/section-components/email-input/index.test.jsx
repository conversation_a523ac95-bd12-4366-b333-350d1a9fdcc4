/* global jest, it, expect, describe */
import React from 'react';
import { shallow } from 'enzyme';
import EmailInput from './index'; // Adjust the import path as necessary

jest.mock('../../../redux/variables', () => require('__mocks__/variables'));
describe('EmailInput Component', () => {
  const defaultProps = {
    disabled: false,
    isValid: true,
    setFollowupEmail: jest.fn(),
    huddleOnChange: jest.fn(),
    followupEmail: '',
    tabIndex: 0
  };

  it('should render the component without crashing', () => {
    const wrapper = shallow(<EmailInput {...defaultProps} />);
    expect(wrapper.exists()).toBe(true);
  });

  it('should render the label and input correctly', () => {
    const wrapper = shallow(<EmailInput {...defaultProps} />);
    expect(wrapper.find('label').text()).toBe('Email ID (Optional)'); // Update if using a mocked translation
    expect(wrapper.find('input[type="email"]').exists()).toBe(true);
  });

  it('should handle `disabled` prop', () => {
    const wrapper = shallow(<EmailInput {...defaultProps} disabled={true} />);
    const input = wrapper.find('input[type="email"]');
    expect(input.prop('disabled')).toBe(true);
  });

  it('should call `setFollowupEmail` on input change', () => {
    const setFollowupEmailMock = jest.fn();
    const wrapper = shallow(
      <EmailInput {...defaultProps} setFollowupEmail={setFollowupEmailMock} />
    );
    const input = wrapper.find('input[type="email"]');
    input.simulate('change', { target: { value: '<EMAIL>' } });
    expect(setFollowupEmailMock).toHaveBeenCalledWith('<EMAIL>');
  });

  it('should call `huddleOnChange` on input event', () => {
    const huddleOnChangeMock = jest.fn();
    const wrapper = shallow(<EmailInput {...defaultProps} huddleOnChange={huddleOnChangeMock} />);
    const input = wrapper.find('input[type="email"]');
    input.simulate('input', { target: { value: '<EMAIL>' } });
    expect(huddleOnChangeMock).toHaveBeenCalledWith('<EMAIL>');
  });

  it('should render error message when `isValid` is false', () => {
    const wrapper = shallow(<EmailInput {...defaultProps} isValid={false} />);
    const errorMessage = wrapper.find('.su__error-msg--visible');
    expect(errorMessage.exists()).toBe(false); // Adjust visibility based on `isInvalidEmailAlertVisible` logic
  });

  it('should apply proper ARIA attributes for accessibility', () => {
    const wrapper = shallow(<EmailInput {...defaultProps} />);
    const input = wrapper.find('input[type="email"]');
    expect(input.prop('aria-label')).toBe('Email ID'); // Update if using mocked translation
    const liveRegion = wrapper.find('#invalidEmailText');
    expect(liveRegion.prop('aria-live')).toBe('polite');
    expect(liveRegion.prop('aria-atomic')).toBe('true');
  });

  it('should apply custom `tabIndex` to input', () => {
    const wrapper = shallow(<EmailInput {...defaultProps} tabIndex={2} />);
    const input = wrapper.find('input[type="email"]');
    expect(input.prop('tabIndex')).toBe(2);
  });
});
