import React, { useState } from 'react';
import { useTranslation } from 'react-i18next'; // Assuming you're using react-i18next
import variables from '../../../redux/variables';
import StaticStrings from '../../../StaticStrings';
import { A11Y_HIDDEN, tabIndexes } from 'constants/a11y';
import PropTypes from 'prop-types';
const EmailInput = ({
  disabled,
  isValid,
  tabIndex = 0, // Set a default tabIndex
  setFollowupEmail,
  huddleOnChange,
  followupEmail
}) => {
  const { t } = useTranslation();
  const [emailValidity, setEmailValidity] = useState(false);

  const handleFocus = () => {
    /** changed aria-describedy approach to mitigate NVDA + firefox announcing twice issue as below*/
    setEmailValidity(false);
    const announce = document.getElementById('invalidEmailText');
    if (announce) {
      announce.textContent = ''; // set the alert text to empty string
      setTimeout(() => {
        announce.textContent = !isValid ? t(StaticStrings.invalid_email) : ''; // reset to actual value after a delay
      }, 200);
    }
  };

  const checkBlur = () => {
    setEmailValidity(true);
  };

  return (
    <div className="su__feed-email-box">
      <label
        htmlFor="su__feedback-email"
        lang={variables.searchCallVariables.langAttr}
        className="su__feedback-label su__font-11 su__color-lgray su__d-block"
      >
        {t(StaticStrings.email_text)} <span>(Optional)</span>
      </label>
      <input
        lang={variables.searchCallVariables.langAttr}
        className="su__input-feedack su__form-control su__w-100 su__su__font-14 su__text-black su__border su__radius-2"
        type="email"
        name="su__feedback-email"
        id="su__feedback-email"
        disabled={disabled}
        aria-label={t(StaticStrings.email_text)} // Use aria-labelledby if provided
        value={followupEmail}
        onInput={(e) => huddleOnChange(e.target.value)}
        onChange={(e) => setFollowupEmail(e.target.value)}
        onFocus={handleFocus}
        tabIndex={tabIndex}
        onBlur={checkBlur}
      />
      <span
        lang={variables.searchCallVariables.langAttr}
        aria-hidden="true"
        className={`su__error-msg ${!isValid ? 'su__d-block' : ''}`} // Toggle visibility based on validity and focus state
      >
        {(!isValid && followupEmail === '') ||
        (!isValid && !emailValidity && followupEmail !== '') ||
        (isValid && emailValidity && followupEmail !== '')
          ? ''
          : t(StaticStrings.invalid_email)}
      </span>
      <div
        lang={variables.searchCallVariables.langAttr}
        id={'invalidEmailText'}
        aria-live="polite"
        aria-atomic="true"
        tabIndex={tabIndexes.tabIndex_minus_1}
        className={A11Y_HIDDEN}
      >
        {!isValid ? t(StaticStrings.invalid_email) : ''}
      </div>
    </div>
  );
};
EmailInput.propTypes = {
  /** Indicates if the input is disabled */
  disabled: PropTypes.bool,
  /** Indicates if the email input is valid */
  isValid: PropTypes.bool.isRequired,
  /** Function to handle focus events */
  onFocus: PropTypes.func,
  /** Function to handle input events */
  onInput: PropTypes.func,
  /** TabIndex for the input element */
  tabIndex: PropTypes.number,
  /** Function to set the follow-up email state */
  setFollowupEmail: PropTypes.func.isRequired,
  /** Function to handle changes via `huddleOnChange` */
  huddleOnChange: PropTypes.func.isRequired,
  /** Current value of the follow-up email */
  followupEmail: PropTypes.string.isRequired
};

EmailInput.defaultProps = {
  disabled: false,
  onFocus: () => {},
  onInput: () => {},
  tabIndex: -1 // Default tab index
};

export default EmailInput;
