import React from 'react';
import { Rating } from 'components/section-components/rating/index.jsx';
import Icons from '../../../assets/svg-icon/svg';

const _Yes_No = 'Yes/No';
const THUMBS_UP_DOWN = 'Thumbs up/Down';
const STARS = 'Stars';
const EMOTICONS = 'Emoticons';

const FeedbackYesOrNo = (props) => {
  let {
    pageRateFeed,
    handlenewSaverating,
    starhanleMouseEnter,
    starhandleMouseLeave,
    starhoverRating
  } = props;
  try {
    let {
      selectedPageTemplete,
      selectedHeader,
      storeRate,
      storePageRatingFeedback,
      storePageRatingData
    } = props;
    return (
      <div
        className={`su_feedback_form su__flex-vcenter su__feedbackform_custom ${
          selectedPageTemplete == 'Stars' || selectedPageTemplete == 'Emoticons'
            ? 'su__feedbackflex'
            : ''
        }`}
      >
        <div className="su__text-black su__flex-1 su__text-left su__font-12">{selectedHeader}</div>
        {selectedPageTemplete == _Yes_No && (
          <>
            <span
              className="su_helpful su__mx-3"
              onClick={() => (storeRate ? storePageRatingData(1) : storePageRatingFeedback(1))}
            >
              Yes
            </span>
            <span
              className="su_unhelpful"
              onClick={() => (storeRate ? storePageRatingData(0) : storePageRatingFeedback(0))}
            >
              No
            </span>
          </>
        )}
        {selectedPageTemplete == THUMBS_UP_DOWN && (
          <>
            <span
              className="su_thumb-yes su__mx-3"
              onClick={() => (storeRate ? storePageRatingData(1) : storePageRatingFeedback(1))}
            >
              <Icons
                IconName="FtSnippet__ThumbsUp"
                className="su__cursor feedbackRatingthumbs"
                width="24"
                height="24"
                fill="#323232"
                transform="scale(.549) translate(8, 4)"
              />
            </span>
            <span
              className="su_thumb-no"
              onClick={() => (storeRate ? storePageRatingData(0) : storePageRatingFeedback(0))}
            >
              <Icons
                IconName="FtSnippet__ThumbsDown"
                className="su__cursor feedbackRatingthumbs"
                width="24"
                height="24"
                fill="#323232"
                widthInner="24"
                heightInner="24"
                transform="translate(3,4)"
              />
            </span>
          </>
        )}
        {selectedPageTemplete == STARS && (
          <>
            <span className="su_thumb-yes su__mx-3">
              <Rating
                feedbackType={selectedPageTemplete}
                size={40}
                stars={5}
                rating={pageRateFeed}
                hoverRating={starhoverRating}
                onMouseEnter={starhanleMouseEnter}
                onMouseLeave={starhandleMouseLeave}
                onClick={handlenewSaverating}
              />
            </span>
          </>
        )}
        {selectedPageTemplete == EMOTICONS && (
          <>
            <span className="su_thumb-yes su__mx-3">
              <div>
                <Rating
                  feedbackType={selectedPageTemplete}
                  size={40}
                  stars={5}
                  rating={pageRateFeed}
                  hoverRating={starhoverRating}
                  onMouseEnter={starhanleMouseEnter}
                  onMouseLeave={starhandleMouseLeave}
                  onClick={handlenewSaverating}
                />
              </div>
            </span>
          </>
        )}
      </div>
    );
  } catch (e) {
    console.log('Error in FeedbackYesOrNo component', e);
    return <div></div>;
  }
};

export default React.memo(FeedbackYesOrNo);
