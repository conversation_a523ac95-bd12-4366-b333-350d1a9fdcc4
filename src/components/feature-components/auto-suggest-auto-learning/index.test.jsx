/* global jest, it, expect, describe, beforeEach */
import React from 'react';
import { mount } from 'enzyme';
import AutoLearningSuggestion from './';
import variables from '__mocks__/variables';
import setCookies from '../../../setCookie/setCookie';

const SELECTORS = {
  productSuggestion: '[data-test-id^="product-suggestions-"]',
  productSuggestionTitle: '[data-test-id^="product-suggestion-title-"]',
  productSuggestionCategory: '[data-test-id^="product-suggestion-category-"]'
};

// Mock the necessary modules
jest.mock('../../../redux/variables', () => require('__mocks__/variables'));

jest.mock('../../../setCookie/setCookie', () => ({
  setSmartFacetOff: jest.fn()
}));

const autocompleteResultMock = {
  smartAggregations: [
    {
      key: 'category',
      label: 'Category',
      order: 0,
      values: [
        { Contentname: 'Electronics', selected: false, parent: 'category' },
        { Contentname: 'Books', selected: false, parent: 'category' },
        { Contentname: 'Clothing', selected: true, parent: 'category' }
      ]
    },
    {
      key: 'post_time',
      label: 'Post Time',
      order: 1,
      values: [
        { Contentname: 'Last 24 hours', selected: false },
        { Contentname: 'Last week', selected: false },
        { Contentname: 'Last month', selected: false }
      ]
    }
  ]
};

describe('AutoLearningSuggestion component', () => {
  beforeEach(() => {
    variables.searchCallVariables.smartFacetsClicked = false;
    setCookies.setSmartFacetOff.mockClear();
  });

  it('renders without crashing', () => {
    const wrapper = mount(<AutoLearningSuggestion autocompleteResult={autocompleteResultMock} />);
    expect(wrapper.exists()).toBe(true);
  });

  it('renders correct number of suggestions', () => {
    const wrapper = mount(<AutoLearningSuggestion autocompleteResult={autocompleteResultMock} />);
    expect(wrapper.find(SELECTORS.productSuggestion)).toHaveLength(2);
    expect(wrapper.find(SELECTORS.productSuggestionCategory)).toHaveLength(6);
  });

  it('updates the selected state of filter when product suggestion is clicked', () => {
    const wrapper = mount(<AutoLearningSuggestion autocompleteResult={autocompleteResultMock} />);
    /* 
      0 - Electronics
      2 - Clothing
      4 - Last week
    */
    [0, 2, 4].forEach((index) => {
      wrapper.find(SELECTORS.productSuggestionCategory).at(index).simulate('click');
      expect(
        wrapper.find(SELECTORS.productSuggestionCategory).at(index).hasClass('su__product-active')
      ).toBe(index !== 2);
    });
  });

  it('renders correct label for suggestions', () => {
    const wrapper = mount(<AutoLearningSuggestion autocompleteResult={autocompleteResultMock} />);
    autocompleteResultMock.smartAggregations.forEach((item, index) => {
      expect(wrapper.find(SELECTORS.productSuggestionTitle).at(index).text()).toBe(item.label);
    });
  });
});
