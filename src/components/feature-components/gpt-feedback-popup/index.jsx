import useOnClickOutsideRef from 'event-handler/outside-click';
import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { a11y, A11Y_IDS, tabIndexes, useFocusTrap } from 'constants/a11y';
import StaticStrings from 'StaticStrings';
import { useDevice } from 'function-library/hooks';
import variables from '../../../redux/variables';
import { bool, func, string } from 'prop-types';
import Icons from 'assets/svg-icon/svg';
import IconColors from 'IconColors';
import { SVGS } from 'assets/svg-icon/index';
const GptFeedbackPopUp = (props) => {
  const { t } = useTranslation();
  const [focusTrap] = useFocusTrap();
  const {
    heading,
    IsShown,
    likeBtn,
    textContent,
    showThanks,
    buttonContent1,
    buttonContent2,
    onAction
  } = props;
  const [feedbackButtons, setFeedbackButtons] = useState({
    feedbackGivenAcc: false,
    feedbackGivenAcc2: false
  });
  const { isDeviceMobile, isDeviceDesktop } = useDevice();
  const [isSubmitDisabled, setIsSubmitDisabled] = useState(true);
  const [feedback, setFeedback] = useState('');
  const modalRef = useOnClickOutsideRef(() => {
    setFeedbackButtons({
      feedbackGivenAcc: false,
      feedbackGivenAcc2: false
    });
    setFeedback('');
  }, 'popup');
  const maxCharacters = 300;

  useEffect(() => {
    setIsSubmitDisabled(
      !feedbackButtons.feedbackGivenAcc &&
        !feedbackButtons.feedbackGivenAcc2 &&
        feedback.trim().length === 0
    );
  }, [feedbackButtons.feedbackGivenAcc, feedbackButtons.feedbackGivenAcc2, feedback]);

  useEffect(() => {
    focusTrap(IsShown);
  }, [IsShown]);

  const closePopup = () => {
    onAction('close');
  };
  const openThanksModal = () => {
    const feedbackTags = [];

    if (feedbackButtons.feedbackGivenAcc) {
      feedbackTags.push(buttonContent1);
    }

    if (feedbackButtons.feedbackGivenAcc2) {
      feedbackTags.push(buttonContent2);
    }
    onAction('openthanks', feedback, feedbackTags);
    setFeedbackButtons({
      feedbackGivenAcc: false,
      feedbackGivenAcc2: false
    });
  };

  const handleButtonClick = (buttonKey) => {
    setFeedbackButtons((prevButtons) => ({
      ...prevButtons,
      [buttonKey]: !prevButtons[buttonKey]
    }));
  };

  const handleChange = (event) => {
    const inputValue = event.target.value;
    if (inputValue.length >= maxCharacters) {
      setFeedback(inputValue.slice(0, maxCharacters));
    } else {
      setFeedback(inputValue);
    }
  };

  return (
    IsShown && (
      <div
        ref={modalRef}
        className="su__flex-hcenter su__position-fixed su__trbl-0 su__zindex-4 su__px-sm-1"
      >
        <div
          role={a11y.ROLES.DIALOG}
          aria-labelledby="dialog1_label"
          aria-modal="true"
          id={A11Y_IDS.trap}
          className={`su__modal-inner ${
            !showThanks
              ? isDeviceMobile
                ? 'su__mobile-modal'
                : 'su__new-modal'
              : 'su__thanks-modal'
          } su__radius su__animate-fadown su__zindex-1 su__shadow-lg su__bg-white su__radius-1`}
        >
          <div className="su__popup-text">
            <div
              className={`${
                !showThanks ? 'su__mt-14 su__d-flex su__position-relative' : ''
              } su__kh_mx-18px`}
            >
              {!showThanks && heading ? (
                <h2
                  id={'dialog1_label'}
                  lang={variables.searchCallVariables.langAttr}
                  className="su__cancel su__heading-feedback su__my-0 su__color-blue su__mx-18px su__f-normal"
                >
                  {heading}
                </h2>
              ) : null}
              {!showThanks && (
                <button
                  type="button"
                  lang={variables.searchCallVariables.langAttr}
                  className="a11y-btn p-0 order-4 su__close-icon su__cursor su__position-absolute su__right_20px su__cross_icon_rtl"
                  aria-label={t(StaticStrings.close_popup)}
                  role={a11y.ROLES.BTN}
                  tabIndex={tabIndexes.tabIndex_0}
                  onClick={closePopup}
                >
                  <Icons
                    IconName="Close"
                    width="12"
                    height="12"
                    color={IconColors.AdvanceSearchCrossIcon}
                    onClick={closePopup}
                  />
                </button>
              )}
            </div>
            {showThanks ? (
              <div className="su__flex-hcenter su__margin_top_15">
                <SVGS.thanksCheck />
              </div>
            ) : null}
            <div
              className={`su__d-flex su__align-items-center su__justify-content-center ${
                !showThanks ? 'su__justify-content-between' : null
              } `}
            >
              <div className="su__flex-column">
                {textContent ? (
                  <div className={showThanks ? '' : 'su__pt-new '}>
                    <div
                      aria-live="polite"
                      lang={variables.searchCallVariables.langAttr}
                      className={`su__new-padding su__font-14 ${
                        !showThanks ? 'su__margin-norm' : 'su__margin-thanks-color'
                      } su__text-black su__f-normal su__flex-1 su__pb-new  su__rtlmr-3 su__rtlml-0`}
                    >
                      {!showThanks ? textContent : 'Thank You! Your feedback helps'}
                    </div>
                  </div>
                ) : null}
                {!showThanks ? (
                  <div className="su__Conform-block  su__flex-vcenter su__bottom-padding su__mr-18px su__mob-mr-0 su__mob-ar-mr-16 su__gpt-pop-up-mb">
                    <button
                      type="button"
                      lang={variables.searchCallVariables.langAttr}
                      role={a11y.ROLES.BTN}
                      tabIndex={tabIndexes.tabIndex_0}
                      aria-label={buttonContent1}
                      className={`su_new-btn ${
                        isDeviceDesktop ? 'su_new-btn-hover' : null
                      } su__rtlml-3 su__rtlmr-0 su__radius su__cursor a11y-btn  ${
                        feedbackButtons.feedbackGivenAcc ? 'su__add-click' : ''
                      } `}
                      onClick={() => handleButtonClick('feedbackGivenAcc')}
                    >
                      {t(buttonContent1)}
                    </button>
                    <button
                      type="button"
                      lang={variables.searchCallVariables.langAttr}
                      role={a11y.ROLES.BTN}
                      tabIndex={tabIndexes.tabIndex_0}
                      aria-label={buttonContent2}
                      className={` su_new-btn ${
                        isDeviceDesktop ? 'su_new-btn-hover' : null
                      } su__second-btn su__radius su__cursor a11y-btn  ${
                        feedbackButtons.feedbackGivenAcc2 ? 'su__add-click' : ''
                      } `}
                      onClick={() => handleButtonClick('feedbackGivenAcc2')}
                    >
                      {t(buttonContent2)}
                    </button>
                  </div>
                ) : null}
              </div>

              {!showThanks && (
                <div className="su__mb-pr-14 su__mob-ar-pr-0 su__mob-ar-pl-14">
                  {likeBtn ? (
                    <svg
                      className="su__mr-mt-10 su__gpt_svg"
                      xmlns="http://www.w3.org/2000/svg"
                      width="60"
                      height="60"
                      viewBox="0 0 61 61"
                    >
                      <g
                        id="Group_19861"
                        data-name="Group 19861"
                        transform="translate(-6311 -1202)"
                      >
                        <g
                          id="Group_19788"
                          data-name="Group 19788"
                          transform="translate(6311.638 1202.639)"
                          opacity="0.06"
                        >
                          <circle
                            id="Ellipse_6"
                            data-name="Ellipse 6"
                            cx="30.5"
                            cy="30.5"
                            r="30.5"
                            transform="translate(-0.638 -0.639)"
                            fill="#919bb0"
                          />
                        </g>
                        <path
                          id="svgexport-12"
                          d="M19.237,2.9A1.778,1.778,0,0,1,21,2.014l.807.1a7.112,7.112,0,0,1,5.916,9.149l-.979,3.183h1.277a7.112,7.112,0,0,1,6.862,8.984l-2.425,8.89A7.112,7.112,0,0,1,25.6,37.562H8.334A5.334,5.334,0,0,1,3,32.227V19.781a5.334,5.334,0,0,1,5.334-5.334H11.89a1.286,1.286,0,0,0,1.116-.648Zm-3.791,31.11H25.6a3.556,3.556,0,0,0,3.431-2.621l2.425-8.89A3.556,3.556,0,0,0,28.022,18H24.337a1.778,1.778,0,0,1-1.7-2.3l1.687-5.484a3.557,3.557,0,0,0-2.6-4.511l-5.632,9.856a4.842,4.842,0,0,1-2.426,2.1V32.227A1.778,1.778,0,0,0,15.447,34.006ZM10.112,18V32.227a5.326,5.326,0,0,0,.3,1.778H8.334a1.778,1.778,0,0,1-1.778-1.778V19.781A1.778,1.778,0,0,1,8.334,18Z"
                          transform="translate(6322 1213)"
                          fill="#919bb0"
                          fillRule="evenodd"
                          opacity="0.35"
                        />
                      </g>
                    </svg>
                  ) : (
                    <svg
                      className="su__mr-mt-10  su__gpt_svg"
                      xmlns="http://www.w3.org/2000/svg"
                      width="60"
                      height="60"
                      viewBox="0 0 61 61"
                    >
                      <g
                        id="Group_19860"
                        data-name="Group 19860"
                        transform="translate(-6308 -1205)"
                      >
                        <g
                          id="Group_19788"
                          data-name="Group 19788"
                          transform="translate(6308.638 1205.639)"
                          opacity="0.06"
                        >
                          <circle
                            id="Ellipse_6"
                            data-name="Ellipse 6"
                            cx="30.5"
                            cy="30.5"
                            r="30.5"
                            transform="translate(-0.638 -0.639)"
                            fill="#919bb0"
                          />
                        </g>
                        <path
                          id="svgexport-12"
                          d="M18.9,36.666a1.778,1.778,0,0,1-1.764.882l-.807-.1A7.112,7.112,0,0,1,10.414,28.3l.979-3.183H10.116a7.112,7.112,0,0,1-6.862-8.984l2.425-8.89A7.112,7.112,0,0,1,12.541,2H29.8a5.334,5.334,0,0,1,5.334,5.334V19.781A5.334,5.334,0,0,1,29.8,25.115H26.248a1.286,1.286,0,0,0-1.116.648Zm3.791-31.11H12.541A3.556,3.556,0,0,0,9.11,8.177l-2.425,8.89a3.556,3.556,0,0,0,3.431,4.492H13.8a1.778,1.778,0,0,1,1.7,2.3l-1.687,5.484a3.557,3.557,0,0,0,2.6,4.511L22.044,24a4.842,4.842,0,0,1,2.426-2.1V7.334A1.778,1.778,0,0,0,22.691,5.556Zm5.334,16V7.334a5.326,5.326,0,0,0-.3-1.778H29.8a1.778,1.778,0,0,1,1.778,1.778V19.781A1.778,1.778,0,0,1,29.8,21.559Z"
                          transform="translate(6319 1216)"
                          fill="#919bb0"
                          fillRule="evenodd"
                          opacity="0.35"
                        />
                      </g>
                    </svg>
                  )}
                </div>
              )}
            </div>
          </div>
          {!showThanks && (
            <div className="su__justify-content-between su__margin_top_15 su__mb-8 su__flex-vcenter ">
              <span lang={variables.searchCallVariables.langAttr} className="su__say-more ">
                Would you like to say more?
              </span>
              <span className="su__font-10 su__mx-3">
                <span className="su__black-color">{feedback.length}</span>
                <span className="su__grey-color">/{maxCharacters}</span>
              </span>
            </div>
          )}
          {!showThanks && (
            <textarea
              lang={variables.searchCallVariables.langAttr}
              placeholder="Share your feedback"
              name="feeback-txtarea"
              onChange={handleChange}
              className="su__feedtext-area-gpt  su__box-sizing su__f-regular su__ph-gray"
              id="su__feedtext-area"
              rows="4"
              value={feedback}
              maxLength={maxCharacters}
            ></textarea>
          )}
          {!showThanks && (
            <div
              className={`su__flex-vcenter su__justify-content-center su__mb-20 ${
                variables.isConsoleTypeSC ? 'su__pb-0 su__pt-20px' : 'su__bottom-padding-button'
              }`}
            >
              <button
                lang={variables.searchCallVariables.langAttr}
                type="button"
                role={a11y.ROLES.BTN}
                disabled={isSubmitDisabled}
                aria-label={
                  isSubmitDisabled ? t(StaticStrings.SUBMIT_DISABLED) : t(StaticStrings.SUBMIT)
                }
                className={`su__btn-gpt su__submit su__rtlml-3 su__rtlmr-0 su__radius  su__cursor su__rtlml-0 ${
                  isSubmitDisabled ? 'disabled-btn su__bookmark_SavedResult_opacity ' : ''
                }`}
                onClick={openThanksModal}
              >
                {t(StaticStrings.submit)}
              </button>
            </div>
          )}
        </div>
        <div className="su__overlay su__zindex-1" onClick={closePopup} />
      </div>
    )
  );
};

GptFeedbackPopUp.propTypes = {
  heading: string,
  IsShown: bool,
  likeBtn: bool,
  textContent: string,
  showThanks: bool,
  buttonContent1: string,
  buttonContent2: string,
  onAction: func
};

GptFeedbackPopUp.defaultProps = {
  heading: '',
  IsShown: false,
  likeBtn: false,
  textContent: '',
  showThanks: false,
  buttonContent1: '',
  buttonContent2: '',
  onAction: () => {}
};

export default GptFeedbackPopUp;
