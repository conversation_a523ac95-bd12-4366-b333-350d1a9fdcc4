/* global jest, it, expect, describe, beforeEach, afterEach */
import React from 'react';
import { mount } from 'enzyme';
import ScrollToTop from './';
import { useTranslation } from 'react-i18next';
import variables from '__mocks__/variables';
import { SC_IDS } from 'constants/constants';

// Mock the dependencies

jest.mock('react-i18next', () => ({
  useTranslation: jest.fn()
}));

jest.mock('../../../redux/variables', () => require('__mocks__/variables'));

jest.mock('StaticStrings', () => ({
  search: 'Search',
  page_top: 'Top of Page'
}));

jest.mock('../../../assets/svg-icon/svg', () => {
  const MockIcon = () => <div data-testid="mock-icon" />;
  MockIcon.displayName = 'MockIcon';
  return { __esModule: true, default: MockIcon };
});

describe('ScrollToTop Component', () => {
  let wrapper, originalScType;
  let testObj = {
    style: { display: 'none' }
  };

  beforeEach(() => {
    // Mock window methods
    window.scroll = jest.fn();
    document.querySelector = jest.fn(() => ({ focus: jest.fn(), offsetTop: 0 }));
    document.querySelectorAll = jest.fn(() => [{ scrollTop: 32 }]);
    useTranslation.mockImplementation(() => ({ t: (key) => key }));

    // Mock getElementById
    document.getElementById = jest.fn(() => testObj);
    originalScType = variables.searchClientType;
  });

  afterEach(() => {
    variables.searchClientType = originalScType;
    jest.clearAllMocks();
    wrapper?.unmount?.();
  });

  it('renders without crashing', () => {
    wrapper = mount(<ScrollToTop />);
    expect(wrapper.exists()).toBe(true);
  });

  it('renders mobile version when isDeviceMobile is true', () => {
    wrapper = mount(<ScrollToTop isDeviceMobile={true} />);
    expect(wrapper.find('.su__mobile-child-block').exists()).toBe(true);
  });

  it('renders desktop version when isDeviceMobile is false', () => {
    wrapper = mount(<ScrollToTop isDeviceMobile={false} />);
    expect(wrapper.find('.su__back-to-top').exists()).toBe(true);
  });

  it('calls goToTop function when button is clicked', () => {
    wrapper = mount(<ScrollToTop isDeviceMobile={false} />);
    wrapper.find('#goToTopBtn').simulate('click');
    expect(window.scroll).toHaveBeenCalled();
  });

  it('renders correctly with default props', () => {
    wrapper = mount(<ScrollToTop />);
    expect(wrapper.find('#goToTopBtn').exists()).toBe(true);
    expect(wrapper.find('#goToTopBtn').prop('aria-label')).toBe('Top of Page');
  });

  it('should handle scroll events and show/hide got to top button accordingly', () => {
    wrapper = mount(<ScrollToTop />);
    // scenario #1 = <40 units of space from top
    document.body.scrollTop = 20;
    window.dispatchEvent(new Event('scroll'));
    wrapper.update();
    expect(document.getElementById).toHaveBeenCalledWith('goToTopBtn');
    expect(testObj.style.display).toBe('none');
    // scenario #2 = >40 units of space from top
    document.body.scrollTop = 50;
    window.dispatchEvent(new Event('scroll'));
    wrapper.update();
    expect(document.getElementById).toHaveBeenCalledWith('goToTopBtn');
    expect(testObj.style.display).toBe('block');
    // scenario #3 = special case for search client type SC_IDS.SHAREPOINT
    variables.searchClientType = SC_IDS.SHAREPOINT;
    wrapper = mount(<ScrollToTop />);
    wrapper.update();
    window.dispatchEvent(new Event('scroll'));
    wrapper.update();
    expect(document.querySelectorAll).toHaveBeenCalledWith(
      '[data-automation-id="contentScrollRegion"]'
    );
  });

  it('should capture error gracefully in case the component encounters one', () => {
    const consoleSpy = jest.spyOn(console, 'log').mockImplementation(() => {});
    useTranslation.mockImplementation(() => {
      throw new Error('error');
    });
    wrapper = mount(<ScrollToTop />);
    wrapper.update();
    expect(wrapper.html()).toBe('<div></div>');
    expect(consoleSpy).toHaveBeenCalledWith('Error in goToTop component', expect.any(Error));
    consoleSpy.mockRestore();
  });
});
