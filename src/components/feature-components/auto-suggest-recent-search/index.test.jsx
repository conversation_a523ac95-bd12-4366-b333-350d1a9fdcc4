/* global jest, it, expect, describe, beforeEach, afterEach */
import React from 'react';
import { shallow } from 'enzyme';
import { useSelector, useDispatch } from 'react-redux';
import RecentSearch from './';
import AutoCompleteResultIcon from 'components/section-components/auto-suggest-icon/index.jsx';
import variables from '__mocks__/variables';
import state from '__mocks__/state';
import { search } from '../../../redux/ducks';

const SELECTORS = {
  recentSearchHistory: '[data-test-id^="recentSearchHistory-"]'
};

// Mock necessary imports
jest.mock('react-redux', () => ({
  useSelector: jest.fn(),
  useDispatch: jest.fn()
}));

jest.mock('../../../redux/variables', () => require('__mocks__/variables'));

describe('RecentSearch Component', () => {
  let dispatchMock, mockProps;

  beforeEach(() => {
    dispatchMock = jest.fn();
    useDispatch.mockReturnValue(dispatchMock);
    useSelector.mockImplementation((callback) => callback(state));

    mockProps = {
      currentSearchString: 'Recent Search 1',
      dataType: 'recentSearch',
      redirection: jest.fn(),
      component: 'searchbox'
    };

    // Mock the document.getElementById function
    global.document.getElementById = jest.fn().mockReturnValue({
      value: ''
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
    global.document.getElementById.mockReset();
  });

  it('should render correctly', () => {
    const wrapper = shallow(<RecentSearch {...mockProps} />);
    expect(wrapper.find(SELECTORS.recentSearchHistory)).toHaveLength(2);
    expect(wrapper.find(AutoCompleteResultIcon)).toHaveLength(2);
  });

  it('should update search string on click for autocomplete', () => {
    const autocompleteProps = { ...mockProps, component: 'autocomplete' };
    const wrapper = shallow(<RecentSearch {...autocompleteProps} />);
    wrapper.find(SELECTORS.recentSearchHistory).at(0).simulate('click');
    expect(variables.autocompleteCallVariables.searchString).toBe('Recent Search 1');
  });

  it('should update search string on click for searchbox', () => {
    const searchboxProps = { ...mockProps, component: 'searchbox' };
    const wrapper = shallow(<RecentSearch {...searchboxProps} />);
    wrapper.find(SELECTORS.recentSearchHistory).at(0).simulate('click');
    expect(variables.searchCallVariables.searchString).toBe('Recent Search 1');
    expect(dispatchMock).toHaveBeenCalledWith(search.start(variables.searchCallVariables));
  });

  it('should handle errors gracefully', () => {
    const consoleSpy = jest.spyOn(console, 'log').mockImplementation(() => {});
    useSelector.mockImplementation(() => {
      throw new Error('Test error');
    });
    useDispatch.mockImplementation(() => jest.fn());
    const wrapper = shallow(<RecentSearch {...mockProps} />);
    expect(wrapper.text()).toBe('');
    expect(consoleSpy).toHaveBeenCalledWith('Error in Recent Search component', expect.any(Error));
    consoleSpy.mockRestore();
  });
});
