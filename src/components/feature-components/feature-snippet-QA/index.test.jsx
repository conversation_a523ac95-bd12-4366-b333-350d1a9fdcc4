/* global jest, it, expect, describe, beforeEach, afterEach */
import React from 'react';
import { mount } from 'enzyme';
import FeaturedSnippetQA from './';
import { useTranslation } from 'react-i18next';
import state from '__mocks__/state';
import variables from '__mocks__/variables';

jest.mock('react-i18next', () => ({
  useTranslation: jest.fn()
}));

jest.mock('../../../redux/variables', () => require('__mocks__/variables'));

jest.mock('../../../assets/svg-icon/svg', () => {
  const Icon = () => <div>Icon</div>;
  return Icon;
});

describe('FeaturedSnippetQA', () => {
  let wrapper;
  const mockProps = {
    featuredSnippetResult: state.searchResult.featuredSnippetResult,
    linkOpened: jest.fn(),
    isDeviceMobile: false
  };

  beforeEach(() => {
    window.gza = jest.fn();
    jest.useFakeTimers(); // Enable fake timers
    useTranslation.mockImplementation(() => ({ t: (key) => key }));
    wrapper = mount(<FeaturedSnippetQA {...mockProps} />);
    wrapper.update();
  });

  afterEach(() => {
    jest.clearAllTimers();
    jest.clearAllMocks();
  });

  it('should render correctly', () => {
    expect(wrapper).toBeTruthy();
  });

  it('should render the answer and summary', () => {
    expect(wrapper.find('.su__featureSnippet-title').text()).toBe(
      mockProps.featuredSnippetResult.questionAnswer.answer
    );
    expect(wrapper.find('.su__su__featureSnippet-desc span').text()).toBe(
      mockProps.featuredSnippetResult.questionAnswer.short_summary
    );
  });

  it('should render the multimedia content', () => {
    expect(wrapper.find('.su__img-featured').prop('src')).toBe(
      mockProps.featuredSnippetResult.multiMedia[0].image_urls
    );
  });

  it('should handle link clicks on desktop', () => {
    const links = wrapper.find('a');
    links.forEach((link) => {
      link.simulate('click');
      link.simulate('mouseDown');
      link.simulate('contextMenu');
      expect(mockProps.linkOpened).toHaveBeenCalledWith(mockProps.featuredSnippetResult, 1);
    });
  });

  it('should handle link clicks on mobile', () => {
    const mobileProps = { ...mockProps, isDeviceMobile: true };
    wrapper = mount(<FeaturedSnippetQA {...mobileProps} />);
    wrapper.update();
    const links = wrapper.find('a');
    links.forEach((link) => {
      link.simulate('click');
      link.simulate('mouseDown');
      link.simulate('contextMenu');
      expect(mockProps.linkOpened).toHaveBeenCalledWith(mockProps.featuredSnippetResult, 1);
      mockProps.linkOpened.mockClear();
    });
  });

  it('should render correctly in mobile view', () => {
    const mobileProps = { ...mockProps, isDeviceMobile: true };
    const mobileWrapper = mount(<FeaturedSnippetQA {...mobileProps} />);

    expect(mobileWrapper.find('.su__snippet_container-width-mobile').exists()).toBe(true);
  });

  it('should track analytics on feature snippet feedback', () => {
    const thumbsDown = wrapper.find('span.su__featured-feedback').first();
    const thumbsUp = wrapper.find('span.su__featured-feedback').at(1);
    const actionButtons = [
      { btn: thumbsDown, feedbackVal: 0 },
      { btn: thumbsUp, feedbackVal: 1 }
    ];
    actionButtons.forEach((item, index) => {
      item.btn.simulate('click', {});
      jest.advanceTimersByTime(5500);
      expect(window.gza).toHaveBeenCalledTimes(index + 1);
      expect(window.gza).toHaveBeenCalledWith('featuredSnippet', {
        feedback: item.feedbackVal,
        searchString: variables.searchCallVariables.searchString,
        t: state.searchResult.featuredSnippetResult.highlight.TitleToDisplayString[0],
        uid: variables.searchCallVariables.uid,
        url: state.searchResult.featuredSnippetResult.href
      });
    });
  });

  it('should display empty div and log error if component catches errors', () => {
    const consoleSpy = jest.spyOn(console, 'log').mockImplementation(() => {});
    useTranslation.mockImplementation(() => {
      throw new Error('customError');
    });
    wrapper = mount(<FeaturedSnippetQA {...mockProps} />);
    wrapper.update();
    expect(consoleSpy).toHaveBeenCalledWith(
      'Error in feature-snippet component',
      expect.any(Error)
    );
    consoleSpy.mockRestore();
  });
});
