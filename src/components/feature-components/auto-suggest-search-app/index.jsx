/* global gza */
import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import variables from '../../../redux/variables';
import RecentSearch from 'components/feature-components/auto-suggest-recent-search/index.jsx';
import AutoCompleteResultIcon from 'components/section-components/auto-suggest-icon/index.jsx';
import AutoCompleteResultTitle from 'components/section-components/auto-suggest-title/index.jsx';
import AutoCompleteResultMetadata from 'components/section-components/auto-suggest-metadata/index.jsx';
import AutoLearningSuggestion from 'components/feature-components/auto-suggest-auto-learning/index.jsx';
import { tabIndexes } from '../../../constants/a11y';
import StaticStrings from 'StaticStrings';
import { useTranslation } from 'react-i18next';
import { useDevice } from 'function-library/hooks';
import { v4 as uuid } from 'uuid';
const Suggest = (props) => {
  try {
    const { t } = useTranslation();
    let urlOpensInNewTab = props.urlOpensInNewTab;
    let currentSearchString = props.data;
    let changeSearchDataOnArrowKey = props.changeSearchDataOnArrowKey;
    let tempData = props.tempData;
    let dataType = props.dataType;
    let previousAction = props.previousAction;
    let setIsComponentVisible = props.setIsComponentVisible;
    const [highlightCondition, setHighlightCondition] = useState(currentSearchString);
    const autocompleteResult = useSelector((state) => state.autocomplete);
    /**
     *  autocomplete gza function
     */
    const resultOpenNewTab = (item, index) => {
      gza('search', {
        searchString: variables.autocompleteCallVariables.searchString,
        result_count: autocompleteResult.result.hits.length,
        page_no: 0,
        uid: variables.autocompleteCallVariables.uid,
        search_type: autocompleteResult.searchType,
        searchUid: variables.autocompleteCallVariables.searchUid,
        filter: [],
        conversion: [
          {
            rank: index + 1,
            relevance_score: item['_score'],
            url: item['href'],
            subject: item['highlight']['TitleToDisplayString'][0] || '',
            es_id:
              item['sourceName'] + '/' + item['objName'] + '/' + encodeURIComponent(item['_id']),
            sc_analytics_fields: item['trackAnalytics']
          }
        ]
      });
    };
    const { isDeviceMobile } = useDevice();
    useEffect(() => {
      if (!changeSearchDataOnArrowKey) {
        setHighlightCondition(currentSearchString);
        return;
      }

      const selectedData = tempData[changeSearchDataOnArrowKey[0]];

      if (selectedData?.Id && selectedData?.highlight?.TitleToDisplayString) {
        setHighlightCondition(selectedData.Id || selectedData.href);
      } else {
        setHighlightCondition(currentSearchString);
      }
    }, [changeSearchDataOnArrowKey, tempData]);

    return (
      <>
        <div
          lang={variables.searchCallVariables.langAttr}
          aria-label={
            autocompleteResult.result.hits.length + ' ' + `${t(StaticStrings.suggestions_found)}`
          }
          aria-live="assertive"
          aria-atomic="true"
          tabIndex={tabIndexes.tabIndex_0}
          className={`su__autoSuggestion-border su__autosearchapp-suggestion su__w-100 su__bg-white su__sm-shadow su__position-absolute su__zindex-2 ${
            isDeviceMobile
              ? variables.isConsoleTypeSC
                ? ' su__mobile_zendesk_resultbox su__br-unset'
                : 'su__mobile_resultbox'
              : 'su__mt-6px'
          } `}
        >
          <div
            id="my_div"
            className={`${
              isDeviceMobile
                ? 'su__suggestions-box_mobile'
                : 'su__suggestions-box su__suggestions-box-height'
            }  su__minscroller`}
          >
            <div className="auto-suggestion su__autosuggestion_container">
              {autocompleteResult &&
                autocompleteResult.recentSearchHistory &&
                autocompleteResult.recentSearchHistory.length != 0 && (
                  <RecentSearch
                    currentSearchString={currentSearchString}
                    component={props.component}
                    dataType={dataType}
                    previousAction={previousAction}
                  />
                )}
              {autocompleteResult.result &&
                variables.autocompleteCallVariables.searchString &&
                autocompleteResult.result.hits.length != 0 && (
                  <div className="su__suggested_text su__mr-rtl-20">
                    {t(StaticStrings.SUGGESTED_RESULTS)}
                  </div>
                )}
              {autocompleteResult.result &&
                variables.autocompleteCallVariables.searchString &&
                autocompleteResult.result.hits.length != 0 &&
                React.Children.toArray(
                  autocompleteResult.result.hits.map((item, index) => (
                    <div
                      key={uuid()}
                      data-key={index}
                      tabIndex={tabIndexes.tabIndex_0}
                      className={`su__suggestions-list su__bg-gray-hover su__flex-vcenter su__fontsize-14 su__text-black su__my-1 ${
                        highlightCondition === (item.Id || item.href) &&
                        currentSearchString ===
                          (item.highlight.TitleToDisplayString[0] || item.href) &&
                        dataType == 'autosuggest' &&
                        previousAction
                          ? 'su__highlight_result'
                          : ''
                      }`}
                    >
                      <AutoCompleteResultIcon item={item} />
                      <div className="su__text-truncate su__d-flex su__flex-column su__autocomplete_alignment">
                        <AutoCompleteResultTitle
                          item={item}
                          index={index}
                          resultOpenNewTab={resultOpenNewTab}
                          urlOpensInNewTab={urlOpensInNewTab}
                        />
                        <AutoCompleteResultMetadata item={item} />
                      </div>
                    </div>
                  ))
                )}
              {autocompleteResult &&
                autocompleteResult.smartAggregations &&
                autocompleteResult.smartAggregations.length && (
                  <div
                    className={`su__product-sugt-row su__minscroller su__product-length-${
                      autocompleteResult &&
                      autocompleteResult.smartAggregations &&
                      autocompleteResult.smartAggregations.length
                    }`}
                    style={{
                      position: autocompleteResult.result.hits.length ? 'absolute' : 'inherit'
                    }}
                  >
                    <AutoLearningSuggestion autocompleteResult={autocompleteResult} />
                  </div>
                )}
            </div>
          </div>
        </div>
        <div
          className="su__overlay-transparent su__zindex-1"
          onClick={() => setIsComponentVisible(false)}
        ></div>
      </>
    );
  } catch (e) {
    console.log('Error in auto-suggest component', e);
    return <div></div>;
  }
};

export default Suggest;
