/* global jest, it, expect, describe, beforeEach */
import React from 'react';
import { shallow, mount } from 'enzyme';
import { useSelector } from 'react-redux';
import Suggest from './';
import RecentSearch from 'components/feature-components/auto-suggest-recent-search/index.jsx';
import AutoCompleteResultIcon from 'components/section-components/auto-suggest-icon/index.jsx';
import AutoCompleteResultTitle from 'components/section-components/auto-suggest-title/index.jsx';
import AutoCompleteResultMetadata from 'components/section-components/auto-suggest-metadata/index.jsx';
import AutoLearningSuggestion from 'components/feature-components/auto-suggest-auto-learning/index.jsx';
import state from '__mocks__/state';
import variables from '__mocks__/variables';

jest.mock('react-redux', () => ({
  useSelector: jest.fn()
}));

jest.mock('react-i18next', () => ({
  useTranslation: () => ({ t: (key) => key })
}));
const TestComponent = () => <></>;
const autocompleteResult = {
  result: {
    hits: [
      { highlight: { TitleToDisplayString: ['Electronics'] }, href: 'mockHref1' },
      { highlight: { TitleToDisplayString: ['Books'] }, href: 'mockHref2' }
    ]
  },
  smartAggregations: []
};

const mockSetState = jest.fn();

jest.mock('react', () => ({
  ...jest.requireActual('react'),
  useState: jest.fn().mockImplementation(() => ['test', mockSetState])
}));

jest.mock('../../../redux/variables', () => require('__mocks__/variables'));

describe('AutoSuggest Search app component', () => {
  beforeEach(() => {
    useSelector.mockReturnValue(state.autocomplete);
    window.gza = jest.fn();
  });

  it('should render recent search history', () => {
    const wrapper = shallow(<Suggest data="search query" dataType="autosuggest" />);
    expect(wrapper.find(RecentSearch)).toHaveLength(1);
  });

  it('should render autocomplete result items', () => {
    const wrapper = shallow(<Suggest data="search query" dataType="autosuggest" />);
    expect(wrapper.find(AutoCompleteResultIcon)).toHaveLength(2);
    expect(wrapper.find(AutoCompleteResultTitle)).toHaveLength(2);
    expect(wrapper.find(AutoCompleteResultMetadata)).toHaveLength(2);
  });

  it('should render auto learning suggestions', () => {
    const wrapper = shallow(<Suggest data="search query" dataType="autosuggest" />);
    expect(wrapper.find(AutoLearningSuggestion)).toHaveLength(1);
  });

  it('should track analytics when results are clicked', () => {
    const wrapper = shallow(<Suggest data="search query" dataType="autosuggest" />);

    state.autocomplete.result.hits.forEach((item, index) => {
      const resultOpenNewTab = wrapper
        .find(AutoCompleteResultTitle)
        .at(index)
        .prop('resultOpenNewTab');
      resultOpenNewTab(item, index);

      expect(window.gza).toHaveBeenCalledWith('search', {
        searchString: variables.autocompleteCallVariables.searchString,
        result_count: state.autocomplete.result.hits.length,
        page_no: 0,
        uid: variables.autocompleteCallVariables.uid,
        filter: [],
        conversion: [
          {
            rank: index + 1,
            url: item.href,
            subject: item.highlight.TitleToDisplayString[0],
            es_id: item.sourceName + '/' + item.objName + '/' + encodeURIComponent(item._id),
            sc_analytics_fields: item.trackAnalytics
          }
        ]
      });
    });
  });

  it('should update highlight condition based on id/href if there is a change in selectedData', () => {
    /* scenario #1 | changeSearchDataOnArrowKey is not null and Id / href is exists in selectedData */
    const props = {
      data: 'test',
      autoCompleteResult: autocompleteResult,
      resultOpenNewTab: true
    };
    const mockId = 'mock_id';
    props.changeSearchDataOnArrowKey = ['mockData'];
    props.tempData = {
      mockData: {
        Id: mockId,
        highlight: { TitleToDisplayString: ['Books'] },
        href: 'mockHref2'
      }
    };
    let wrapper = mount(<Suggest {...props}></Suggest>);
    wrapper.update();
    expect(mockSetState).toHaveBeenCalledTimes(2);
    expect(mockSetState).toHaveBeenCalledWith(mockId);
    mockSetState.mockClear();
    /* scenario #2 | changeSearchDataOnArrowKey is not null but Id / href is missing in selectedData */
    props.tempData = {
      mockData: {
        href: 'mockHref2'
      }
    };
    wrapper = mount(
      <Suggest {...props}>
        <TestComponent />
      </Suggest>
    );
    wrapper.update();
    expect(mockSetState).toHaveBeenCalledTimes(2);
    expect(mockSetState).toHaveBeenCalledWith(props.data);
    mockSetState.mockClear();
    /* scenario #3 | changeSearchDataOnArrowKey is null */
    props.changeSearchDataOnArrowKey = null;
    wrapper = mount(
      <Suggest {...props}>
        <TestComponent />
      </Suggest>
    );
    wrapper.update();
    expect(mockSetState).toHaveBeenCalledTimes(2);
    expect(mockSetState).toHaveBeenCalledWith(props.data);
    mockSetState.mockClear();
  });

  it('catches component error and renders empty div', () => {
    const consoleSpy = jest.spyOn(console, 'log').mockImplementation(() => {});
    useSelector.mockImplementation(() => {
      throw new Error('Test error');
    });
    const wrapper = shallow(<Suggest data="search query" dataType="autosuggest" />);

    expect(wrapper.html()).toBe('<div></div>');
    expect(consoleSpy).toHaveBeenCalledWith('Error in auto-suggest component', expect.any(Error));
    consoleSpy.mockRestore();
  });
});
