import React, { Fragment } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import variables from '../../../redux/variables';
import { useTranslation } from 'react-i18next';
import { search } from '../../../redux/ducks';
import StaticStrings from 'StaticStrings';
import { a11y, tabIndexes } from '../../../constants/a11y';
import { v4 as uuid } from 'uuid';
import Icons from '../../../assets/svg-icon/svg';
import IconColors from '../../../IconColors';
const SimilarSearches = () => {
  try {
    /**
     *  Change Languages state
     */
    const { t } = useTranslation();
    const searchResult = useSelector((state) => state.searchResult);
    const dispatch = useDispatch();
    let searchCall = (value) => {
      const searchBox = document.getElementById('search-box-search');
      if (searchBox && value != null) {
        searchBox.value = value;
      }
      variables.searchCallVariables.searchString = value;
      variables.searchSource = 'similar-search';
      variables.searchCallVariables.pagingAggregation = [];
      dispatch(search.start(variables.searchCallVariables));
      window.scrollTo({ top: 0, behavior: 'smooth' });
    };

    return (
      <Fragment>
        {variables.searchCallVariables.searchString &&
        searchResult &&
        searchResult.similarSearches &&
        searchResult.similarSearches.length !== 0 ? (
          <div className="su__similarSearches">
            <div className="su__recommendations-results su__position-relative ">
              <div className="su__ribbon-block ">
                <div className="su__ribbon-row-block su__w-auto su__loading-view su__d-flex">
                  <Icons
                    IconName="SearchBoxSearchBtn"
                    width="24"
                    height="24"
                    color={IconColors.simillar_search_icon}
                    // transform="translate(0 0)"
                  />
                  <h2
                    lang={variables.searchCallVariables.langAttr}
                    tabIndex={tabIndexes.tabIndex_0}
                    className="su__ribbon-text su__font-17 su__text-center su__text-black su__f-regular su__mb-2 su__radius-3 su__my-0"
                  >
                    {t(StaticStrings.similar_searches)}
                  </h2>
                </div>
              </div>
              <div className="su__mb-3 su__mt-3 su__sim_s_inner-div su__px-3 su__bg-white">
                {variables.searchCallVariables.searchString &&
                  searchResult &&
                  searchResult.similarSearches &&
                  searchResult.similarSearches.map((item) => (
                    <div
                      key={uuid()}
                      data-test-id="su__searchCall_test"
                      onClick={() => searchCall(item)}
                      className="su__py-3 su__d-flex su__border-b-lg su__radius su__align-items-start su__sim_s_item"
                    >
                      <div className="su__w-100 su__cursor">
                        <div className="su__recommendations-title">
                          <h3
                            role={a11y.ROLES.BTN}
                            tabIndex={tabIndexes.tabIndex_0}
                            className="su__search-title su__list-item-title su__text-truncate su__m-0 su__loading-view su__text-decoration su__text-hover-underline"
                          >
                            <a className="su__font-15 su__text-decoration su__f-normal hover-color-dblue su__color_black su__line-height-22 su__hover-color-black">
                              {item}
                            </a>
                          </h3>
                        </div>
                      </div>
                    </div>
                  ))}
              </div>
            </div>
          </div>
        ) : null}
      </Fragment>
    );
  } catch (e) {
    console.log('Error in Similar searches component', e);
    return <div></div>;
  }
};

export default SimilarSearches;
