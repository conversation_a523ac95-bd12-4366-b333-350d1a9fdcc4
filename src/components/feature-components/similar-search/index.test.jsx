/* global jest, it, expect, describe, beforeEach */
import React from 'react';
import { shallow } from 'enzyme';
import { useDispatch, useSelector } from 'react-redux';
import SimilarSearches from './';
import variables from '../../../redux/variables';

jest.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key) => key
  })
}));

jest.mock('react-redux', () => ({
  useDispatch: jest.fn(),
  useSelector: jest.fn()
}));

jest.mock('../../../redux/ducks', () => ({
  search: {
    start: jest.fn()
  }
}));

jest.mock('../../../redux/variables', () => ({
  searchCallVariables: {
    pagingAggregation: [],
    searchString: 'search1',
    langAttr: 'en'
  },
  previousDymSearchString: '',
  searchSource: ''
}));

describe('SimilarSearches component', () => {
  let wrapper;
  const dispatch = jest.fn();

  const mockState = {
    searchResult: {
      similarSearches: ['search1', 'search2']
    }
  };

  beforeEach(() => {
    useDispatch.mockReturnValue(dispatch);
    useSelector.mockImplementation((selector) => selector(mockState));
    variables.searchCallVariables = {
      searchString: 'search1',
      langAttr: 'en',
      from: 0,
      pageNo: 1,
      pagingAggregation: []
    };
    wrapper = shallow(<SimilarSearches />);
  });

  it('should render without crashing', () => {
    expect(wrapper.exists()).toBe(true);
  });

  it('should not render the similar searches section if there are no similar searches', () => {
    useSelector.mockImplementation((selector) =>
      selector({ searchResult: { similarSearches: [] } })
    );
    wrapper = shallow(<SimilarSearches />);
    expect(wrapper.find('.su__similarSearches').exists()).toBe(false);
  });

  it('should render the similar searches section if similar searches are present', () => {
    expect(wrapper.find('.su__similarSearches').exists()).toBe(true);
  });

  it('should render the correct number of similar search items', () => {
    const items = wrapper.find('.su__recommendations-title');
    expect(items).toHaveLength(mockState.searchResult.similarSearches.length);
  });

  it('should call searchCall on item click', () => {
    window.scrollTo = jest.fn();

    wrapper.find('div[data-test-id="su__searchCall_test"]').at(0).simulate('click');

    expect(variables.searchCallVariables.searchString).toBe('search1');
    expect(variables.searchSource).toBe('similar-search');
    expect(variables.searchCallVariables.pagingAggregation).toEqual([]);

    expect(window.scrollTo).toHaveBeenCalledWith({ top: 0, behavior: 'smooth' });
  });

  it('should show error when error caught', () => {
    jest.spyOn(require('react-i18next'), 'useTranslation').mockReturnValue({
      t: jest.fn(() => {
        throw new Error('Simulated translation error');
      })
    });

    const consoleLog = jest.spyOn(console, 'log').mockImplementation();
    const wrapper = shallow(<SimilarSearches />);
    expect(consoleLog).toHaveBeenCalledWith(
      'Error in Similar searches component',
      expect.any(Error)
    );
    expect(wrapper.html()).toBe('<div></div>');
    consoleLog.mockRestore();
  });
});
