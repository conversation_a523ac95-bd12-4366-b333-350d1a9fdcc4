/* eslint-disable react/prop-types */
import React, { useState, Fragment, useEffect, useRef } from 'react';
import Icons from '../../../assets/svg-icon/svg';
import variables from '../../../redux/variables';
import { useDispatch } from 'react-redux';
import { search } from '../../../redux/ducks';
import { useTranslation } from 'react-i18next';
import Tooltip from 'components/feature-components/tooltip/index.jsx';
import UnderConstruction from 'components/feature-components/under-consruction/index.jsx';
import setCookies from '../../../setCookie/setCookie';
import IconColors from '../../../IconColors';
import StaticStrings from '../../../StaticStrings';
import { a11y, A11Y_IDS, useFocusTrap, tabIndexes } from '../../../constants/a11y';
import { setWildcardToggle } from 'function-library/commonFunctions';
import { savedResultReq } from '../../../redux/searchClientTypes';
import { v4 as uuid } from 'uuid';
import { useDevice } from 'function-library/hooks';
import { SVGS } from 'assets/svg-icon/index';
import { EVENT_NAMES, STATUS_CODES, SC_IDS } from 'constants/constants';
import utilityMethods from 'redux/utilities/utility-methods';

const BOOKMARK = 'bookmark';
const SAVED_RESULT = 'savedResult';

const ListBookmarks = (props) => {
  /**
   *  Change Languages state
   */
  try {
    const dispatch = useDispatch();
    const { t } = useTranslation();
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [bookmarkSelect, setBookmark] = useState(false);
    const [savedResultSelect, setSavedResult] = useState(false);
    const [bookmarkAndSavedResultTab, setBookmarkAndSavedResultTab] = useState(BOOKMARK);
    const [focusTrap, resetTrap] = useFocusTrap();
    const storedData = localStorage.getItem(
      'bookmark_searches_' + variables.searchCallVariables.uid
    );
    const initialBookmarkListing = storedData ? JSON.parse(storedData) : [];
    const [bookmarkListing, setList] = useState(initialBookmarkListing);
    let initialOopsIndex;
    if (bookmarkListing.length === 0) {
      initialOopsIndex = tabIndexes.tabIndex_minus_1;
    } else {
      initialOopsIndex = tabIndexes.tabIndex_0;
    }
    const [oopsIndex, setOopsIndex] = useState(initialOopsIndex);
    const [savedResultListing, setsavedResultList] = useState(
      JSON.parse(localStorage.getItem('savedResult_' + variables.searchCallVariables.uid) || '[]')
    );
    const [loading, setLoading] = useState(null);
    const isFirstRender = useRef(true);
    const parseEntities = (txt) => new DOMParser().parseFromString(txt, 'text/html').body.innerText;
    const { isDeviceMobile } = useDevice();
    const topActionsView = utilityMethods.isTopActionsView();
    /**
     *  Press Esc button Hide funtion
     */
    const handleHideDropdown = (e) => {
      e.key === EVENT_NAMES.ESCAPE && setIsModalOpen(false);
    };

    const parentfun = () => {
      props.savedResultBookmark();
    };
    useEffect(() => {
      if (!isFirstRender.current) {
        let savedResultList = JSON.parse(
          localStorage.getItem('savedResult_' + variables.searchCallVariables.uid) || '[]'
        );
        if (savedResultList) {
          setsavedResultList(savedResultList);
        }
        if (savedResultList && savedResultList.length >= 50) {
          savedResultReqFunc();
          setIsModalOpen(true);
          setBookmarkAndSavedResultTab(SAVED_RESULT);
        }
      } else {
        isFirstRender.current = false;
      }
    }, [props.limitReached]);

    useEffect(() => {
      if (props.bookmark.length) {
        setList(props.bookmark);
      }
      let savedResultList = JSON.parse(
        localStorage.getItem('savedResult_' + variables.searchCallVariables.uid) || '[]'
      );
      if (savedResultList && savedResultList.length) {
        setsavedResultList(savedResultList);
      } else {
        setsavedResultList(savedResultList && savedResultList.length);
      }
      document.addEventListener('keydown', handleHideDropdown);
      return () => {
        document.removeEventListener('keydown', handleHideDropdown);
      };
    }, [props.bookmark, props.bookmarkListIconActive]);

    useEffect(() => {
      focusTrap(isModalOpen);
    }, [isModalOpen]);
    /**
     *  Checkbox check change call
     */
    const bookmarkCheck = (index, event) => {
      let bookmarkCheck = -1;
      if (bookmarkAndSavedResultTab === BOOKMARK) {
        bookmarkListing[index].bookmark = event.target.checked;
        bookmarkListing.some((list, index) => {
          if (list.bookmark === true) {
            bookmarkCheck = index;
            return true;
          }
        });
        bookmarkCheck > -1 ? setBookmark(true) : setBookmark(false);
      } else {
        savedResultListing[index].savedResult = event.target.checked;
        savedResultListing.some((list, index) => {
          if (list.savedResult === true) {
            bookmarkCheck = index;
            return true;
          }
        });
        bookmarkCheck > -1 ? setSavedResult(true) : setSavedResult(false);
      }
    };

    const removeBookmarkSubmit = (e) => {
      e.key === EVENT_NAMES.ENTER && setIsModalOpen(false);
    };

    /**
     * Open the bookmark popup
     */
    const openBookmarkPopup = () => {
      bookmarkListing.map((item) => {
        if (item.bookmark) {
          item.bookmark = false;
        }
      });
      savedResultListing &&
        savedResultListing.map((item) => {
          item.savedResult = !item.savedResult;
        });
      let savedResultData = JSON.parse(
        localStorage.getItem('savedResult_' + variables.searchCallVariables.uid)
      );
      if (savedResultData !== null) {
        setsavedResultList(savedResultData);
      } else {
        setsavedResultList([]);
      }
      setBookmarkAndSavedResultTab(BOOKMARK);
      setBookmark(false);
      setSavedResult(false);
      setIsModalOpen(true);

      /** updating bookmarList state every time popup opens **/
      const list = JSON.parse(
        localStorage.getItem('bookmark_searches_' + variables.searchCallVariables.uid) || `[]`
      );
      setList(list);
    };

    /**
     * Remove Bookmarks from local Storage
     */
    const removeBookmark = () => {
      let updatedBookmarkListing;
      if (bookmarkAndSavedResultTab === SAVED_RESULT) {
        const updatedList = savedResultListing.filter((item) => !item.savedResult);
        setsavedResultList(updatedList);
        localStorage.setItem(
          'savedResult_' + variables.searchCallVariables.uid,
          JSON.stringify(updatedList)
        );
        setSavedResult(false);
      } else {
        updatedBookmarkListing = bookmarkListing.filter((item) => !item.bookmark);
        setList(updatedBookmarkListing);
        localStorage.setItem(
          'bookmark_searches_' + variables.searchCallVariables.uid,
          JSON.stringify(updatedBookmarkListing)
        );
        if (updatedBookmarkListing.length === 0) {
          setTimeout(() => {
            document?.getElementById('oopsText')?.focus();
          });
        }
        setBookmark(false);
      }
      props.callBack(
        bookmarkAndSavedResultTab === SAVED_RESULT ? savedResultListing : updatedBookmarkListing
      );
      parentfun();
    };

    /**
     * Search call on bookmark list click
     */
    const searchCall = (index) => {
      let click = JSON.parse(
        localStorage.getItem('bookmark_searches_' + variables.searchCallVariables.uid) || '[]'
      );
      variables.searchSource = 'listBookmark';
      variables.searchCallVariables = click[index].href;
      setWildcardToggle(click[index].toggle);
      setCookies.setSmartFacetOff();
      dispatch(search.start(variables.searchCallVariables));
      if (variables.searchCallVariables.aggregations.length) {
        if (
          !variables.searchCallVariables.aggregations.find(
            (f) => f.type == props.getArrAggregation[0]?.key
          )
        ) {
          variables.allSelected = true;
          variables.activeType = 'all';
        } else {
          variables.allSelected = false;
          variables.activeType = variables.searchCallVariables.aggregations[0]?.filter[0];
        }
      } else {
        variables.allSelected = true;
        variables.activeType = 'all';
      }
      setIsModalOpen(false);
    };

    const savedResultReqFunc = () => {
      let savedResultData = JSON.parse(
        localStorage.getItem('savedResult_' + variables.searchCallVariables.uid) || '[]'
      );
      let queryPassed = {};
      queryPassed.uid = variables.searchCallVariables.uid;
      if (
        [
          SC_IDS.WEB_APP,
          SC_IDS.ZENDESK_GUIDE,
          SC_IDS.ZENDESK_SUPPORT,
          SC_IDS.ZENDESK_SUPPORT_CONSOLE,
          SC_IDS.NICE_CXONE,
          SC_IDS.SHAREPOINT,
          SC_IDS.JOOMLA,
          SC_IDS.HIGHER_LOGIC_VANILLA,
          SC_IDS.FRESHSERVICE
        ].includes(variables.searchClientType)
      ) {
        queryPassed.accessToken = variables.searchCallVariables.accessToken;
      }
      if (
        [
          SC_IDS.ZENDESK_GUIDE,
          SC_IDS.ZENDESK_SUPPORT,
          SC_IDS.ZENDESK_SUPPORT_CONSOLE,
          SC_IDS.FRESHSERVICE
        ].includes(variables.searchClientType)
      ) {
        queryPassed.email = variables.searchCallVariables.email;
      }
      queryPassed.recordIds =
        savedResultData?.map((item) => ({
          indexName: item.csIndexName,
          _id: item.savedResultId,
          objectName: item.objName,
          uniqueField: item.uniqueField
        })) || [];
      (async function () {
        setLoading(true);
        let paramsUrlReq = await savedResultReq(variables.searchClientType, queryPassed);
        try {
          const response = await fetch(paramsUrlReq.url, paramsUrlReq.req);
          if (!response.ok) {
            throw Error(response.statusText);
          }
          const resp = response;
          const data = await resp.json();
          if (data.statusCode === STATUS_CODES.AUTH_SUCCESS.statusCode) {
            savedResultData &&
              savedResultData.forEach((savedResult) => {
                const foundResult = data.result.hits.find(
                  (item) =>
                    savedResult.savedResultId === item._id &&
                    savedResult.csIndexName === item.sourceName &&
                    savedResult.objName === item.objName
                );
                savedResult.savedResultExists = 1;
                if (foundResult) {
                  savedResult.title = foundResult.highlight.TitleToDisplay[0] || foundResult.href;
                  savedResult.savedResultExists = 0;
                }
              });

            setsavedResultList(() => {
              const updatedList = savedResultData;
              return updatedList;
            });
            localStorage.setItem(
              'savedResult_' + variables.searchCallVariables.uid,
              JSON.stringify(savedResultData)
            );
          }
        } catch (err) {
          console.log('Error in facet searching', err);
        }
        setLoading(false);
      })();
    };

    const bookmarkOrSavedResult = (value) => {
      setBookmarkAndSavedResultTab(value);
      if (value === SAVED_RESULT) {
        savedResultReqFunc();
      }
    };

    return (
      <Fragment>
        <div
          className={`${
            variables.isConsoleTypeSC ? 'su__d-none' : ''
          }  su__d-md-none su__mob-search su__text-center su__font-9 su__font-bold su__mobile-child-block su__kh-btn-fw-n su__word-normal-kh`}
        >
          <button
            type="button"
            lang={variables.searchCallVariables.langAttr}
            aria-label={t(StaticStrings.bookmark)}
            role={a11y.ROLES.BTN}
            tabIndex={tabIndexes.tabIndex_0}
            className={`su__mob-search-iner a11y-btn p-0  su__color-black ${
              isModalOpen ? 'su__mob-active' : ''
            } ${!isDeviceMobile ? 'su__sc-loading' : 'su__loading-Dnone'}`}
            onClick={() => openBookmarkPopup()}
          >
            <div className="su__mob-icon">
              <svg
                id="collections_bookmark_black_24dp"
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
              >
                <path id="Path_13" data-name="Path 13" d="M0,0H24V24H0Z" fill="none" />
                <path
                  id="Path_14"
                  data-name="Path 14"
                  d="M17,20H5a1,1,0,0,1-1-1V7A1,1,0,0,0,2,7V20a2.006,2.006,0,0,0,2,2H17a1,1,0,0,0,0-2ZM20,2H8A2.006,2.006,0,0,0,6,4V16a2.006,2.006,0,0,0,2,2H20a2.006,2.006,0,0,0,2-2V4A2.006,2.006,0,0,0,20,2Zm0,10-2.5-1.5L15,12V4h5Z"
                  fill="#434343"
                  className="su__active-path"
                />
              </svg>
            </div>
            <div
              lang={variables.searchCallVariables.langAttr}
              className="su__bookmark_text su__mob-txt su__line-height-n su__active-text su__font-12px"
            >
              {t(StaticStrings.bookmarks)} / {t(StaticStrings.Results)}{' '}
            </div>
          </button>
        </div>
        <div className="su__kh-btn-lh-0">
          {topActionsView && (
            <Tooltip
              text={t(StaticStrings.saved_bookmarks_and_result)}
              position="bottom"
              className="position-relative"
              tipClassName="su__position-absolute  su__zindex-1  su__tooltip-positions "
            >
              <button
                type="button"
                data-trigger-a11y={A11Y_IDS.trap}
                data-name-a11y="saved_bookmarks_and_result"
                lang={variables.searchCallVariables.langAttr}
                role={a11y.ROLES.BTN}
                aria-label={t(StaticStrings.saved_bookmarks_and_result)}
                tabIndex={tabIndexes.tabIndex_0}
                className={`${
                  variables.isConsoleTypeSC && isDeviceMobile
                    ? 'su__zendesk_bookmark_wd_ht'
                    : 'su__d-xs-none su__save-bookmarks'
                } ${
                  bookmarkListing.length || (savedResultListing && savedResultListing.length)
                    ? 'su__active-bookmark-list-icon'
                    : ''
                } su__flex-hcenter su__d-md-flex  a11y-btn p-0 su__pr_14px ${
                  !isDeviceMobile ? 'su__sc-loading' : 'su__loading-Dnone'
                }`}
                onClick={() => openBookmarkPopup()}
              >
                <span
                  className={`su__bookmark-list su__mt-1 su__rtlml-0 su__cursor su__outline-none su__radius-50 bookmark-list-dots `}
                >
                  <Icons
                    className="su__savebookmark-icon"
                    IconName="Savebookmark"
                    width={`${variables.isConsoleTypeSC && isDeviceMobile ? '17' : '24'}`}
                    height={`${variables.isConsoleTypeSC && isDeviceMobile ? '17' : '24'}`}
                    color={IconColors.ListBookmarksInModalIcon}
                  />
                </span>
              </button>
            </Tooltip>
          )}
          {!topActionsView && (
            <Tooltip
              text={t(StaticStrings.saved_bookmarks_and_result)}
              position="bottom"
              className="position-relative"
              tipClassName="su__position-absolute  su__zindex-1   "
            >
              {/* su__tooltip-positions */}
              <button
                type="button"
                data-trigger-a11y={A11Y_IDS.trap}
                data-name-a11y="saved_bookmarks_and_result"
                lang={variables.searchCallVariables.langAttr}
                role={a11y.ROLES.BTN}
                aria-label={t(StaticStrings.saved_bookmarks_and_result)}
                tabIndex={tabIndexes.tabIndex_0}
                className={`${
                  variables.isConsoleTypeSC && isDeviceMobile
                    ? 'su__zendesk_bookmark_wd_ht'
                    : 'su__d-xs-none su__save-bookmarks-zendesk'
                } ${
                  bookmarkListing.length || (savedResultListing && savedResultListing.length)
                    ? 'su__active-bookmark-list-icon-web'
                    : ''
                } su__flex-hcenter su__d-md-flex  a11y-btn p-0 su__pr_14px ${
                  !isDeviceMobile ? 'su__sc-loading' : 'su__loading-Dnone'
                }`}
                onClick={() => openBookmarkPopup()}
              >
                <span
                  className={`su__bookmark-list su__mt-1 su__rtlml-0 su__cursor su__outline-none su__radius-50 bookmark-list-dots `}
                >
                  <Icons
                    className="su__savebookmark-icon"
                    IconName="Savebookmark"
                    width={`${variables.isConsoleTypeSC && isDeviceMobile ? '17' : '24'}`}
                    height={`${variables.isConsoleTypeSC && isDeviceMobile ? '17' : '24'}`}
                    color={IconColors.save_and_list_bookmark_Icon}
                  />
                </span>
              </button>
            </Tooltip>
          )}
        </div>
        {isModalOpen && (
          <form
            onSubmit={removeBookmarkSubmit}
            id={A11Y_IDS.trap}
            role={a11y.ROLES.DIALOG}
            aria-labelledby="dialog1_label"
            aria-modal="true"
          >
            <div className="su__flex-hcenter su__position-fixed su__trbl-0 su__zindex-3 su__px-sm-1">
              <div className="su__modal-inner su__radius su__animate-fadown su__zindex-3 su__shadow-lg su__bg-white su__radius-1 su__no_bookmark_min_height su__pos_relative">
                <div className="su__bookmark-popup-title su__pl_14px su__pr_14px su__pb_14px su__pt_14px su__py-2 su__flex-vcenter">
                  <h2
                    id={'dialog1_label'}
                    lang={variables.searchCallVariables.langAttr}
                    className="su__font-17 su__text-white su__f-medium su__flex-1 su__position-relative su__my-0 su__font_save_bookmark su__text-blue "
                  >
                    {t(StaticStrings.saved_bookmarks_and_result)}
                  </h2>
                  <button
                    type="button"
                    lang={variables.searchCallVariables.langAttr}
                    className="a11y-btn p-0 su__close_bookmark"
                    aria-label={t(StaticStrings.close_popup)}
                    role={a11y.ROLES.BTN}
                    tabIndex={tabIndexes.tabIndex_0}
                    onClick={() => setIsModalOpen(false)}
                  >
                    {' '}
                    <Icons
                      className="su__close-icon su__cursor"
                      IconName="Close"
                      width="12"
                      height="12"
                      color={IconColors.SaveBookmarksCrossIcon}
                    />
                  </button>
                </div>
                <div role={a11y.ROLES.ALERT} className="su__bookmark-inner su__font-14">
                  <div className="su__w-100 su__border-b su__mb-10">
                    <button
                      lang={variables.searchCallVariables.langAttr}
                      aria-label={t(StaticStrings.bookmarks)}
                      role={a11y.ROLES.BTN}
                      className={
                        bookmarkAndSavedResultTab === BOOKMARK
                          ? 'su__tabs su__tabs_bookmarkList su__cursor su__border-none su__bg-white su__ml-3 su__BookmarkActive-tab '
                          : 'su__tabs su__tabs_bookmarkList su__cursor su__border-none su__bg-white su__ml-2'
                      }
                      type="button"
                      onClick={() => bookmarkOrSavedResult(BOOKMARK)}
                    >
                      {t(StaticStrings.bookmarks)}
                    </button>
                    <button
                      data-trigger-a11y="saved_results"
                      data-name-a11y="tab-saved_results"
                      lang={variables.searchCallVariables.langAttr}
                      aria-label={t(StaticStrings.Results)}
                      role={a11y.ROLES.BTN}
                      className={
                        bookmarkAndSavedResultTab === SAVED_RESULT
                          ? 'su__tabs su__tabs_bookmarkList su__cursor su__border-none su__bg-white su__ml-2 su__BookmarkActive-tab '
                          : 'su__tabs su__tabs_bookmarkList su__cursor su__border-none su__bg-white'
                      }
                      type="button"
                      onClick={() => bookmarkOrSavedResult(SAVED_RESULT)}
                    >
                      {t(StaticStrings.Results)}
                    </button>
                  </div>
                  <div
                    className={` ${bookmarkAndSavedResultTab === SAVED_RESULT ? '' : 'su__d-none'}`}
                  >
                    {!loading && savedResultListing && savedResultListing.length >= 50 && (
                      <div
                        className="su__savedResultLimitReached su__mb-1"
                        lang={variables.searchCallVariables.langAttr}
                        aria-label={t(
                          StaticStrings.Limit_Reached_Please_delete_to_save_new_results
                        )}
                      >
                        {' '}
                        {t(StaticStrings.Limit_Reached_Please_delete_to_save_new_results)}
                      </div>
                    )}
                  </div>
                  <div className="su__p-0 su__bookmark-ul su__minscroller su__position-relative">
                    {bookmarkAndSavedResultTab === BOOKMARK && (
                      <div
                        className={`${bookmarkAndSavedResultTab === BOOKMARK ? '' : 'su__d-none'}`}
                      >
                        {bookmarkListing.length !== 0 ? (
                          React.Children.toArray(
                            bookmarkListing.map((item, index) => (
                              // eslint-disable-next-line react/jsx-key
                              <div key={uuid()} className="su__flex-vcenter su__px-2 su__border-b">
                                <div className="su__flex-1 su__cursor su__w-75">
                                  <button
                                    type="button"
                                    tabIndex={tabIndexes.tabIndex_0}
                                    role={a11y.ROLES.LNK}
                                    className=" su__bookmark_SavedResult_RTL su__text-decoration su__d-block su__p-2 su__color_black su__font-regular su__text-truncate a11y-btn su__width-100 su__text-left"
                                    onClick={() => searchCall(index)}
                                  >
                                    <span className="su__w-100 su__font-14 ">{item.title}</span>
                                  </button>
                                </div>
                                <div className="su__ml-3 su__w-savebook su__rtlmr-3 su__rtlml-0 su__outline-none su__cursor">
                                  <div className="su__filter-checkbox">
                                    <div className="su__filter-toggle su__border-color su__font-18 su__position-relative">
                                      <input
                                        tabIndex={tabIndexes.tabIndex_0}
                                        aria-label={item.title}
                                        id={item.title}
                                        className="su__toggle-input  su__position-absolute su__cursor su__dimensions-check-box su__opacity-0"
                                        type="checkbox"
                                        key={item.title}
                                        defaultChecked={item.bookmark === true}
                                        name={item.title}
                                        value={item.title}
                                        onChange={(e) => bookmarkCheck(index, e)}
                                      />
                                      <label
                                        htmlFor={item.title}
                                        className="su__filter-label su__d-inline-block  "
                                      ></label>
                                      <span className="su__toggle-label su__d-inline-flex su__cursor">
                                        <span className="su__toggle__text" />
                                        <span className="su__nested__text su__loading su__d-none su__position-absolute" />
                                      </span>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            ))
                          )
                        ) : (
                          <div
                            id={'oopsText'}
                            role="status"
                            tabIndex={oopsIndex}
                            onFocus={() => resetTrap()}
                            onBlur={() => {
                              setOopsIndex(tabIndexes.tabIndex_minus_1);
                              resetTrap(true);
                            }}
                            className="su__bookmark-inner su__overflow_hidden"
                          >
                            <div className="u__w-100 su__text-center su__no_bookmark_svg">
                              <SVGS.emptyBookmarks />
                            </div>
                            {/* <div
                            lang={variables.searchCallVariables.langAttr}
                            className="su__w-100 su__font-32 su__text-center su__text-black su__font-bold su__line-height-n su__mb-2 su__font_bookmark"
                          >
                            {' '}
                            {t(StaticStrings.oops)}!
                          </div> */}
                            <div
                              lang={variables.searchCallVariables.langAttr}
                              className="su__w-100 su__font-14 su__text-center su__color_black  su__f-normal  su__font_bookmark_text su__line-height_24px su__no_bookmark_padding su__color-black"
                            >
                              {t(StaticStrings.create_your_first_bookmark)}
                            </div>
                          </div>
                        )}
                      </div>
                    )}

                    {loading && bookmarkAndSavedResultTab === 'savedResult' && (
                      <div className="loading su__centre-align-dots">
                        <div className="dot"></div>
                        <div className="dot"></div>
                        <div className="dot"></div>
                        <div className="dot"></div>
                        <div className="dot"></div>
                      </div>
                    )}
                    {bookmarkAndSavedResultTab === 'savedResult' && (
                      <div
                        id="saved_results"
                        className={` ${
                          bookmarkAndSavedResultTab === 'savedResult' ? '' : 'su__d-none'
                        }`}
                      >
                        {!loading &&
                          (savedResultListing && savedResultListing.length !== 0 ? (
                            React.Children.toArray(
                              savedResultListing.map((item, index) => (
                                // eslint-disable-next-line react/jsx-key
                                <div
                                  key={uuid()}
                                  className="su__flex-vcenter su__px-2 su__mb-1 su__border-b"
                                >
                                  <div className="su__flex-1  su__w-75">
                                    <Tooltip
                                      savedTooltip={item.savedResultExists}
                                      text={
                                        item.savedResultExists === 0
                                          ? item.title
                                          : 'This result is not available anymore.'
                                      }
                                      position="bottom"
                                      className="position-relative"
                                    >
                                      <div
                                        className={`${
                                          item.savedResultExists === 1
                                            ? 'su__bookmark_SavedResult_opacity'
                                            : ''
                                        } su__text-decoration su__bookmark_SavedResult_RTL su__d-block su__p-2 su__text-black su__font-regular su__text-truncate a11y-btn su__width-100 su__text-left`}
                                      >
                                        {item.savedResultExists === 0 && (
                                          <a
                                            href={item.href}
                                            target="_blank"
                                            className="su__cursor su__w-100 su__font-14  su__bookmarkSavedResultText"
                                            rel="noreferrer"
                                          >
                                            {parseEntities(item.title)}
                                          </a>
                                        )}
                                        {item.savedResultExists === 1 && (
                                          <a className="su__w-100 su__font-14  su__deletedSavedResultText">
                                            {parseEntities(item.title)}
                                          </a>
                                        )}
                                      </div>
                                    </Tooltip>
                                  </div>
                                  <div className="su__ml-3 su__w-savebook su__rtlmr-3 su__rtlml-0 su__outline-none su__cursor">
                                    <div className="su__filter-checkbox ">
                                      <div className="su__filter-toggle su__border-color su__font-18 su__position-relative ">
                                        <input
                                          aria-label={item.title}
                                          id={item.title}
                                          className=" su__toggle-input su__position-absolute su__cursor su__dimensions-check-box"
                                          type="checkbox"
                                          name={item.title}
                                          value={item.title}
                                          key={item.title}
                                          defaultChecked={item.savedResult === true}
                                          onChange={(e) => bookmarkCheck(index, e)}
                                        />
                                        <label
                                          htmlFor={item.title}
                                          className="su__filter-label su__d-inline-block  "
                                        ></label>
                                        <span className="su__toggle-label su__d-inline-flex su__cursor">
                                          <span className="su__toggle__text" />
                                          <span className="su__nested__text su__loading su__d-none su__position-absolute" />
                                        </span>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              ))
                            )
                          ) : (
                            <div
                              id={'oopsText'}
                              role="status"
                              tabIndex={oopsIndex}
                              onFocus={() => resetTrap()}
                              onBlur={() => {
                                setOopsIndex(tabIndexes.tabIndex_minus_1);
                                resetTrap(true);
                              }}
                              className="su__bookmark-inner su__overflow_hidden"
                            >
                              <div className="su__w-100 su__text-center su__no_bookmark_svg">
                                <SVGS.emptyBookmarks />
                              </div>

                              <div
                                lang={variables.searchCallVariables.langAttr}
                                className="su__w-100 su__font-14 su__text-center su__color-black su__f-normal su__line-height-n su__font_bookmark_text su__line-height_24px su__no_bookmark_padding"
                              >
                                {t(StaticStrings.save_your_first_result)}
                              </div>
                            </div>
                          ))}
                      </div>
                    )}
                  </div>
                </div>
                {bookmarkAndSavedResultTab === BOOKMARK && (
                  <div className={`${bookmarkAndSavedResultTab === BOOKMARK ? '' : 'su__d-none'}`}>
                    {bookmarkListing.length !== 0 ? (
                      <div className="su__bookmark-btn su__flex-vcenter su__justify-content-between su__p-3  su__bottom-0 su__width-100 su__padding_top_28 su__word-normal-kh">
                        <button
                          lang={variables.searchCallVariables.langAttr}
                          aria-label={t(StaticStrings.remove)}
                          type="button"
                          className="su__btn su__bg-blue-grd su__py-3 su__w-100 su__btn su__p-2 su__bg-gray-40 su__radius-1 su__bookinput-h su__bookmark-active su__border-none"
                          disabled={!bookmarkSelect}
                          onClick={removeBookmark}
                        >
                          {t(StaticStrings.remove)}
                        </button>
                      </div>
                    ) : null}
                  </div>
                )}
                {bookmarkAndSavedResultTab === 'savedResult' && (
                  <div
                    className={`${bookmarkAndSavedResultTab === 'savedResult' ? '' : 'su__d-none'}`}
                  >
                    {!loading &&
                      (savedResultListing && savedResultListing.length !== 0 ? (
                        <div className="su__bookmark-btn su__flex-vcenter su__justify-content-between  su__width-100 su__bottom-0 su__p_14px su__mt-3">
                          <button
                            lang={variables.searchCallVariables.langAttr}
                            aria-label={t(StaticStrings.remove)}
                            type="button"
                            className="su__btn su__bg-blue-grd su__py-3 su__w-100 su__btn su__p-2 su__bg-gray-40 su__radius-1 su__bookinput-h su__bookmark-active"
                            disabled={!savedResultSelect}
                            onClick={removeBookmark}
                          >
                            {t(StaticStrings.remove)}
                          </button>
                        </div>
                      ) : null)}
                  </div>
                )}
              </div>
              <div className="su__overlay su__zindex-1" onClick={() => setIsModalOpen(false)} />
            </div>
          </form>
        )}
      </Fragment>
    );
  } catch (e) {
    console.log('Error in List bookmark Component', e);
    const { t } = useTranslation();
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [bookmarkListing, setList] = useState(
      JSON.parse(
        localStorage.getItem('bookmark_searches_' + variables.searchCallVariables.uid) || '[]'
      )
    );
    useEffect(() => {
      if (props.bookmark.length) {
        setList(props.bookmark);
      }
    }, [props.bookmark]);
    const showPopUp = () => {
      setIsModalOpen(true);
      setTimeout(() => {
        setIsModalOpen(false);
      }, 1500);
    };
    return (
      <Fragment>
        <div className="su__d-md-none su__d-xs-block su__col su__mob-search su__text-center su__font-9 su__font-bold">
          <div
            className={`su__mob-search-iner ${isModalOpen ? 'su__mob-active' : ''}`}
            onClick={() => showPopUp()}
          >
            <div
              className={`su__mob-icon ${
                bookmarkListing.length ? 'su__bookmarkListIconActive' : ''
              }`}
            >
              <Icons
                className="su__savebokmark-icon su__bg-white-hover-50"
                IconName="Savebookmark"
                width="24"
                height="24"
                color="#333"
                IconClass="su__active-path"
              />
            </div>
            <div
              lang={variables.searchCallVariables.langAttr}
              className="su__mob-txt su__line-height-n su__active-text"
            >
              {t(StaticStrings.bookmark)}
            </div>
          </div>
        </div>
        <div
          className="su__save-bookmarks su__flex-hcenter su__d-md-flex su__d-xs-none"
          onClick={() => showPopUp()}
        >
          <span
            lang={variables.searchCallVariables.langAttr}
            tabIndex={tabIndexes.tabIndex_0}
            className={`su__bookmark-list su__mt-1 su__ml-2 su__rtlml-0 su__cursor su__outline-none su__bg-white-hover-50 su__radius-50 bookmark-list-dots ${
              bookmarkListing.length ? 'mob_bookmark_list-icon' : ''
            } su__sc-loading`}
          >
            <Tooltip
              text={t(StaticStrings.show_bookmark)}
              position="top"
              className="position-relative"
            >
              <Icons
                className="su__savebookmark-icon su__bg-white-hover-50"
                IconName="Savebookmark"
                width="24"
                height="24"
                color={
                  bookmarkListing.length
                    ? IconColors.ListBookMarksNotEmptyIcon
                    : IconColors.ListBookmarksEmptyIcon
                }
              />
            </Tooltip>
          </span>
        </div>
        {isModalOpen && <UnderConstruction component="Show Bookmarks" />}
      </Fragment>
    );
  }
};
export default ListBookmarks;
