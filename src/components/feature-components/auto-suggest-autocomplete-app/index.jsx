import React, { useEffect, useState } from 'react';
import variables from '../../../redux/variables';
import AutoLearningSuggestion from 'components/feature-components/auto-suggest-auto-learning/index.jsx';
import { tabIndexes } from '../../../constants/a11y';
import StaticStrings from 'StaticStrings';

const Suggest = (props) => {
  try {
    let autocompleteResult = props.autoCompleteResult;
    let resultOpenNewTab = props.resultOpenNewTab;
    let changeSearchDataOnArrowKey = props.changeSearchDataOnArrowKey;
    let tempData = props.tempData;
    let currentSearchString = props.currentSearchString;
    const [highlightCondition, setHighlightCondition] = useState(currentSearchString);
    useEffect(() => {
      if (!changeSearchDataOnArrowKey) {
        setHighlightCondition(currentSearchString);
        return;
      }
      const selectedData = tempData[changeSearchDataOnArrowKey[0]];
      if (selectedData?.Id && selectedData?.highlight?.TitleToDisplayString) {
        setHighlightCondition(selectedData.Id || selectedData.href);
      } else {
        setHighlightCondition(currentSearchString);
      }
    }, [changeSearchDataOnArrowKey, tempData]);
    return (
      <div
        className={`su__autocomplete-suggestion su__w-100 su__bg-white su__sm-shadow su__position-absolute su__zindex-2 su__mobile_resultbox su__hideScroller su__mt-6px`}
      >
        <div
          id="my_div"
          className={`su__suggestions-box su__suggestions-box-height su__minscroller`}
        >
          <div className="auto-suggestion su__autosuggestion_container">
            {React.Children.map(props.children, (child) => {
              if (!child) return false;
              else if (child.props.position == 'aboveAllResult') {
                return React.cloneElement(child, { autocompleteResult, ...props }, null);
              }
            })}
            {autocompleteResult.result &&
            variables.autocompleteCallVariables.searchString &&
            autocompleteResult.result.hits.length != 0 ? (
              <div className="su__recent_search_text su__mr-rtl-20">
                {StaticStrings.SUGGESTED_RESULTS}
              </div>
            ) : null}
            {autocompleteResult.result &&
              variables.autocompleteCallVariables.searchString &&
              autocompleteResult.result.hits.length != 0 &&
              React.Children.toArray(
                autocompleteResult.result.hits.map((item, index) => (
                  <div
                    key={'autoCompleteResult' + item?.href}
                    data-key={index}
                    tabIndex={tabIndexes.tabIndex_minus_1}
                    className={`su__suggestions-list su__bg-gray-hover su__flex-vcenter  su__text-black  su__fontsize-14 su__mb-1 ${
                      highlightCondition === (item.Id || item.href) &&
                      currentSearchString === (item.highlight.TitleToDisplayString[0] || item.href)
                        ? 'su__highlight_result'
                        : null
                    }`}
                  >
                    {React.Children.map(props.children, (child) => {
                      if (!child) return false;
                      else if (child.props.position == 'icon') {
                        return React.cloneElement(
                          child,
                          { item, key: 'autoCompleteResult' + item?.href + child?.props?.position },
                          null
                        );
                      }
                    })}
                    <div className="su__text-truncate su__d-flex su__flex-column su__autocomplete_alignment su__flex-column">
                      {React.Children.map(props.children, (child) => {
                        if (!child) return false;
                        else if (child.props.position == 'result') {
                          return React.cloneElement(
                            child,
                            {
                              item,
                              index,
                              resultOpenNewTab,
                              key: 'autoCompleteResult' + item?.href + child?.props?.position
                            },
                            null
                          );
                        }
                      })}
                    </div>
                  </div>
                ))
              )}
            {React.Children.map(props.children, (child) => {
              if (!child) return false;
              else if (child.props.position == 'belowAllResult') {
                return React.cloneElement(child, { autocompleteResult }, null);
              }
            })}
          </div>
          {autocompleteResult &&
            autocompleteResult.smartAggregations &&
            autocompleteResult.smartAggregations.length && (
              <div
                className={`su__product-sugt-row su__minscroller su__product-length-${
                  autocompleteResult &&
                  autocompleteResult.smartAggregations &&
                  autocompleteResult.smartAggregations.length
                }`}
                style={{
                  position:
                    autocompleteResult.result && autocompleteResult.result.hits.length
                      ? 'absolute'
                      : 'inherit'
                }}
              >
                <AutoLearningSuggestion autocompleteResult={autocompleteResult} />
              </div>
            )}
        </div>
      </div>
    );
  } catch (e) {
    console.log('Error in auto-suggest component', e);
    return <div></div>;
  }
};

export default Suggest;
