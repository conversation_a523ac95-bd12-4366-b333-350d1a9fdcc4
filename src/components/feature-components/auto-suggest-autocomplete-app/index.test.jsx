/* eslint-disable react/no-unknown-property */
/* global jest, it, expect, describe */
import React from 'react';
import { mount } from 'enzyme';
import Suggest from './';

// Mock child components and props
jest.mock('components/feature-components/auto-suggest-auto-learning/index.jsx', () =>
  jest.fn(() => <div className="mock-auto-learning-suggestion" />)
);

jest.mock('../../../redux/variables', () => require('__mocks__/variables'));

/* MOCK DATA */
const TestComponent = () => <></>;
const ErrorComponent = () => {
  throw new Error('Test error');
};

const autocompleteResult = {
  result: {
    hits: [
      { highlight: { TitleToDisplayString: ['Electronics'] }, href: 'mockHref1' },
      { highlight: { TitleToDisplayString: ['Books'] }, href: 'mockHref2' }
    ]
  },
  smartAggregations: []
};

const mockSetState = jest.fn();

jest.mock('react', () => ({
  ...jest.requireActual('react'),
  useState: jest.fn().mockImplementation(() => ['test', mockSetState])
}));

const props = {
  currentSearchString: 'test',
  autoCompleteResult: autocompleteResult,
  resultOpenNewTab: true
};

const positions = {
  aboveAllResult: {
    autocompleteResult,
    ...props,
    children: <TestComponent position={'aboveAllResult'} />
  },
  icon: {
    item: { highlight: { TitleToDisplayString: ['Electronics'] }, href: 'mockHref1' },
    key: 'autoCompleteResultmockHref1icon'
  },
  result: {
    item: { highlight: { TitleToDisplayString: ['Books'] }, href: 'mockHref2' },
    index: 1,
    resultOpenNewTab: props.resultOpenNewTab,
    key: 'autoCompleteResultmockHref2result'
  },
  belowAllResult: {
    autocompleteResult
  }
};

describe('Auto Suggest AutoComplete component', () => {
  it('renders without crashing', () => {
    const wrapper = mount(<Suggest {...props} />);
    expect(wrapper.exists()).toBe(true);
  });

  it('should create child components for position props', () => {
    Object.keys(positions).forEach((position) => {
      const spy = jest.spyOn(React, 'cloneElement');
      mount(
        <Suggest {...props}>
          <TestComponent position={position} />
        </Suggest>
      );
      expect(spy).toHaveBeenCalledWith(
        <TestComponent position={position} />,
        positions[position],
        null
      );
      spy.mockRestore();
    });
  });

  it('should update highlight condition based on id/href if there is a change in selectedData', () => {
    const mockId = 'mock_id';
    props.changeSearchDataOnArrowKey = ['mockData'];
    props.tempData = {
      mockData: {
        Id: mockId,
        highlight: { TitleToDisplayString: ['Books'] },
        href: 'mockHref2'
      }
    };
    let wrapper = mount(
      <Suggest {...props}>
        <TestComponent />
      </Suggest>
    );
    wrapper.update();
    expect(mockSetState).toHaveBeenCalledTimes(1);
    expect(mockSetState).toHaveBeenCalledWith(mockId);
    mockSetState.mockClear();
    props.tempData = {
      mockData: {
        href: 'mockHref2'
      }
    };
    wrapper = mount(
      <Suggest {...props}>
        <TestComponent />
      </Suggest>
    );
    wrapper.update();
    expect(mockSetState).toHaveBeenCalledTimes(1);
    expect(mockSetState).toHaveBeenCalledWith(props.currentSearchString);
    mockSetState.mockClear();
  });

  it('catches component error and renders empty div', () => {
    const consoleSpy = jest.spyOn(console, 'log').mockImplementation(() => {});

    const wrapper = mount(
      <Suggest>
        <ErrorComponent />
      </Suggest>
    );

    expect(wrapper.html()).toBe('<div></div>');
    expect(consoleSpy).toHaveBeenCalledWith('Error in auto-suggest component', expect.any(Error));
    consoleSpy.mockRestore();
  });
});
