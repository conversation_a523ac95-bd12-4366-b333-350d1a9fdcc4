/* global it, expect, describe */
import React from 'react';
import { shallow } from 'enzyme';
import Tooltip from './';
import { EVENT_NAMES } from 'constants/constants';

describe('Tooltip Component', () => {
  it('renders children correctly', () => {
    const wrapper = shallow(
      <Tooltip text="Tooltip text" savedTooltip={2}>
        <button>Hover me</button>
      </Tooltip>
    );
    expect(wrapper.find('button').text()).toBe('Hover me');
  });

  it('shows tooltip on mouse enter', () => {
    const wrapper = shallow(
      <Tooltip text="Tooltip text" savedTooltip={2}>
        <button>Hover me</button>
      </Tooltip>
    );
    wrapper.find('[data-testid="tooltip"]').simulate('mouseEnter');
    expect(wrapper.find('[data-testid="tooltip-content"]').exists()).toBe(true);
  });

  it('hides tooltip on mouse leave', () => {
    const wrapper = shallow(
      <Tooltip text="Tooltip text" savedTooltip={2}>
        <button>Hover me</button>
      </Tooltip>
    );
    wrapper.find('[data-testid="tooltip"]').simulate('mouseEnter');
    expect(wrapper.find('[data-testid="tooltip-content"]').exists()).toBe(true);
    wrapper.find('[data-testid="tooltip"]').simulate('mouseLeave');
    expect(wrapper.find('[data-testid="tooltip-content"]').exists()).toBe(false);
  });

  it('toggles tooltip on key press', () => {
    const wrapper = shallow(
      <Tooltip text="Tooltip text" savedTooltip={2}>
        <button>Hover me</button>
      </Tooltip>
    );
    wrapper.find('[data-testid="tooltip"]').simulate('keypress');
    expect(wrapper.find('[data-testid="tooltip-content"]').exists()).toBe(true);
    wrapper.find('[data-testid="tooltip"]').simulate('keypress');
    expect(wrapper.find('[data-testid="tooltip-content"]').exists()).toBe(false);
  });

  it('hides tooltip on escape key press', () => {
    const wrapper = shallow(
      <Tooltip text="Tooltip text" savedTooltip={2}>
        <button>Hover me</button>
      </Tooltip>
    );
    wrapper.find('[data-testid="tooltip"]').simulate('mouseEnter');
    expect(wrapper.find('[data-testid="tooltip-content"]').exists()).toBe(true);

    // Simulate escape key press on the document
    const event = new KeyboardEvent('keydown', {
      key: EVENT_NAMES.ESCAPE,
      code: EVENT_NAMES.ESCAPE
    });
    document.dispatchEvent(event);

    // Wait for the effect to take place
    setTimeout(() => {
      expect(wrapper.find('[data-testid="tooltip-content"]').exists()).toBe(false);
    }, 0);
  });
});
