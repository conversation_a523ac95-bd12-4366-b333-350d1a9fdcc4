import React, { useEffect, useRef, useState } from 'react';
import PropTypes, { any, node, string } from 'prop-types';
import { EVENT_NAMES } from 'constants/constants';

const propTypes = {
  text: PropTypes.string.isRequired,
  position: PropTypes.string,
  children: PropTypes.node.isRequired,
  color: PropTypes.string,
  tipClassName: PropTypes.string,
  tipMinWidth: PropTypes.string
};

const Tooltip = ({
  text,
  position,
  savedTooltip,
  children,
  tipClassName = '',
  tipMinWidth = '',
  color
}) => {
  const node = useRef();
  const parseEntities = (txt) => new DOMParser().parseFromString(txt, 'text/html').body.innerText;
  const [isVisible, setState] = useState(false);
  const handleClick = () => {
    setState(false);
  };

  useEffect(() => {
    // add when mounted
    document.addEventListener('mousedown', handleClick);
    // return function to be called when unmounted
    return () => {
      document.removeEventListener('mousedown', handleClick);
    };
  }, []);
  const showTooltip = (e) => {
    if (e) {
      if (e.code === EVENT_NAMES.ESCAPE) {
        setState(false);
      }
    }
  };
  const hideTooltip = (e) => {
    if (e) {
      setState(false);
    }
  };
  useEffect(() => {
    document.addEventListener('keydown', showTooltip);
    return () => {
      document.removeEventListener('keydown', showTooltip);
    };
  });

  return (
    <div
      className="su__position-relative su__tooltip-container"
      style={{ color: color }}
      data-testid="tooltip"
      ref={node}
      onKeyPress={() => setState(!isVisible)}
      onMouseEnter={() => setState(!isVisible)}
      onMouseLeave={() => setState(false)}
      onFocus={() => setState((prevState) => !prevState)}
      onBlur={(event) => hideTooltip(event)}
    >
      <div
        data-testid="tooltip-placeholder"
        className="su__d-flex su__tooltipIconsOutline su__sc-loading"
      >
        {children}
      </div>
      {isVisible && (
        <div
          className={`su__tooltip su__d-inline-block  ${tipClassName}  ${
            savedTooltip === 0 ? 'su__savedResultTooltip su__savedResultTooltipArabic' : ''
          }  ${
            savedTooltip === 1 ? 'savedResultDontExist su__savedResultTooltipArabic' : ''
          } su__line-height-n su__tooltip-${
            savedTooltip != 0 && savedTooltip != 1 ? position : ''
          } `}
          data-testid="tooltip-content"
        >
          <div
            className={`su__flex-vcenter ${
              savedTooltip === 0 || savedTooltip === 1
                ? ' '
                : 'su__tooltip_wrap su__text-center su__color_black'
            }  ${tipMinWidth}`}
          >
            {parseEntities(text)}
          </div>
        </div>
      )}
    </div>
  );
};

Tooltip.propTypes = {
  position: string,
  savedTooltip: any,
  children: node,
  color: string
};

Tooltip.defaultProps = {
  position: 'top',
  children: <></>,
  color: 'black'
};

Tooltip.propTypes = propTypes;

export default Tooltip;
