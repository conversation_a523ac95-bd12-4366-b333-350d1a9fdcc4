/* global jest, it, expect, describe, beforeEach, afterEach */
import React from 'react';
import { shallow } from 'enzyme';
import StickyFacets from './';

// Mock the dependencies
jest.mock('react-redux', () => ({
  useSelector: jest.fn()
}));

jest.mock('react-i18next', () => ({
  useTranslation: () => ({ t: (key) => key })
}));

jest.mock('../../../redux/variables', () => ({
  searchCallVariables: {
    langAttr: 'en'
  },
  activeType: 'all'
}));

jest.mock('components/feature-components/sticky-facets-child/index.jsx', () => {
  return function DummyFacets() {
    return <div>Mocked Facets</div>;
  };
});

describe('StickyFacets Component', () => {
  let useSelector;

  beforeEach(() => {
    useSelector = require('react-redux').useSelector;
    useSelector.mockImplementation((selector) =>
      selector({
        searchResult: {
          aggregationsArray: [
            {
              key: 'content_type',
              label: 'Content Type',
              order: 1,
              values: [
                { selected: true, Contentname: 'Articles', displayName: 'Articles' },
                { selected: false, Contentname: 'Videos', displayName: 'Videos' }
              ]
            }
          ]
        }
      })
    );
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders without crashing', () => {
    const wrapper = shallow(<StickyFacets />);
    expect(wrapper.exists()).toBe(true);
  });

  // it('renders selected facets', () => {
  //   const wrapper = shallow(<StickyFacets />);
  //   expect(wrapper.find('.su__sticky__top').length).toBe(1);
  //   expect(wrapper.find('.su__sticky__head').text()).toBe('Content Type:');
  // });

  it('does not render show more/less button when facets are 2 or less', () => {
    const wrapper = shallow(<StickyFacets />);
    expect(wrapper.find('button').length).toBe(0);
  });
});
