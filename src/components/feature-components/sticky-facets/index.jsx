import React, { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import variables from '../../../redux/variables';
import { useTranslation } from 'react-i18next';
import Facets from 'components/feature-components/sticky-facets-child/index.jsx';
import StaticStrings from 'StaticStrings';
import { a11y } from '../../../constants/a11y';
import { v4 as uuid } from 'uuid';
import { useDevice } from 'function-library/hooks';
import { AGGR_KEYS } from 'constants/constants';

const StickyFacets = (props) => {
  try {
    const { t } = useTranslation();
    const searchResult = useSelector((state) => state.searchResult);
    let clientFilters = searchResult.aggregationsArray;
    let selectedStickyFilter = [];
    let [showExpandBtn, setExpandBtn] = useState(false);
    let [expandBox, setExpandBox] = useState(false);
    const { isDeviceMobile } = useDevice();
    const checkChildArray = (tempValues, childArray, name) => {
      childArray.forEach(function (y) {
        if (y.selected) {
          y.sticky_name = name + ' > ' + (y.displayName || y.Contentname);
          tempValues.push(y);
        }
        if (y.childArray) {
          checkChildArray(
            tempValues,
            y.childArray,
            name + ' > ' + (y.displayName || y.Contentname)
          );
        }
      });
    };

    if (clientFilters) {
      let tempAggregations = [];
      tempAggregations = JSON.parse(JSON.stringify(clientFilters));
      selectedStickyFilter = tempAggregations.filter(function (x) {
        x.tempValues = [];
        if (x.order === 0 && x.key == AGGR_KEYS._INDEX && variables.activeType !== 'all') {
          x.sticky_label = 'Tab';
          x.values.map(function (o) {
            if (o.Contentname == variables.activeType || (o.merged && o.selected)) {
              o.sticky_name = o.displayName;
              x.tempValues.push(o);
            }
            if (o.childArray && (x.order != 0 || !o.merged || (o.merged && o.showChild != 0))) {
              checkChildArray(x.tempValues, o.childArray, o.displayName || o.Contentname);
            }
          });
        }
        if (x.key != AGGR_KEYS._INDEX) {
          x.values.map(function (f) {
            if (f.selected) x.tempValues.push(f);
            if (f.childArray && (x.order != 0 || !f.merged || (f.merged && f.showChild != 0))) {
              checkChildArray(x.tempValues, f.childArray, f.displayName || f.Contentname);
            }
          });
        }
        if (x.tempValues.length) {
          x.values = JSON.parse(JSON.stringify(x.tempValues || []));
          delete x.tempValues;
          return x;
        }
      });
    }

    /**
     *
     * @param {sticky Facet} facet
     */
    const showMore = () => {
      if (expandBox) {
        setExpandBox(false);
      } else {
        setExpandBox(true);
      }
    };

    let advanceSearchAsStickyFacet = (key, label, value) => {
      return {
        key: key,
        label: label,
        values: [{ selected: true, Contentname: value }]
      };
    };

    let { exactPhrase, withOneOrMore, withoutTheWords } = variables.searchCallVariables;
    if (exactPhrase)
      selectedStickyFilter.unshift(
        advanceSearchAsStickyFacet('exactPhrase', 'With the exact phrase', exactPhrase)
      );
    if (withOneOrMore)
      selectedStickyFilter.unshift(
        advanceSearchAsStickyFacet('withOneOrMore', 'With one or more words', withOneOrMore)
      );
    if (withoutTheWords)
      selectedStickyFilter.unshift(
        advanceSearchAsStickyFacet('withoutTheWords', 'Without the words', withoutTheWords)
      );
    if (sessionStorage.getItem('toggleWildcardSearch'))
      selectedStickyFilter.unshift(advanceSearchAsStickyFacet('wildCard', '', 'Wildcard Search'));
    useEffect(() => {
      const contentDiv = document.getElementsByClassName('su__sticky_facet-container');
      if (contentDiv.length && contentDiv[0] && contentDiv[0]?.scrollHeight > 100) {
        setExpandBtn(true);
      } else {
        setExpandBtn(false);
      }
    }, [searchResult]);

    return (
      <>
        {(selectedStickyFilter.length && selectedStickyFilter[0]?.key === 'wildCard') ||
        (!props.standOut &&
          selectedStickyFilter.length >= 1 &&
          selectedStickyFilter[0]?.key !== 'wildCard') ? (
          <div
            className={`su__sticky_facet-container su__clear-filter-row su__flex-vcenter su__flex-wrap su__pb-sm-1 su__loading-view su__pb-sm-0 su__mt-5px su__mb-10 ${
              props.standOut ? 'su__w-100' : ''
            }  ${expandBox ? 'su__h-auto' : 'su__stickyfacet_height'} ${
              isDeviceMobile ? 'su__mx-12px' : null
            } ${
              showExpandBtn
                ? !isDeviceMobile
                  ? 'su__mb-20 su__pb-20'
                  : 'su__mb-10 su__pb-20'
                : null
            }`}
          >
            {selectedStickyFilter.length
              ? selectedStickyFilter
                  .filter((item) =>
                    props.standOut ? item.key === 'wildCard' : item.key !== 'wildCard'
                  )
                  .map((item, index) => (
                    <React.Fragment key={uuid()}>
                      {item.key !== 'wildCard' && (
                        <div
                          lang={variables.searchCallVariables.langAttr}
                          className="su__sticky__head su__font-11 su__royal-blue su__f-normal su__mb-2 su__sc-loading"
                        >
                          {item.label}
                          {':'}
                        </div>
                      )}
                      <Facets
                        item={item}
                        index={index}
                        standOut={props.standOut}
                        isButtonActive={props.isButtonActive}
                        setIsButtonActive={props.setIsButtonActive}
                        bgColor={props.bgColor}
                        borderColor={props.borderColor}
                      />
                    </React.Fragment>
                  ))
              : null}
            {!props.standOut && showExpandBtn ? (
              <div
                className={`su__width-100 ${
                  !expandBox ? 'su__facet_top_blur' : ''
                }  su__sticky_expend_btn`}
              >
                <button
                  type="button"
                  lang={variables.searchCallVariables.langAttr}
                  aria-expanded={Boolean(expandBox)}
                  aria-label={expandBox ? t(StaticStrings.show_less) : t(StaticStrings.Show_more)}
                  role={a11y.ROLES.BTN}
                  className="su__flex-hcenter su__align-items-center su__justify-content-center su__color-blue a11y-btn m-auto w-auto su__sc-loading"
                  onClick={() => showMore()}
                >
                  {expandBox ? (
                    <div className="su__show-less su__cursor su__loading-view su__text-center">
                      <span
                        lang={variables.searchCallVariables.langAttr}
                        className="su__rtlmr-3 su__rtlml-0 su__font-12 su__mr_4px"
                      >
                        {t(StaticStrings.show_less)}
                      </span>
                      <span className="su__arrow-up"></span>
                    </div>
                  ) : (
                    <div className="su__show-all su__cursor su__loading-view">
                      <span
                        lang={variables.searchCallVariables.langAttr}
                        className="su__mr-2 su__rtlmr-3 su__rtlml-2 su__font-12"
                      >
                        {t(StaticStrings.Show_more)}
                      </span>
                      <span className="su__arrow-down"></span>
                    </div>
                  )}
                </button>
              </div>
            ) : null}
          </div>
        ) : null}
      </>
    );
  } catch (e) {
    console.log('Error in Stickfacet component', e);
    return <div></div>;
  }
};

export default StickyFacets;
