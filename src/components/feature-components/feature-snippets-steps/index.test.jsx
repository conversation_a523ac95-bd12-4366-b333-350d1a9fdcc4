/* global jest, it, expect, describe, beforeEach, afterEach */
import React from 'react';
import { mount } from 'enzyme';
import FeaturedSnippetSteps from './';
import { useTranslation } from 'react-i18next';
import state from '__mocks__/state';
import variables from '__mocks__/variables';

// Mock the gza function
global.gza = jest.fn();

jest.mock('react-i18next', () => ({
  useTranslation: jest.fn()
}));

jest.mock('../../../redux/variables', () => require('__mocks__/variables'));

jest.mock('components/section-components/title/index.jsx', () => {
  const Title = () => <div>Title</div>;
  return Title;
});

jest.mock('components/section-components/href/index.jsx', () => {
  const Href = () => <div>Title</div>;
  return Href;
});

jest.mock('../../../assets/svg-icon/svg', () => {
  const Icon = () => <div>Icon</div>;
  return Icon;
});

describe('FeaturedSnippetSteps', () => {
  let wrapper;
  const mockProps = {
    featuredSnippetResult: state.searchResult.featuredSnippetResult,
    linkOpened: jest.fn(),
    isDeviceMobile: false
  };

  beforeEach(() => {
    useTranslation.mockImplementation(() => ({ t: (key) => key }));
    jest.useFakeTimers();
    window.gza = jest.fn();
    wrapper = mount(<FeaturedSnippetSteps {...mockProps} />);
    wrapper.update();
  });

  afterEach(() => {
    jest.clearAllTimers();
    jest.clearAllMocks();
  });

  it('should render correctly', () => {
    expect(wrapper).toBeTruthy();
  });

  it('should render the header and steps', () => {
    expect(wrapper.find('.su__featureSnippet-title').text()).toBe(
      mockProps.featuredSnippetResult.header
    );
    expect(wrapper.find('.su__font-13').length).toBe(3);
  });

  it('should show more steps on click', () => {
    wrapper.find('.su__showmore-text').simulate('click');
    expect(wrapper.find('.su__font-13').length).toBe(4);
  });

  it('should render the multimedia content', () => {
    expect(wrapper.find('.su__img-featured').prop('src')).toBe(
      mockProps.featuredSnippetResult.multiMedia[0].image_urls
    );
  });

  it('should render correctly in mobile view', () => {
    const mobileProps = { ...mockProps, isDeviceMobile: true };
    const mobileWrapper = mount(<FeaturedSnippetSteps {...mobileProps} />);
    mobileWrapper.update();
    expect(mobileWrapper.find('.su__FeaturedSnippet').exists()).toBe(true);
  });

  it('should handle link clicks', () => {
    const link = wrapper.find('a').at(0);
    link.simulate('click');
    expect(mockProps.linkOpened).toHaveBeenCalledWith(mockProps.featuredSnippetResult, 1);
  });

  it('should handle link clicks for mobile', () => {
    const mobileProps = { ...mockProps, isDeviceMobile: true };
    wrapper = mount(<FeaturedSnippetSteps {...mobileProps} />);
    wrapper.update();
    const link = wrapper.find('a').at(0);
    link.simulate('click');
    expect(mockProps.linkOpened).toHaveBeenCalledWith(mockProps.featuredSnippetResult, 1);
  });

  it('should track analytics for feedback submission', () => {
    const thumbsDown = wrapper.find('span.su__featured-feedback').at(0);
    const thumbsUp = wrapper.find('span.su__featured-feedback').at(1);
    const feedbackbtns = [
      { btn: thumbsDown, feedbackVal: 0 },
      { btn: thumbsUp, feedbackVal: 1 }
    ];
    feedbackbtns.forEach((item) => {
      item.btn.simulate('click', {});
      jest.advanceTimersByTime(5000);
      expect(window.gza).toHaveBeenCalled();
      expect(window.gza).toHaveBeenLastCalledWith('featuredSnippet', {
        feedback: item.feedbackVal,
        searchString: variables.searchCallVariables.searchString,
        t: state.searchResult.featuredSnippetResult.highlight.TitleToDisplayString[0],
        uid: variables.searchCallVariables.uid,
        url: state.searchResult.featuredSnippetResult.href
      });
    });
  });

  it('should track analytics for feedback submission for mobile', () => {
    const mobileProps = { ...mockProps, isDeviceMobile: true };
    wrapper = mount(<FeaturedSnippetSteps {...mobileProps} />);
    wrapper.update();
    const thumbsDown = wrapper.find('span.su__featured-feedback').at(0);
    const thumbsUp = wrapper.find('span.su__featured-feedback').at(1);
    const feedbackbtns = [
      { btn: thumbsDown, feedbackVal: 0 },
      { btn: thumbsUp, feedbackVal: 1 }
    ];
    feedbackbtns.forEach((item) => {
      item.btn.simulate('click', {});
      jest.advanceTimersByTime(5000);
      expect(window.gza).toHaveBeenCalled();
      expect(window.gza).toHaveBeenLastCalledWith('featuredSnippet', {
        feedback: item.feedbackVal,
        searchString: variables.searchCallVariables.searchString,
        t: state.searchResult.featuredSnippetResult.highlight.TitleToDisplayString[0],
        uid: variables.searchCallVariables.uid,
        url: state.searchResult.featuredSnippetResult.href
      });
    });
  });

  it('should catch error in case component encounters one', () => {
    const consoleSpy = jest.spyOn(console, 'log').mockImplementation(() => {});
    useTranslation.mockImplementation(() => {
      throw new Error('error');
    });
    wrapper = mount(<FeaturedSnippetSteps {...mockProps} />);
    wrapper.update();
    expect(wrapper.html()).toBe('<div></div>');
    expect(consoleSpy).toHaveBeenCalledWith(
      'Error in feature-snippet component',
      expect.any(Error)
    );
    consoleSpy.mockRestore();
  });
});
