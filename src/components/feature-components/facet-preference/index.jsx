import React, { useState, Fragment, useEffect, useRef } from 'react';
import { useDispatch } from 'react-redux';
import variables from '../../../redux/variables';
import { search } from '../../../redux/ducks';
import Icons from '../../../assets/svg-icon/svg';
import { useTranslation } from 'react-i18next';
import { mergeFilters, mergeFilterClicked } from '../../../mergeFacet';
import StaticStrings from 'StaticStrings';
import IconColors from 'IconColors';
import Menu from '@material-ui/core/Menu';
import { A11Y_IDS, a11y, tabIndexes, useFocusTrap } from '../../../constants/a11y';
import { useDevice } from 'function-library/hooks';
import { array } from 'prop-types';
import { EVENT_NAMES, AGGR_KEYS } from 'constants/constants';

const handleClick = (event, id, setAnchorEl, contentDetails, a) => {
  setAnchorEl(event.currentTarget);
  let checkId = setInterval(function () {
    if (document.getElementById('su__MCS' + id)) {
      clearInterval(checkId);
      let clickedOpen = document.getElementById('su__MCS' + id);
      clickedOpen.classList.add('su__d-block');
      let mergedCsList = document.getElementsByClassName('su__merge-options-item');
      if (a?.initialTab.contentName && (!contentDetails || !contentDetails.activeTabIndex)) {
        [...mergedCsList].forEach((removeClasses) => {
          removeClasses.classList.remove('su__merge-options-active');
          if (removeClasses.classList.contains(a.initialTab.contentName)) {
            removeClasses.classList.add('su__merge-options-active');
          }
        });
      } else if ((contentDetails || contentDetails.activeTabIndex) !== a?.initialTab.contentName) {
        [...mergedCsList].forEach((removeClasses) => {
          removeClasses.classList.remove('su__merge-options-active');
          if (removeClasses.classList.contains(contentDetails.contentName)) {
            removeClasses.classList.add('su__merge-options-active');
          }
        });
      }
    }
  });
};
const FacetPreference = (props) => {
  try {
    let a = JSON.parse(localStorage.getItem('theme' + variables.searchCallVariables.uid));
    let searchResult = props.data;
    let mergedFacet = props.mergedFacet;
    let smartAggregation = props.smartFacetsAggregation;
    let allContentHideFacet = props.isAllContentShow;
    const { t } = useTranslation();
    const dispatch = useDispatch();
    let [metadataKeys, toggleDisplayValue] = useState(variables.toggleDisplayKeys);
    const [isConfirm, setToggleConfirm] = useState(false);
    const [contentDetails, setContentName] = useState({
      contentName: a && a.initialTab.contentName ? a && a.initialTab.contentName : 'all',
      index: a && a.initialTab.index ? a && a.initialTab.index : AGGR_KEYS._INDEX,
      activeTabIndex: a && a.initialTab.activeTabIndex ? a && a.initialTab.activeTabIndex : 'all'
    });
    const [rearrangedArray, setRearrangedArray] = useState(searchResult ? searchResult : []);
    const { isDeviceMobile, isTouchScreen } = useDevice();
    const [focusTrap] = useFocusTrap();
    const [mobilevisbilty, setMobilevisiblity] = useState('RearrangeFacets');
    const mobileDivArr = ['RearrangeFacets', 'PreselectedTab', 'SearchResult'];
    const mobileDivString = [
      t(StaticStrings.rearrange_facets),
      t(StaticStrings.preselect_tab),
      t(StaticStrings.search_result)
    ];
    const [isscrollboxLeft, setIsscrollboxLeft] = useState(false);
    const [isscrollboxRight, setIsscrollboxRight] = useState(true);
    const InnerslideBox = useRef(null);
    const [mergedtabs, setMergedtabs] = useState(null);
    let { editPageLayout, toggleEditMode } = props;
    const handleHideDropdown = (e) => {
      if (e.key === EVENT_NAMES.ESCAPE) {
        toggleEditMode(false);
        setToggleConfirm(false);
      }
    };
    let parsed_array = [];

    if (mergedFacet && typeof mergedFacet === 'string') {
      try {
        const parsed = JSON.parse(mergedFacet);
        parsed_array = Array.isArray(parsed) ? parsed : [];
      } catch (e) {
        parsed_array = [];
      }
    }
    parsed_array.length &&
      parsed_array.forEach(function (o) {
        mergeFilters(o, searchResult);
      });

    variables.searchCallVariables.smartFacets &&
      smartAggregation &&
      smartAggregation.forEach((hide) => {
        rearrangedArray.forEach((item) => {
          if (item.key == hide.key) {
            item.smartHidden = true;
          }
        });
      });

    const hiddenFilters = a?.hiddenFilters ? a.hiddenFilters : [];
    let dragId, dropId, old_index, new_index;
    const [dragging, setDragging] = useState(null);
    const c = JSON.parse(localStorage.getItem('theme' + variables.searchCallVariables.uid));
    if (c?.facetsOrder && searchResult) {
      for (let i = 0; i < c.facetsOrder.length; i++) {
        for (let j of rearrangedArray) {
          if (c.facetsOrder[i] == j.label) {
            j.index = i;
          }
        }
      }
    }
    useEffect(() => {
      if (a) {
        if (a.facetsOrder) {
          setRearrangedArray(a.facetsOrder);
        }
      } else {
        setRearrangedArray(searchResult);
      }
      document.addEventListener('keydown', handleHideDropdown);

      return () => {
        document.removeEventListener('keydown', handleHideDropdown);
      };
    }, []);

    const toggleKeys = (key) => {
      let index = metadataKeys.findIndex((element) => element.key === key);
      let tempArray = JSON.parse(JSON.stringify(metadataKeys));
      tempArray[index]['hideEye'] = !tempArray[index]['hideEye'];
      toggleDisplayValue(tempArray);
    };

    const cancel = (event) => {
      event.preventDefault();
    };

    /**
     * dragstart - event handler for onDragStart / onTouchStart
     * @param {TouchEvent | DragEvent} event
     * @returns {void}
     */
    const dragstart = (event) => {
      if (isTouchScreen) {
        document.body.style.overflow = 'hidden';
      }
      dragId = event?.target?.getAttribute('data-drag-id');
      setDragging(dragId);
      event?.dataTransfer?.setData('dragStart', dragId);
    };

    /**
     * onDragOver - event handler for onDragOver / onTouchMove
     * @param {TouchEvent | DragEvent} event
     * @returns {void}
     */
    const onDragOver = (event) => {
      !isTouchScreen && event.preventDefault();
    };

    const handlemergetabs = (pram) => {
      if (pram.Contentname === mergedtabs) {
        setMergedtabs(null);
      } else {
        setMergedtabs(pram.Contentname);
      }
    };
    /**
     * drop - event handler for draggable element onDrop / onTouchEnd
     * @param {TouchEvent | DragEvent} event
     * @returns {void}
     */
    const drop = (event) => {
      document.body.style.overflow = 'auto';
      setDragging(() => null);
      dropId = isTouchScreen
        ? document
            ?.elementFromPoint(event?.changedTouches[0]?.clientX, event?.changedTouches[0]?.clientY)
            ?.getAttribute('data-drag-id')
        : event?.target?.dataset?.dragId;
      dropId != undefined && array_move();
      !isTouchScreen && event.preventDefault();
    };

    /**
     * array_move - shifts the position of facet preferences for a valid drop
     * @param {Array} arr - Array of facet order preference
     * @returns {void}
     */
    const array_move = () => {
      if (!dragging || !dropId) return;
      old_index = parseInt(dragging);
      new_index = parseInt(dropId);
      // make array copy
      let arr = [...rearrangedArray];
      // switch positions
      const buffer = { ...arr[old_index] };
      arr[old_index] = arr[new_index];
      arr[new_index] = buffer;
      setRearrangedArray(arr);
    };

    const getContentResults = (e, label, item, child) => {
      let obj = e.currentTarget.parentNode.children;
      let values = Object.keys(obj).map((e) => obj[e]);
      let tabsContentActive = document.getElementsByClassName(
        'su__tabs su__getClassName initialTab'
      );
      if (tabsContentActive) {
        Array.prototype.forEach.call(tabsContentActive, function (el) {
          el.classList.remove('initialTab');
          el.classList.remove('su__editl-active-tab');
        });
      }
      let tabsContentInActive = document.getElementsByClassName(
        item + ' su__tabs su__getClassName'
      )[0];
      if (tabsContentInActive) {
        tabsContentInActive.classList.add('initialTab');
        tabsContentInActive.classList.add('su__editl-active-tab');
      }
      if (child) {
        values.forEach((element) => {
          if (element.classList.contains('su__merge-options-active')) {
            element.classList.remove('su__merge-options-active');
            return true;
          }
        });
        e.currentTarget.classList.add('su__merge-options-active');
      }
      if ((e.currentTarget.innerHTML || e.currentTarget.value) === 'All Content') {
        setContentName({
          contentName: 'all',
          index: AGGR_KEYS._INDEX,
          activeTabIndex: 'all'
        });
      } else {
        setContentName({
          contentName: e.currentTarget.classList[0],
          index: label,
          activeTabIndex: item
        });
      }
    };

    const toggleHideEye = (label) => {
      let tempArr = JSON.parse(JSON.stringify(rearrangedArray));
      for (let value of tempArr) {
        if (value.label == label) {
          if (!value.hideEye) {
            value.hideEye = true;
          } else {
            value.hideEye = !value.hideEye;
          }
          break;
        }
      }
      setRearrangedArray(tempArr);
    };

    const openConfirmbox = () => {
      setToggleConfirm(true);
    };

    const reset = () => {
      localStorage.removeItem('theme' + variables.searchCallVariables.uid);
      let tempArray = [...metadataKeys];
      tempArray.forEach((el) => {
        el.hideEye = false;
      });
      toggleDisplayValue(tempArray);
      variables.toggleDisplayKeys = [...metadataKeys];
      setToggleConfirm(false);
      toggleEditMode(true);
      props.toggleEditMode(false);
      variables.allSelected = true;
      variables.activeType = 'all';
      searchResult[0]?.values.forEach((element) => {
        element.selected = false;
      });
      variables.searchCallVariables.aggregations = [];
      variables.searchSource = 'facetPreference';
      variables.searchCallVariables.from = 0;
      variables.searchCallVariables.pageNo = 1;
      dispatch(search.start(variables.searchCallVariables));
    };

    const applyTheme = () => {
      let tempArr = JSON.parse(JSON.stringify(rearrangedArray));
      for (let element of tempArr) {
        if (element.hideEye) {
          let aggrArray = variables.searchCallVariables.aggregations;
          for (let i = 0; i < aggrArray.length; i++) {
            if (element.key === aggrArray[i].type) {
              variables.searchCallVariables.aggregations.splice(i, 1);
            }
          }
        }
      }
      props.toggleEditMode(false);

      if (a?.initialTab.contentName != contentDetails.contentName) {
        variables.searchCallVariables.aggregations = [];
      }

      let merge = false;
      let arr = [];
      if (variables.activeType !== contentDetails.contentName) {
        variables.searchCallVariables.aggregations = [{ type: contentDetails.index, filter: arr }];
      }

      if (contentDetails?.contentName.indexOf('merged_') > -1) {
        let filterChecked = true;
        mergeFilterClicked(contentDetails.contentName, arr, searchResult[0]?.values, filterChecked);
        merge = true;
      } else {
        arr.push(contentDetails?.contentName);
      }

      if (contentDetails?.contentName !== 'all' && contentDetails?.contentName != '') {
        if (
          (contentDetails.index == AGGR_KEYS._INDEX || contentDetails.index == AGGR_KEYS._TYPE) &&
          variables.searchCallVariables.aggregations.length == 0
        ) {
          variables.searchCallVariables.aggregations = [
            { type: contentDetails.index, filter: arr }
          ];
        }
        variables.allSelected = false;
      } else if (contentDetails?.contentName == 'all') {
        if (variables.activeType !== contentDetails.contentName)
          variables.searchCallVariables.aggregations = [];
        variables.allSelected = true;
      }

      variables.searchSource = 'facetPreference';

      variables.searchCallVariables.from = 0;
      variables.searchCallVariables.pageNo = 1;

      variables.activeType = contentDetails.contentName;
      dispatch(search.start(variables.searchCallVariables));
      variables.toggleDisplayKeys = [...metadataKeys];

      /* Save the variables to localStorage */
      let defaultValue =
        contentDetails && contentDetails.contentName != '' ? contentDetails.contentName : 'all';
      let value = merge ? arr : defaultValue;

      rearrangedArray.forEach((item) => (item.smartHidden = false));

      let b = {
        hideObject: variables.toggleDisplayKeys,
        initialTab: contentDetails
          ? {
              contentName: value,
              index: contentDetails.index,
              mergeContentName: contentDetails.contentName,
              activeTabIndex: contentDetails.contentName
            }
          : {
              index: AGGR_KEYS._INDEX,
              contentName: 'all',
              activeTabIndex: 'all'
            },
        hiddenFilters: hiddenFilters,
        facetsOrder: rearrangedArray
      };
      localStorage.setItem('theme' + variables.searchCallVariables.uid, JSON.stringify(b));
    };
    const [anchorEl, setAnchorEl] = React.useState(null);

    const handleClose = () => {
      setAnchorEl(null);
      setMergedtabs(null);
    };

    // Focus trap
    useEffect(() => {
      if (editPageLayout) {
        focusTrap(editPageLayout); // Call focusTrap for editPageLayout
      } else if (isConfirm) {
        focusTrap(isConfirm); // Call focusTrap for isConfirm if editPageLayout is not active
      }
    }, [editPageLayout, isConfirm]);

    const handleMobileVisiblity = (DivId) => {
      setMobilevisiblity(DivId);
    };
    /**
     * PreviewTile - Preview tile for facet preference
     * @returns {JSX.Element}
     */
    const PreviewTile = ({ metadataKeys }) => (
      <div className="su__PreviewTile su__list-items su__flex-column su__border-light-gray su__bg-white su__bg-bright-white su__box-shadow-bl-6 su__radius-1 su__position-relative ">
        {!metadataKeys[5].hideEye && variables.searchCallVariables.showContentTag && (
          <span
            title="item.contentTag"
            className="su__ribbon-title su__ribbon-list su__font-11 su__line-height-14 su__d-inline-block su__p-1 su__radius-5p su__border-light-gray su__bg-bright-white"
          >
            {t('SearchUnify Docs')}
          </span>
        )}
        <div className="su__list-item-row su__media su__w-100">
          <div className="su__list-item-text su__media-body su__overflow-hide su__word-break">
            <h3 className="su__list-item-title su__text-black su__text-truncate su__m-0 su__text-decoration  su__pt-1">
              {!metadataKeys[4].hideEye && (
                <div className="su__messagepreview su__list-item-img su__rtlml-3 su__rtlmr-0  su__pt-sm-1 su__d-inline-block su__background-Oceanblue su__p-2px su__mr-1 su__rtlmr-0 su__rtlml-1">
                  <Icons
                    className="su__zindex-1"
                    IconName="MessageSquare"
                    width="16"
                    height="16"
                    color={IconColors.FacetPreferenceSaveBookmark}
                    fill="#176ed1"
                  />
                </div>
              )}
              {!metadataKeys[0].hideEye && (
                <span className="su__pref-title su__font-17 su__line-height-22  su__dark-gray su__f-normal">
                  What is Lorem Ipsum?
                </span>
              )}
            </h3>

            {!metadataKeys[2].hideEye && (
              <p className="su__list-item-url su__w-100 su__my-1">
                <a className="su__text-decoration su__font-13 su__line-height-22 su__dim-gray su__font-italic">
                  https://www.lipsum.com/
                </a>
              </p>
            )}
            {!metadataKeys[1].hideEye && (
              <p className="su__list-item-desc su__w-100 su__my-1 su__font-regular  su__font-13 su__line-height-14 su__dark-gray">
                Skip To Main Content Account Settings Logout placeh-older Account Setting
              </p>
            )}
            {!metadataKeys[3].hideEye && (
              <div className="su__meta-item su__d-flex su__flex-wrap su__align-content-between su__mt-2 su__font-12">
                <div className="su__meta-date su__word-break ">
                  <span
                    lang={variables.searchCallVariables.langAttr}
                    className="su__f-normal su__mr-2 su__rtlmr-0 su__rtlml-2 su__font-11 su__line-height-14 su__dim-gray"
                  >
                    {t(StaticStrings.Tags)}:
                  </span>
                  <span className="su__mr-2 su__rtlmr-0 su__rtlml-2 su__radius-5p su__bg-p-gray su__p-1 su__font-11 su__line-height-14 su__dim-gray">
                    {t('SearchUnify Docs')}
                  </span>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    );

    PreviewTile.propTypes = {
      metadataKeys: array
    };
    PreviewTile.defaultProps = {
      metadataKeys: [{}]
    };

    const scrollLeft = () => {
      const box = InnerslideBox.current;
      box.scrollTo({
        left: box.scrollLeft - box.clientWidth / 2,
        behavior: 'smooth'
      });
    };

    const scrollRight = () => {
      const box = InnerslideBox.current;
      box.scrollTo({
        left: box.scrollLeft + box.clientWidth / 2,
        behavior: 'smooth'
      });
    };
    useEffect(() => {
      const handleScroll = () => {
        const scrollinnerDiv = InnerslideBox.current;
        if (scrollinnerDiv) {
          const maxScrollLeft = scrollinnerDiv.scrollWidth - scrollinnerDiv.clientWidth;

          const isRTL = window.getComputedStyle(scrollinnerDiv).direction === 'rtl';

          let scrollLeft = scrollinnerDiv.scrollLeft;
          if (isRTL) {
            scrollLeft = scrollLeft < 0 ? -scrollLeft : scrollLeft;
          }
          setIsscrollboxLeft(isRTL ? scrollLeft < maxScrollLeft - 1 : scrollLeft > 1);
          setIsscrollboxRight(isRTL ? scrollLeft > 1 : scrollLeft < maxScrollLeft - 1);
        }
      };
      const scrollinnerDiv = InnerslideBox.current;
      if (scrollinnerDiv) {
        scrollinnerDiv.addEventListener('scroll', handleScroll);
        handleScroll(); // Initial check
      }
      return () => {
        if (scrollinnerDiv) {
          scrollinnerDiv.removeEventListener('scroll', handleScroll);
        }
      };
    }, [editPageLayout, isDeviceMobile]);

    const SearchResultTile = ({ metadataKeys }) => (
      <>
        {React.Children.toArray(
          metadataKeys
            .filter((item) => {
              return !(!variables.searchCallVariables.showContentTag && item.key === 'Tag');
            })
            .map((item, index, FilteredArray) => (
              <div
                key={item?.key}
                className={`${
                  isDeviceMobile
                    ? `su__w-100 su__flex-vcenter su__border-top su__py-2  su__pl-2 su__pr-3 ${
                        index === FilteredArray.length - 1 ? 'su__border-bottom' : ''
                      }`
                    : 'su__d-inline-block'
                }`}
              >
                <span
                  className={` su-display ${
                    isDeviceMobile
                      ? 'su__radius-0 su__flex-vcenter su__justify-content-between su__w-100'
                      : ' su__radius-1 su__border-light-gray su__margin-8px su__mb-2  su__eyes-btn su__px-2 su__cursor'
                  }`}
                  onClick={() => toggleKeys(item.key)}
                >
                  <span
                    className={`su__dark-gray su__font-regular su__font-16 su__line-height-19 su__text-black ${
                      isDeviceMobile ? 'su__my-1  su__f-normal su__p-2' : 'su__padding-top-3px'
                    }`}
                  >
                    {item.key}
                  </span>
                  <button
                    type="button"
                    aria-live="assertive"
                    aria-atomic="true"
                    lang={variables.searchCallVariables.langAttr}
                    aria-label={`${item.key} ${
                      item.hideEye ? t(StaticStrings.hide) : t(StaticStrings.show)
                    }`}
                    role={a11y.ROLES.BTN}
                    tabIndex={tabIndexes.tabIndex_0}
                    className={`su__eye su__flex-hcenter a11y-btn  ${
                      !item.hideEye ? 'su__eye-show' : 'su__eye-hide'
                    }`}
                  >
                    {!item.hideEye && (
                      <Icons
                        className="su__cursor su__eye-open"
                        IconName="showEye"
                        width="24"
                        height="25"
                        color={IconColors.FacetPreferenceShow_HideEye}
                        cx="1.5"
                        cy="1.5"
                        r="1.5"
                      />
                    )}
                    {item.hideEye && (
                      <Icons
                        className="su__cursor su__eye-close"
                        IconName="hideEye"
                        width="24"
                        height="24"
                        color={IconColors.FacetPreferenceShow_HideEye}
                      />
                    )}
                  </button>
                </span>
              </div>
            ))
        )}
      </>
    );
    SearchResultTile.propTypes = {
      metadataKeys: array
    };
    SearchResultTile.defaultProps = {
      metadataKeys: [{}]
    };

    return (
      <Fragment>
        {editPageLayout && (
          <Fragment>
            <div className="su__position-fixed su__trbl-0 su__zindex-3 su__flex-vcenter">
              <div
                id={A11Y_IDS.childTrap}
                className={` su__shadow-lg su__bg-white su__radius-1 su__mx-auto su__zindex-3 su__d-flex su__flex-column
                  ${isDeviceMobile ? 'su__editPglayout' : 'su__editClient  su__p-3'} 
                  ${isTouchScreen && dragging !== null ? 'su__no-overflow' : ''} `}
              >
                <div
                  className={`su__border-none ${
                    isDeviceMobile ? 'su__pb-2 su__pt-3 ' : 'su__flex-vcenter su__rtlmb-3 su__pb-1 '
                  }`}
                >
                  <h2
                    lang={variables.searchCallVariables.langAttr}
                    className={`su-edit su__flex-1 su__text-blue su__font-regular su__font-17 su__f-normal line su__line-height-22 su__mt-0 su__mb-1  ${
                      isDeviceMobile ? 'su__pl-3 su__rtlpr-1' : ''
                    }`}
                  >
                    {t(StaticStrings.edit_page)}
                  </h2>
                  {!isDeviceMobile && (
                    <div className="editLayout  su__d-inline-block su__float-right su__cursor  su__float-leftRtl">
                      <button
                        type="button"
                        data-trigger-a11y={A11Y_IDS.nested_trap}
                        data-name-a11y="set_default"
                        lang={variables.searchCallVariables.langAttr}
                        role={a11y.ROLES.BTN}
                        tabIndex={tabIndexes.tabIndex_0}
                        className="su__border_skyblue su__text-blue su__py-2  su__px-3 su__font-regular su__font-14 su__radius-1 su__cursor su__line-height-n su__bg-transparent"
                        onClick={openConfirmbox}
                      >
                        {t(StaticStrings.Set_to_default)}
                      </button>
                      <button
                        type="button"
                        lang={variables.searchCallVariables.langAttr}
                        role={a11y.ROLES.BTN}
                        tabIndex={tabIndexes.tabIndex_0}
                        onClick={applyTheme}
                        className="apply su__m-is-p6 su__m-ie-1 su__text-white su__bg-blue-grd su__radius-1 su__font-regular su__font-14 su__py-2 su__cursor border-0 su__line-height-n su__border_skyblue"
                      >
                        {' '}
                        {t(StaticStrings.apply)}
                      </button>
                      <button
                        type="button"
                        lang={variables.searchCallVariables.langAttr}
                        className="a11y-btn p-0"
                        role={a11y.ROLES.BTN}
                        aria-label={t(StaticStrings.close_popup)}
                        tabIndex={tabIndexes.tabIndex_0}
                        onClick={() => props.toggleEditMode(false)}
                      >
                        <Icons
                          className="su__cursor"
                          IconName="Close"
                          width="12"
                          height="12"
                          color={IconColors.FacetPreferenceCrossIcon}
                        />
                      </button>
                    </div>
                  )}
                  {isDeviceMobile && (
                    <div className="su__w-100 ">
                      <div className="su__radius-1 su__slider-outer su__tabsSection su__px-2 su__flex-hcenter su__position-relative su__sildeBox su__overflow-x ">
                        {isscrollboxLeft && (
                          <button
                            lang={variables.searchCallVariables.langAttr}
                            aria-label={t(StaticStrings.slide_left)}
                            className={`arrow-left arrow-btn su__slider-button su__lr-arrows su__flex-hcenter `}
                            onClick={scrollLeft}
                          >
                            {' '}
                            <div className="su__rotate-180 su__flex-hcenter">
                              <Icons
                                className="su__leftarrow"
                                IconName="LeftArrow"
                                width="24"
                                height="24"
                              />{' '}
                            </div>
                          </button>
                        )}
                        <div
                          ref={InnerslideBox}
                          className="scrollinnerDiv box-inner-wrapper su-tabsSection su__w-100  su__text-nowrap su__overflowx-auto su__slider-list su__position-relative su__bottom-1px"
                        >
                          {mobileDivArr.map((Div, index) => (
                            <button
                              key={index}
                              lang="en"
                              aria-label={mobileDivString[index]}
                              role="tab"
                              aria-selected={`${mobilevisbilty === Div}`}
                              tabIndex="0"
                              className={`a11y-btn  su__tabs  su__line-height-16 su__font-13 su__f-regular su__zindex-1 su__mr-2  ${
                                mobilevisbilty === Div ? 'initialTab  su__editl-active-tab ' : ''
                              } `}
                              onClick={() => {
                                handleMobileVisiblity(Div);
                              }}
                            >
                              {mobileDivString[index]}{' '}
                            </button>
                          ))}
                        </div>
                        {isscrollboxRight && (
                          <button
                            lang={variables.searchCallVariables.langAttr}
                            aria-label={t(StaticStrings.slide_right)}
                            className={`arrow-right-btn  arrow-right arrow-btn su__slider-button su__lr-arrows su__flex-hcenter`}
                            onClick={scrollRight}
                          >
                            <div className="su__flex-hcenter">
                              <Icons
                                className="su__RightArrow"
                                IconName="RightArrow"
                                width="24"
                                height="24"
                              />
                            </div>
                          </button>
                        )}
                      </div>
                      <div className="su__slide-line"></div>
                    </div>
                  )}
                </div>
                <div
                  className={`su__facet-pref-blocks  ${
                    isDeviceMobile ? 'su__y-scroll' : 'su__d-flex'
                  }`}
                >
                  <div
                    id="RearrangeFacets"
                    className={
                      allContentHideFacet && variables.allSelected
                        ? 'su__allSelected-pref-yes su-customizes su__d-none'
                        : `su__allSelected-pref-ncol- su-draggableFilters su__pt-0 su__overflow-hide  ${
                            isDeviceMobile
                              ? mobilevisbilty == 'RearrangeFacets'
                                ? ''
                                : 'su__d-none'
                              : 'su__pr-2 su__custom-width-facets '
                          }`
                    }
                  >
                    <div
                      lang={variables.searchCallVariables.langAttr}
                      className={`su__font-13   su__text-dark su__pt-2 su__font-13 su__line-height-16 su__font-heavy ${
                        isDeviceMobile ? 'su__d-none' : ''
                      }`}
                    >
                      {t(StaticStrings.rearrange_facets)}
                    </div>
                    <div
                      id="filterList"
                      className={`su__pl-0 su__overflow-auto su__minscroller su__word-break-all filterlistscroll ${
                        isDeviceMobile ? '' : 'sortable'
                      }`}
                    >
                      <div>
                        {searchResult &&
                          [...rearrangedArray]
                            .sort((a, b) => {
                              return a.index - b.index;
                            })
                            .map(
                              (item, index, FilteredArray) =>
                                item.order !== 0 && (
                                  <div
                                    data-drag-id={index}
                                    key={item.label}
                                    onTouchStart={dragstart}
                                    onTouchMove={onDragOver}
                                    onTouchEnd={drop}
                                    onDragEnter={cancel}
                                    onDrop={drop}
                                    draggable="true"
                                    onDragOver={onDragOver}
                                    onDragStart={dragstart}
                                    className={`su__font-regular su__font-16 su__line-height-19 draggableList su__dark-gray border 
                                    ${
                                      isDeviceMobile
                                        ? 'su__border-top'
                                        : index !== 1
                                        ? 'su__border-top'
                                        : ''
                                    }
                                    ${
                                      index === FilteredArray.length - 1
                                        ? ' su__border-bottom  '
                                        : ''
                                    } 
                                    ${item.smartHidden ? 'su__fp_indicator' : ''} ${
                                      isTouchScreen && dragging == index
                                        ? 'su__touch-scroll-control'
                                        : ''
                                    }`}
                                  >
                                    <div
                                      data-drag-id={index}
                                      className={`${
                                        item.smartHidden
                                          ? 'su__d-flex su__align-items-start'
                                          : 'su__flex-vcenter'
                                      } su__justify-content-between su__max-height_25px ${
                                        isDeviceMobile ? 'su__pl-3 su__rtlpl-0' : ''
                                      }`}
                                    >
                                      <div className="su__flex-vcenter su__min-w20">
                                        {!isDeviceMobile && (
                                          <div className="su__pb-5px">
                                            <Icons
                                              className="su__cursor DragItem"
                                              IconName="DragItems"
                                              width="24"
                                              height="24"
                                            />
                                          </div>
                                        )}
                                        <span data-drag-id={index}>{t(item.label)}</span>
                                      </div>
                                      <div className="su__flex-vcenter su__justify-content-between">
                                        <div>
                                          {!item.hideEye ? (
                                            <button
                                              type="button"
                                              aria-live="assertive"
                                              aria-atomic="true"
                                              lang={variables.searchCallVariables.langAttr}
                                              aria-label={`${t(item.label)} ${
                                                item.hideEye
                                                  ? t(StaticStrings.hide)
                                                  : t(StaticStrings.show)
                                              }`}
                                              role={a11y.ROLES.BTN}
                                              tabIndex={tabIndexes.tabIndex_0}
                                              className={`su__line-height-n a11y-btn su__rearrange_facet-eye-btn ${
                                                item.smartHidden && 'disabled'
                                              }`}
                                              onClick={() => toggleHideEye(item.label)}
                                            >
                                              <Icons
                                                className="su__cursor "
                                                IconName="showEye"
                                                width="24"
                                                height="24"
                                                color={IconColors.FacetPreferenceShow_HideEye}
                                                cx="1.5"
                                                cy="1.5"
                                                r="1.5"
                                              />
                                            </button>
                                          ) : (
                                            <button
                                              type="button"
                                              aria-live="assertive"
                                              aria-atomic="true"
                                              lang={variables.searchCallVariables.langAttr}
                                              aria-label={`${t(item.label)} ${
                                                item.hideEye
                                                  ? t(StaticStrings.hide)
                                                  : t(StaticStrings.show)
                                              }`}
                                              role={a11y.ROLES.BTN}
                                              tabIndex={tabIndexes.tabIndex_0}
                                              className={`a11y-btn su__rearrange_facet-eye-btn  ${
                                                item.smartHidden && 'disabled'
                                              }`}
                                              onClick={() => toggleHideEye(item.label)}
                                            >
                                              <Icons
                                                className="su__cursor"
                                                IconName="hideEye"
                                                width="24"
                                                height="24"
                                                color={IconColors.FacetPreferenceShow_HideEye}
                                              />
                                            </button>
                                          )}
                                        </div>
                                        {isDeviceMobile && (
                                          <div>
                                            <Icons
                                              className="su__cursor DragItem"
                                              IconName="DragItems"
                                              width="24"
                                              height="24"
                                            />
                                          </div>
                                        )}
                                      </div>
                                    </div>
                                    {item.smartHidden && item.hideEye ? (
                                      <div
                                        lang={variables.searchCallVariables.langAttr}
                                        className="su__w-100 su__font-12"
                                      >
                                        {t(StaticStrings.facet_still_visible)}
                                      </div>
                                    ) : (
                                      item.smartHidden &&
                                      !item.hideEye && (
                                        <div
                                          lang={variables.searchCallVariables.langAttr}
                                          className="su__w-100 su__font-12"
                                        >
                                          {t(StaticStrings.facet_not_visible)}
                                        </div>
                                      )
                                    )}
                                  </div>
                                )
                            )}
                      </div>
                    </div>
                  </div>
                  {!isDeviceMobile && (
                    <div
                      className={
                        allContentHideFacet && variables.allSelected
                          ? 'su__allSelected-pref-yes su-customizes su__col-md-12 su__py-2 su__px-0'
                          : 'su__allSelected-pref-no su__custom-width-facet-results su-customizes su__pt-2 su__px-0'
                      }
                    >
                      <div className="navbar-filter">
                        <div
                          lang={variables.searchCallVariables.langAttr}
                          className="su__font-13 su__text-dark su__line-height-16  su__font-heavy"
                        >
                          {' '}
                          {t(StaticStrings.preselect_tab)}
                        </div>

                        <div className="su__radius-1 su__slider-outer su__flex-vcenter  su__flex-column">
                          {searchResult &&
                            searchResult
                              .filter((item) => item.order == 0)
                              .map((item) => (
                                <React.Fragment key={item.order}>
                                  <div
                                    key={item.order}
                                    className=" su__preselectedTab box-inner-wrapper su-tabsSection su__w-100 su__border-b su__text-nowrap su__overflow-auto  su__slider-list su__zindex-1"
                                  >
                                    <button
                                      lang={variables.searchCallVariables.langAttr}
                                      aria-current={'' + (contentDetails.activeTabIndex == 'all')}
                                      tabIndex={tabIndexes.tabIndex_0}
                                      className={
                                        a?.initialTab.activeTabIndex == 'all' || !a
                                          ? 'a11y-btn all-content su__tabs su__getClassName initialTab su__editl-active-tab su__line-height-16 su__font-13 su__mx-1'
                                          : 'a11y-btn all-content su__tabs su__getClassName su__line-height-16 su__font-13 su__mx-1'
                                      }
                                      onClick={(e) => getContentResults(e, item.key, 'all-content')}
                                    >
                                      {t(StaticStrings.all_content)}
                                    </button>
                                    {React.Children.toArray(
                                      item.values.map((value) => {
                                        let conditionForContentResult =
                                          value.showChild !== '1' || !value.merged;
                                        return (
                                          <button
                                            data-trigger-a11y={`tab-${value.Contentname}`}
                                            data-name-a11y={`tab-${value.displayName}`}
                                            aria-current={
                                              '' +
                                              !!(
                                                contentDetails.activeTabIndex ==
                                                  value.Contentname ||
                                                (value.merged &&
                                                  value.showChild &&
                                                  value.filterList.indexOf(
                                                    contentDetails.contentName
                                                  ) > -1)
                                              )
                                            }
                                            key={value.displayName || value.Contentname}
                                            lang={variables.searchCallVariables.langAttr}
                                            tabIndex={tabIndexes.tabIndex_0}
                                            className={
                                              value.Contentname +
                                              ' su__tabs su__getClassName a11y-btn  su__line-height-16 su__font-13 su__mr-1 ' +
                                              (a?.initialTab.activeTabIndex == value.Contentname ||
                                              (value.merged &&
                                                value.showChild &&
                                                value.filterList.indexOf(
                                                  a?.initialTab.contentName
                                                ) > -1)
                                                ? 'initialTab su__editl-active-tab '
                                                : '')
                                            }
                                            onClick={(e) =>
                                              conditionForContentResult
                                                ? getContentResults(e, item.key, value.Contentname)
                                                : null
                                            }
                                          >
                                            {!value.merged ||
                                            (value.merged && value.showChild !== '1')
                                              ? value.displayName
                                              : ''}
                                            {value.merged && value.showChild === '1' ? (
                                              <div className="su__mergedFilter-CS-tab su__mergedFilter_preference">
                                                <div
                                                  className="su__mergedFilter-CS-button"
                                                  onClick={(e) =>
                                                    handleClick(
                                                      e,
                                                      value.Contentname,
                                                      setAnchorEl,
                                                      contentDetails,
                                                      a
                                                    )
                                                  }
                                                >
                                                  <span
                                                    className="su__mergedFilter-CS-svg su__merged_tabs  su__position-relative su__pr-4 su__right-0-rtl su__rtlpl-4 "
                                                    onClick={() => handlemergetabs(value)}
                                                  >
                                                    {value.displayName}
                                                    {mergedtabs === value.Contentname ? (
                                                      <span className="su__arrow-up su__arrow_position su__arrow-align-rtl "></span>
                                                    ) : (
                                                      <span className="su__arrow-down su__arrow_position su__arrow-align-rtl su__top-5px "></span>
                                                    )}
                                                  </span>
                                                </div>
                                                <Menu
                                                  id={'su__MCS' + value.Contentname}
                                                  className="su__facet-preference su__d-none su__w-100 "
                                                  anchorEl={anchorEl}
                                                  open={Boolean(anchorEl)}
                                                  onClose={handleClose}
                                                  onClick={handleClose}
                                                  getContentAnchorEl={null}
                                                  transformOrigin={{
                                                    horizontal: 'left',
                                                    vertical: 'top'
                                                  }}
                                                  anchorOrigin={{
                                                    horizontal: 'left',
                                                    vertical: 'bottom'
                                                  }}
                                                >
                                                  {React.Children.toArray(
                                                    value.childArray.map((child) => (
                                                      <div
                                                        key={child.displayName || child.Contentname}
                                                        className={
                                                          child.Contentname +
                                                          ' su__merge-options-item'
                                                        }
                                                        onClick={(e) =>
                                                          getContentResults(
                                                            e,
                                                            item.key,
                                                            value.Contentname,
                                                            child.Contentname
                                                          )
                                                        }
                                                      >
                                                        {child.displayName || child.Contentname}
                                                      </div>
                                                    ))
                                                  )}
                                                </Menu>
                                              </div>
                                            ) : null}
                                          </button>
                                        );
                                      })
                                    )}
                                  </div>
                                </React.Fragment>
                              ))}
                          {searchResult && <div className="su__slide-line"></div>}
                        </div>
                      </div>
                      <div id={`tab-${contentDetails.contentName}`}>
                        <div className="hideFacets  su__word-break-all su__pt-20px">
                          <div
                            lang={variables.searchCallVariables.langAttr}
                            className="su__font-13 su__text-dark su__line-height-16 su__pb-2 su__font-heavy"
                          >
                            {t(StaticStrings.search_result)}
                          </div>
                          <SearchResultTile metadataKeys={metadataKeys} />
                        </div>
                        {!isDeviceMobile && <PreviewTile metadataKeys={metadataKeys} />}
                      </div>
                    </div>
                  )}
                  {/* {.........Mobile View.......................} */}
                  {isDeviceMobile && (
                    <>
                      <div
                        id="PreselectedTab"
                        className={`su__tabsSection su__radius-1  su__flex-shrink-1 ${
                          mobilevisbilty == 'PreselectedTab' ? '' : 'su__d-none'
                        }`}
                      >
                        {searchResult &&
                          searchResult
                            .filter((item) => item.order === 0)
                            .map((item, index) => (
                              <React.Fragment key={index}>
                                <div
                                  key={item.order}
                                  className="box-inner-wrapper su-tabsSection su__w-100 su__border-b su__text-nowrap su__minscroller "
                                >
                                  <div
                                    className={`su__w-100 su__flex-vcenter su__border-top su__p-3 su__kh_label`}
                                  >
                                    <label className="radio-button-label su__flex-vcenter su__justify-content-between su__w-100">
                                      <span className="su__font-16 su__line-height-19 su__dark-gray su__f-normal">
                                        {t(StaticStrings.all_content)}
                                      </span>
                                      <span className="su__position-relative">
                                        <input
                                          id={`tab-group-all-content`}
                                          type="radio"
                                          name="tab-group"
                                          value={`${t(StaticStrings.all_content)}`}
                                          defaultChecked={contentDetails.activeTabIndex === 'all'}
                                          lang={variables.searchCallVariables.langAttr}
                                          aria-current={
                                            '' + (contentDetails.activeTabIndex === 'all')
                                          }
                                          tabIndex={tabIndexes.tabIndex_0}
                                          className={
                                            (a && a.initialTab.activeTabIndex == 'all') || !a
                                              ? 'a11y-btn all-content su__tabs su__getClassName  initialTab  su__editl-active-tab  su__radio-btn '
                                              : 'a11y-btn all-content su__tabs su__getClassName  su__radio-btn'
                                          }
                                          onClick={(e) =>
                                            getContentResults(e, item.key, 'all-content')
                                          }
                                        />
                                        <label
                                          htmlFor={`tab-group-all-content`}
                                          className={
                                            (a && a.initialTab.activeTabIndex == 'all') || !a
                                              ? 'a11y-btn all-content su__tabs su__getClassName  initialTab  su__radio-btn su__radio-button-label su__padding-0'
                                              : 'a11y-btn all-content su__tabs su__getClassName  su__radio-btn su__radio-button-label'
                                          }
                                        ></label>
                                      </span>
                                    </label>
                                  </div>
                                  {React.Children.toArray(
                                    item.values.map((value, index, FilteredArray) => (
                                      <div
                                        className={`su__w-100  su__border-top su__p-3   ${
                                          index === FilteredArray.length - 1
                                            ? 'su__border-bottom'
                                            : ''
                                        }`}
                                        key={`${value.displayName}__${index}`}
                                      >
                                        <div className="su__flex-vcenter su__kh_label">
                                          <label
                                            className={`radio-button-label  su__w-100
                                            ${
                                              !value.merged ||
                                              (value.merged && value.showChild !== '1')
                                                ? 'su__flex-vcenter su__justify-content-between'
                                                : 'su__d-none'
                                            }`}
                                          >
                                            <span className="su__font-16 su__line-height-19 su__dark-gray su__f-normal su__text-truncate">
                                              {!value.merged ||
                                              (value.merged && value.showChild !== '1')
                                                ? value.displayName
                                                : ''}
                                            </span>
                                            <span className="su__position-relative">
                                              <input
                                                id={`tab-group-${value.Contentname}`}
                                                type="radio"
                                                name="tab-group"
                                                value={value.Contentname}
                                                defaultChecked={
                                                  contentDetails.activeTabIndex ===
                                                  value.Contentname
                                                }
                                                aria-current={
                                                  '' +
                                                  !!(
                                                    contentDetails.activeTabIndex ==
                                                      value.Contentname ||
                                                    (value.merged &&
                                                      value.showChild &&
                                                      value.filterList.indexOf(
                                                        contentDetails.contentName
                                                      ) > -1)
                                                  )
                                                }
                                                key={`${value.displayName}__${index}`}
                                                lang={variables.searchCallVariables.langAttr}
                                                tabIndex={tabIndexes.tabIndex_0}
                                                className={
                                                  value.Contentname +
                                                  ' su__tabs su__getClassName a11y-btn su__radio-btn  ' +
                                                  ((a &&
                                                    a.initialTab.activeTabIndex ==
                                                      value.Contentname) ||
                                                  (value.merged &&
                                                    value.showChild &&
                                                    value.filterList.indexOf(
                                                      a && a.initialTab.contentName
                                                    ) > -1)
                                                    ? 'initialTab'
                                                    : '')
                                                }
                                                onClick={(e) =>
                                                  getContentResults(e, item.key, value.Contentname)
                                                }
                                              />
                                              <label
                                                htmlFor={`tab-group-${value.Contentname}`}
                                                className={
                                                  value.Contentname +
                                                  ' su__tabs su__getClassName a11y-btn  su__radio-btn  su__radio-button-label ' +
                                                  ((a &&
                                                    a.initialTab.activeTabIndex ==
                                                      value.Contentname) ||
                                                  (value.merged &&
                                                    value.showChild &&
                                                    value.filterList.indexOf(
                                                      a && a.initialTab.contentName
                                                    ) > -1)
                                                    ? 'initialTab'
                                                    : '')
                                                }
                                              ></label>
                                            </span>
                                          </label>
                                          {value.merged && value.showChild === '1' ? (
                                            <div className="su__mergedFilter-CS-tab su__w-100 ">
                                              <div
                                                className="su__mergedFilter-CS-button su__font-16 su__dark-gray su__f-normal su__w-100 "
                                                onClick={() => {
                                                  handlemergetabs(value);
                                                }}
                                              >
                                                <span className="su__mergedFilter-CS-svg su__merged_tabs su__mergedtbmobile ">
                                                  {mergedtabs === value.Contentname ? (
                                                    <Icons
                                                      className="su__transfrom-180"
                                                      IconName="ArrowDown"
                                                      width="16"
                                                      height="16"
                                                      fill="#464646"
                                                      transform=""
                                                    />
                                                  ) : (
                                                    <Icons
                                                      IconName="ArrowDown"
                                                      width="16"
                                                      height="16"
                                                      fill="#464646"
                                                    />
                                                  )}
                                                </span>
                                                {value.merged && value.showChild === '1'
                                                  ? value.displayName
                                                  : ''}
                                              </div>
                                              <ul
                                                id={'su__MCS' + value.Contentname}
                                                className={`su__merge-options ${
                                                  mergedtabs === value.Contentname
                                                    ? ''
                                                    : 'su__d-none'
                                                }`}
                                              >
                                                {React.Children.toArray(
                                                  value.childArray.map((child, index) => (
                                                    <li
                                                      key={child.displayName || child.Contentname}
                                                      className={
                                                        child.Contentname +
                                                        ' su__merge-options-item radio-button-label su__flex-vcenter su__justify-content-between su__w-100 su__mergedtab-items'
                                                      }
                                                    >
                                                      <label
                                                        className={
                                                          'radio-button-label  su__w-100  su__flex-vcenter su__justify-content-between'
                                                        }
                                                        key={`${child.displayName}__${index}`}
                                                      >
                                                        <span className="su__font-16 su__line-height-19 su__dark-gray su__f-normal su__text-truncate">
                                                          {value.merged && value.showChild === '1'
                                                            ? child.displayName
                                                            : ''}
                                                        </span>
                                                        <span className="su__position-relative">
                                                          <input
                                                            id={`tab-group-${child.Contentname}`}
                                                            type="radio"
                                                            name="tab-group"
                                                            value={child.Contentname}
                                                            defaultChecked={
                                                              contentDetails.activeTabIndex ===
                                                              child.Contentname
                                                            }
                                                            aria-current={
                                                              '' +
                                                              (contentDetails.activeTabIndex ==
                                                                (value.merged &&
                                                                  value.showChild &&
                                                                  value.filterList.indexOf(
                                                                    contentDetails.contentName
                                                                  ) > -1))
                                                            }
                                                            key={`${child.displayName}__${index}`}
                                                            lang={
                                                              variables.searchCallVariables.langAttr
                                                            }
                                                            tabIndex={tabIndexes.tabIndex_0}
                                                            className={
                                                              child.Contentname +
                                                              ' su__tabs su__getClassName a11y-btn su__radio-btn  ' +
                                                              (value.merged &&
                                                              value.showChild &&
                                                              value.filterList.indexOf(
                                                                a && a.initialTab.contentName
                                                              ) > -1
                                                                ? 'initialTab'
                                                                : '')
                                                            }
                                                            onClick={(e) =>
                                                              getContentResults(
                                                                e,
                                                                item.key,
                                                                value.Contentname,
                                                                child.Contentname
                                                              )
                                                            }
                                                          />
                                                          <label
                                                            htmlFor={`tab-group-${child.Contentname}`}
                                                            className={
                                                              child.Contentname +
                                                              ' su__tabs su__getClassName a11y-btn  su__radio-btn  su__radio-button-label ' +
                                                              (child.merged &&
                                                              child.showChild &&
                                                              child.filterList.indexOf(
                                                                a && a.initialTab.contentName
                                                              ) > -1
                                                                ? 'initialTab'
                                                                : '')
                                                            }
                                                          ></label>
                                                        </span>
                                                      </label>
                                                    </li>
                                                  ))
                                                )}
                                              </ul>
                                            </div>
                                          ) : null}
                                        </div>
                                      </div>
                                    ))
                                  )}
                                </div>
                              </React.Fragment>
                            ))}
                      </div>
                      <div
                        id="SearchResult"
                        className={`hideFacets su__pb-3 su__word-break-all ${
                          mobilevisbilty == 'SearchResult' ? '' : 'su__d-none'
                        }`}
                      >
                        <SearchResultTile metadataKeys={metadataKeys} />
                      </div>
                    </>
                  )}
                </div>
                {isDeviceMobile && (
                  <div className="su__d-flex su__flex-column su__flex-1 su__justify-content-end">
                    <div className=" su__flex-hcenter  su__zindex-5">
                      <button
                        type="button"
                        lang={variables.searchCallVariables.langAttr}
                        role={a11y.ROLES.BTN}
                        tabIndex={tabIndexes.tabIndex_0}
                        className=" su__text-blue su__border-none su__cursor su__bg-transparent  su__font-14  su__line-height-22 su__text-underline  su__mb-1  su__loading-view ${color}  su__f-normal su__pb-3 su__pt-2"
                        onClick={openConfirmbox}
                      >
                        {t(StaticStrings.Set_to_default)}
                      </button>
                    </div>
                    <div className=" su__zindex-5 su__bottom su__flex-vcenter   su__w-100 su__h-44px su__bg-bright-white su__box-shadow su__radius-1 ">
                      <button
                        lang={variables.searchCallVariables.langAttr}
                        aria-label={t(StaticStrings.Close)}
                        type="button"
                        role={a11y.ROLES.BTN}
                        tabIndex={tabIndexes.tabIndex_0}
                        className=" su__border-none su__cursor   su__flex-1  su__h-100 su__bottom-buttons su__dark-gray su__radius-1 "
                        onClick={() => props.toggleEditMode(false)}
                      >
                        {' '}
                        {t(StaticStrings.cancel).toUpperCase()}{' '}
                      </button>
                      <div className="su__vertical-line"></div>
                      <button
                        lang={variables.searchCallVariables.langAttr}
                        aria-label={t(StaticStrings.Apply_Button)}
                        type="button"
                        role={a11y.ROLES.BTN}
                        tabIndex={tabIndexes.tabIndex_0}
                        className=" su__border-none su__cursor  su__flex-1 su__h-100 su__bottom-buttons su__royal-blue su__radius-1"
                        onClick={applyTheme}
                      >
                        {t(StaticStrings.Apply_Button)}
                      </button>
                    </div>
                  </div>
                )}
              </div>
              <div
                className="su__bg-overlay su__position-fixed"
                onClick={() => props.toggleEditMode(false)}
              ></div>
            </div>
          </Fragment>
        )}
        {/* confirm box start */}
        {isConfirm && (
          <Fragment>
            <div className="su__flex-hcenter su__position-fixed su__trbl-0 su__zindex-4 su__px-sm-1">
              <div
                id={A11Y_IDS.nested_trap}
                className="su__modal-inner su__radius su__animate-fadown su__zindex-1 su__shadow-lg su__bg-white su__radius-1 su__flex-hcenter su__flex-column su__facet-popup-width"
              >
                <div className="su__bookmark-popup-title su__px-4 su__py-3 su__flex-vcenter">
                  <div
                    lang={variables.searchCallVariables.langAttr}
                    className="su__font-14 su__text-black-shade su__f-regular su__flex-1 su__position-relative su__mb-3"
                  >
                    {t(StaticStrings.reset_changes)}
                  </div>
                </div>
                <div className="su__Conform-block su__flex-vcenter su__p-3 su__justify-content-end">
                  <button
                    lang={variables.searchCallVariables.langAttr}
                    tabIndex={tabIndexes.tabIndex_0}
                    className="su__px-4 su__py-2 su__mr-3 su__rtlml-3 su__rtlmr-0 su__radius su__cursor su__border_skyblue su__color-blue su__bg-bright-white su__line-height-n"
                    onClick={() => setToggleConfirm(false)}
                  >
                    {t(StaticStrings.su_no)}
                  </button>
                  <button
                    lang={variables.searchCallVariables.langAttr}
                    tabIndex={tabIndexes.tabIndex_0}
                    className="su__btn su__px-4 su__py-2 su__radius su__cursor su__color-white su__bg_theme_blue su__line-height-n"
                    onClick={reset}
                  >
                    {t(StaticStrings.su_yes)}
                  </button>
                </div>
              </div>
              <div className="su__overlay su__zindex-1" onClick={() => setToggleConfirm(false)} />
            </div>
          </Fragment>
        )}
        {/* confirm box end */}
      </Fragment>
    );
  } catch (e) {
    console.log('Error in facet-preference component', e);
    return <div></div>;
  }
};

export default FacetPreference;
