import React, { useState, useRef, useEffect } from 'react';
import variables from '../../../redux/variables';
import { useSelector } from 'react-redux';
import { any, func, string } from 'prop-types';
import { SC_IDS } from 'constants/constants';

const SandboxGptFeedback = ({ searchSuggest, gptResponse, setGptResponse }) => {
  const [selectedOptions, setSelectedOptions] = useState('');
  const [selectedFeedbackOptions, setSelectedFeedbackOptions] = useState('');
  const [selectedRadioValue, setSelectedRadioValue] = useState('');
  const [showHideWigetValue, setShowHideWigetValue] = useState(true);
  const [thanksModalValue, setThanksValue] = useState(false);
  const [thanksModalSpinner, setThanksModalSpinner] = useState(false);
  const [thanksModalError, setThanksModalError] = useState(false);

  const inputRef = useRef(null);
  // const [showHideSandboxFeedback, setShowHideSandboxFeedback] = useState(true);

  const gptContext = useSelector(
    (state) =>
      state.searchResult &&
      state.searchResult.searchClientSettings &&
      state.searchResult.searchClientSettings.gptConfig &&
      state.searchResult.searchClientSettings.gptConfig.gptContext
  );

  const handleRadioChange = (event) => {
    setSelectedRadioValue(event.target.value);
  };

  const handleOptionClick = (event) => {
    setSelectedOptions(event.target.value);
  };

  const handleFeedbackOptionClick = (event) => {
    setSelectedFeedbackOptions(event.target.value);
  };

  const submitSandboxFeedback = async () => {
    // setThanksValue(true);
    let queryPassed = {};
    queryPassed.uuid = variables.searchCallVariables.uid;
    if (
      variables.searchClientType == SC_IDS.WEB_APP ||
      variables.searchClientType == SC_IDS.ZENDESK_GUIDE ||
      variables.searchClientType == SC_IDS.ZENDESK_SUPPORT ||
      variables.searchClientType == SC_IDS.ZENDESK_SUPPORT_CONSOLE ||
      variables.searchClientType == SC_IDS.NICE_CXONE ||
      variables.searchClientType == SC_IDS.SHAREPOINT ||
      variables.searchClientType == SC_IDS.JOOMLA ||
      variables.searchClientType == SC_IDS.HIGHER_LOGIC_VANILLA
    ) {
      queryPassed.accessToken = variables.searchCallVariables.accessToken;
    }
    if (
      variables.searchClientType == SC_IDS.ZENDESK_GUIDE ||
      variables.searchClientType == SC_IDS.ZENDESK_SUPPORT ||
      variables.searchClientType == SC_IDS.ZENDESK_SUPPORT_CONSOLE
    ) {
      queryPassed.email = variables.searchCallVariables.email;
    }
    queryPassed.performance = selectedOptions;
    queryPassed.citationRelevance = selectedFeedbackOptions;
    queryPassed.responseIssue = selectedRadioValue;
    queryPassed.ExpectedResult =
      inputRef.current.value.length > 500
        ? inputRef.current.value.substring(0, 500)
        : inputRef.current.value;
    queryPassed.searchString = variables.searchCallVariables.searchString;
    queryPassed.gptContext = gptContext;
    queryPassed.context_id = variables.llmContextIdGpt;
    const regex = /<sup><button class='su_citation a11y-btn'[^>]*>.*?<\/button><\/sup>/g;
    let removedCitation = gptResponse.replace(regex, '');
    queryPassed.gptResponse = removedCitation;
    let url;
    let instanceName = variables.searchClientProps.instanceName;
    let searchEndpoint = '/resources/gptfeedback/add-feedback';
    url = `${instanceName}${searchEndpoint}`;

    let options = {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json; charset=UTF-8'
      },
      credentials: 'include',
      body: JSON.stringify(queryPassed)
    };
    setThanksModalSpinner(true);
    try {
      const response = await fetch(url, options);
      if (!response.ok) {
        throw new Error('Network response was not ok');
      }
      setThanksValue(true);
    } catch (e) {
      setThanksModalError(true);
      console.error('Error:', e);
    }
    setThanksModalSpinner(false);
    setSelectedOptions('');
    setSelectedFeedbackOptions('');
    setSelectedRadioValue('');
    if (inputRef.current) {
      inputRef.current.value = '';
    }
    setTimeout(() => {
      setShowHideWigetValue(false);
    }, 300);

    setTimeout(() => {
      setThanksValue(false);
      setThanksModalError(false);
    }, 1000);
    document.querySelectorAll('.su__sandboxfeedback-input').forEach((input) => {
      input.checked = false;
    });
    setGptResponse('');
  };

  const cancelSandboxFeedback = () => {
    setShowHideWigetValue(false);
  };

  useEffect(() => {
    setSelectedOptions('');
    setSelectedFeedbackOptions('');
    setSelectedRadioValue('');
    if (inputRef.current) {
      inputRef.current.value = '';
    }
  }, [gptResponse]);

  useEffect(() => {
    setShowHideWigetValue(true);
  }, [searchSuggest]);

  return (
    <>
      {
        <div>
          {thanksModalValue && (
            <div>
              <div className="su__feedshow-center su__bg-white  su__feedback-modal su__zindex-2">
                <div className="su__feedback-row su__text-center su__p-4">
                  <div className="su__svg-ok">
                    <svg
                      width="39"
                      height="39"
                      viewBox="0 0 39 39"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        id="Icon ionic-ios-checkmark-circle"
                        d="M19.6391 0.552002C15.8369 0.551804 12.1201 1.67908 8.95862 3.79127C5.79714 5.90347 3.333 8.90572 1.87779 12.4184C0.422592 15.931 0.0416915 19.7963 0.78326 23.5254C1.52483 27.2546 3.35556 30.6801 6.04394 33.3687C8.73233 36.0574 12.1576 37.8885 15.8867 38.6304C19.6157 39.3724 23.4811 38.9919 26.9939 37.537C30.5067 36.0822 33.5092 33.6184 35.6217 30.4571C37.7342 27.2959 38.8619 23.5792 38.8621 19.777C38.8626 17.2524 38.3657 14.7523 37.3999 12.4198C36.4341 10.0872 35.0182 7.96769 33.2331 6.18241C31.448 4.39712 29.3287 2.981 26.9962 2.01492C24.6637 1.04884 22.1637 0.551739 19.6391 0.552002ZM29.4821 14.462L17.1241 26.877H17.1151C16.8307 27.172 16.4477 27.3521 16.0391 27.383C15.6244 27.3527 15.2373 27.164 14.9581 26.856L9.78607 21.677C9.7509 21.6427 9.72296 21.6017 9.70387 21.5565C9.68479 21.5112 9.67496 21.4626 9.67496 21.4135C9.67496 21.3644 9.68479 21.3158 9.70387 21.2705C9.72296 21.2253 9.7509 21.1843 9.78607 21.15L11.4311 19.505C11.4644 19.4698 11.5046 19.4418 11.5491 19.4227C11.5936 19.4036 11.6416 19.3937 11.6901 19.3937C11.7385 19.3937 11.7865 19.4036 11.831 19.4227C11.8756 19.4418 11.9157 19.4698 11.9491 19.505L16.0491 23.605L27.3281 12.253C27.3617 12.2183 27.402 12.1906 27.4464 12.1715C27.4909 12.1524 27.5387 12.1424 27.5871 12.142C27.6357 12.1406 27.6841 12.1499 27.7288 12.169C27.7735 12.1882 27.8135 12.2169 27.8461 12.253L29.4631 13.926C29.5013 13.9588 29.5324 13.9993 29.5542 14.0447C29.576 14.0902 29.5882 14.1397 29.5899 14.19C29.5915 14.2404 29.5827 14.2906 29.564 14.3374C29.5453 14.3843 29.518 14.4267 29.4821 14.462Z"
                        fill="#5BB543"
                      />
                    </svg>
                  </div>
                  <div className="su__feed-title su__font-14 su__text-black su__word-break su__f-regular">
                    thanks for your feedback
                  </div>
                </div>
              </div>
              <div className="su__zindex-1 su__overlay "></div>
            </div>
          )}
          {thanksModalError ? (
            <>
              <div>
                <div className="su__feedshow-center su__bg-white  su__feedback-modal su__zindex-2">
                  <div className="su__feedback-row su__text-center su__p-4">
                    <div className="su__svg-ok">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="60"
                        height="60"
                        viewBox="0 0 24 24"
                      >
                        <path fill="currentColor" d="M11.001 10h2v5h-2zM11 16h2v2h-2z" />
                        <path
                          fill="currentColor"
                          d="M13.768 4.2C13.42 3.545 12.742 3.138 12 3.138s-1.42.407-1.768 1.063L2.894 18.064a1.99 1.99 0 0 0 .054 1.968A1.98 1.98 0 0 0 4.661 21h14.678c.708 0 1.349-.362 1.714-.968a1.99 1.99 0 0 0 .054-1.968zM4.661 19L12 5.137L19.344 19z"
                        />
                      </svg>
                    </div>
                    <div className="su__feed-title su__font-14 su__text-black su__word-break">
                      Something went wrong.
                    </div>
                  </div>
                </div>
                <div className="su__zindex-1 su__overlay "></div>
              </div>
            </>
          ) : null}
          {showHideWigetValue && (
            <>
              <div
                className={`su__w-100 su__font-12 su__bg-white su__p-4 su__my-4 su__sm-shadow su__radius-1`}
              >
                {thanksModalSpinner ? (
                  <div className="su__sandboxgpt-spinner" data-test-id="su__sandboxgpt-spinner">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="48"
                      height="48"
                      viewBox="0 0 24 24"
                    >
                      <path
                        fill="#494949"
                        d="M12,1A11,11,0,1,0,23,12,11,11,0,0,0,12,1Zm0,19a8,8,0,1,1,8-8A8,8,0,0,1,12,20Z"
                        opacity="0.25"
                      />
                      <path
                        fill="#a4a3a6"
                        d="M10.14,1.16a11,11,0,0,0-9,8.92A1.59,1.59,0,0,0,2.46,12,1.52,1.52,0,0,0,4.11,10.7a8,8,0,0,1,6.66-6.61A1.42,1.42,0,0,0,12,2.69h0A1.57,1.57,0,0,0,10.14,1.16Z"
                      >
                        <animateTransform
                          attributeName="transform"
                          dur="0.75s"
                          repeatCount="indefinite"
                          type="rotate"
                          values="0 12 12;360 12 12"
                        />
                      </path>
                    </svg>
                  </div>
                ) : null}
                <div className={`${thanksModalSpinner ? 'su__sandboxgpt-feedback-blur' : ''}`}>
                  <div className="su__text-center su__font-16 su__font-bold">
                    SearchUnifyGPT Feedback
                  </div>
                  <div className="su__d-flex su__mt-4">
                    <div className="su__w-45">How relevant was the generated response?</div>
                    <div className="su__w-55 su__d-flex su__align-items-start ">
                      <input
                        type="radio"
                        name="relevantResponseData"
                        value="Relevant"
                        className="su__mr-1 su__radius-1 su__border-none  su__cursor  su__bg-blue su__sandboxfeedback-input"
                        onChange={handleOptionClick}
                        checked={selectedOptions === 'Relevant'}
                      />{' '}
                      <label htmlFor="Relevant" className="su__mr-3">
                        Relevant
                      </label>
                      <input
                        type="radio"
                        name="relevantResponseData"
                        value="Irrelevant"
                        className="su__mr-1 su__radius-1 su__border-none  su__cursor  su__bg-blue su__sandboxfeedback-input"
                        onChange={handleOptionClick}
                      />
                      <label htmlFor="Irrelevant" className="su__mr-3">
                        Irrelevant
                      </label>
                      <input
                        type="radio"
                        name="relevantResponseData"
                        value="Misleading"
                        className="su__mr-1 su__radius-1 su__border-none  su__cursor  su__bg-blue su__sandboxfeedback-input"
                        onChange={handleOptionClick}
                      />
                      <label htmlFor="Misleading" className="su__mr-3">
                        Misleading
                      </label>
                      <input
                        type="radio"
                        name="relevantResponseData"
                        value="No response generated"
                        className="su__mr-1 su__radius-1 su__border-none  su__cursor su__bg-blue su__sandboxfeedback-input"
                        onChange={handleOptionClick}
                      />
                      <label htmlFor="No response generated">No response generated</label>
                    </div>
                  </div>
                  <div className="su__d-flex su__mt-4">
                    <div className="su__w-45">Issue with the response - </div>
                    <div className="su__w-55 ">
                      <span className="su__d-flex  su__align-items-start ">
                        <input
                          className="su__cursor su__mr-2 su__sandboxfeedback-input"
                          checked={
                            selectedRadioValue ===
                            'Generated response was relevant but top 5 search results were irrelevant'
                          }
                          type="radio"
                          name="issueWithResponse"
                          value="Generated response was relevant but top 5 search results were irrelevant"
                          onChange={handleRadioChange}
                        />{' '}
                        <label htmlFor="one">
                          Generated response was relevant but top 5 search results were irrelevant
                        </label>
                      </span>
                      <span className="su__d-flex su__mt-2  su__align-items-start">
                        <input
                          className="su__cursor su__mr-2 su__sandboxfeedback-input"
                          checked={
                            selectedRadioValue ===
                            'Both generated response and top 5 search results were irrelevant'
                          }
                          type="radio"
                          name="issueWithResponse"
                          value="Both generated response and top 5 search results were irrelevant"
                          onChange={handleRadioChange}
                        />{' '}
                        <label htmlFor="two">
                          Both generated response and top 5 search results were irrelevant
                        </label>
                      </span>
                      <span className="su__d-flex su__mt-2 su__align-items-start">
                        <input
                          className="su__cursor su__mr-2 su__sandboxfeedback-input"
                          checked={
                            selectedRadioValue ===
                            'Generated response was not perfect but close to expectation'
                          }
                          type="radio"
                          name="issueWithResponse"
                          value="Generated response was not perfect but close to expectation"
                          onChange={handleRadioChange}
                        />{' '}
                        <label htmlFor="three">
                          Generated response was not perfect but close to expectation
                        </label>
                      </span>
                      <span className="su__d-flex su__mt-2 su__align-items-start">
                        <input
                          className="su__cursor su__mr-2 su__sandboxfeedback-input"
                          type="radio"
                          name="issueWithResponse"
                          value="None of the above"
                          onChange={handleRadioChange}
                        />{' '}
                        <label htmlFor="four">None of the above</label>
                      </span>
                    </div>
                  </div>
                  <div className="su__d-flex su__mt-4">
                    <div className="su__w-45">Please share the expected document/response</div>
                    <div className="su__w-55 ">
                      <input
                        className="su__w-100 su__border-t su__search-facet-input su__p-0 su__sandboxfeedback-input"
                        type="text"
                        placeholder="Enter expected response or paste document link"
                        name="documentLink"
                        ref={inputRef}
                        maxLength="500"
                      />
                    </div>
                  </div>
                  <div className="su__d-flex su__mt-4">
                    <div className="su__w-45">Citations/reference links feedback</div>
                    <div className="su__w-55 su__d-flex  su__align-items-start">
                      <input
                        type="radio"
                        name="expectedDocumentResponse"
                        value="Relevant"
                        className="su__mr-2 su__radius-1 su__border-none su__px-2 su__py-1 su__cursor  su__bg-blue su__sandboxfeedback-input"
                        onChange={handleFeedbackOptionClick}
                      />
                      <label htmlFor="Relevant" className="su__mr-3">
                        Relevant
                      </label>
                      <input
                        type="radio"
                        name="expectedDocumentResponse"
                        value="Irrelevant"
                        className="su__mr-2 su__radius-1 su__border-none su__px-2 su__py-1 su__cursor  su__bg-blue su__sandboxfeedback-input"
                        onChange={handleFeedbackOptionClick}
                      />
                      <label htmlFor="Irrelevant" className="su__mr-3">
                        Irrelevant
                      </label>
                      <input
                        type="radio"
                        name="expectedDocumentResponse"
                        value="Some of them were relevant"
                        className="su__mr-2 su__radius-1 su__border-none su__px-2 su__py-1 su__cursor  su__bg-blue su__sandboxfeedback-input"
                        onChange={handleFeedbackOptionClick}
                      />
                      <label htmlFor="Some of them were relevant" className="su__mr-3">
                        Some of them were relevant
                      </label>
                    </div>
                  </div>
                  <div className="su__text-center">
                    <button
                      onClick={submitSandboxFeedback}
                      className="su__mt-4 su__bg-blue-grd su__text-white su__font-13  su__radius-1 su__border-none su__px-3 su__py-2 su__cursor su__d-inline-block su__sandboxfeedback-input"
                    >
                      Submit feedback
                    </button>
                    <button
                      onClick={cancelSandboxFeedback}
                      className="su__btn-gpt su__ml-15 su__bngpt-h su__sandboxfeedback-input su__w-85px"
                    >
                      Cancel
                    </button>
                  </div>
                </div>
              </div>
            </>
          )}
        </div>
      }
    </>
  );
};

SandboxGptFeedback.propTypes = {
  searchSuggest: any,
  gptResponse: string,
  setGptResponse: func,
  onSubmitForm: func
};

SandboxGptFeedback.defaultProps = {
  searchSuggest: {},
  gptResponse: '',
  setGptResponse: () => {}
};

export default SandboxGptFeedback;
