/* global jest, it, expect, describe, beforeEach, afterEach, beforeAll */
import React from 'react';
import { mount } from 'enzyme';
import { useTranslation } from 'react-i18next';
import SandboxGptFeedback from './index';

jest.mock('react-redux', () => ({
  useSelector: jest.fn()
}));
// Mock the gza function
global.gza = jest.fn();

jest.mock('react-i18next', () => ({
  useTranslation: jest.fn()
}));

jest.mock('../../../redux/variables', () => require('__mocks__/variables'));
// const SELECTORS = {
//   sandboxFeedback: "[data-test-id='su__sandboxgpt-spinner']"
// };
describe('SandboxGptFeedback', () => {
  let wrapper;
  const props = {
    thanksModalSpinner: true,
    setThanksModalSpinner: jest.fn(),
    showHideWigetValue: true,
    setShowHideWigetValue: jest.fn(),
    onSubmitForm: jest.fn()
  };

  beforeAll(() => {
    jest.setTimeout(10000);
  });
  beforeEach(() => {
    useTranslation.mockImplementation(() => ({ t: (key) => key }));
    jest.useFakeTimers();
    window.gza = jest.fn();
    wrapper = mount(<SandboxGptFeedback {...props} />);
    wrapper.update();
  });

  afterEach(() => {
    jest.clearAllTimers();
    jest.clearAllMocks();
    jest.resetModules();
  });
  it('should update the SandboxGptFeedback based on searchFeedback', () => {
    const wrapper = mount(<SandboxGptFeedback {...props} />);
    // After the effect runs, the state should be updated
    expect(wrapper.find(SandboxGptFeedback)).toHaveLength(1);
  });
  it('renders all radio buttons correctly', () => {
    expect(wrapper.find('input[type="radio"]').length).toBe(11); // Update count as per your radio buttons
  });

  it('renders a radio button with label "Relevant"', () => {
    const relevantRadio = wrapper.find('input[type="radio"][value="Relevant"]');
    expect(relevantRadio.exists()).toBe(true);

    const label = wrapper.find('label').filterWhere((node) => node.text() === 'Relevant');
    expect(label.exists()).toBe(true);
  });

  it("calls handleOptionClick when 'Relevant' is selected", () => {
    const wrapper = mount(<SandboxGptFeedback />);

    // Find the radio button and simulate change event
    wrapper
      .find('input[name="relevantResponseData"][value="Relevant"]')
      .first()
      .simulate('change', { target: { value: 'Relevant' } });

    wrapper.update();

    // Add a check to confirm if state changes (as a side effect)
    const inputElement = wrapper.find('input[name="relevantResponseData"][value="Relevant"]');
    expect(inputElement.prop('checked')).toBeTruthy(); // Check if it's selected
  });
  // it('should set thanksModalSpinner to true on submit and reset after request', async () => {
  //   // Mock fetch to prevent actual API calls
  //   global.fetch = jest.fn(() =>
  //     Promise.resolve({
  //       ok: true,
  //       json: () => Promise.resolve({ message: 'Success' }),
  //     })
  //   );

  //   // Ensure spinner is initially not visible
  //   expect(wrapper.find('.su__sandboxgpt-spinner').exists()).toBe(false);

  //   // Click the submit button
  //   wrapper.find('button').first().simulate('click');

  //   // Spinner should now be visible
  //   expect(wrapper.find('.su__sandboxgpt-spinner').exists()).toBe(true);

  //   // Wait for the async operation
  //   await new Promise((resolve) => setTimeout(resolve, 0));
  //   wrapper.update();

  //   // Spinner should disappear after request completes
  //   expect(wrapper.find('.su__sandboxgpt-spinner').exists()).toBe(false);

  //   // Cleanup fetch mock
  //   global.fetch.mockRestore();
  // });

  // it('should set thanksModalSpinner to true on submit and reset after request', async () => {
  //   const wrapper = mount(<SandboxGptFeedback {...props} />);

  //   // Ensure form is rendered
  //   expect(wrapper.find(SELECTORS.sandboxFeedback).exists()).toBe(true);

  //   await act(async () => {
  //     wrapper.find(SELECTORS.submitButton).simulate('click');
  //   });

  //   wrapper.update(); // Ensure updates are reflected

  //   // ✅ Check spinner state is set to true
  //   expect(wrapper.find(SELECTORS.spinner).exists()).toBe(true);

  //   // ✅ Wait for the fetch request and state update
  //   await act(async () => {
  //     await new Promise((resolve) => setTimeout(resolve, 100)); // Allow state update
  //   });

  //   wrapper.update(); // Ensure updates are reflected

  //   // ✅ Check spinner state is reset to false
  //   expect(wrapper.find(SELECTORS.spinner).exists()).toBe(false);
  // });
});
