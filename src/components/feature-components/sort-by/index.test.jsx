/* global jest, it, expect, describe, beforeEach */
import React from 'react';
import { shallow } from 'enzyme';
import { useDispatch } from 'react-redux';
import SortBy from './';
import variables from '../../../redux/variables';
import { search } from '../../../redux/ducks';
import Tooltip from 'components/feature-components/tooltip/index.jsx';
import Dropdown from '../dropdown/index'; // Ensure the correct path

jest.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key) => key
  })
}));

jest.mock('../../../redux/variables', () => ({
  searchCallVariables: {
    pagingAggregation: [],
    searchString: '',
    langAttr: 'en'
  },
  previousDymSearchString: '',
  searchSource: ''
}));

jest.mock('react-redux', () => ({
  useDispatch: jest.fn()
}));

jest.mock('../../../redux/ducks', () => ({
  search: {
    start: jest.fn()
  }
}));

describe('SortBy component', () => {
  let wrapper;
  const dispatch = jest.fn();

  beforeEach(() => {
    useDispatch.mockReturnValue(dispatch);
    variables.sortingOrder = {
      sortPreference: {
        keyLabelMapping: [
          { value: 'relevance', label: 'Relevance' },
          { value: 'date', label: 'Date' }
        ]
      }
    };
    variables.searchCallVariables = {
      sortby: 'relevance',
      langAttr: 'en',
      from: 0,
      pageNo: 1,
      pagingAggregation: []
    };
    wrapper = shallow(<SortBy />);
  });

  it('should render without crashing', () => {
    expect(wrapper.exists()).toBe(true);
  });

  it('should render the Tooltip component', () => {
    expect(wrapper.find(Tooltip).exists()).toBe(true);
  });

  it('should dispatch search action on changing the sort value', () => {
    // Simulate change in Dropdown
    const dropdown = wrapper.find(Dropdown);
    const handleChange = dropdown.prop('handleChange');
    handleChange({ target: { getAttribute: () => 'date', textContent: 'Date' } });
    expect(variables.searchCallVariables.sortby).toBe('date');
    expect(dispatch).toHaveBeenCalledWith(search.start(variables.searchCallVariables));
  });
});
