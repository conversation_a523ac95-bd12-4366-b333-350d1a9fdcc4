import React, { useState } from 'react';
import { useDispatch } from 'react-redux';
import variables from '../../../redux/variables';
import { search } from '../../../redux/ducks';
import { useTranslation } from 'react-i18next';
import Dropdown from '../dropdown/index';
import Tooltip from 'components/feature-components/tooltip/index.jsx';
import StaticStrings from '../../../StaticStrings';
const SortBy = () => {
  try {
    const { t } = useTranslation();
    /**
     * @param {sortby-value} Value
     */
    const handleChange = (e) => {
      let sortValue = e?.target?.getAttribute?.('value');
      variables.searchCallVariables.sortby = sortValue;
      variables.searchCallVariables.sortbylabel = e?.target?.textContent;
      variables.searchSource = 'sort-by';
      variables.searchCallVariables.from = 0;
      variables.searchCallVariables.pageNo = 1;
      variables.searchCallVariables.pagingAggregation = [];
      dispatch(search.start(variables.searchCallVariables));
    };
    const [showOvarlay, setShowoverlay] = useState(false);
    const dispatch = useDispatch();
    return (
      <div className="su__d-flex su__flex-hcenter su__mt-14px-mobile  su__sc-loading">
        <Tooltip
          text={t(StaticStrings.sort_by_label)}
          position="bottom"
          className="position-relative"
          tipClassName="su__position-absolute  su__zindex-1 su__bottom-unset su__min-w-80"
          tipMinWidth="su__min-w-80"
        >
          <Dropdown
            dropDownTitle={variables.searchCallVariables.sortby}
            dropdownItems={variables?.sortingOrder?.sortPreference?.keyLabelMapping}
            handleChange={handleChange}
            setShowoverlay={setShowoverlay}
          />
        </Tooltip>
        {showOvarlay && <div className="su__overlay-transparent su__zindex-1"></div>}
      </div>
    );
  } catch (e) {
    console.log('Error in sort by component', e);
    return <div></div>;
  }
};

export default SortBy;
