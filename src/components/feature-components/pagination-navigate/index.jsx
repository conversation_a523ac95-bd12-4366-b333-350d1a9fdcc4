import React, { Fragment } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import variables from '../../../redux/variables';
import { search } from '../../../redux/ducks';
import { usePagination } from '@material-ui/lab/Pagination';
import { useTranslation } from 'react-i18next';
import { a11y, tabIndexes } from '../../../constants/a11y';
import StaticStrings from 'StaticStrings';
import { v4 as uuid } from 'uuid';
import Icons from '../../../assets/svg-icon/svg';
import IconColors from 'IconColors';

const NavigatePagination = () => {
  try {
    const { t } = useTranslation();
    let searchSuggest = useSelector((state) => state.searchResult);
    let totalResults = Math.ceil(
      (Object.keys(searchSuggest).length && searchSuggest.result.total) /
        parseInt(variables.searchCallVariables.pageSize)
    );

    const pageChanged = (event, page) => {
      variables.searchCallVariables.pageNo = page;
      variables.searchCallVariables.from = (page - 1) * variables.searchCallVariables.pageSize;
      variables.searchCallVariables.pagingAggregation = [];
      variables.searchSource = 'pagination';
      dispatch(search.start(variables.searchCallVariables));
      let focus =
        document.querySelector(`[su-container]`) ||
        document.querySelector(`[ng-app = "searchUnifyApp"]`);
      window.scroll({
        top: focus.offsetTop,
        behavior: 'smooth'
      });
    };
    const { items } = usePagination({
      count: totalResults,
      siblingCount: 1,
      onChange: pageChanged,
      page: parseInt(variables.searchCallVariables.pageNo)
    });

    if (variables.searchCallVariables.pageNo) {
      items.forEach((element) => {
        if (element.page == variables.searchCallVariables.pageNo) {
          element.selected = true;
        } else {
          element.selected = false;
        }
      });
    }

    const dispatch = useDispatch();

    return (
      <Fragment>
        {searchSuggest?.result && totalResults > 1 ? (
          <div
            className="su__d-inline-block su__pagination-row su__pagination-section su__pagination_mobile su__mobile-pagination-mt-30px su__ml-10 su__mr-10"
            role={a11y.ROLES.NAVIGATION}
          >
            <ul
              tabIndex={tabIndexes.tabIndex_0}
              aria-label={StaticStrings.pagination}
              className="su__pagination su__justify-content-end su__loading-view a11y-pagination su__align-items-center su__sc-loading"
            >
              {items.map(({ page, type, selected, ...item }) => {
                let children;
                let items = { ...item };
                let disabled = items.disabled;
                if (type == 'previous') {
                  type = 'Previous';
                } else if (type == 'next') {
                  type = 'Next';
                }
                if (type === 'start-ellipsis' || type === 'end-ellipsis') {
                  children = (
                    <span
                      role={a11y.ROLES.BTN}
                      tabIndex={tabIndexes.tabIndex_0}
                      className="su__appearance-none"
                      type="button"
                      disabled
                    >
                      …
                    </span>
                  );
                } else if (type === 'page') {
                  children = (
                    <button
                      lang={variables.searchCallVariables.langAttr}
                      role={a11y.ROLES.BTN}
                      aria-label={`${t(StaticStrings.page)} ${t(page)}`}
                      tabIndex={tabIndexes.tabIndex_0}
                      type="button"
                      className={`a11y-btn  ${
                        selected ? 'su__pagination-active su__appearance-none' : ''
                      }`}
                      {...item}
                    >
                      {t(page)}
                    </button>
                  );
                } else {
                  children = (
                    <button
                      aria-label={t(type)}
                      role={a11y.ROLES.BTN}
                      tabIndex={tabIndexes.tabIndex_0}
                      className="su__appearance-none a11y-btn su__font-16"
                      type="button"
                      {...item}
                    >
                      {type === 'Previous' ? (
                        <div className="su__previousBtn su__rotate-180-rtl">
                          <Icons
                            className="su__pagination_previous"
                            IconName="PaginationLeftbtn"
                            width="8px"
                            height="12px"
                            color={disabled ? '' : IconColors.pagination_btn_fill}
                          />
                        </div>
                      ) : (
                        <div className="su__nextBtn su__rotate-180-rtl">
                          <Icons
                            className="su__pagination_next"
                            IconName="PaginationRightbtn"
                            width="8"
                            height="12"
                            color={disabled ? '' : IconColors.pagination_btn_fill}
                          />
                        </div>
                      )}
                    </button>
                  );
                }
                return (
                  <li key={uuid()}>
                    <span key={uuid()}>{page <= 500 ? children : null}</span>
                  </li>
                );
              })}
            </ul>
          </div>
        ) : null}
      </Fragment>
    );
  } catch (e) {
    console.log('Error in navigation paginaition component', e);
    return <div></div>;
  }
};

export default NavigatePagination;
