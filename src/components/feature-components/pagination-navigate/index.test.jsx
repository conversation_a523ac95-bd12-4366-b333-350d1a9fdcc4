/* global jest, it, expect, describe, beforeEach, afterEach */
import React from 'react';
import { shallow, mount } from 'enzyme';
import NavigatePagination from './';

jest.mock('react-redux', () => ({
  useSelector: jest.fn(),
  useDispatch: jest.fn()
}));

jest.mock('react-i18next', () => ({
  useTranslation: () => ({ t: (key) => key })
}));

jest.mock('@material-ui/lab/Pagination', () => ({
  usePagination: jest.fn()
}));

jest.mock('../../../redux/variables', () => ({
  searchCallVariables: {
    pageSize: 10,
    pageNo: 1,
    from: 0,
    langAttr: 'en'
  },
  searchSource: ''
}));

jest.mock('../../../redux/ducks', () => ({
  search: {
    start: jest.fn()
  }
}));

jest.mock('uuid', () => ({
  v4: jest.fn(() => 'mocked-uuid')
}));

describe('NavigatePagination Component', () => {
  let useSelector;
  let useDispatch;
  let usePagination;

  beforeEach(() => {
    useSelector = jest.spyOn(require('react-redux'), 'useSelector');
    useDispatch = jest.spyOn(require('react-redux'), 'useDispatch');
    usePagination = jest.spyOn(require('@material-ui/lab/Pagination'), 'usePagination');

    global.document.querySelector = jest.fn().mockReturnValue({ offsetTop: 100 });
    global.window.scroll = jest.fn();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders without crashing', () => {
    useSelector.mockReturnValue({ result: { total: 50 } });
    useDispatch.mockReturnValue(jest.fn());
    usePagination.mockReturnValue({ items: [] });
    const wrapper = shallow(<NavigatePagination />);
    expect(wrapper.exists()).toBe(true);
  });

  it('renders pagination when totalResults > 1', () => {
    useSelector.mockReturnValue({ result: { total: 50 } });
    useDispatch.mockReturnValue(jest.fn());
    usePagination.mockReturnValue({ items: [{ type: 'page', page: 1 }] });
    const wrapper = shallow(<NavigatePagination />);
    expect(wrapper.find('.su__pagination-row').exists()).toBe(true);
  });

  it('does not render pagination when totalResults <= 1', () => {
    useSelector.mockReturnValue({ result: { total: 5 } });
    useDispatch.mockReturnValue(jest.fn());
    usePagination.mockReturnValue({ items: [] });
    const wrapper = shallow(<NavigatePagination />);
    expect(wrapper.find('.su__pagination-row').exists()).toBe(false);
  });

  it('renders correct pagination items', () => {
    useSelector.mockReturnValue({ result: { total: 50 } });
    useDispatch.mockReturnValue(jest.fn());
    usePagination.mockReturnValue({
      items: [
        { type: 'previous', page: 0, ...{ onClick: jest.fn() } },
        { type: 'page', page: 1, ...{ onClick: jest.fn() } },
        { type: 'page', page: 2, ...{ onClick: jest.fn() } },
        { type: 'next', page: 0, ...{ onClick: jest.fn() } }
      ]
    });

    const wrapper = mount(<NavigatePagination />);
    expect(wrapper.find('button')).toHaveLength(4);
    expect(wrapper.find('button').at(1).text()).toBe('1');
    expect(wrapper.find('button').at(2).text()).toBe('2');
  });

  it('sets selected page correctly', () => {
    useSelector.mockReturnValue({ result: { total: 50 } });
    useDispatch.mockReturnValue(jest.fn());
    usePagination.mockReturnValue({
      items: [
        { type: 'page', page: 1, ...{ onClick: jest.fn() } },
        { type: 'page', page: 2, ...{ onClick: jest.fn() } }
      ]
    });

    const wrapper = mount(<NavigatePagination />);
    expect(wrapper.find('.su__pagination-active').text()).toBe('1');
  });

  it('should call dispatch with correct parameters when pageChanged is called', () => {
    useSelector.mockReturnValue({ result: { total: 50 } });
    const dispatchMock = jest.fn();
    useDispatch.mockReturnValue(dispatchMock);
    usePagination.mockReturnValue({
      items: [
        { page: 1, type: 'page', selected: false, onClick: jest.fn() },
        { page: 2, type: 'page', selected: false, onClick: jest.fn() },
        { type: 'next', onClick: jest.fn() }
      ],
      onChange: jest.fn()
    });

    const wrapper = mount(<NavigatePagination />);
    expect(wrapper.exists()).toBe(true);
    const pageChanged = usePagination.mock.calls[0][0].onChange;

    const event = { preventDefault: jest.fn() };

    pageChanged(event);

    expect(window.scroll).toHaveBeenCalledWith({
      top: 100,
      behavior: 'smooth'
    });
  });

  it('should show error when error caught', () => {
    jest.spyOn(require('react-i18next'), 'useTranslation').mockReturnValue({
      t: jest.fn(() => {
        throw new Error('Simulated translation error');
      })
    });

    const consoleLog = jest.spyOn(console, 'log').mockImplementation();
    const wrapper = shallow(<NavigatePagination />);
    expect(consoleLog).toHaveBeenCalledWith(
      'Error in navigation paginaition component',
      expect.any(Error)
    );
    expect(wrapper.html()).toBe('<div></div>');
    consoleLog.mockRestore();
  });
});
