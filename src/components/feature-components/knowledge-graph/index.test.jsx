/* global jest, it, expect, describe, beforeEach, afterEach */
import React from 'react';
import { mount } from 'enzyme';
import { useSelector } from 'react-redux';
import KnowledgeGraph from './';
import variables from '../../../redux/variables';
import StaticStrings from 'StaticStrings';
import state from '__mocks__/state';

jest.mock('react-redux', () => ({
  useSelector: jest.fn()
}));

jest.mock('react-i18next', () => ({
  useTranslation: () => ({ t: jest.fn((key) => key) })
}));

jest.mock('../../../redux/variables', () => require('__mocks__/variables'));

jest.mock('uuid', () => ({
  v4: jest.fn(() => 'uuid')
}));

describe('KnowledgeGraph', () => {
  let useSelectorMock;

  beforeEach(() => {
    useSelectorMock = useSelector;
    useSelectorMock.mockReturnValue(state.searchResult);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should render correctly', () => {
    const wrapper = mount(<KnowledgeGraph />);
    wrapper.update();
    expect(wrapper.exists()).toBe(true);
  });

  it('should display related tiles information', () => {
    const wrapper = mount(<KnowledgeGraph />);
    expect(wrapper.find('[data-test-id="su__tagLine_test"]').text()).toBe('Related Tile 1');
    expect(wrapper.find('[data-test-id="su__heading1_test"]').text()).toBe('Related Heading 1');
  });

  it('should handle feedback button clicks', () => {
    global.gza = jest.fn(); // Mock the global gza function
    let wrapper = mount(<KnowledgeGraph />);
    wrapper.update();
    const thumbsUp = wrapper.find('span.su__knowledge-feedback');
    const thumbsDown = wrapper.find('span.su__knowledge_downlikeicon');
    const feedbacks = [
      { btn: thumbsUp, feedbackVal: 1 },
      { btn: thumbsDown, feedbackVal: 0 }
    ];
    feedbacks.forEach((item) => {
      item.btn.simulate('click');
      expect(global.gza).toHaveBeenCalledWith(
        'knowledgeGraph',
        expect.objectContaining({
          searchString: variables.searchCallVariables.searchString,
          url: state.searchResult.metaGraph.link,
          t: state.searchResult.metaGraph.title,
          uid: variables.uid,
          feedback: item.feedbackVal
        })
      );
      global.gza.mockClear();
    });
  });

  it('should show thank you message after feedback', () => {
    const wrapper = mount(<KnowledgeGraph />);
    wrapper.find('.su__knowledge-feedback').first().simulate('click');
    wrapper.update();
    expect(wrapper.find(`[data-test-id="su__knowledge_thankyou"]`).text()).toBe(
      StaticStrings.thanks_response
    );
  });

  it('should show thank you message after feedback', () => {
    const wrapper = mount(<KnowledgeGraph />);
    wrapper.find('[data-test-id="su__knowledge_thumbsDown"]').simulate('click');
    wrapper.update();
    expect(wrapper.find(`[data-test-id="su__knowledge_thankyou"]`).text()).toBe(
      StaticStrings.thanks_response
    );
  });

  it('should show error when error caught', () => {
    jest.spyOn(require('react-i18next'), 'useTranslation').mockReturnValue({
      t: jest.fn(() => {
        throw new Error('Simulated translation error');
      })
    });

    const consoleLog = jest.spyOn(console, 'log').mockImplementation();
    const wrapper = mount(<KnowledgeGraph />);
    expect(consoleLog).toHaveBeenCalledWith(
      'Error in Knowledge graph component',
      expect.any(Error)
    );
    expect(wrapper.html()).toBe('<div></div>');
    consoleLog.mockRestore();
  });

  it('should handle errors gracefully', () => {
    jest.spyOn(console, 'log').mockImplementation(() => {});
    useSelectorMock.mockImplementation(() => {
      throw new Error('Test error');
    });

    const wrapper = mount(<KnowledgeGraph />);
    wrapper.update();
    expect(wrapper.html()).toBe('<div></div>');
    expect(console.log).toHaveBeenCalledWith(
      'Error in Knowledge graph component',
      expect.any(Error)
    );
  });
});
