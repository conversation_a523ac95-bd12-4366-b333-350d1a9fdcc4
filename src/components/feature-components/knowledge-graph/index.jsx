/* global gza */
/* eslint-disable react/jsx-key */
import React, { useState, Fragment, useEffect } from 'react';
import { useSelector } from 'react-redux';
import Icons from '../../../assets/svg-icon/svg';
import variables from '../../../redux/variables';
import { useTranslation } from 'react-i18next';
import StaticStrings from 'StaticStrings';
import { v4 as uuid } from 'uuid';
import { useDevice } from 'function-library/hooks';

const KnowledgeGraph = () => {
  try {
    /**
     *  Change Languages state
     */
    const { t } = useTranslation();
    let searchSuggest = useSelector((state) => state.searchResult);
    const { isDeviceMobile } = useDevice();
    const [knowledgeGraphResponseRecorded, setKnowledgeGraphFeedback] = useState(false);
    useEffect(() => {
      setKnowledgeGraphFeedback(false);
    }, [searchSuggest]);

    const sendKnowledgeGraphFeedback = (feedback, result) => {
      gza('knowledgeGraph', {
        searchString: variables.searchCallVariables.searchString,
        url: result.link,
        t: result.title || result.link,
        uid: variables.uid,
        feedback: feedback
      });
      setKnowledgeGraphFeedback(true);
    };
    return (
      <Fragment>
        {searchSuggest.metaGraph && Object.keys(searchSuggest.metaGraph).length !== 0 ? (
          <div className="">
            <div className="su__knowledge-boxShadow su__bg-white su__radius-1 su__p-10">
              <div className="su__px-2">
                <div className="su__d-inline-block su__mb-15px su__w-100 su__position-relative su__pb-0">
                  {searchSuggest.metaGraph && Object.keys(searchSuggest.metaGraph).length !== 0 ? (
                    <div className="su__overflow-hide su__truncate-two-lines">
                      <div className=" su__m-0 su__loading-view">
                        <a
                          className="su__text-decoration su__font-17 su__knowledge-head su__knowledge-head-line-height"
                          href={searchSuggest.metaGraph.link}
                          target="_blank"
                          rel="noopener noreferrer"
                        >
                          <span
                            dangerouslySetInnerHTML={{
                              __html: searchSuggest.metaGraph.title || searchSuggest.metaGraph.link
                            }}
                          ></span>
                        </a>
                      </div>
                    </div>
                  ) : null}
                  {searchSuggest.metaGraph.subtitle ? (
                    <div
                      className="su__mt-1 su__font-14 su__word-break su__loading-view"
                      dangerouslySetInnerHTML={{ __html: searchSuggest.metaGraph.subtitle }}
                    ></div>
                  ) : null}
                  {searchSuggest.metaGraph.link ? (
                    <div className="su__list-item-desc su__w-100 su__font-12 su__word-break su__loading-view su__text-truncate su__mt-6px">
                      <div className="su__text-decoration su__knowledgeGraph-meta-color  su__fontsize-13 su__line-height-22 su__text-truncate">
                        {searchSuggest.metaGraph.link}
                      </div>
                    </div>
                  ) : null}
                  {searchSuggest.metaGraph.img ? (
                    <div className="su__my-2 su__loading-view">
                      <img
                        className="su__img-fluid su__br-7"
                        src={searchSuggest.metaGraph.img}
                        alt={searchSuggest.metaGraph.img}
                      ></img>
                    </div>
                  ) : null}
                  {searchSuggest.metaGraph.description ? (
                    <div
                      className="su__list-item-desc su__w-100 su__mt-10 su__fontsize-14 su__line-height-22 su__word-break su__loading-view su__f-normal su__fill-black "
                      dangerouslySetInnerHTML={{ __html: searchSuggest.metaGraph.description }}
                    ></div>
                  ) : null}
                  {searchSuggest.metaGraph.metaFields ? (
                    <div className="su__meta-row" key={uuid()}>
                      {React.Children.toArray(
                        searchSuggest.metaGraph.metaFields.map((item) => (
                          <div className="su__mt-1 su__font-12 su__text-truncate" key={uuid()}>
                            <span className="su__knowledgeGraph-meta-color su__rtlmr-0 su__rtlml-2 su__loading-view su__font-12-22 su__f-medium su__text-capitalize">
                              {item.key}
                            </span>
                            :
                            <span
                              className="su__color-lgray su__loading-view su__font-12-22 su__ml-1 su__knowledgeGraph-meta-color"
                              dangerouslySetInnerHTML={{ __html: item.value }}
                            ></span>
                          </div>
                        ))
                      )}
                    </div>
                  ) : null}
                </div>
              </div>
              {searchSuggest.relatedTiles &&
              Object.keys(searchSuggest.relatedTiles).length &&
              searchSuggest.relatedTiles.length != 0 ? (
                <span className="su__relate_header su__px-2">{StaticStrings.Suggestions}</span>
              ) : null}

              {searchSuggest.relatedTiles &&
              Object.keys(searchSuggest.relatedTiles).length &&
              searchSuggest.relatedTiles.length !== 0
                ? React.Children.toArray(
                    searchSuggest.relatedTiles.map((item) => (
                      <div className="su__py-1" key={uuid()}>
                        <div className="su__w-100 su__px-2">
                          <div
                            className="su__font-12-22  su__knowledgeGraph-meta-color su__f-regular su__mr_4px "
                            data-test-id="su__tagLine_test"
                          >
                            {item.tagLine}
                          </div>
                          {item.relatedFields && item.relatedFields.length !== 0
                            ? React.Children.toArray(
                                item.relatedFields.map((item) => (
                                  <div key={uuid()} className="su__overflow-hide su__word-break">
                                    <div className="su__truncate-two-lines su__font-14 su__m-0 su__pl-0 su__rtlpr-0 su__loading-view">
                                      <a
                                        className={`su__font-12-22 su__text-hover-underline su__cursor su__color-blue ${
                                          !isDeviceMobile ? 'su__text-decoration' : null
                                        }`}
                                        data-test-id="su__heading1_test"
                                        href={item.link}
                                        target="_blank"
                                        rel="noopener noreferrer"
                                      >
                                        {item.heading1}
                                      </a>
                                    </div>
                                  </div>
                                ))
                              )
                            : null}
                        </div>
                      </div>
                    ))
                  )
                : null}
              {searchSuggest.metaGraph && Object.keys(searchSuggest.metaGraph).length !== 0 ? (
                <div className="su__d-inline-block su__w-100">
                  {!knowledgeGraphResponseRecorded ? (
                    <div className="su-featured-text su__flex-vcenter su__py-1 su__bg-blue-grd su__loading-view su__knowledge_feedback su__border_radius-3">
                      <div
                        lang={variables.searchCallVariables.langAttr}
                        className="su__line-height-22 su__mt-1 su__text-black-shade su__rtlmr-0 su__rtlmr-2 su__rtlml-2 su__loading-view su__mr-2"
                      >
                        <span className="su__fontsize-14">{t(StaticStrings.was_helpful)}?</span>
                      </div>
                      <div className="su__d-flex Knowledge_graph_feedback su__ml-3 su__rtlmr-3 su__rtlml-0">
                        <span
                          className="su__knowledge-feedback su__cursor su__loading-view"
                          data-test-id="su__knowledge-feedback"
                          onClick={() => sendKnowledgeGraphFeedback(1, searchSuggest.metaGraph)}
                        >
                          <Icons
                            IconName="Knowledge_graph_thumbsUp"
                            width="20"
                            height="20"
                            // color="#fff"
                            color="#2E2F30"
                            // color={IconColors.KnowledgeGraphThumbsup_down}
                          />
                        </span>
                        <span
                          className="su__knowledge_downlikeicon su__cursor su__loading-view"
                          data-test-id="su__knowledge_thumbsDown"
                          onClick={() => sendKnowledgeGraphFeedback(0, searchSuggest.metaGraph)}
                        >
                          <Icons
                            IconName="Knowledge_graph_thumbsDown"
                            width="20"
                            height="20"
                            color="#2E2F30"
                          />
                        </span>
                      </div>
                    </div>
                  ) : null}
                  {knowledgeGraphResponseRecorded ? (
                    <div className="su__knowledge-thankyou su__text-success su__font-14 su__p-3 su__position-relative su__loading-view">
                      <div className="su__flex-vcenter su__mr-2 su__rtlmr-0 su__font-12 su__rtlml-2">
                        <svg
                          className="ft-green-tick su__animate-zoom su__mr-2"
                          height="100"
                          width="100"
                          viewBox="0 0 48 48"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <circle className="su__circle" fill="#5bb543" cx="24" cy="24" r="22" />
                          <path
                            className="su__tick"
                            fill="none"
                            stroke="#FFF"
                            strokeWidth="6"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeMiterlimit="10"
                            d="M14 27l5.917 4.917L34 17"
                          />
                        </svg>
                        <span
                          lang={variables.searchCallVariables.langAttr}
                          className="su__pl-3 su__thankyou-text"
                          data-test-id="su__knowledge_thankyou"
                        >
                          {t(StaticStrings.thanks_response)}
                        </span>
                      </div>
                    </div>
                  ) : null}
                </div>
              ) : null}
            </div>
          </div>
        ) : null}
      </Fragment>
    );
  } catch (e) {
    console.log('Error in Knowledge graph component', e);
    return <div></div>;
  }
};

export default KnowledgeGraph;
