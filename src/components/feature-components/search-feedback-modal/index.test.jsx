/* global jest, it, expect, describe, beforeEach */
import React from 'react';
import { mount } from 'enzyme';
import { useSelector } from 'react-redux';
import state from '__mocks__/state';
import SearchResultFeedback from './index';
import { act } from 'react-dom/test-utils';
import variables from '__mocks__/variables';
const SELECTORS = {
  singleStepFeedbackModal: "[data-test-id='su__singleStepModal']",
  multiStepFeedbackModal: "[data-test-id='su__multiStepModal']"
};
jest.mock('react-redux', () => ({
  useSelector: jest.fn()
}));
jest.mock('react-i18next', () => ({
  useTranslation: () => ({ t: (key) => key })
}));
jest.mock('../../../redux/variables', () => require('__mocks__/variables'));
// Mock sessionStorage and localStorage
const mockSessionStorage = {};
const mockLocalStorage = {};
Object.defineProperty(window, 'sessionStorage', {
  value: {
    getItem: jest.fn((key) => mockSessionStorage[key]),
    setItem: jest.fn((key, value) => {
      mockSessionStorage[key] = value;
    }),
    removeItem: jest.fn((key) => {
      delete mockSessionStorage[key];
    })
  }
});

Object.defineProperty(window, 'localStorage', {
  value: {
    getItem: jest.fn((key) => mockLocalStorage[key]),
    setItem: jest.fn((key, value) => {
      mockLocalStorage[key] = value;
    }),
    removeItem: jest.fn((key) => {
      delete mockLocalStorage[key];
    })
  }
});

describe('SearchResultFeedback component', () => {
  let useSelectorMock;
  let setThanksModelMock;
  let setShowConversionMock;
  // let closeFeedbackModalMock;
  // let setInitialStateOfFormFieldsMock;
  let setOpenFeedbackMock;
  let wrapper;
  beforeEach(() => {
    jest.clearAllMocks();
    useSelectorMock = useSelector;
    window.gza = jest.fn();
    useSelectorMock.mockReturnValue(state.pageRatingResult);
    window.gza = jest.fn();
    setThanksModelMock = jest.fn();
    setShowConversionMock = jest.fn();
    // setInitialStateOfFormFieldsMock = jest.fn();
    // closeFeedbackModalMock = jest.fn();
    setOpenFeedbackMock = jest.fn();
    wrapper = mount(<SearchResultFeedback {...props} />);
  });
  const props = {
    isMultiStepQuestions: { isMultiForm: false, step: 0 },
    setisMultiStepQuestions: jest.fn(),
    isOpenFeedback: false,
    setOpenFeedback: jest.fn(),
    isThanksModel: { isOpen: false, message: '' },
    setThanksModel: jest.fn(),
    setclickBasedAutoTrigger: jest.fn(),
    setIsPopupDismissed: jest.fn(),
    setIsTooEarlyFeedbackClicked: jest.fn(),
    isTooEarlyFeedbackClicked: false,
    isSupportPageUrl: false,
    setisSearchFeedbackModal: jest.fn(),
    isPageRating: false,
    isShowConversion: false,
    setShowConversion: jest.fn(),
    setThankYouModal: jest.fn(),
    setThankyouShow: jest.fn()
  };

  it('should render the SearchResultFeedback component', () => {
    const wrapper = mount(<SearchResultFeedback {...props} />);

    // Assert that the component has been rendered
    expect(wrapper.exists()).toBe(true);
  });

  it('should show single Step Modal when supportPageUrl not configured', () => {
    sessionStorage.setItem('isFeedbackSubmitted', 'false');
    let newProps = {
      ...props,
      isMultiStepQuestions: { ...props.isMultiStepQuestions, isMultiForm: false },
      isOpenFeedback: true,
      isSupportPageUrl: false
    };
    const wrapper = mount(<SearchResultFeedback {...newProps} />);

    // Assert that the component has been rendered
    expect(wrapper.exists(SELECTORS.singleStepFeedbackModal)).toBe(true);
  });
  it('should show Multi Step Modal when supportPageUrl is configured', () => {
    sessionStorage.setItem('isFeedbackSubmitted', 'false');
    let newProps = {
      ...props,
      isMultiStepQuestions: { ...props.isMultiStepQuestions, isMultiForm: true },
      isOpenFeedback: true,
      isSupportPageUrl: true
    };
    const wrapper = mount(<SearchResultFeedback {...newProps} />);

    // Assert that the component has been rendered
    expect(wrapper.exists(SELECTORS.multiStepFeedbackModal)).toBe(true);
  });

  // it('should close the modal when close button is clicked', () => {
  //   sessionStorage.setItem('isFeedbackSubmitted', 'false');

  //   let newProps = {
  //     ...props,
  //     isMultiStepQuestions: { ...props.isMultiStepQuestions, isMultiForm: false },
  //     isOpenFeedback: true,
  //     isSupportPageUrl: false,
  //     setThanksModel: setThanksModelMock,
  //     setShowConversion: setShowConversionMock,
  //     closeFeedbackModal: closeFeedbackModalMock,
  //     setInitialStateOfFormFields: setInitialStateOfFormFieldsMock
  //   };

  //   const wrapper = mount(<SearchResultFeedback {...newProps} />);

  //   expect(typeof newProps.closeFeedbackModal).toBe('function'); // Ensure function exists

  //   const closeButton = wrapper.find('button.su__close-svg-feedback');
  //   expect(closeButton.exists()).toBe(true);
  //   expect(closeButton.props().onClick).toBeDefined();
  //   act(() => {
  //     closeButton.getDOMNode().click(); // Trigger click directly on the DOM node
  //   });
  //   // act(() => {
  //   //   closeButton.simulate('click');
  //   // });

  //   wrapper.update();

  //   //expect(setThanksModelMock).toHaveBeenCalledWith(expect.any(Function));
  //   //expect(setShowConversionMock).toHaveBeenCalledWith(false);
  //   //expect(closeFeedbackModalMock).toHaveBeenCalled();
  //   expect(setInitialStateOfFormFieldsMock).toHaveBeenCalled();
  // });
  it('should disable submit button when followupEmail is invalid', () => {
    sessionStorage.setItem('isFeedbackSubmitted', 'false');
    let newProps = {
      ...props,
      isMultiStepQuestions: { ...props.isMultiStepQuestions, isMultiForm: false },
      isOpenFeedback: true,
      isSupportPageUrl: false
    };
    const wrapper = mount(<SearchResultFeedback {...newProps} />);
    wrapper.setProps({ followupEmail: 'invalidEmail', isValid: false });
    expect(wrapper.find('button[disabled=true]').exists()).toBe(true);
  });

  // it('should enable submit button when followupEmail is valid', () => {
  //   sessionStorage.setItem('isFeedbackSubmitted', 'false');
  //   let newProps = {
  //     ...props,
  //     isMultiStepQuestions: { ...props.isMultiStepQuestions, isMultiForm: false },
  //     isOpenFeedback: true,
  //     isSupportPageUrl: false
  //   };
  //   const wrapper = mount(<SearchResultFeedback {...newProps} />);
  //   console.log(wrapper.debug(), 'Modal Rendered in valid9999'); // Debug the modal
  //   // wrapper.setProps({ followupEmail: '<EMAIL>', isValid: true });
  //   const emailInput = wrapper.find('input[type="email"]');
  //   emailInput.simulate('change', { target: { value: '<EMAIL>' } });

  //   // Wait for re-render
  //   act(() => {
  //     wrapper.update();
  //   });

  //   // Ensure props update after input change
  //   act(() => {
  //     wrapper.setProps({
  //       followupEmail: '<EMAIL>', // Send this to the email component
  //       isValid: true, // Ensure it's valid
  //       textAreaFeedback: 'test feedback', // Extra feedback for validation
  //       selectedOption: 'Yes' // Another valid option
  //     });
  //   });

  //   wrapper.update();

  //   expect(wrapper.find('button[disabled=true]').exists()).toBe(false);
  // });

  it('should handle Yes feedback selection', () => {
    // Mock sessionStorage values
    sessionStorage.setItem('isFeedbackSubmitted', 'false');
    sessionStorage.setItem('isTooEarlyFeedback', 'true');

    let newProps = {
      ...props,
      isMultiStepQuestions: { ...props.isMultiStepQuestions, isMultiForm: true },
      isOpenFeedback: true,
      isSupportPageUrl: true
    };

    const wrapper = mount(<SearchResultFeedback {...newProps} />);
    // Simulate the radio button selection
    const yesRadioButton = wrapper.find('#su__feed-yes');

    yesRadioButton.simulate('change', { target: { value: 'Yes' } });

    // Assert that the radio button is now checked
    expect(wrapper.find('#su__feed-yes').prop('checked')).toBe(true);
    expect(wrapper.find('#su__feed-no').prop('checked')).toBe(false);
  });

  it('should handle No feedback selection', () => {
    let newProps = {
      ...props,
      isMultiStepQuestions: { ...props.isMultiStepQuestions, isMultiForm: true },
      isOpenFeedback: true,
      isSupportPageUrl: true
    };

    const wrapper = mount(<SearchResultFeedback {...newProps} />);
    // Simulate the radio button selection
    const noRadioButton = wrapper.find('#su__feed-no');
    noRadioButton.simulate('change', { target: { value: 'No' } });

    // Assert that the radio button is now checked
    expect(wrapper.find('#su__feed-yes').prop('checked')).toBe(false);
    expect(wrapper.find('#su__feed-no').prop('checked')).toBe(true);
  });
  it('should not call onSubmitfeedback on submit without rating or text feedback', () => {
    const onSubmitfeedback = jest.fn();
    let newProps = {
      ...props,
      isMultiStepQuestions: { ...props.isMultiStepQuestions, isMultiForm: true },
      isOpenFeedback: true,
      isSupportPageUrl: true
    };
    const wrapper = mount(
      <SearchResultFeedback onSubmitfeedback={onSubmitfeedback} {...newProps} />
    );
    const button = wrapper.find('[type="button"]').at(1);
    button.simulate('click');
    expect(onSubmitfeedback).not.toHaveBeenCalled();
  });
  it('renders the thank you modal when isThanksModel.isOpen is true', () => {
    wrapper.setProps({ isThanksModel: { isOpen: true, message: 'Thanks!' } });
    wrapper.update();
    expect(wrapper.find('.thanksModal').exists()).toBe(true);
  });
  it('submits feedback and closes the modal when isSupportPageUrl is false', () => {
    jest.useFakeTimers();
    sessionStorage.setItem('isFeedbackSubmitted', 'false');

    let newProps = {
      ...props,
      isMultiStepQuestions: { ...props.isMultiStepQuestions, isMultiForm: false },
      isOpenFeedback: true,
      isSupportPageUrl: false,
      setOpenFeedback: setOpenFeedbackMock,
      isThanksModel: setThanksModelMock,
      isShowConversion: setShowConversionMock
    };
    // const closeThanksPopAfterTwoSecondsMock = jest.fn(); // Mock the function

    const wrapper = mount(<SearchResultFeedback {...newProps} />);

    // Simulate user interaction (e.g., provide rating)
    const emoticonButton = wrapper.find('#emoticons button').first();

    act(() => {
      emoticonButton.simulate('click', { target: emoticonButton.getDOMNode() });
    });

    // Simulate submit button click
    const submitButton = wrapper.find('button').last();

    act(() => {
      submitButton.simulate('click');
    });
    // Advance timers by 2 seconds (2000 milliseconds)
    act(() => {
      jest.advanceTimersByTime(2000);
    });
    //expect(closeThanksPopAfterTwoSecondsMock).toHaveBeenCalled();
    // Assertions
    expect(window.gza).toHaveBeenCalledTimes(1);
    expect(window.gza).toHaveBeenCalledWith(
      'searchfeedback',
      expect.objectContaining({
        uid: variables.searchCallVariables.uid,
        rating: 1,
        reported_by: variables.searchCallVariables.email,
        text_entered: variables.searchCallVariables.searchString,
        feedback: ''
      })
    );
    expect(setOpenFeedbackMock).toHaveBeenCalledWith(false);
    //expect(setThanksModelMock).toHaveBeenCalledWith(expect.any(Function));
    //expect(setShowConversionMock).toHaveBeenCalledWith(true);
    expect(sessionStorage.getItem('isFeedbackSubmitted')).toBe('true');
    jest.useRealTimers();
  });
  it('should modify getAllConversion when getPageRes is true', () => {
    sessionStorage.setItem('isFeedbackSubmitted', 'false');
    // Mock necessary variables
    variables.searchResultClicked = true;
    variables.visitedtitle = 'Test Title';
    variables.visitedUrl = 'https://example.com/test';
    variables.visiteRank = 5;
    variables.searchCallVariables.pageSize = 10;
    variables.searchCallVariables.pageNo = 2;

    // Mock other necessary props
    let newProps = {
      ...props,
      isMultiStepQuestions: { ...props.isMultiStepQuestions, isMultiForm: false },
      isOpenFeedback: true,
      isSupportPageUrl: false,
      setOpenFeedback: setOpenFeedbackMock
    };

    const wrapper = mount(<SearchResultFeedback {...newProps} />);

    // Simulate user interaction (e.g., provide rating)
    const emoticonButton = wrapper.find('#emoticons button').first();
    act(() => {
      emoticonButton.simulate('click', { target: emoticonButton.getDOMNode() });
    });

    // Simulate submit button click
    const submitButton = wrapper.find('button').last();
    act(() => {
      submitButton.simulate('click');
    });

    // Verify that gza was called with the modified getAllConversion
    expect(window.gza).toHaveBeenCalledWith(
      'searchfeedback',
      expect.objectContaining({
        conversion_title: 'Test Title',
        conversion_url: 'https://example.com/test',
        conversion_position: 5,
        pageSize: 10,
        page_no: 2
      })
    );

    // Reset variables
    variables.searchResultClicked = false;
    variables.visitedtitle = '';
    variables.visitedUrl = '';
    variables.visiteRank = 0;
    variables.searchCallVariables.pageSize = 0;
    variables.searchCallVariables.pageNo = 0;
  });
  it('should highlight the clicked emoticon', () => {
    sessionStorage.setItem('isFeedbackSubmitted', 'false');

    // Mock event target with parentElement
    const mockTarget = {
      classList: {
        add: jest.fn(),
        remove: jest.fn()
      },
      parentElement: {
        classList: {
          add: jest.fn(),
          remove: jest.fn()
        }
      }
    };

    // Mock document.querySelectorAll to return emoticons
    const mockEmoticons = [
      {
        classList: {
          add: jest.fn(),
          remove: jest.fn()
        },
        parentElement: {
          classList: {
            add: jest.fn(),
            remove: jest.fn()
          }
        }
      },
      {
        classList: {
          add: jest.fn(),
          remove: jest.fn()
        },
        parentElement: {
          classList: {
            add: jest.fn(),
            remove: jest.fn()
          }
        }
      }
    ];
    document.querySelectorAll = jest.fn().mockReturnValue(mockEmoticons);

    let newProps = {
      ...props,
      isMultiStepQuestions: { ...props.isMultiStepQuestions, isMultiForm: false },
      isOpenFeedback: true,
      isSupportPageUrl: false,
      setOpenFeedback: setOpenFeedbackMock
    };
    const wrapper = mount(<SearchResultFeedback {...newProps} />);

    // Find the first emoticon button
    const secondEmoticonButton = wrapper.find('#emoticons button').at(1);

    // Simulate a click on the emoticon button
    act(() => {
      secondEmoticonButton.simulate('click', { target: mockTarget });
    });

    // Assertions
    expect(document.querySelectorAll).toHaveBeenCalledWith('#emoticons button');
    expect(mockEmoticons[0].classList.remove).toHaveBeenCalledWith('su__emoji-white-icon0');
    expect(mockEmoticons[0].classList.add).toHaveBeenCalledWith('su__emoji-icon0');
    expect(mockEmoticons[0].parentElement.classList.remove).toHaveBeenCalledWith(
      'su__bg_theme_blue'
    );

    // Ensure correct icon is highlighted
    expect(mockTarget.classList.add).toHaveBeenCalledWith(`su__emoji-white-icon1`);
    expect(mockTarget.classList.remove).toHaveBeenCalledWith(`su__emoji-icon1`);
    expect(mockTarget.parentElement.classList.add).toHaveBeenCalledWith('su__bg_theme_blue');
  });
  it('should set isValid to true for a valid email', async () => {
    sessionStorage.setItem('isFeedbackSubmitted', 'false');

    let newProps = {
      ...props,
      isMultiStepQuestions: { ...props.isMultiStepQuestions, isMultiForm: false },
      isOpenFeedback: true,
      isSupportPageUrl: false
    };

    const wrapper = mount(<SearchResultFeedback {...newProps} />);
    const input = wrapper.find('input[type="email"]');
    await act(async () => {
      input.simulate('input', { target: { value: '<EMAIL>' } });
      input.simulate('change', { target: { value: '<EMAIL>' } });
      await new Promise((resolve) => setTimeout(resolve, 0));
    });

    wrapper.update(); // Ensure the DOM re-renders with the new state
    expect(wrapper.find('input[type="email"]').prop('value')).toBe('<EMAIL>');
    const emailInput = wrapper.find('EmailInput');
    expect(emailInput.prop('followupEmail')).toBe('<EMAIL>');
    expect(emailInput.prop('isValid')).toBe(true); // Ensure the state is properly set
  });

  it('should set isValid to false and clear followupEmail for an invalid email', async () => {
    sessionStorage.setItem('isFeedbackSubmitted', 'false');

    let newProps = {
      ...props,
      isMultiStepQuestions: { ...props.isMultiStepQuestions, isMultiForm: false },
      isOpenFeedback: true,
      isSupportPageUrl: false
    };

    const wrapper = mount(<SearchResultFeedback {...newProps} />);
    const input = wrapper.find('input[type="email"]');
    await act(async () => {
      input.simulate('input', { target: { value: 'testexample.com' } });
      input.simulate('change', { target: { value: 'testexample.com' } });
      await new Promise((resolve) => setTimeout(resolve, 0));
    });

    wrapper.update(); // Ensure the DOM re-renders with the new state
    expect(wrapper.find('input[type="email"]').prop('value')).toBe('testexample.com');
    const emailInput = wrapper.find('EmailInput');
    expect(emailInput.prop('followupEmail')).toBe('testexample.com');
    expect(emailInput.prop('isValid')).toBe(false); // Ensure the state is properly set
  });
});
