/* global LITHIUM, gza */
import React, { useState, useEffect, useRef } from 'react';
import Icons from '../../../assets/svg-icon/svg';
import Modal from 'components/feature-components/modal/index.jsx';

import { useSelector } from 'react-redux';
import variables from '../../../redux/variables';
import FeedbackConfirmation from 'components/section-components/feedback-confirmation/index.jsx';
import FeedbackSearch from 'components/section-components/feedback-search/index.jsx';
import FeedbackFollowUP from 'components/section-components/feedback-followup/index.jsx';
import IconColors from '../../../IconColors';
import { A11Y_IDS, useFocusTrap, a11y, tabIndexes } from '../../../constants/a11y';
import { useTranslation } from 'react-i18next';
import StaticStrings from 'StaticStrings';
import EmailInput from 'components/section-components/email-input/index';
import { SC_IDS } from 'constants/constants';

const YES = 'yes';
const NO = 'no';
const _YES = 'Yes';
const _NO = 'No';

const SearchResultFeedback = (props) => {
  try {
    const { t } = useTranslation();
    const [selectedOption, setSelectedOption] = useState('');
    const [rating, setRating] = useState(() => {
      return parseInt(sessionStorage.getItem('rating'), 10) || 0;
    });
    const [hoverRating, setHoverRating] = useState(0);
    const [textAreaFeedback, settextAreaFeedback] = useState('');
    const [followupEmail, setFollowupEmail] = useState('');
    const [isValid, setIsValid] = useState(false);
    const [isEmailPrefilled, setIsEmailPrefilled] = useState('');
    const [isShowConversion, setShowConversion] = useState(false);
    const [feedback, setFeedback] = useState(null);
    const [focusTrap] = useFocusTrap();
    const maxCharacters = 300;
    const thankYouPopUpRef = useRef(null);
    const emailRegex = new RegExp(/^[^\s@]+@[^\s@]+\.[^\s@]+$/);
    const {
      isMultiStepQuestions,
      setisMultiStepQuestions,
      isOpenFeedback,
      setOpenFeedback,
      isThanksModel,
      setThanksModel,
      setclickBasedAutoTrigger,
      setIsPopupDismissed,
      setIsTooEarlyFeedbackClicked,
      isTooEarlyFeedbackClicked,
      isSupportPageUrl,
      setisSearchFeedbackModal,
      isPageRating,
      setThankYouModal,
      setThankyouShow
    } = props;
    const isFeedbackSubmitted = sessionStorage.getItem('isFeedbackSubmitted') === 'true';
    let { pageRatingCustomization, searchFeedback } = useSelector(
      (state) => state.pageRatingResult
    );
    let pageRatingObj = {};
    let searchFeedbackObj = {};
    if (pageRatingCustomization) {
      pageRatingObj = JSON.parse(pageRatingCustomization);
    }
    if (searchFeedback) {
      searchFeedbackObj = JSON.parse(searchFeedback);
    }
    let { selectedAck, searchToggle, submitButton } = pageRatingObj;
    let {
      selectedSearchFollowUp,
      selectedSearchSubHeader,
      selectedSearchTemplate,
      selectedTextFeedback,
      selectedSearchAcknowledgement,
      selectedSearchHeader,
      selectedQuestionThree,
      selectedQuestionFour,
      selectedMinuteAutoTrigger,
      selectedBasedActivityAutoTrigger
    } = searchFeedbackObj;
    /**
     * highlightEmoticons - The function adds the highlighted emoticon class and removed the class for grey emoticon
     * @param {*} rate - It gives the rating given by the user
     */
    const highlightEmoticons = (rate, event) => {
      const svgElement = event && event.target;
      const emoticons = document.querySelectorAll('#emoticons button');
      emoticons.forEach((emoticon, index) => {
        const currentRate = index + 1;
        emoticon.classList.remove(`su__emoji-white-icon${currentRate - 1}`);
        emoticon.classList.add(`su__emoji-icon${currentRate - 1}`);
        emoticon.parentElement.classList.remove('su__bg_theme_blue');
      });

      // Highlight the clicked emoticon
      if (emoticons.length > 0) {
        svgElement.classList.add(`su__emoji-white-icon${rate - 1}`);
        svgElement.classList.remove(`su__emoji-icon${rate - 1}`);
        svgElement.parentElement.classList.add('su__bg_theme_blue');
      }
    };

    const handleSaveRating = (rate, event) => {
      highlightEmoticons(rate, event);
      setRating(rate);
    };
    useEffect(() => {
      if (variables.searchClientType === SC_IDS.KHOROS) {
        variables.getUserEmailId = LITHIUM ? LITHIUM.CommunityJsonObject.User.emailRef : '';
        if (variables.getUserEmailId.length > 0 && emailRegex.test(variables.getUserEmailId)) {
          setFollowupEmail(variables.getUserEmailId);
          setIsEmailPrefilled(variables.getUserEmailId);
          setIsValid(true);
        }
      } else {
        if (
          variables.searchCallVariables.email &&
          variables.searchCallVariables.email.length > 0 &&
          emailRegex.test(variables.searchCallVariables.email)
        ) {
          setFollowupEmail(variables.searchCallVariables.email);
          setIsEmailPrefilled(variables.searchCallVariables.email);
          setIsValid(true);
        }
      }
    }, [variables.searchCallVariables.email, variables.getUserEmailId]);

    useEffect(() => {
      if (isTooEarlyFeedbackClicked === true) {
        sessionStorage.setItem('isTooEarlyFeedback', isTooEarlyFeedbackClicked);
      } else {
        sessionStorage.removeItem('isTooEarlyFeedback', isTooEarlyFeedbackClicked);
      }
    }, [isTooEarlyFeedbackClicked]);

    /**
     * Handles the selection of a radio button option.
     * Updates the state with the selected option value.
     *
     * @param {string} option - The selected option value (e.g., 'Yes' or 'No').
     */
    const handleSelection = (option) => {
      setSelectedOption(option);
    };

    /**
     * Handles the "Yes" feedback selection.
     * Sets the "Yes" feedback state to true and "No" feedback state to false.
     * No parameters.
     * No return value.
     */
    const handleYesFeedback = () => {
      setFeedback(YES);
    };

    /**
     * Handles the "No" feedback selection.
     * Sets the "No" feedback state to true and "Yes" feedback state to false.
     * No parameters.
     * No return value.
     */
    const handleNoFeedback = () => {
      setFeedback(NO);
    };

    // stepper for showing the second form
    const handleNext = () => {
      setisMultiStepQuestions((prev) => {
        return { ...prev, isMultiForm: true, step: prev.step + 1 };
      });
    };

    const huddleOnChange = (event) => {
      if (emailRegex.test(event)) {
        setIsValid(true);
      } else {
        setIsValid(false);
        setFollowupEmail('');
      }
    };
    const closeThanksPopAfterTwoSeconds = () => {
      if (thankYouPopUpRef.current) {
        clearTimeout(thankYouPopUpRef.current);
      }
      thankYouPopUpRef.current = setTimeout(() => {
        setThanksModel((prev) => {
          return { ...prev, isOpen: false, message: '' };
        });
        setThanksModel((prev) => {
          return { ...prev, isOpen: false, message: '' };
        });
        setShowConversion(true);
        closeFeedbackModal();
      }, 2000);
    };

    const onSubmitfeedback = (e, stateSub = true, stateMain = false) => {
      if (isSupportPageUrl && (selectedOption === _YES || selectedOption === _NO)) {
        let getAllConversionsSPURL = {
          referer: document.referrer,
          window_url: window.location.href,
          uid: variables.searchCallVariables.uid,
          survey_viewed: true,
          resolution_satisfaction: selectedOption === _YES ? true : false,
          // resolution_satisfaction - yes, no, too early to comment
          potential_support_contact:
            selectedOption === _YES ? (feedback === YES ? true : false) : false,
          // potential_support_contact -  Would you have logged a case, if you couldn't find the relevant information?

          additional_feedback: textAreaFeedback,
          email: followupEmail ? followupEmail : ''
        };
        gza('survey_responses', getAllConversionsSPURL); //new GZA Function
      }

      //[SUPPORT PAGE URL NOT CONFIGURED] (OLD GZA FUNCTION WHICH WAS BEING CALLED ALREADY Needs to be called as It was being called earlier)
      let getAllConversion = {
        referer: document.referrer,
        window_url: window.location.href,
        uid: variables.searchCallVariables.uid,
        rating: rating,
        reported_by: followupEmail ? followupEmail : '',
        text_entered: variables.searchCallVariables.searchString,
        feedback: textAreaFeedback
      };
      let getPageRes = variables.searchResultClicked;
      if (getPageRes === true) {
        variables.searchResultClicked = true;
        getAllConversion['conversion_title'] = variables.visitedtitle;
        getAllConversion['conversion_url'] = variables.visitedUrl;
        getAllConversion['conversion_position'] = variables.visiteRank;
        getAllConversion['pageSize'] = variables.searchCallVariables.pageSize;
        getAllConversion['page_no'] = variables.searchCallVariables.pageNo;
      }
      if (selectedOption === 'Early' && rating) {
        setIsTooEarlyFeedbackClicked(true);
        sessionStorage.setItem('rating', rating);
      } else if (selectedOption === 'Early' && !rating) {
        setIsTooEarlyFeedbackClicked(true);
      } else {
        setIsTooEarlyFeedbackClicked(false);
      }
      gza('searchfeedback', getAllConversion);
      if (
        selectedOption === 'Yes' ||
        selectedOption === 'No' ||
        (!isSupportPageUrl && (rating || textAreaFeedback || followupEmail))
      ) {
        sessionStorage.setItem('isFeedbackSubmitted', 'true');
        localStorage.setItem('isSearchExpFeedback', 'true');
      }
      sessionStorage.removeItem('isFeedbackPopupDismissed'); // Remove popup dismissed state
      setisMultiStepQuestions((prev) => {
        return { ...prev, isMultiForm: false, step: 0 };
      });
      setShowConversion(stateMain);
      setThanksModel((prev) => {
        return { ...prev, isOpen: stateSub, message: '' };
      });
      setThankyouShow(true);
      closeThanksPopAfterTwoSeconds();
      setRating(0);
      setFollowupEmail(isEmailPrefilled.length > 0 ? isEmailPrefilled : '');
      settextAreaFeedback('');
      setSelectedOption(null);
      setOpenFeedback(false);
      setIsValid(isEmailPrefilled.length > 0 ? true : false);
    };

    const hanleMouseEnter = (index) => {
      setHoverRating(index);
    };

    const handleMouseLeave = () => {
      setHoverRating(0);
    };

    //Focus trap
    useEffect(() => {
      focusTrap(!isThanksModel.isOpen);
    }, [isThanksModel.isOpen]);

    const closeFeedbackModal = () => {
      setOpenFeedback(false);
      setisSearchFeedbackModal(false);
      setThankYouModal(false);
      setRating(0);
      if (!isFeedbackSubmitted) {
        sessionStorage.setItem('isFeedbackPopupDismissed', 'true'); // Set a flag in session storage
        localStorage.setItem('isFeedbackPopupDismissed', 'true');
        setIsPopupDismissed(true);
        setclickBasedAutoTrigger(0);
      } else {
        setIsPopupDismissed(false);
      }
      if (selectedOption == 'Early') {
        setIsTooEarlyFeedbackClicked(true);
      }
    };
    const shouldShowSingleStepModal =
      !isMultiStepQuestions.isMultiForm &&
      isOpenFeedback &&
      !isFeedbackSubmitted &&
      !isTooEarlyFeedbackClicked;

    const shouldShowMultiStepModal =
      (isMultiStepQuestions.isMultiForm && !isFeedbackSubmitted && isOpenFeedback) ||
      (isOpenFeedback && isTooEarlyFeedbackClicked);

    useEffect(() => {
      const shouldFocusTrap = shouldShowMultiStepModal || shouldShowSingleStepModal;
      focusTrap(shouldFocusTrap);
      return () => {
        focusTrap(false);
      };
    }, [shouldShowMultiStepModal, shouldShowSingleStepModal]);

    const sharedFeedbackProps = {
      questionTwo: selectedSearchSubHeader,
      questionFour: selectedQuestionFour,
      isSupportPageUrl,
      selectedOption,
      isValid,
      selectedTextFeedback,
      textAreaFeedback,
      settextAreaFeedback,
      isEmailPrefilled,
      followupEmail,
      huddleOnChange,
      setFollowupEmail,
      selectedMinuteAutoTrigger,
      selectedBasedActivityAutoTrigger
    };
    const setInitialStateOfFormFields = () => {
      settextAreaFeedback('');
      setFollowupEmail(isEmailPrefilled.length > 0 ? isEmailPrefilled : '');
      setFeedback(null);
      setSelectedOption('');
      setIsValid(isEmailPrefilled.length > 0 ? true : false);
      setisMultiStepQuestions((prev) => {
        return { ...prev, isMultiForm: false, step: 0 };
      });
    };
    return (
      <>
        {shouldShowSingleStepModal && (
          <Modal
            data-test-id="su__singleStepModal"
            className={`su__feedback-modal su__p_14px su__search_feedback_modal su__padding-modal-singleStep  ${
              isThanksModel.isOpen ? 'thxOpen' : 'thxClose'
            } ${
              (!isValid && followupEmail === '') || (isValid && followupEmail !== '')
                ? 'su__formnot-error'
                : 'su__formError'
            }`}
            isOpen={isShowConversion}
            hasOverlay={false}
            boxShadow={'0px 0px 20px 0px rgba(21, 105, 200, 0.30)'}
            closeModal={() => {
              setThanksModel((prev) => {
                return { ...prev, isOpen: false, message: '' };
              });
              setShowConversion(false);
              closeFeedbackModal();
              setInitialStateOfFormFields();
            }}
          >
            <div id={A11Y_IDS.trap} role={a11y.ROLES.DIALOG}>
              <button
                type="button"
                lang={variables.searchCallVariables.langAttr}
                aria-label={t(StaticStrings.close_popup)}
                role={a11y.ROLES.BTN}
                tabIndex={tabIndexes.tabIndex_0}
                className="a11y-btn su__close-svg-feedback su__position-absolute su__flex-vcenter su__bg-light-gray  su__bg-white-circle su__radius-50 su__cross_icon-fb_rtl"
                onClick={() => {
                  setThanksModel((prev) => {
                    return { ...prev, isOpen: false, message: '' };
                  });
                  setShowConversion(false);
                  closeFeedbackModal();
                  setInitialStateOfFormFields();
                }}
              >
                <Icons
                  className="su__close-icon su__cursor"
                  IconName="Close"
                  width="12"
                  height="12"
                  color={IconColors.SearchResultFeedbackCloseIcon}
                />
              </button>
              <div className="su__feedback-row">
                <FeedbackSearch
                  questionOne={selectedSearchHeader}
                  {...sharedFeedbackProps}
                  rating={rating}
                  questionTwo={selectedQuestionFour}
                  hoverRating={hoverRating}
                  hanleMouseEnter={hanleMouseEnter}
                  handleMouseLeave={handleMouseLeave}
                  handleSaveRating={handleSaveRating}
                  selectedSearchTemplate={selectedSearchTemplate}
                />
                <FeedbackFollowUP
                  {...sharedFeedbackProps}
                  selectedSearchFollowUp={selectedSearchFollowUp}
                  onSelectionChange={handleSelection}
                  isMultiStepQuestions={isMultiStepQuestions}
                  isValid={isValid}
                />
              </div>

              {/* Contionally rendering the Next and Submit button for the forms */}
              {selectedOption === _YES ? (
                <button
                  type="button"
                  lang={variables.searchCallVariables.langAttr}
                  aria-label={`${t(submitButton)} ${!rating ? t(StaticStrings.disabled_text) : ''}`}
                  onClick={() => {
                    if (selectedOption === 'Yes') {
                      isSupportPageUrl ? handleNext() : onSubmitfeedback();
                    }
                  }}
                  className={`su__feedback-btn su__hover-bg-blue su__text-white-hover su__font-12 su__mt-3 su__p-2 su__radius su__bg-white su__border su__btn su__border_skyblue su__text-blue ${
                    !selectedOption ? 'disabled-btn' : ''
                  }`}
                  disabled={!selectedOption}
                >
                  {isSupportPageUrl ? 'Next' : 'Submit'}
                </button>
              ) : (
                <button
                  type="button"
                  lang={variables.searchCallVariables.langAttr}
                  aria-label={`${t(StaticStrings.submit)} ${
                    !(rating || textAreaFeedback) || (followupEmail && !isValid)
                      ? t(StaticStrings.disabled_text)
                      : ''
                  }`}
                  onClick={
                    (followupEmail && isValid) ||
                    selectedOption === 'No' ||
                    (selectedOption === 'Early' && rating) ||
                    textAreaFeedback ||
                    (!isSupportPageUrl && rating) ||
                    followupEmail
                      ? onSubmitfeedback
                      : closeFeedbackModal
                  }
                  className={`su__feedback-btn su__hover-bg-blue su__text-white-hover su__font-12 su__mt-3 su__p-2 su__radius su__bg-white su__border su__btn su__border_skyblue su__text-blue ${
                    (followupEmail && !isValid) ||
                    !(
                      selectedOption ||
                      textAreaFeedback ||
                      followupEmail ||
                      (!isSupportPageUrl && rating)
                    )
                      ? 'disabled-btn'
                      : ''
                  }`}
                  disabled={
                    (followupEmail && !isValid) ||
                    !(
                      selectedOption ||
                      textAreaFeedback ||
                      followupEmail ||
                      (!isSupportPageUrl && rating)
                    )
                  }
                >
                  {t(StaticStrings.submit)}
                </button>
              )}
            </div>
          </Modal>
        )}

        {/* This below piece of code handles multi step feedback pop ups */}

        {shouldShowMultiStepModal && (
          <Modal
            data-test-id="su__multiStepModal"
            className={`su__feedback-modal su__padding-modal-multiStep ${
              isThanksModel.isOpen ? 'thxOpen' : 'thxClose'
            } ${
              (!isValid && followupEmail === '') || (isValid && followupEmail !== '')
                ? 'su__formnot-error'
                : 'su__formError'
            }`}
            isOpen={isShowConversion}
            hasOverlay={false}
            boxShadow={'0px 0px 20px 0px rgba(21, 105, 200, 0.30)'}
            closeModal={() => {
              setThanksModel((prev) => {
                return { ...prev, isOpen: false, message: '' };
              });
              setShowConversion(false);
              closeFeedbackModal();
              setInitialStateOfFormFields();
            }}
          >
            <>
              <div id={A11Y_IDS.trap} role={a11y.ROLES.DIALOG}>
                <button
                  type="button"
                  lang={variables.searchCallVariables.langAttr}
                  aria-label={t(StaticStrings.close_popup)}
                  role={a11y.ROLES.BTN}
                  tabIndex={tabIndexes.tabIndex_0}
                  className="a11y-btn su__close-svg-feedback su__position-absolute su__flex-vcenter su__bg-light-gray  su__bg-white-circle su__radius-50"
                  onClick={() => {
                    setThanksModel((prev) => {
                      return { ...prev, isOpen: false, message: '' };
                    });
                    setShowConversion(false);
                    closeFeedbackModal();
                    setInitialStateOfFormFields();
                  }}
                >
                  <Icons
                    className="su__close-icon su__cursor"
                    IconName="Close"
                    width="12"
                    height="12"
                    color={IconColors.SearchResultFeedbackCloseIcon}
                  />
                </button>
                {((isSupportPageUrl && isTooEarlyFeedbackClicked) ||
                  (isSupportPageUrl && isMultiStepQuestions.isMultiForm)) && (
                  <div className="su__feedback-row">
                    {isTooEarlyFeedbackClicked && !sessionStorage.getItem('rating') && (
                      <FeedbackSearch
                        questionOne={selectedSearchHeader}
                        {...sharedFeedbackProps}
                        rating={rating}
                        hoverRating={hoverRating}
                        hanleMouseEnter={hanleMouseEnter}
                        handleMouseLeave={handleMouseLeave}
                        handleSaveRating={handleSaveRating}
                        selectedSearchTemplate={selectedSearchTemplate}
                      />
                    )}
                    <FeedbackFollowUP
                      {...sharedFeedbackProps}
                      selectedSearchFollowUp={selectedSearchFollowUp}
                      onSelectionChange={handleSelection}
                      isTooEarlyFeedback={isTooEarlyFeedbackClicked}
                      isMultiStepQuestions={isMultiStepQuestions}
                      setisMultiStepQuestions={setisMultiStepQuestions}
                    />
                  </div>
                )}
                <div className="multiform-step-two">
                  <div className="su__feed-txtarea">
                    {((isSupportPageUrl && isTooEarlyFeedbackClicked && selectedOption === 'Yes') ||
                      (isSupportPageUrl &&
                        isMultiStepQuestions.isMultiForm &&
                        selectedOption === 'Yes')) && (
                      <>
                        <div
                          lang={variables.searchCallVariables.langAttr}
                          aria-label={t(selectedSearchFollowUp)}
                          className="su__feedback-label su__font-11 su__color-lgray feedback-question-three"
                        >
                          {t(selectedQuestionThree)}
                        </div>
                        <div className="su__feedradio-group">
                          <div className="su__feedradio-row su__flex-vcenter su__font-14">
                            <>
                              <input
                                lang="en"
                                aria-label="Yes"
                                tabIndex={tabIndexes.tabIndex_0}
                                type="radio"
                                id="su__feed-yess"
                                name="su__feedback-ques"
                                checked={feedback === YES}
                                value="Yes"
                                onChange={handleYesFeedback}
                                className="su__feedback-radio-btns"
                              />
                              <label
                                lang="en"
                                aria-label="Yes"
                                role="radio"
                                className="su__feed-labels su__cursor su__font-12 su__position-relative su__mr-2"
                                htmlFor="su__feed-yess"
                              >
                                Yes
                              </label>
                            </>
                            <>
                              <input
                                lang="en"
                                aria-label="No"
                                tabIndex={tabIndexes.tabIndex_0}
                                type="radio"
                                id="su__feed-noo"
                                name="su__feedback-ques"
                                checked={feedback === NO}
                                value="No"
                                onChange={handleNoFeedback}
                                className="su__feedback-radio-btns"
                              />
                              <label
                                lang="en"
                                aria-label="No"
                                role="radio"
                                className="su__feed-labels su__cursor su__font-12 su__position-relative su__mr-2"
                                htmlFor="su__feed-noo"
                              >
                                No
                              </label>
                            </>
                          </div>
                        </div>
                      </>
                    )}
                  </div>

                  <div className="su__w-100 su__feedback-textarea">
                    <div
                      lang={variables.searchCallVariables.langAttr}
                      aria-label={t(selectedTextFeedback)}
                      className="su__feedback-label su__font-11 su__color-lgray"
                    >
                      {t(selectedQuestionFour)}
                    </div>
                    <textarea
                      lang={variables.searchCallVariables.langAttr}
                      name="feeback-txtarea"
                      className="su__feedtext-area su__w-100 su__box-sizing"
                      id="su__feedtext-area"
                      rows="4"
                      value={textAreaFeedback}
                      maxLength={maxCharacters}
                      onChange={(e) => {
                        const value = !e.target.value.trim();
                        settextAreaFeedback(value ? '' : e.target.value);
                      }}
                      aria-label={t(StaticStrings.say_more)}
                    ></textarea>
                    <div className="su__feedback-charlimit">
                      {textAreaFeedback.length}/{maxCharacters}{' '}
                    </div>
                  </div>
                  <EmailInput
                    setFollowupEmail={setFollowupEmail}
                    huddleOnChange={huddleOnChange}
                    followupEmail={followupEmail}
                    tabIndex={tabIndexes.tabIndex_0}
                    isValid={isValid}
                  />
                </div>
                <button
                  type="button"
                  lang={variables.searchCallVariables.langAttr}
                  aria-label={`${t(StaticStrings.submit)} ${
                    !(feedback === YES || feedback === NO || textAreaFeedback) ||
                    (followupEmail && !isValid)
                      ? t(StaticStrings.disabled_text)
                      : ''
                  }`}
                  onClick={
                    (followupEmail && isValid) ||
                    feedback === YES ||
                    feedback === NO ||
                    selectedOption === _NO
                      ? onSubmitfeedback
                      : null
                  }
                  className={`su__feedback-btn su__hover-bg-blue su__text-white-hover su__font-12 su__mt-3 su__p-2 su__radius su__bg-white su__border su__btn su__border_skyblue su__text-blue ${
                    (followupEmail && !isValid) ||
                    !(feedback === YES || feedback === NO || selectedOption === _NO)
                      ? 'disabled-btn'
                      : ''
                  }`}
                  disabled={
                    (followupEmail && !isValid) ||
                    !(feedback === YES || feedback === NO || selectedOption === _NO)
                  }
                >
                  {t(StaticStrings.submit)}
                </button>
              </div>
            </>
          </Modal>
        )}

        {isThanksModel.isOpen && (
          <Modal
            className={'su__feedback-modal thanksModal'}
            isOpen={isThanksModel.isOpen}
            closeModal={() => {
              setThanksModel((prev) => {
                return { ...prev, isOpen: false, message: '' };
              });
              setShowConversion(true);
              closeFeedbackModal();
            }}
          >
            <FeedbackConfirmation
              selectedAck={selectedAck}
              searchToggle={searchToggle}
              selectedSearchAcknowledgement={selectedSearchAcknowledgement}
              isSearchFeedback={true}
              isThanksModel={isThanksModel}
              setThanksModel={setThanksModel}
              isPageRating={isPageRating}
            />
          </Modal>
        )}
      </>
    );
  } catch (e) {
    console.log('Error in page rating component', e);
    return <div></div>;
  }
};

export default SearchResultFeedback;
