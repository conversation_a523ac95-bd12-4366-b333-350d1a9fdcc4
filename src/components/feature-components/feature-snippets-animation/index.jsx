/* eslint-disable no-constant-condition */
import React, { useState, useEffect, useRef } from 'react';
import variables from '../../../redux/variables';
import { useSelector, useDispatch } from 'react-redux';
import { suGPTSearch } from 'redux/ducks';
import { useTranslation } from 'react-i18next';
import StaticStrings from 'StaticStrings';

import { a11y, tabIndexes } from '../../../constants/a11y';
import Tooltip from 'components/feature-components/tooltip/index.jsx';
import GptFeedbackPopUp from 'components/feature-components/gpt-feedback-popup/index.jsx';
import { animateText, readStream, checkForError } from '../../../function-library/commonFunctions';
import { EVENT_NAMES, EVENT_CODES } from 'constants/constants';
import { SVGS } from 'assets/svg-icon/index';

/**
 * FeaturedSnippetAnimation - AKA ( SU GPT )
 */
const FeaturedSnippetAnimation = ({
  setPreview,
  closeCitationModal,
  showPortal,
  modalRef,
  isDeviceMobile,
  isDeviceIpad,
  setGptResponse,
  setShowSandboxForm
}) => {
  try {
    const { t } = useTranslation();
    const dispatch = useDispatch();
    const POPUP_CLOSE_TIMEOUT = 2000;
    const FAIL_TEXT = 'Unable to generate';
    const EMPTY_SEARCH_STRING = 'Run a query to generate a response.';
    const [noAnswerArray, setnoAnswerArray] = useState([]);
    const [gptClassList, setGptClassList] = useState('');
    const [{ hasError, loader, printing }, setTriggers] = useState({
      loader: false,
      hasError: false,
      printing: false
    });
    const suGPTContentRef = useRef(null);
    const searchResult = useSelector((state) => state.searchResult);
    const gptContext = useSelector(
      (state) =>
        state.searchResult &&
        state.searchResult.searchClientSettings &&
        state.searchResult.searchClientSettings.gptConfig &&
        state.searchResult.searchClientSettings.gptConfig.gptContext
    );
    const gptLinks = useSelector(
      (state) =>
        state.searchResult &&
        state.searchResult.searchClientSettings &&
        state.searchResult.searchClientSettings.gptConfig &&
        state.searchResult.searchClientSettings.gptConfig.gptLinks
    );

    const {
      llmContextId: llmContextIdGpt,
      accessToken: accessTokenFromResponse,
      boardsArr: boardsArrResponse
    } = useSelector((state) => state.searchResult || {});

    const suGPTStream = useSelector((state) => state.suGPTStream);
    const [showMore, setShowMore] = useState(false);
    const [showCopy, setShowCopy] = useState(false);
    const [showPopup, setShowPopup] = useState(false);
    const [feedbackGivenlike, setFeedbackGivenlike] = useState(false);
    const [integerVal, setIntegerVal] = useState(0);
    const [feedbackGivendislike, setFeedbackGivendislike] = useState(false);
    const [likeBtn, setLikeBtn] = useState(false);
    const [showThankModal, setShowThankModal] = useState(false);
    const [buttonContent1, setButtonContent1] = useState('');
    const [buttonContent2, setButtonContent2] = useState('');
    const [suGPTSays, setSuGPTSays] = useState('');
    const [showLike, setShowLike] = useState(false);
    const [showDislike, setShowDislike] = useState(false);
    const [showMoreValid, setShowMoreValid] = useState(false);
    const [llmId, setLlmId] = useState('');
    const [errorObject, setErrorObject] = useState({
      show: false,
      statusCode: '',
      showRetry: false,
      message: ''
    });
    const latestEffectId = useRef(null);

    let animatorRefs = [];
    const scrollableAreaRef = useRef(null);
    variables.gptStreaming = true; // set it to true to enable stream ( multiple chunks of data instead of one )
    const closeTimeout = useRef(null);
    const ContentBoxRef = useRef(0);
    const copyTimeouts = useRef([]); // Track copy timeouts for cleanup
    const parser = new DOMParser();
    const [inputValue, setInputValue] = useState('');

    const SuGPTrespone = t(suGPTSays?.trim?.(), { defaultValue: suGPTSays?.trim?.() });

    // Cleanup all timeouts on unmount
    useEffect(() => {
      return () => {
        // Clear all copy timeouts
        copyTimeouts.current.forEach((timeoutId) => clearTimeout(timeoutId));
        copyTimeouts.current = [];

        // Clear close timeout
        if (closeTimeout.current) {
          clearTimeout(closeTimeout.current);
        }

        // Clear animator refs
        animatorRefs.forEach((ref) => clearTimeout(ref));
      };
    }, []);

    /**
     * This function closes modal after below mentioned timeout.
     * @param null
     */
    const scheduleClose = () => {
      clearCloseTimeOut();
      closeTimeout.current = setTimeout(() => {
        closeCitationModal();
      }, 50);
    };

    /**
     * This function clears setTimeOut that is currently running if user pointer moves over modal
     */
    const clearCloseTimeOut = () => {
      clearTimeout(closeTimeout.current);
    };

    useEffect(() => {
      if (hasError) {
        setGptClassList(() => 'su__noResult_container');
      } else {
        setGptClassList(() => (printing ? 'su__typing_annimation' : 'su__position-relative'));
      }
      setShowSandboxForm(!printing && !loader && !hasError);
    }, [hasError, printing]);

    /**
     * handleHoverStates - It opens citation preview modal if user hovers on citation svg
     * @param {void}
     * @returns {void}
     */

    useEffect(() => {
      const handleHoverStates = (event) => {
        if (!isDeviceMobile && !isDeviceIpad) {
          if (
            event.target.tagName === 'BUTTON' &&
            event.target.classList.contains('su_citation') &&
            event.target.hasAttribute('data-url')
          ) {
            clearCloseTimeOut();
            setPreview(event);
          } else if (modalRef?.current?.contains?.(event.target)) {
            clearCloseTimeOut();
          } else if (showPortal && event.keyCode !== EVENT_CODES.TAB) {
            clearCloseTimeOut();
            scheduleClose();
          }
        }
      };
      document.addEventListener('mouseover', handleHoverStates);
      document.addEventListener('focusin', handleHoverStates);
      document.addEventListener('focusout', handleHoverStates);
      window.addEventListener('scroll', closeCitationModal);
      return () => {
        document.removeEventListener('mouseover', handleHoverStates);
        document.removeEventListener('focusin', handleHoverStates);
        document.removeEventListener('focusout', handleHoverStates);
        window.removeEventListener('scroll', closeCitationModal);
      };
    }, [isDeviceMobile, isDeviceIpad, showPortal, setPreview, modalRef, closeCitationModal]);

    /**
     * handleClickStates - It works for mobile view and open citation preview on click
     * @param {void}
     * @returns {void}
     */
    useEffect(() => {
      const handleClickStates = (event) => {
        if (isDeviceMobile || isDeviceIpad) {
          if (
            event.target.tagName === 'BUTTON' &&
            event.target.classList.contains('su_citation') &&
            event.target.hasAttribute('data-url')
          ) {
            setPreview(event);
          }
        }
      };
      document.removeEventListener('click', handleClickStates);
      document.addEventListener('click', handleClickStates);
      return () => document.removeEventListener('click', handleClickStates);
    });
    /**
     * CloseCitationModalOnOutsideClick - This function closes the modal in mobile view if user clicks outside
     * the modal.
     * @param {void}
     * @returns {void}
     */

    useEffect(() => {
      const CloseCitationModalOnOutsideClick = (event) => {
        if (
          // event.target !== modalRef.current &&
          (isDeviceMobile || isDeviceIpad) &&
          !event.target.classList.contains('su_citation') &&
          !modalRef?.current?.contains?.(event.target)
        ) {
          closeCitationModal();
        }
      };
      document.removeEventListener('click', CloseCitationModalOnOutsideClick);
      document.addEventListener('click', CloseCitationModalOnOutsideClick);
      return () => document.removeEventListener('click', CloseCitationModalOnOutsideClick);
    }, [modalRef.current]);

    const removeCitationButtons = (htmlContent) => {
      const regex = /<sup><button class="su_citation a11y-btn"[^>]*>.*?<\/button><\/sup>/g;
      return htmlContent.replace(regex, '');
    };

    const removeCopyToClipboard = (htmlContent) => {
      // Used to remove copy to clipboard text from copied text
      const regex =
        /<span class="su__copied-span su__d-flex su__gap-5 su__mr-10px su__mb-0"[^>]*>[\s\S]*?<\/span>/g;
      const regex2 = /<span class="su__SUgpt-tooltiptext"[^>]*>[\s\S]*?<\/span>/g;
      return htmlContent.replace(regex, '').replace(regex2, '');
    };

    const convertToMarkdown = (html) => {
      // Replace links
      html = html.replace(/<a href="(.*?)">(.*?)<\/a>/g, '[$2]($1)');

      // Replace unordered lists (supports nested lists)
      html = html.replace(/<ul>(.*?)<\/ul>/gs, (_, innerHtml) => {
        return innerHtml.replace(/<li>(.*?)<\/li>/gs, '• $1\n');
      });

      // Replace ordered lists (supports nested lists)
      html = html.replace(/<ol>(.*?)<\/ol>/gs, (_, innerHtml) => {
        let count = 1;
        return innerHtml.replace(/<li>(.*?)<\/li>/gs, (__, liContent) => {
          return `${count++}. ${liContent.trim()}\n`;
        });
      });

      // Replace paragraph tags
      html = html.replace(/<p>(.*?)<\/p>/g, '\n$1\n');
      html = html.replace(/<li>/g, '<br>$1');

      html = html.replace(/<pre>(.*?)<\/pre>/gs, '```\n$1\n```');

      html = html.replace(/<blockquote>(.*?)<\/blockquote>/gs, (_, content) => {
        return content
          .split('\n')
          .map((line) => `> ${line.trim()}`)
          .join('\n');
      });

      html = html.replace(/<br\s*\/?>/g, '\n');

      html = html.replace(/<[^>]+>/g, '');

      return html.trim();
    };

    const handleCopyToClipboard = async (sugptFollowUp = false) => {
      if (suGPTContentRef.current) {
        try {
          let contentToCopy = suGPTContentRef.current.innerHTML;
          contentToCopy = removeCitationButtons(contentToCopy);
          contentToCopy = removeCopyToClipboard(contentToCopy);
          const textCopied = convertToMarkdown(contentToCopy);
          const processedText = textCopied
            .split('\n')
            .filter((line) => line.trim() !== '')
            .join('\n')
            .trim();

          const parsedText = parser.parseFromString(processedText, 'text/html');
          const ParsedTextContent = parsedText.documentElement.textContent;

          await navigator.clipboard.writeText(ParsedTextContent.trim());
          if (typeof sugptFollowUp == 'boolean' && sugptFollowUp) {
            return ParsedTextContent.trim();
          } else {
            setShowCopy(true);
            const timeoutId = setTimeout(() => {
              setShowCopy(false);
            }, 1000);
            // Store timeout for potential cleanup if component unmounts
            return () => clearTimeout(timeoutId);
          }
        } catch (error) {
          console.error('Error copying text to clipboard:', error);
        }
      } else {
        console.error('suGPTContentRef is not available.');
      }
    };

    useEffect(() => {
      if (feedbackGivenlike) {
        setIntegerVal(0);
      } else {
        setIntegerVal(1);
      }
    }, [feedbackGivenlike]);

    const handleCheckForError = (inputString, errorArray) => {
      checkForError(inputString, errorArray, setTriggers);
    };

    const handlePopupAction = (action, feedback, feedbackTags) => {
      if (action === 'close') {
        closePopup();
      }
      if (action === 'openthanks') {
        sendFeedbackAnalytics(feedback, feedbackTags);
        showThankyouModal();
        closePopupAfterDelay();
        handleLikeReaction();
      }
    };

    const closePopup = () => {
      setShowPopup(false);
    };

    const sendFeedbackAnalytics = (feedback, feedbackTags) => {
      let gptResponseWithoOutCitation = suGPTContentRef?.current?.innerHTML;
      gptResponseWithoOutCitation = removeCitationButtons(gptResponseWithoOutCitation);
      window.gza('llm_response_feedback', {
        uid: variables.searchCallVariables.uid,
        sid_session: variables.searchCallVariables.sid,
        text_entered: variables.searchCallVariables.searchString,
        llm_response: gptResponseWithoOutCitation,
        feedback: (feedback && feedback.trim()) || '',
        reaction: integerVal,
        email: variables.searchCallVariables.email || window.su_utm,
        feedback_tags: feedbackTags || []
      });
    };

    const showThankyouModal = () => {
      setShowPopup(true);
      setShowThankModal(true);
    };

    const closePopupAfterDelay = () => {
      const timeoutId = setTimeout(() => {
        setShowPopup(false);
      }, POPUP_CLOSE_TIMEOUT);

      // Store timeout ID for potential cleanup
      return timeoutId;
    };

    const handleLikeReaction = () => {
      if (integerVal === 0) {
        setShowLike(true);
      } else if (integerVal === 1) {
        setShowDislike(true);
      }
    };
    // const breakCheck = (effectId) => {
    //   const exit = effectId != latestEffectId.current;
    //   // clear old animator refs from memory
    //   exit && animatorRefs.forEach((ref) => clearTimeout(ref));
    //   // return exit flag
    //   return exit;
    // };

    /**
     * animateText - typing animation for custom text
     * @param {string} str - input string to animate
     * @param {*} effectId - unique ID for current run
     */
    const handleAnimateText = (str, effectId) => {
      setShowMore(true);
      // Use the shared utility function with component-specific parameters
      animateText(
        str,
        effectId,
        latestEffectId,
        animatorRefs,
        setTriggers,
        setSuGPTSays,
        FAIL_TEXT,
        handleCheckForError,
        noAnswerArray
      );
      // Add extra component-specific actions after animation
      setTimeout(addCopyButton, 100);
    };

    const openModal = () => {
      setShowPopup(true);
      setLikeBtn(true);
      setShowThankModal(false);
      setFeedbackGivenlike(true);
      setFeedbackGivendislike(false);
      setButtonContent1('Accurate');
      setButtonContent2('Comprehensive');
    };
    const openModalDislike = () => {
      setShowPopup(true);
      setLikeBtn(false);
      setShowThankModal(false);
      setFeedbackGivenlike(false);
      setFeedbackGivendislike(true);
      setButtonContent1('Offensive');
      setButtonContent2('Incorrect');
    };

    /**
     * The effect listens to search calls and conditionally dispatches GPT API call action
     */
    useEffect(() => {
      /**
       * gptRoutine - triggers GPT routine after search response
       * @param {*} effectId - unique ID for current run
       * @returns {void}
       */
      async function gptRoutine() {
        // Allowed to make calls only on page number 1
        setShowSandboxForm(false);
        if (String(variables.searchCallVariables.pageNo) == '1' && searchResult) {
          setTriggers((prev) => ({ ...prev, hasError: false, loader: true }));
          setSuGPTSays(() => '');
          variables.gptContext = gptContext;
          variables.gptLinks = gptLinks;
          variables.llmContextIdGpt = llmContextIdGpt;
          variables.accessToken = accessTokenFromResponse;
          variables.boardsArr = boardsArrResponse;
          dispatch(suGPTSearch.start());
          setShowLike(false);
          setShowDislike(false);
          // }
        }
        if (String(variables.searchCallVariables.pageNo) !== '1' && searchResult) {
          setShowMore(true);
          setInputValue('');
        }
      }
      // Create a random unique ID
      const effectId = Math.random();
      latestEffectId.current = effectId;
      // EARLY EXIT - if search string is empty render a custom text
      if (variables.searchCallVariables.searchString == '' && searchResult) {
        setTriggers((prev) => ({ ...prev, hasError: true }));
        handleAnimateText(EMPTY_SEARCH_STRING, effectId);
        return;
      }
      /* Start GPT routine */
      gptRoutine();
      setGptResponse(suGPTSays);
      setShowMoreValid(false);
    }, [searchResult]);

    /**
     * An effect that reads gpt stream
     */
    useEffect(() => {
      if (!suGPTStream || variables.searchCallVariables.searchString == '') return;
      const effectId = Math.random();
      latestEffectId.current = effectId;
      if (suGPTStream.includes?.('ERROR_GENERATING')) {
        setTriggers((prev) => ({ ...prev, loader: false, hasError: true }));
        handleAnimateText(FAIL_TEXT, effectId);
        return;
      }

      /**
       * handleReadStream - reads gpt stream
       * @param {*} effectId - unique ID for current run
       * @returns {void}
       */
      const handleReadStream = (effectId) => {
        const llmIdRef = { current: null };
        readStream(
          suGPTStream,
          effectId,
          latestEffectId,
          animatorRefs,
          setTriggers,
          setSuGPTSays,
          FAIL_TEXT,
          handleCheckForError,
          null,
          (jsonData) => {
            if (!llmIdRef.current) {
              const llmRequestId = jsonData && jsonData.data && jsonData.data.id;
              if (llmRequestId) {
                setLlmId(llmRequestId);
                llmIdRef.current = llmRequestId;
              }
            }
          },
          setErrorObject,
          setnoAnswerArray
        ).then(() => {
          addCopyButton();
        });
      };

      try {
        // clear old animator refs from memory
        animatorRefs.forEach((ref) => clearTimeout(ref));
        // add fresh entry of animator ref to memory
        animatorRefs.push(setTimeout(() => handleReadStream(effectId)));
      } catch (e) {
        console.log(e);
      }
    }, [suGPTStream]);

    /**
     * This function is used to make scroll when annimation is taking place otherwise scrollbar is
     * going downwards along with content
     * @param {void}
     * @returns {void}
     */

    const handleShowMoreClick = () => {
      setShowMore(false);
      setInputValue('');
      document.getElementById('setFocusOnGPTResponse').focus();
      window.gza('llm_show_more', {
        llmRequestId: llmId,
        searchId: window._gza_analytics_id,
        uid: variables.searchCallVariables.uid,
        sid_session: variables.searchCallVariables.sid
      });
    };

    const codeCopyToClipboard = (e) => {
      const currentDiv = e.target;
      const copiedSpan = currentDiv.closest('div').parentElement.querySelector('span');
      const TextTocopy = currentDiv.closest('pre').querySelector('code').innerText;

      copiedSpan.style.display = '';
      const timeoutId = setTimeout(() => {
        copiedSpan.style.display = 'none';
      }, 1000);

      // Track timeout for cleanup
      copyTimeouts.current.push(timeoutId);

      navigator.clipboard
        .writeText(TextTocopy)
        .then(() => {
          console.log('Text copied to clipboard:', TextTocopy);
        })
        .catch((error) => {
          console.error('Error copying text to clipboard:', error);
        });
    };

    useEffect(() => {
      if (!showMoreValid && ContentBoxRef?.current?.clientHeight >= 265) {
        setShowMoreValid(true);
        setShowMore(true);
      }
    }, [ContentBoxRef?.current?.clientHeight]);

    useEffect(() => {
      if (window.showSuSearch) {
        const observer = new IntersectionObserver(
          ([entry]) => {
            if (entry.isIntersecting) {
              if (ContentBoxRef?.current?.clientHeight >= 265) {
                setShowMoreValid(true);
                setShowMore(true);
              }
            }
          },
          { threshold: 0.1 }
        );

        const element = document.querySelector('.su__snippets_container');
        if (element) observer.observe(element);

        return () => observer.disconnect();
      }
    }, []);

    const addCopyButton = () => {
      const temp = document.getElementsByClassName('su__copy_svg');
      const temparr = Array.from(temp);

      temparr.forEach((val) => {
        val.style.display = 'flex';
        val.style.alignItems = 'center';
        val.style.justifyContent = 'center';

        val.addEventListener('click', (e) => {
          codeCopyToClipboard(e);
        });
        val.style.cursor = 'pointer';

        const parentDiv = val.parentElement;

        parentDiv.style.display = 'flex';
        parentDiv.style.justifyContent = 'space-between';
        parentDiv.style.alignItems = 'center';
        val.innerHTML = `
                                      <span class="su__copied-span su__d-flex su__gap-5 su__mr-10px su__mb-0" style="display:none; font-family: 'Montserrat', sans-serif; font-size:12px; font-weight:500;">
                                        <svg
                                          xmlns="http://www.w3.org/2000/svg"
                                          width="12"
                                          height="9.189"
                                          viewBox="0 0 12 9.189"
                                        >
                                          <path
                                            id="Icon_ionic-ios-checkmark"
                                            data-name="Icon ionic-ios-checkmark"
                                            d="M22.371,13.282l-.981-1.009a.211.211,0,0,0-.156-.067h0a.2.2,0,0,0-.156.067l-6.8,6.849-2.474-2.474a.215.215,0,0,0-.312,0l-.992.992a.222.222,0,0,0,0,.318l3.121,3.121a.987.987,0,0,0,.652.318,1.034,1.034,0,0,0,.646-.306h.006l7.451-7.49A.238.238,0,0,0,22.371,13.282Z"
                                            transform="translate(-10.434 -12.206)"
                                            fill="#fff"
                                          />
                                        </svg>
                                        <span>Copied</span>
                                      </span>
                             <div class="su__SUgpt-tooltip">
<svg id="Component_51_10" class="su__copyToBoard_hov_fill" data-name="Component 51 – 10" xmlns="http://www.w3.org/2000/svg" width="18.322" height="18.322" viewBox="0 0 18.322 18.322" fill="#707070">
  <path id="Path_18329" data-name="Path 18329" d="M19.214,9.566H9.035V11.6h6.107a2.036,2.036,0,0,1,2.036,2.036V23.817a2.036,2.036,0,0,1-2.036,2.036H4.963a2.036,2.036,0,0,1-2.036-2.036V13.638A2.036,2.036,0,0,1,4.963,11.6H7V9.566A2.036,2.036,0,0,1,9.035,7.53H19.214A2.036,2.036,0,0,1,21.25,9.566V19.745a2.036,2.036,0,0,1-2.036,2.036H17.178V19.745h2.036ZM7,13.638H4.963V23.817H15.142V13.638H7v0Z" transform="translate(-2.927 -7.53)" fill-rule="evenodd"/>
</svg>
 <span class="su__SUgpt-tooltiptext">Copy code</span>
 </div>

`;
      });
    };

    // sugpt ask a follow up question
    const handleInputChange = (event) => {
      setInputValue(event.target.value);
    };

    const sendDataToAi = async () => {
      const copiedContent = await handleCopyToClipboard(true);
      window.sugptFollowUpHistory = {
        human: variables.searchCallVariables.searchString,
        assistant: copiedContent
      };
      const messageToSend = { searchString: inputValue };
      // Create a URLSearchParams object from the current search parameters
      const searchParams = new URLSearchParams(window.location.search);

      // Update the searchString parameter
      searchParams.set('sendSearchString', messageToSend.searchString);

      // Construct the new URL
      const newUrl = `${window.location.pathname}?${searchParams.toString()}`;

      // Update the URL without reloading
      window.history.pushState({}, '', newUrl); // or replaceState for replacing

      setInputValue('');
    };

    return (
      <>
        {variables && variables.searchCallVariables && variables.searchCallVariables.pageNo == 1 ? (
          <div className={`su__center-gpt-widget su__mt-1 su__w-100 su__sc-loading`}>
            <div
              aria-label={t(StaticStrings.ongpt)}
              className={` su__center-gpt-widget su__snippets_container ${
                isDeviceIpad || isDeviceMobile ? 'su__gpt_mobile_padding' : 'su__gpt_padding'
              } ${!loader && !hasError ? 'su__show-more-height-min' : ''}`}
              ref={ContentBoxRef}
            >
              <div
                className={`su__w-100 su__mt-0${
                  showMore && !loader && !errorObject.show && !hasError
                    ? ' su__typing-animation su__height-showmore'
                    : ' su__typing-animation'
                }
                su__typing-animation  su__mt-0 ${errorObject.show ? 'error-displayed' : ''}
                `}
              >
                <span
                  id="scrollableArea"
                  ref={scrollableAreaRef}
                  className={` ${
                    suGPTSays && !hasError && !loader ? 'typing-text su__typed_text' : ''
                  } ${hasError || !printing ? 'removeCursor' : ''}`}
                >
                  {loader && (
                    <>
                      <div className="su__d-flex su__gap-5">
                        <SVGS.sparkle />
                        <p className="su__font-14 su__line-height-15 loader-text su__mt-0 su__mb-0 su__f-regular">
                          Generating...
                        </p>
                      </div>
                      <ul className="su__loader_snippet su__pl-0">
                        <li className="">
                          <div className="skeleton-box"></div>
                          <div className="skeleton-box"></div>
                          <div className="skeleton-box"></div>
                        </li>
                      </ul>
                    </>
                  )}
                  {errorObject.show && !loader && (
                    <div className="retry-section">
                      <p
                        lang={variables.searchCallVariables.langAttr}
                        tabIndex={tabIndexes.tabIndex_0}
                        className={`${
                          isDeviceMobile || isDeviceIpad
                            ? 'su__snippet_heading_color su__font-13'
                            : 'su__snippet_heading_color su__font-14'
                        }`}
                      >
                        {errorObject.message}
                      </p>
                      {errorObject.showRetry && (
                        <button
                          onClick={() => dispatch(suGPTSearch.start())}
                          lang={variables.searchCallVariables.langAttr}
                          role={a11y.ROLES.BTN}
                          tabIndex={tabIndexes.tabIndex_0}
                          type="button"
                          className="retry-btn su__cursor"
                        >
                          {t(StaticStrings.retry)}
                          <SVGS.retry />
                        </button>
                      )}
                    </div>
                  )}
                  {!!suGPTSays && !loader && (
                    <>
                      {!errorObject.show && (
                        <div className={gptClassList}>
                          <div
                            className={
                              showMore
                                ? 'su__show-more-height su__overflow-hide'
                                : 'su__overflow-visible'
                            }
                          >
                            <div className="su__mb-10px">
                              <div className="su__d-flex">
                                <SVGS.sparkle />
                                <p className="su__font-14 su__line-height-15 loader-text su__mt-0 su__mb-0 su__f-regular">
                                  {StaticStrings.Generated_answer_for_you}
                                </p>
                              </div>
                            </div>
                            <span
                              lang={variables.searchCallVariables.langAttr}
                              id="setFocusOnGPTResponse"
                              ref={suGPTContentRef}
                              aria-live="polite"
                              className={
                                hasError
                                  ? 'su__noresult_text_color'
                                  : 'su__remove_space su__font-14px su__lh-28px'
                              }
                              dangerouslySetInnerHTML={{ __html: SuGPTrespone }}
                            ></span>
                            {!printing && !hasError && (
                              <div
                                className={`su__d-flex su__zindex-1 su__mt-20 su__mb-10px su__align-items-center su__flex-gap-13`}
                              >
                                <>
                                  <Tooltip
                                    text={t(StaticStrings.COPYCLIPBOARD)}
                                    position="top-right"
                                  >
                                    <button
                                      type="button"
                                      className="a11y-btn su__padding-0 su__cursor_pointer"
                                      lang={variables.searchCallVariables.langAttr}
                                      tabIndex={
                                        showMore
                                          ? tabIndexes.tabIndex_minus_1
                                          : tabIndexes.tabIndex_0
                                      }
                                      role={a11y.ROLES.BTN}
                                      aria-label={t(StaticStrings.COPYCLIPBOARD)}
                                      onClick={handleCopyToClipboard}
                                    >
                                      <SVGS.copy />
                                    </button>
                                  </Tooltip>
                                </>

                                {!showLike && !showDislike ? (
                                  <>
                                    <button
                                      type="button"
                                      className="a11y-btn su__padding-0 su__cursor_pointer"
                                      tabIndex={
                                        showMore
                                          ? tabIndexes.tabIndex_minus_1
                                          : tabIndexes.tabIndex_0
                                      }
                                      role={a11y.ROLES.BTN}
                                      aria-label={t(StaticStrings.positive_feedback)}
                                      lang={variables.searchCallVariables.langAttr}
                                      onClick={openModal}
                                    >
                                      <SVGS.thumb position={'up'} />
                                    </button>
                                  </>
                                ) : (
                                  !showDislike && (
                                    <>
                                      <button
                                        role={a11y.ROLES.BTN}
                                        type="button"
                                        className="a11y-btn su__padding-0 su__cursor_pointer"
                                        aria-live="assertive"
                                        aria-atomic="true"
                                        lang={variables.searchCallVariables.langAttr}
                                        aria-label={t(StaticStrings.positive_given)}
                                      >
                                        <SVGS.thumb position={'up'} fill="#00b029" />
                                      </button>
                                      {showCopy && (
                                        <span
                                          className={`su__copied-span su__d-flex su__gap-5 ${
                                            isDeviceMobile ? 'su__ml-auto' : null
                                          }`}
                                        >
                                          <SVGS.copy copied={showCopy} />
                                          <span>Copied</span>
                                        </span>
                                      )}
                                    </>
                                  )
                                )}
                                {showDislike && (
                                  <>
                                    <button
                                      type="button"
                                      lang={variables.searchCallVariables.langAttr}
                                      role={a11y.ROLES.BTN}
                                      className="a11y-btn su__padding-0 su__cursor_pointer"
                                      aria-live="assertive"
                                      aria-atomic="true"
                                      aria-label={t(StaticStrings.negative_given)}
                                    >
                                      <SVGS.thumb position={'down'} fill={'#ff1616'} />
                                    </button>
                                    {showCopy && (
                                      <span
                                        className={`su__copied-span su__d-flex su__gap-5 ${
                                          isDeviceMobile ? 'su__ml-auto' : null
                                        }`}
                                      >
                                        <SVGS.copy copied={showCopy} />
                                        <span>Copied</span>
                                      </span>
                                    )}
                                  </>
                                )}
                                {!showLike && !showDislike && (
                                  <>
                                    <button
                                      type="button"
                                      className="a11y-btn su__padding-0 su__cursor_pointer "
                                      tabIndex={
                                        showMore
                                          ? tabIndexes.tabIndex_minus_1
                                          : tabIndexes.tabIndex_0
                                      }
                                      lang={variables.searchCallVariables.langAttr}
                                      role={a11y.ROLES.BTN}
                                      aria-label={t(StaticStrings.negative_feedback)}
                                      onClick={openModalDislike}
                                    >
                                      <SVGS.thumb position={'down'} />
                                    </button>
                                    {showCopy && (
                                      <span
                                        className={`su__copied-span su__d-flex su__gap-5 ${
                                          isDeviceMobile ? 'su__ml-auto' : null
                                        }`}
                                      >
                                        <SVGS.copy copied={showCopy} />
                                        <span>Copied</span>
                                      </span>
                                    )}
                                  </>
                                )}
                              </div>
                            )}
                            {!printing && window.showSuSearch && (
                              <div className="chat-container">
                                <div className="input-box">
                                  <SVGS.chat />
                                  <input
                                    type="text"
                                    placeholder={t(StaticStrings.gpt_box_follow_up)}
                                    value={inputValue}
                                    onChange={handleInputChange}
                                    onKeyDown={(e) => {
                                      if (e.key === EVENT_NAMES.ENTER) {
                                        sendDataToAi();
                                      }
                                    }}
                                  />
                                  <button
                                    type="submit"
                                    onClick={sendDataToAi}
                                    disabled={!inputValue.trim()}
                                  >
                                    <SVGS.paperPlane />
                                    Send
                                  </button>
                                </div>
                              </div>
                            )}
                          </div>
                        </div>
                      )}
                    </>
                  )}
                </span>
                {!hasError && showMore && !errorObject.show && !loader && showMoreValid && (
                  <div className="su__show-all su__show-more-bg su__d-flex su__justify-content-center su__flex-vcenter su__loading-view su__show-more-before su__pt-4px">
                    <div
                      className="su__showMore-btn-wrap su__cursor_pointer su__line-height-n"
                      onClick={handleShowMoreClick}
                    >
                      <button
                        type="button"
                        className="su__show-more-gpt su__cursor su__txt-capitalize a11y-btn"
                        tabIndex={tabIndexes.tabIndex_0}
                        lang={variables.searchCallVariables.langAttr}
                        role={a11y.ROLES.BTN}
                        aria-label={t(StaticStrings.show_more)}
                        onClick={handleShowMoreClick}
                      >
                        {t(StaticStrings.show_more)}
                      </button>
                      <span
                        className="su__arrow-down su__arrow-down-zendesk su__cursor su__t-m2"
                        onClick={handleShowMoreClick}
                      ></span>
                    </div>
                  </div>
                )}
              </div>
            </div>
            {feedbackGivenlike && (
              <GptFeedbackPopUp
                heading="Additional Feedback"
                IsShown={showPopup}
                likeBtn={likeBtn}
                textContent="How is your search experience with SearchUnifyGPT?"
                onAction={handlePopupAction}
                buttonContent1={buttonContent1}
                buttonContent2={buttonContent2}
                showThanks={showThankModal}
              />
            )}
            {feedbackGivendislike && (
              <GptFeedbackPopUp
                heading="Additional Feedback"
                IsShown={showPopup}
                likeBtn={likeBtn}
                textContent="How is your search experience with SearchUnifyGPT?"
                onAction={handlePopupAction}
                buttonContent1={buttonContent1}
                buttonContent2={buttonContent2}
                showThanks={showThankModal}
              />
            )}

            <div
              className={`su__text-right su__mt-0 su__mb-1 ${
                isDeviceMobile || isDeviceIpad
                  ? 'su__snippet_heading su__text-right'
                  : 'su__snippet_heading'
              }`}
            >
              <span
                className={`${
                  isDeviceMobile || isDeviceIpad
                    ? 'su__snippet_heading_color su__font-10'
                    : 'su__snippet_heading_color su__font-11'
                }`}
              >
                SearchUnifyGPT
              </span>
              <span className="su__snippet_fontStyles">TM</span>
            </div>
          </div>
        ) : null}
      </>
    );
  } catch (e) {
    console.log('Error in feature-snippet component', e);
    return <div />;
  }
};

export default React.memo(FeaturedSnippetAnimation);
