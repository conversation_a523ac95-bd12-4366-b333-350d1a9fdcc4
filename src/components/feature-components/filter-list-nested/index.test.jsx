/* global jest, it, expect, describe, beforeEach, afterEach */
import React from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { mount } from 'enzyme';
import ChildComponent from './';
import { useTranslation } from 'react-i18next';
import state from '__mocks__/state';
import { facetClickNested } from '../../../mergeFacet';

// Mock the gza function
global.gza = jest.fn();
const dispatch = jest.fn();

jest.mock('react-i18next', () => ({
  useTranslation: jest.fn()
}));

jest.mock('react-redux', () => ({
  useSelector: jest.fn(),
  useDispatch: jest.fn()
}));

jest.mock('../../../redux/variables', () => require('__mocks__/variables'));

jest.mock('../../../mergeFacet', () => ({
  facetClickNested: jest.fn(() => {})
}));

describe('ChildComponent', () => {
  let mockProps;
  let wrapper;

  beforeEach(() => {
    useSelector.mockImplementation(() => state.searchResult);
    useDispatch.mockImplementation(() => dispatch);
    facetClickNested.mockImplementation(() => {});
    useTranslation.mockImplementation(() => ({ t: (key) => key }));
    mockProps = {
      childArray: [
        {
          displayName: 'Parent Item',
          value: '10',
          open: false,
          selected: false,
          childArray: [
            {
              displayName: 'Child Item',
              value: '5',
              open: false,
              selected: false
            }
          ]
        }
      ],
      type: 'type1',
      order: 1
    };
    wrapper = mount(<ChildComponent {...mockProps} />);
    wrapper.update();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should render correctly', () => {
    expect(wrapper).toBeTruthy();
  });

  it('should open facet on arrow clicks', () => {
    expect(wrapper.find('i.su__arrow-down')).toHaveLength(0);
    expect(wrapper.find('i.su__arrow-right')).toHaveLength(1);
    wrapper.find('div.su__nested-arrow').simulate('click', {});
    expect(wrapper.find('i.su__arrow-down')).toHaveLength(1);
    expect(wrapper.find('i.su__arrow-right')).toHaveLength(0);
  });

  // it('should dispatch search action on input change in facet search', () => {
  //   wrapper.find('input.su__toggle-input').first().simulate('change', {});
  //   expect(dispatch).toHaveBeenCalled();
  //   expect(facetClickNested).toHaveBeenCalled();
  // });

  it('should capture error gracefully in case the component encounters one', () => {
    const consoleSpy = jest.spyOn(console, 'log').mockImplementation(() => {});
    useSelector.mockImplementation(() => {
      throw new Error('error');
    });
    wrapper = mount(<ChildComponent {...mockProps} />);
    wrapper.update();
    expect(wrapper.html()).toBe('<div></div>');
    expect(consoleSpy).toHaveBeenCalledWith('Error in nestedIndex component', expect.any(Error));
    consoleSpy.mockRestore();
  });
});
