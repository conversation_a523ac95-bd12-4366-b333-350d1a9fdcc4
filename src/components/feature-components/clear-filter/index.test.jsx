/* global jest, it, expect, describe, beforeEach */
import React from 'react';
import { useDispatch } from 'react-redux';
import { shallow } from 'enzyme';
import ClearFilter from './';
import { search } from '../../../redux/ducks';
import variables from '__mocks__/variables';
import setCookies from '../../../setCookie/setCookie';

const SELECTORS = {
  clearFilters: '[data-test-id="su__clear_filters"]'
};

const original = variables.searchCallVariables.aggregations;

// Mock dependencies
jest.mock('react-redux', () => ({
  useDispatch: jest.fn()
}));

jest.mock('react-i18next', () => ({
  useTranslation: jest.fn().mockReturnValue({ t: jest.fn((key) => key) })
}));

jest.mock('../../../redux/ducks', () => ({
  search: {
    start: jest.fn()
  }
}));

jest.mock('../../../redux/variables', () => require('__mocks__/variables'));

jest.mock('../../../setCookie/setCookie', () => ({
  setSmartFacetOff: jest.fn()
}));

describe('ClearFilter Component', () => {
  let dispatchMock;

  beforeEach(() => {
    dispatchMock = jest.fn();
    useDispatch.mockReturnValue(dispatchMock);
    variables.searchCallVariables.aggregations = original;
  });

  it('renders without crashing', () => {
    const wrapper = shallow(<ClearFilter />);
    expect(wrapper.exists()).toBe(true);
  });

  it('renders button when there are aggregations or advanced search facets', () => {
    const wrapper = shallow(<ClearFilter />);
    expect(wrapper.find(SELECTORS.clearFilters).exists()).toBe(true);
  });

  it('does not render button when there are no aggregations or advanced search facets', () => {
    variables.searchCallVariables.aggregations = [];
    const wrapper = shallow(<ClearFilter />);
    expect(wrapper.find(SELECTORS.clearFilters).exists()).toBe(false);
  });

  it('calls clearReset function and dispatches search.start when button is clicked', () => {
    const wrapper = shallow(<ClearFilter />);
    wrapper.find(SELECTORS.clearFilters).simulate('click');
    expect(dispatchMock).toHaveBeenCalledWith(search.start(variables.searchCallVariables));
    expect(variables.searchCallVariables.aggregations).toEqual([]);
    expect(variables.searchCallVariables.exactPhrase).toEqual('');
    expect(setCookies.setSmartFacetOff).toHaveBeenCalled();
  });

  it('catches errors and renders empty div', () => {
    useDispatch.mockImplementation(() => {
      throw new Error('Test error');
    });

    const consoleSpy = jest.spyOn(console, 'log').mockImplementation(() => {});
    shallow(<ClearFilter />);
    expect(consoleSpy).toHaveBeenCalledWith('Error in clear filter component', expect.any(Error));
  });
});
