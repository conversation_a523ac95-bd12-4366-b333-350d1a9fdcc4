/* global gza */
import { A11Y_IDS, tabIndexes } from 'constants/a11y';
import { any, bool, func, string, object, oneOfType, number } from 'prop-types';
import React, { useEffect, useState, useRef } from 'react';
import ReactDOM from 'react-dom';
import variables from 'redux/variables';
import { SC_IDS } from 'constants/constants';

const CitationsPortal = ({
  height,
  width,
  href,
  target,
  searchResult,
  showPortal,
  modalRef,
  isDeviceMobile,
  isDeviceIpad,
  setshowPortal,
  Opacity,
  viewedResult
}) => {
  let articleLinkObjectIndex;
  let articleLinkObject;
  const findArticleObject = (arr) => {
    if (!arr) return null;
    return arr.find((element, index) => {
      if (element?.href?.includes(href)) {
        articleLinkObjectIndex = index;
        articleLinkObject = element;
        return true;
      }
      if (element?.hits) {
        const nestedResult = findArticleObject(element.hits);
        if (nestedResult) return true;
      }
      return false;
    });
  };
  findArticleObject(searchResult?.result?.hits);
  let citationHref, citationTitle, citationDescription, sourceLabel;
  citationHref = articleLinkObject?.href;
  citationTitle = articleLinkObject?.highlight?.TitleToDisplayString[0] || articleLinkObject?.href;
  citationDescription = articleLinkObject?.highlight?.SummaryToDisplay;
  sourceLabel = articleLinkObject?.sourceLabel;
  const [showTransition, setshowTransition] = useState(false);
  const nextFocusableElement = useRef(null);
  let counter = 0;

  /**
   * It closes the citations modal
   * @param {void}
   * @returns {void}
   */
  const closeModal = () => {
    setshowPortal(false);
  };

  /**
   * It sets the variable to true after some time to show transition effect on mobile view on citations modal
   * @param {void}
   * @returns {void}
   */
  useEffect(() => {
    let animationFrameId;
    const timeoutId = setTimeout(() => {
      animationFrameId = requestAnimationFrame(() => {
        setshowTransition(true);
      });
    }, 100);

    return () => {
      clearTimeout(timeoutId);
      cancelAnimationFrame(animationFrameId);
    };
  });

  useEffect(() => {
    if (articleLinkObject && articleLinkObject.href && showPortal) {
      handleKeyDown(showPortal);
    }
  }, [articleLinkObject && articleLinkObject.href && showPortal]);

  const extractElementByClassname = (allFocusableArray) => {
    const indices = allFocusableArray
      .map((item, index) => {
        // Check if the item has the className
        if (item.getAttribute('data-url') == document.activeElement.getAttribute('data-url')) {
          return index; // Return the index if it matches
        }
        return -1; // Return -1 if it doesn't match
      })
      .filter((index) => index !== -1); // Filter out the -1 values

    return indices;
  };

  const handleKeyDown = (showPortal) => {
    // add all the elements inside modal which you want to make focusable
    const focusableElements =
      '[button]:not([disabled="true"]), [href], input, select, textarea, [tabindex]:not([tabindex="-1"])';
    let modal =
      document.querySelector(`#${A11Y_IDS.childTrap}`) ||
      document.querySelector(`#${A11Y_IDS.trap}`);
    if (!modal) return;
    const firstFocusableElement = modal.querySelectorAll(focusableElements)[0]; // get first element to be focused inside modal
    const focusableContent = modal.querySelectorAll(focusableElements);
    const lastFocusableElement = focusableContent[focusableContent.length - 1];
    const allFocusableElements = document.querySelectorAll(
      'a[href], button, textarea, input[type="text"], input[type="radio"], input[type="checkbox"], select'
    );
    const allFocusableArray = Array.prototype.slice.call(allFocusableElements);
    const filteredElements = extractElementByClassname(allFocusableArray); // get last element to be focused inside modal
    const fn = (e) => {
      if (document.activeElement.classList.contains('su_citation') && e.shiftKey) {
        lastFocusableElement.focus();
        e.preventDefault();
      } else if (document.activeElement.classList.contains('su_citation') && e.key === 'Tab') {
        firstFocusableElement.focus();
        e.preventDefault();
      }
      let isTabPressed = e.key === 'Tab' || e.keyCode === 9;
      if (!isTabPressed) {
        return;
      }
      if (e.shiftKey) {
        // if shift key pressed for shift + tab combination
        if (document.activeElement === firstFocusableElement) {
          nextFocusableElement.current = allFocusableArray[filteredElements[0] - ++counter];
          nextFocusableElement.current.focus();
          e.preventDefault();
        }
      } else {
        // if tab key is pressed
        if (document.activeElement === lastFocusableElement) {
          nextFocusableElement.current = allFocusableArray[filteredElements[0] + ++counter];
          nextFocusableElement.current.focus();
          e.preventDefault();
        }
      }
    };

    document.removeEventListener('keydown', fn);
    if (showPortal) {
      document.addEventListener('keydown', fn);
    }
  };

  /**
   * linkOpened - When user clicks on href of citATION modal then it tracks the conversion analytics
   * @param {*} result
   * @param {*} rank
   * @param {*} href
   * @param {*} title
   */
  const linkOpened = (result, rank, href, title) => {
    gza('conversion', {
      index: result['sourceName'],
      type: result['objName'],
      id: result['_id'],
      rank: rank,
      relevance_score: result['_score'],
      convUrl: href || result['href'],
      convSub:
        title ||
        (result['highlight']['TitleToDisplayString'][0] &&
          result['highlight']['TitleToDisplayString'][0]?.substring(0, 300)),
      pageSize: variables.searchCallVariables.pageSize,
      page_no: variables.searchCallVariables.pageNo,
      sc_analytics_fields: result['trackAnalytics']
    });
    closeModal();
  };
  return (
    <div>
      {showPortal &&
        ReactDOM.createPortal(
          <div
            ref={modalRef}
            id={A11Y_IDS.trap}
            style={{
              top: height,
              left: width,
              opacity: Opacity,
              position: 'fixed',
              ...((isDeviceMobile || isDeviceIpad) &&
              variables.searchClientType === SC_IDS.ZENDESK_SUPPORT_CONSOLE
                ? { bottom: 'unset' }
                : {})
            }}
            className={`${showPortal ? 'su__fadeIn_animation ' : 'su__fadeOut_animation'} ${
              isDeviceMobile || isDeviceIpad
                ? 'su__fix_preview_bottom su__transition_citation su__zindex-2'
                : 'su__invisible_div'
            } ${showTransition && (isDeviceMobile || isDeviceIpad) ? 'su__transition_in' : ''} ${
              viewedResult ? 'su__viewed-results' : ''
            }`}
          >
            <div
              className={`su__gpt-preview-article-link ${
                isDeviceMobile || isDeviceIpad
                  ? 'su__fix_preview_bottom su__article_links_mobile_view su__mr-ar-10'
                  : ''
              } `}
            >
              {articleLinkObject && articleLinkObject.href && (
                <div
                  className={`${
                    isDeviceMobile || isDeviceIpad
                      ? 'su__padding_bottom_17 su__citation_modal_container'
                      : 'su__citation_modal_container su__position-relative su__p-10px'
                  } ${isDeviceIpad ? 'su__p-20px' : null}`}
                >
                  {(isDeviceMobile || isDeviceIpad) && (
                    <div className="su__heading_container_citation">
                      <span className="su__heading_citation su__flex-vcenter su__gap-7px">
                        <div className="su__d-flex">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            xmlnsXlink="http://www.w3.org/1999/xlink"
                            width="14"
                            height="14"
                            viewBox="0 0 14 14"
                          >
                            <defs>
                              <clipPath id="clip-Sources">
                                <rect width="14" height="14" />
                              </clipPath>
                            </defs>
                            <g id="Sources" clipPath="url(#clip-Sources)">
                              <g id="Element" transform="translate(0.184 0.183)">
                                <path
                                  id="Element-2"
                                  data-name="Element"
                                  d="M2.381,5.242A2.861,2.861,0,0,1-.48,2.381,2.861,2.861,0,0,1,2.381-.48,2.861,2.861,0,0,1,5.242,2.381,2.861,2.861,0,0,1,2.381,5.242Zm0-4.762A1.9,1.9,0,0,0,1.037,3.725,1.9,1.9,0,0,0,3.725,1.037,1.888,1.888,0,0,0,2.381.48Z"
                                  transform="translate(0.868 0.875)"
                                  fill="#176fd4"
                                />
                                <path
                                  id="Element-3"
                                  data-name="Element"
                                  d="M10.936,5.242A2.861,2.861,0,0,1,8.075,2.381,2.861,2.861,0,1,1,12.959,4.4,2.842,2.842,0,0,1,10.936,5.242Zm0-4.762A1.9,1.9,0,0,0,9.592,3.725,1.9,1.9,0,0,0,12.28,1.037,1.888,1.888,0,0,0,10.936.48Z"
                                  transform="translate(-0.553 0.875)"
                                  fill="#176fd4"
                                />
                                <path
                                  id="Element-4"
                                  data-name="Element"
                                  d="M2.381,13.781a2.861,2.861,0,1,1,1.095-.218A2.842,2.842,0,0,1,2.381,13.781Zm0-4.762a1.9,1.9,0,1,0,.727.145A1.888,1.888,0,0,0,2.381,9.019Z"
                                  transform="translate(0.868 -0.542)"
                                  fill="#176fd4"
                                />
                                <path
                                  id="Element-5"
                                  data-name="Element"
                                  d="M10.936,13.781a2.861,2.861,0,1,1,1.095-.218A2.842,2.842,0,0,1,10.936,13.781Zm0-4.762a1.9,1.9,0,1,0,.727.145A1.888,1.888,0,0,0,10.936,9.019Z"
                                  transform="translate(-0.553 -0.542)"
                                  fill="#176fd4"
                                />
                              </g>
                            </g>
                          </svg>
                        </div>{' '}
                        <div>Sources</div>{' '}
                      </span>
                      <figure
                        data-test-id={'su__citation_close'}
                        className="su__cross_svg su__cursor_pointer"
                        onClick={closeModal}
                        tabIndex={tabIndexes.tabIndex_0}
                      >
                        <svg
                          tabIndex={tabIndexes.tabIndex_0}
                          xmlns="http://www.w3.org/2000/svg"
                          width="10"
                          height="10"
                          viewBox="0 0 10 10"
                        >
                          <path
                            id="Path_18325"
                            data-name="Path 18325"
                            d="M10.214,10.2a.71.71,0,0,1,1.006,0L15,13.975,18.78,10.2A.712.712,0,1,1,19.786,11.2L16.01,14.986l3.776,3.783a.712.712,0,0,1-1.006,1.007L15,16l-3.78,3.779a.712.712,0,0,1-1.006-1.007l3.776-3.783L10.214,11.2A.712.712,0,0,1,10.214,10.2Z"
                            transform="translate(-10.005 -9.977)"
                            fill="#666"
                          />
                        </svg>
                      </figure>
                    </div>
                  )}
                  <div className="su__d-flex su__justify-content-between">
                    <div className="su__citationModalSection">
                      <div className="su__citation-cs su_f-Montserrat">{sourceLabel}</div>
                      <div className={`su__article_href_container`}>
                        <a
                          className={`${
                            isDeviceMobile || isDeviceIpad
                              ? 'su__href_mobile su__text-decoration'
                              : 'su__article_href_dimension su__cursor_pointer su__text-decoration su__citation_link'
                          }`}
                          target={target}
                          href={citationHref}
                          tabIndex={tabIndexes.tabIndex_0}
                          aria-label={citationTitle}
                        >
                          <span
                            data-test-id={'su__citation_title'}
                            className="su__line_clamp_2"
                            onClick={() =>
                              linkOpened(articleLinkObject, articleLinkObjectIndex + 1)
                            }
                          >
                            {citationTitle}
                          </span>
                        </a>
                      </div>
                      <div
                        className={`${
                          isDeviceMobile || isDeviceIpad
                            ? 'su__title_mobile sample'
                            : 'su__article_desc_dimensions su__line_clamp_2'
                        }`}
                      >
                        <span
                          data-test-id={'su__citation_desc'}
                          className="su__citation_desc su__line_clamp_3 "
                          dangerouslySetInnerHTML={{
                            __html: citationDescription.join('#<span><br>').split('#')
                          }}
                        ></span>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>,

          document.body
        )}
    </div>
  );
};

CitationsPortal.propTypes = {
  height: oneOfType([string, number]),
  width: oneOfType([string, number]),
  href: string,
  target: string,
  searchResult: any,
  showPortal: bool,
  modalRef: object,
  isDeviceMobile: bool,
  isDeviceIpad: bool,
  setshowPortal: func,
  diamondPositionX: string,
  diamondPositionY: string,
  Opacity: oneOfType([string, number]),
  viewedResult: bool
};

CitationsPortal.defaultProps = {
  height: '',
  width: '',
  href: '',
  target: '',
  searchResult: {},
  showPortal: false,
  modalRef: () => {},
  isDeviceMobile: false,
  isDeviceIpad: false,
  setshowPortal: () => {},
  diamondPositionX: '',
  diamondPositionY: '',
  Opacity: '',
  viewedResult: false
};

export default CitationsPortal;
