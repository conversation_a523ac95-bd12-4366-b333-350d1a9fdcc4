/* global jest, it, expect, describe, beforeEach, afterEach */
import React from 'react';
import { mount } from 'enzyme';
import CitationsPortal from './';
import state from '__mocks__/state';
import variables from '__mocks__/variables';

const SELECTORS = {
  citationTitle: '[data-test-id="su__citation_title"]',
  citationDescription: '[data-test-id="su__citation_desc"]',
  closeCitation: '[data-test-id="su__citation_close"]'
};

jest.mock('../../../redux/variables', () => require('__mocks__/variables'));

describe('CitationsPortal component', () => {
  let wrapper;
  const mockProps = {
    children: <div>Test children</div>,
    height: '100px',
    width: '100px',
    href: 'testHref',
    target: '_blank',
    searchResult: state.searchResult,
    showPortal: true,
    modalRef: jest.fn(),
    isDeviceMobile: true,
    setshowPortal: jest.fn(),
    diamondPosition: 'left',
    Opacity: '1'
  };

  window.gza = jest.fn();

  beforeEach(() => {
    mockProps.isDeviceMobile = false;
  });

  afterEach(() => {
    if (wrapper) {
      wrapper.unmount();
    }
  });

  it('renders without crashing', () => {
    wrapper = mount(<CitationsPortal {...mockProps} />);
    expect(wrapper.exists()).toBe(true);
  });

  it('starts portal animation on showPortal', () => {
    wrapper = mount(<CitationsPortal {...mockProps} />);
    expect(wrapper.find('.su__fadeIn_animation').exists()).toBe(true);
  });

  it('updates state when citation popup is closed on mobile', () => {
    mockProps.isDeviceMobile = true;
    wrapper = mount(<CitationsPortal {...mockProps} />);
    wrapper.find(SELECTORS.closeCitation).simulate('click');
    expect(mockProps.setshowPortal).toHaveBeenCalledWith(false);
  });

  it('renders citation title and description correctly', () => {
    wrapper = mount(<CitationsPortal {...mockProps} />);
    const highlight = state.searchResult.result.hits[0]?.highlight;
    const citationTitle = wrapper.find(SELECTORS.citationTitle).text();
    const citationDesc = wrapper.find(SELECTORS.citationDescription).text();
    expect(citationTitle).toEqual(highlight.TitleToDisplayString[0]);
    expect(citationDesc).toContain(highlight.SummaryToDisplay[0]);
  });

  it('should track conversion in desktop view on citation title click', () => {
    wrapper = mount(<CitationsPortal {...mockProps} />);
    wrapper.find(SELECTORS.citationTitle).simulate('click');
    const result = state.searchResult.result.hits[0];
    expect(window.gza).toHaveBeenCalledWith('conversion', {
      index: result['sourceName'],
      type: result['objName'],
      id: result['_id'],
      rank: 1,
      convUrl: result['href'],
      convSub: result['highlight']['TitleToDisplayString'][0]?.substring(0, 300),
      pageSize: variables.searchCallVariables.pageSize,
      page_no: variables.searchCallVariables.pageNo,
      relevance_score: result['_score'],
      sc_analytics_fields: result['trackAnalytics']
    });
  });

  it('should track conversion in mobile view on citation title click', () => {
    mockProps.isDeviceMobile = true;
    wrapper = mount(<CitationsPortal {...mockProps} />);
    wrapper.find(SELECTORS.citationTitle).simulate('click');
    const result = state.searchResult.result.hits[0];
    expect(window.gza).toHaveBeenCalledWith('conversion', {
      index: result['sourceName'],
      type: result['objName'],
      id: result['_id'],
      rank: 1,
      convUrl: result['href'],
      convSub: result['highlight']['TitleToDisplayString'][0]?.substring(0, 300),
      pageSize: variables.searchCallVariables.pageSize,
      page_no: variables.searchCallVariables.pageNo,
      relevance_score: result['_score'],
      sc_analytics_fields: result['trackAnalytics']
    });
  });
});
