/* global jest, it, expect, describe, beforeEach, afterEach */
import React from 'react';
import { mount } from 'enzyme';
import Modal from './'; // Adjust the import path as needed
import { EVENT_NAMES } from 'constants/constants';

describe('Modal Component', () => {
  let wrapper;
  const mockCloseModal = jest.fn();

  beforeEach(() => {
    wrapper = mount(
      <Modal isOpen={true} closeModal={mockCloseModal} className="test-class">
        <div>Modal Content</div>
      </Modal>
    );
  });

  afterEach(() => {
    wrapper.unmount();
    jest.clearAllMocks();
  });

  it('renders correctly when open', () => {
    expect(wrapper.find('div').at(0).prop('className')).toContain('su_modal_open');
    expect(wrapper.find('div').at(1).prop('className')).toContain('su__animate-fadow');
    expect(wrapper.find('div').at(1).prop('className')).toContain('test-class');
    expect(wrapper.text()).toContain('Modal Content');
  });

  it('renders correctly when closed', () => {
    wrapper.setProps({ isOpen: false });
    expect(wrapper.find('div').at(0).prop('className')).toContain('su_modal_closed');
  });

  it('calls closeModal when clicking outside', () => {
    const event = new MouseEvent('mousedown', { bubbles: true });
    document.dispatchEvent(event);
    expect(mockCloseModal).toHaveBeenCalledWith(false);
  });

  it('does not call closeModal when clicking inside', () => {
    const modalContent = wrapper.find('div').at(1).getDOMNode();
    const event = new MouseEvent('mousedown', { bubbles: true });
    modalContent.dispatchEvent(event);
    expect(mockCloseModal).not.toHaveBeenCalled();
  });

  it('does not call closeModal on Tab key press', () => {
    const event = new KeyboardEvent('keydown', { bubbles: true, code: 'Tab' });
    document.dispatchEvent(event);
    expect(mockCloseModal).not.toHaveBeenCalled();
  });

  it('calls closeModal on other key press', () => {
    const event = new KeyboardEvent('keydown', { bubbles: true, code: EVENT_NAMES.ESCAPE });
    document.dispatchEvent(event);
    expect(mockCloseModal).toHaveBeenCalledWith(false);
  });
});
