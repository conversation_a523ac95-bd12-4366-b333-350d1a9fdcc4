/* global jest, it, expect, describe, beforeEach, afterEach */
import React from 'react';
import { mount } from 'enzyme';
import { useDispatch, useSelector } from 'react-redux';
import LoadMoreResults from './';
import variables from '../../../redux/variables';
import { search } from '../../../redux/ducks';
import { useTranslation } from 'react-i18next';
import state from '__mocks__/state';

jest.mock('react-redux', () => ({
  useDispatch: jest.fn(),
  useSelector: jest.fn()
}));

jest.mock('react-i18next', () => ({
  useTranslation: jest.fn()
}));

jest.mock('../../../redux/variables', () => require('__mocks__/variables'));

jest.mock('../../../redux/ducks', () => ({
  search: {
    start: jest.fn((params) => ({ type: 'SEARCH_START', payload: params }))
  }
}));

describe('LoadMoreResults', () => {
  let useDispatchMock;
  let useSelectorMock;

  beforeEach(() => {
    useDispatchMock = useDispatch;
    useSelectorMock = useSelector;
    useDispatchMock.mockReturnValue(jest.fn());
    useTranslation.mockImplementation(() => ({ t: (key) => key }));
    state.searchResult.result.total = 20;
    useSelectorMock.mockReturnValue(state.searchResult);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should render correctly', () => {
    const wrapper = mount(<LoadMoreResults />);
    expect(wrapper.exists()).toBe(true);
  });

  it('should show the "more results" button if totalResults is greater than 1', () => {
    const wrapper = mount(<LoadMoreResults />);
    expect(wrapper.find('.su__more-btn').exists()).toBe(true);
  });

  it('should not show the "more results" button if totalResults is 1 or less', () => {
    state.searchResult.result.total = 10;
    useSelectorMock.mockReturnValue(state.searchResult);
    const wrapper = mount(<LoadMoreResults />);
    expect(wrapper.find('.su__more-btn').exists()).toBe(false);
  });

  it('should dispatch search action with updated variables on "more results" button click', () => {
    const mockDispatch = jest.fn();
    useDispatchMock.mockReturnValue(mockDispatch);

    const wrapper = mount(<LoadMoreResults />);
    wrapper.find('.su__more-btn').simulate('click');

    expect(variables.searchCallVariables.pageSize).toBe(20);
    expect(mockDispatch).toHaveBeenCalledWith(search.start(variables.searchCallVariables));
  });

  it('should handle errors gracefully', () => {
    jest.spyOn(console, 'log').mockImplementation(() => {});
    useTranslation.mockImplementation(() => {
      throw new Error('Test error');
    });
    const wrapper = mount(<LoadMoreResults />);
    wrapper.update();
    expect(wrapper.html()).toBe('<div></div>');
    expect(console.log).toHaveBeenCalledWith('Error in load-more component', expect.any(Error));
  });
});
