import React, { useEffect, useState, useRef } from 'react';
import variables from 'redux/variables';
import StaticStrings from 'StaticStrings';
import { useTranslation } from 'react-i18next';
import useOnClickOutsideRef from '../../../event-handler/outside-click';
import { tabIndexes, a11y, A11Y_IDS } from 'constants/a11y';
export const Dropdown = (props) => {
  try {
    const { dropdownItems, handleChange, value, language, setShowoverlay } = props;
    const { t } = useTranslation();
    const [openDropDown, setOpenDropDown] = useState(false);
    const divRefs = useRef([]);
    const dropdownElementRef = useOnClickOutsideRef(() => setOpenDropDown(false), null, false);
    const [selectedValue, setSelectedValue] = useState(
      language ? value : variables.searchCallVariables.sortby
    );
    const [pointerIndex, setPointerIndex] = useState(null);

    const movePointer = (e) => {
      if (!['ArrowDown', 'ArrowUp', 'Enter'].includes(e.key)) {
        return;
      }
      e.preventDefault();
      const numItems = dropdownItems.length;
      if (pointerIndex === null && (e.key === 'ArrowDown' || e.key === 'ArrowUp')) {
        setPointerIndex(0);
      } else if (e.key === 'ArrowDown') {
        setPointerIndex((prevIndex) => (prevIndex === numItems - 1 ? 0 : prevIndex + 1));
      } else if (e.key === 'ArrowUp') {
        setPointerIndex((prevIndex) => (prevIndex === 0 ? numItems - 1 : prevIndex - 1));
      }

      if (e.key === 'Enter') {
        handleChange(e);
        setOpenDropDown(false);
      }
    };

    const handleClick = () => {
      setOpenDropDown(!openDropDown);
    };
    const handleFocus = (index) => {
      setPointerIndex(index);
    };

    useEffect(() => {
      setShowoverlay(openDropDown);
      if (!openDropDown) {
        setPointerIndex(null);
      }
    }, [openDropDown]);

    useEffect(() => {
      if (pointerIndex != null && divRefs.current[pointerIndex]) {
        divRefs.current[pointerIndex].focus();
      }
    }, [pointerIndex]);

    useEffect(() => {
      const selectDefaultValue = () => {
        dropdownItems &&
          dropdownItems.map((item) => {
            if (!language && item.value === variables.searchCallVariables.sortby) {
              setSelectedValue(item.label);
            }
            if (language && item.code === value) {
              let selectedLanguageTitle = item.label;
              setSelectedValue(selectedLanguageTitle);
            }
          });
      };
      selectDefaultValue();
    }, [language ? value : variables.searchCallVariables.sortby]);

    useEffect(() => {
      const openDropDown = (e) => {
        if (
          dropdownElementRef &&
          dropdownElementRef.current.contains(e.target) &&
          e.target.tagName === 'LI'
        ) {
          setOpenDropDown(false);
        }
      };

      document.addEventListener('click', openDropDown);
      return () => {
        document.removeEventListener('click', openDropDown);
      };
    }, [dropdownElementRef.current]);

    return (
      <div
        className={`su__custom-dropdown-container su__mr-0-mobile su__ml-0 su__ml-auto su__margin-top-3px`}
        ref={dropdownElementRef}
      >
        <button
          onClick={() => handleClick()}
          lang={variables.searchCallVariables.langAttr}
          id="su__relevance"
          data-trigger-a11y={A11Y_IDS.dropdown_trap}
          data-name-a11y="su__relevance"
          onKeyDown={(e) => movePointer(e)}
          value={language ? value : variables.searchCallVariables.sortby}
          className={`su__dropdown-button su__dropdown-padding su__cursor_pointer ${
            language ? 'su__language-dropdown-min-width' : 'su__sorting-btn-min-width'
          }`}
          aria-label={language ? t(StaticStrings.select_language) : t(StaticStrings.sort_by_label)}
          aria-expanded={openDropDown}
          aria-controls="dropdown-menu"
          role={a11y.ROLES.BTN}
        >
          {language ? (
            <svg
              className="su__mr-5px su__min_max_width-16px su__ml-5px"
              xmlns="http://www.w3.org/2000/svg"
              width="18.473"
              height="18.473"
              viewBox="0 0 18.473 18.473"
            >
              <path
                id="Path_22"
                data-name="Path 22"
                d="M11.227,2a9.237,9.237,0,1,0,9.246,9.237A9.232,9.232,0,0,0,11.227,2Zm6.4,5.542H14.9a14.454,14.454,0,0,0-1.275-3.288A7.417,7.417,0,0,1,17.628,7.542ZM11.237,3.884A13.011,13.011,0,0,1,13,7.542H9.472A13.011,13.011,0,0,1,11.237,3.884Zm-7.149,9.2a7.225,7.225,0,0,1,0-3.695H7.209a15.255,15.255,0,0,0-.129,1.847,15.255,15.255,0,0,0,.129,1.847Zm.757,1.847H7.57A14.455,14.455,0,0,0,8.844,18.22a7.377,7.377,0,0,1-4-3.288ZM7.57,7.542H4.845a7.377,7.377,0,0,1,4-3.288A14.455,14.455,0,0,0,7.57,7.542Zm3.667,11.047a13.011,13.011,0,0,1-1.764-3.658H13A13.011,13.011,0,0,1,11.237,18.589ZM13.4,13.084H9.075a13.59,13.59,0,0,1-.148-1.847,13.472,13.472,0,0,1,.148-1.847H13.4a13.472,13.472,0,0,1,.148,1.847A13.59,13.59,0,0,1,13.4,13.084Zm.231,5.136A14.455,14.455,0,0,0,14.9,14.931h2.725A7.417,7.417,0,0,1,13.629,18.22Zm1.635-5.136a15.255,15.255,0,0,0,.129-1.847,15.255,15.255,0,0,0-.129-1.847h3.122a7.225,7.225,0,0,1,0,3.695Z"
                transform="translate(-2 -2)"
                fill="#57575c"
              />
            </svg>
          ) : (
            <svg
              id="sort_black_24dp"
              xmlns="http://www.w3.org/2000/svg"
              width="17.036"
              height="17.036"
              viewBox="0 0 17.036 17.036"
              className="su__mr-5px su__ml-5px"
            >
              <path id="Path_17" data-name="Path 17" d="M0,0H17.036V17.036H0Z" fill="none" />
              <path
                id="Path_18"
                data-name="Path 18"
                d="M3.811,15.728H7.053a.811.811,0,1,0,0-1.621H3.811a.811.811,0,1,0,0,1.621ZM3,6.811a.813.813,0,0,0,.811.811H16.782a.811.811,0,1,0,0-1.621H3.811A.813.813,0,0,0,3,6.811Zm.811,4.864h8.107a.811.811,0,0,0,0-1.621H3.811a.811.811,0,0,0,0,1.621Z"
                transform="translate(-1.778 -2.346)"
                fill="#57575c"
              />
            </svg>
          )}

          <span className="su__color_grey su__f-normal su__font-13 su__dropdown-text-trucation">
            {selectedValue}
          </span>
          <span
            className={`${
              openDropDown
                ? 'su__arrow-up su__arrow_position su__arrow-align-rtl'
                : 'su__arrow-down su__arrow_position su__arrow-align-rtl'
            }`}
          ></span>
        </button>
        {openDropDown && (
          <>
            <ul
              id={A11Y_IDS.dropdown_trap}
              className={`su__dropdown_modal_content su__m-0 su__zindex-2 su__m-0 su__p-0 su__bullet-none ${
                language
                  ? 'su__language-dropdown-min-width'
                  : 'su__min-height-60px su__sorting-dropdown-min-width'
              }`}
            >
              {dropdownItems &&
                dropdownItems.map((item, index) => {
                  return (
                    <li
                      value={language ? item.code : item.value}
                      key={language ? item.code : item.value}
                      className={`su__dropdown-items su__cursor_pointer su__languages-dropdown ${
                        !language
                          ? variables.searchCallVariables.sortby === item.value
                            ? 'su__text-blue'
                            : ''
                          : item.code === value
                          ? 'su__text-blue'
                          : ''
                      }`}
                      ref={(el) => (divRefs.current[index] = el)}
                      onClick={handleChange}
                      onFocus={() => handleFocus(index)}
                      lang={variables.searchCallVariables.langAttr}
                      onKeyDown={(e) => movePointer(e)}
                      tabIndex={tabIndexes.tabIndex_0}
                      role="button"
                    >
                      {language ? `${item.name} (${item.label})` : item.label}
                    </li>
                  );
                })}
            </ul>
          </>
        )}
      </div>
    );
  } catch (e) {
    console.log('Error in sort by component', e);
    return <div></div>;
  }
};
export default Dropdown;
