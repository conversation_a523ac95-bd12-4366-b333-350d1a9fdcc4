/* global jest, it, expect, describe, beforeEach */
import React from 'react';
import { shallow } from 'enzyme';
import Dropdown from './index';
import variables from '../../../../__mocks__/variables';
jest.mock('react-redux', () => ({
  useSelector: jest.fn()
}));

jest.mock('../../../redux/variables', () => require('__mocks__/variables'));

jest.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key) => key
  })
}));
describe('Dropdown Component', () => {
  let wrapper;
  const mockHandleChange = jest.fn();
  const dropdownItems = [
    { value: 'relevance', label: 'Relevance' },
    { value: 'date', label: 'Date' }
  ];
  beforeEach(() => {
    wrapper = shallow(
      <Dropdown
        dropdownItems={dropdownItems}
        handleChange={mockHandleChange}
        value="relevance"
        language={false}
      />
    );
  });
  it('should render without crashing', () => {
    expect(wrapper.exists()).toBe(true);
  });
  it('should render the correct number of items', () => {
    wrapper.find('button').simulate('click'); // Open the dropdown
    expect(wrapper.find('li').length).toBe(dropdownItems.length);
  });
  it('should set the initial selected value based on props', () => {
    expect(wrapper.find('button span').at(0).text()).toBe('_score');
  });
  it('should toggle dropdown visibility on button click', () => {
    expect(wrapper.find('ul').exists()).toBe(false);
    wrapper.find('button').simulate('click');
    expect(wrapper.find('ul').exists()).toBe(true);
    wrapper.find('button').simulate('click');
    expect(wrapper.find('ul').exists()).toBe(false);
  });
  it('should call handleChange when an item is clicked', () => {
    wrapper.find('button').simulate('click'); // Open the dropdown
    wrapper.find('li').at(1).simulate('click');
    expect(mockHandleChange).toHaveBeenCalled();
  });
  it('should update selected value when a new item is selected', () => {
    wrapper.find('button').simulate('click'); // Open the dropdown
    wrapper.find('li').at(1).simulate('click');
    expect(variables.searchCallVariables.sortby).toBe('_score');
  });

  it('should show error when error caught', () => {
    jest.spyOn(require('react-i18next'), 'useTranslation').mockReturnValue({
      t: jest.fn(() => {
        throw new Error('Simulated translation error');
      })
    });

    const consoleLog = jest.spyOn(console, 'log').mockImplementation();
    const wrapper = shallow(<Dropdown />);
    expect(consoleLog).toHaveBeenCalledWith('Error in sort by component', expect.any(Error));
    expect(wrapper.html()).toBe('<div></div>');
    consoleLog.mockRestore();
  });
});
