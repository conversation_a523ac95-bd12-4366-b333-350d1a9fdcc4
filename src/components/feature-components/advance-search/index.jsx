import React, { useState, Fragment, useEffect } from 'react';
import Icons from '../../../assets/svg-icon/svg';
import { search } from '../../../redux/ducks';
import { useDispatch } from 'react-redux';
import variables from '../../../redux/variables';
import { useTranslation } from 'react-i18next';
import Tooltip from 'components/feature-components/tooltip/index.jsx';
import UnderConstruction from 'components/feature-components/under-consruction/index.jsx';
import setCookies from '../../../setCookie/setCookie';
import IconColors from '../../../IconColors';
import StaticStrings from '../../../StaticStrings';
import { A11Y_IDS, focusTrap, a11y, tabIndexes } from '../../../constants/a11y';
import 'components/feature-components/language/i18n.jsx';
import { getWildcardToggle, setWildcardToggle } from 'function-library/commonFunctions';
import { useDevice } from 'function-library/hooks';
import { EVENT_NAMES, SC_IDS } from 'constants/constants';
import utilityMethods from 'redux/utilities/utility-methods';
const EXACT_PHRASE = 'exactPhrase';
const WITH_ONE_OR_MORE = 'withOneOrMore';
const WITHOUT_THE_WORDS = 'withoutTheWords';
const WILDCARD_SEARCH = 'wildcardSearch';

const AdvanceSearch = ({ setIsAdvanceSearchCall }) => {
  const { t } = useTranslation();

  try {
    /**
     *  Change Languages state
     */
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [showPopup, setShowPopup] = useState(false);
    const { isDeviceMobile } = useDevice();

    const topActionsView = utilityMethods.isTopActionsView();

    const handleHideDropdown = (e) => {
      e.key === EVENT_NAMES.ESCAPE && setIsModalOpen(false);
    };
    useEffect(() => {
      focusTrap(isModalOpen);
    }, [isModalOpen]);

    useEffect(() => {
      document.addEventListener('keydown', handleHideDropdown);
      return () => {
        document.removeEventListener('keydown', handleHideDropdown);
      };
    });

    let [advancedState, setAdvancedState] = useState({
      exactPhrase: variables.searchCallVariables.exactPhrase,
      withOneOrMore: variables.searchCallVariables.exactPhrase,
      withoutTheWords: variables.searchCallVariables.withoutTheWords,
      wildcardSearch: getWildcardToggle() ? variables.searchCallVariables.searchString : ''
    });

    let [advanceSearchEnabled, setAdvanceSearchEnabled] = useState(
      advancedState.exactPhrase ||
        advancedState.withOneOrMore ||
        advancedState.withoutTheWords ||
        advancedState.wildcardSearch
    );

    useEffect(() => {
      setAdvanceSearchEnabled(
        variables.searchCallVariables.exactPhrase ||
          variables.searchCallVariables.withOneOrMore ||
          variables.searchCallVariables.withoutTheWords ||
          getWildcardToggle()
      );
      advanceSearchKeyword(variables.searchCallVariables, null);
    }, [
      variables.searchCallVariables.exactPhrase,
      variables.searchCallVariables.withOneOrMore,
      variables.searchCallVariables.withoutTheWords,
      variables.searchCallVariables.searchString,
      getWildcardToggle()
    ]);

    const closeAdvancedSearch = () => {
      advancedState.exactPhrase = variables.searchCallVariables.exactPhrase;
      advancedState.withOneOrMore = variables.searchCallVariables.withOneOrMore;
      advancedState.withoutTheWords = variables.searchCallVariables.withoutTheWords;
      advancedState.wildcardSearch = getWildcardToggle()
        ? variables.searchCallVariables.searchString
        : '';
    };

    const clearAdvancedFilters = () => {
      setShowPopup(false);
      setIsModalOpen(false);
      setAdvancedState({
        exactPhrase: '',
        withOneOrMore: '',
        withoutTheWords: '',
        wildcardSearch: ''
      });
      setCookies.setSmartFacetOff();
      variables.searchCallVariables.pagingAggregation = [];
      variables.searchCallVariables.exactPhrase = '';
      variables.searchCallVariables.withOneOrMore = '';
      variables.searchCallVariables.withoutTheWords = '';
      variables.searchSource = 'clear-advance-search';
      setWildcardToggle(false);
      dispatch(search.start(variables.searchCallVariables));
    };

    const openClearFiltersModal = () => {
      setShowPopup(!showPopup);
    };
    const dispatch = useDispatch();

    const advanceSearchKeyword = (data, action) => {
      let advanceKeywords = advancedState;
      if (action == EXACT_PHRASE) {
        advanceKeywords.exactPhrase = data;
      } else if (action == WITH_ONE_OR_MORE) {
        advanceKeywords.withOneOrMore = data;
      } else if (action == WITHOUT_THE_WORDS) {
        advanceKeywords.withoutTheWords = data;
      } else if (action == WILDCARD_SEARCH) {
        advanceKeywords.wildcardSearch = data;
      } else {
        /** this case is required for Bookmark search  */
        advanceKeywords = {
          exactPhrase: data.exactPhrase,
          withOneOrMore: data.withOneOrMore,
          withoutTheWords: data.withoutTheWords,
          wildcardSearch: getWildcardToggle() ? data.searchString : ''
        };
      }
      setAdvancedState({
        exactPhrase: advanceKeywords.exactPhrase,
        withOneOrMore: advanceKeywords.withOneOrMore,
        withoutTheWords: advanceKeywords.withoutTheWords,
        wildcardSearch: advanceKeywords.wildcardSearch
      });
    };

    const handleWildcard = () => {
      setWildcardToggle(advancedState.wildcardSearch !== '');
      if (!getWildcardToggle()) {
        setAdvancedState({ wildcardSearch: '' });
      }
    };

    const advancedSearchSubmit = (e) => {
      setIsAdvanceSearchCall(false);
      variables.searchCallVariables.pagingAggregation = [];
      variables.searchCallVariables.from = 0;
      variables.searchCallVariables.pageNo = 1;
      e.preventDefault();
      setIsModalOpen(false);
      variables.searchCallVariables.exactPhrase =
        advancedState && advancedState.exactPhrase && advancedState.exactPhrase.trim();
      variables.searchCallVariables.withOneOrMore =
        advancedState && advancedState.withOneOrMore && advancedState.withOneOrMore.trim();
      variables.searchCallVariables.withoutTheWords =
        advancedState && advancedState.withoutTheWords && advancedState.withoutTheWords.trim();
      variables.searchCallVariables.searchString =
        advancedState && advancedState.wildcardSearch.length > 0
          ? advancedState.wildcardSearch.trim()
          : variables.searchCallVariables.searchString;
      handleWildcard();
      variables.searchSource = 'advance-search';
      setCookies.setSmartFacetOff();
      dispatch(search.start(variables.searchCallVariables));
      setIsAdvanceSearchCall(true);
    };

    const openModel = () => {
      setIsModalOpen(true);
      setShowPopup(false);
    };

    useEffect(() => {
      focusTrap(showPopup);
    }, [showPopup]);

    return (
      <div
        className={`su__advance-blocks ${
          variables.searchClientType == SC_IDS.ZENDESK_SUPPORT_CONSOLE
            ? 'su__p-0'
            : 'su__mobile-child-block'
        }`}
      >
        <div
          className={`${variables.isConsoleTypeSC ? '' : 'su__mobile_component_none'} su__font-12`}
        >
          <div
            onClick={closeAdvancedSearch}
            className={`${!isDeviceMobile && topActionsView ? 'su__ml-il-20 su__mr_10px' : ''}`}
          >
            {topActionsView && (
              <Tooltip
                text={t(StaticStrings.ADVANCEDSEARCH)}
                position="bottom"
                className="position-relative"
              >
                <button
                  lang={variables.searchCallVariables.langAttr}
                  aria-label={t(StaticStrings.ADVANCEDSEARCH)}
                  data-trigger-a11y={A11Y_IDS.trap}
                  data-name-a11y="advanced_search"
                  type="button"
                  onClick={openModel}
                  className={`${
                    variables.isConsoleTypeSC && isDeviceMobile
                      ? 'su__zendesk_wd_ht'
                      : 'su__advance_search_btn '
                  }  su__flex-hcenter`}
                >
                  <Icons
                    IconName="Advancesearch"
                    width={`${variables.isConsoleTypeSC && isDeviceMobile ? '20' : '27.47'}`}
                    height={`${variables.isConsoleTypeSC && isDeviceMobile ? '13' : '18.23'}`}
                    transform="translate(-2 -5.997)"
                    color={IconColors.ListBookmarksInModalIcon}
                  />
                </button>
              </Tooltip>
            )}
            {!topActionsView && (
              <Tooltip
                text={t(StaticStrings.ADVANCEDSEARCH)}
                position="bottom"
                className="position-relative"
              >
                <button
                  lang={variables.searchCallVariables.langAttr}
                  aria-label={t(StaticStrings.ADVANCEDSEARCH)}
                  data-trigger-a11y={A11Y_IDS.trap}
                  data-name-a11y="advanced_search"
                  type="button"
                  onClick={openModel}
                  className={`${
                    variables.isConsoleTypeSC && isDeviceMobile
                      ? 'su__zendesk_wd_ht'
                      : 'su__advance_search_btn-web su__mr-5px  su__advance-search-button'
                  }  su__flex-hcenter ${
                    advanceSearchEnabled
                      ? 'su__advance-active-background'
                      : 'su__advance-icon-color'
                  } `}
                >
                  <Icons
                    className={` ${
                      advanceSearchEnabled
                        ? 'su__advance-icon-svg-fill-white'
                        : 'su__advance-icon-color'
                    }`}
                    IconName="Advancesearch"
                    width={`${variables.isConsoleTypeSC && isDeviceMobile ? '20' : '16.47'}`}
                    height={`${variables.isConsoleTypeSC && isDeviceMobile ? '13' : '10.23'}`}
                    transform="translate(-2 -5.997)"
                  />
                  <span
                    className={`${
                      advanceSearchEnabled ? 'su__color-white' : 'su__color-blue'
                    } advance-search-txt su__font-12 su__ml-5px `}
                  >
                    {t(StaticStrings.ADVANCEDSEARCH)}
                  </span>
                </button>
              </Tooltip>
            )}
          </div>
        </div>
        <div
          className={`${
            variables.isConsoleTypeSC ? 'su__d-none' : ''
          }   su__d-md-none su__text-center su__px-sm-1`}
        >
          <button
            lang={variables.searchCallVariables.langAttr}
            aria-label={t(StaticStrings.ADVANCEDSEARCH)}
            data-trigger-a11y={A11Y_IDS.trap}
            data-name-a11y="advanced_search"
            type="button"
            role={a11y.ROLES.BTN}
            tabIndex={tabIndexes.tabIndex_0}
            className={`su__mob-search-iner a11y-btn p-0 su__color-black ${
              isModalOpen ? 'su__mob-active' : ''
            } ${!isDeviceMobile ? 'su__sc-loading' : 'su__loading-Dnone'}`}
            onClick={() => setIsModalOpen(true)}
          >
            <div className="su__mob-icon">
              <svg
                id="manage_search_black_24dp"
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
              >
                <g id="Group_14" data-name="Group 14">
                  <path id="Path_8" data-name="Path 8" d="M0,0H24V24H0Z" fill="none" />
                </g>
                <g id="Group_15" data-name="Group 15">
                  <path
                    id="Path_9"
                    data-name="Path 9"
                    d="M6,9H3A1,1,0,0,1,2,8H2A1,1,0,0,1,3,7H6A1,1,0,0,1,7,8H7A1,1,0,0,1,6,9Zm0,3H3a1,1,0,0,0-1,1H2a1,1,0,0,0,1,1H6a1,1,0,0,0,1-1H7A1,1,0,0,0,6,12Zm13.88,6.29-3.12-3.12a4.979,4.979,0,0,1-3,.82,5,5,0,1,1,1.11-9.92,5,5,0,0,1,3.3,7.68l3.13,3.13a1,1,0,0,1,0,1.41h0A1.008,1.008,0,0,1,19.88,18.29ZM17,11a3,3,0,1,0-3,3A3.009,3.009,0,0,0,17,11ZM3,19h8a1,1,0,0,0,1-1h0a1,1,0,0,0-1-1H3a1,1,0,0,0-1,1H2A1,1,0,0,0,3,19Z"
                    fill="#434343"
                    className="su__active-path"
                  />
                </g>
              </svg>
            </div>
            <div
              lang={variables.searchCallVariables.langAttr}
              className="su__mob-advance-text su__font-12px su__line-height-n su__active-text"
            >
              {t(StaticStrings.ADVANCEDSEARCH)}
            </div>
          </button>
        </div>
        {isModalOpen && (
          <Fragment>
            <div
              id={A11Y_IDS.trap}
              className=" su__flex-hcenter su__position-fixed su__px-xs-2 su__trbl-0 su__zindex-3"
            >
              <form
                onSubmit={advancedSearchSubmit}
                className="su__modal-inner su__radius su__animate-fadown su__zindex-1 su__shadow-lg su__bg-white su__radius-1"
              >
                <div
                  role={a11y.ROLES.DIALOG}
                  tabIndex={-1}
                  className="su__bookmark-popup-title su__radius su__pr_14px su__pb_22px su__pt_14px su__pl_14px su__flex-vcenter"
                >
                  <h2
                    lang={variables.searchCallVariables.langAttr}
                    className="su__font-17 su__text-black su__f-medium su__text-truncate su__d-inline-block su__flex-1 su__position-relative su__my-0 su__text-blue "
                  >
                    {t(StaticStrings.ADVANCEDSEARCH)}
                  </h2>
                  <button
                    type="button"
                    lang={variables.searchCallVariables.langAttr}
                    className="a11y-btn p-0 order-4"
                    aria-label={t(StaticStrings.close_popup)}
                    role={a11y.ROLES.BTN}
                    tabIndex={tabIndexes.tabIndex_0}
                    onClick={() => setIsModalOpen(false)}
                  >
                    <Icons
                      className="su__close-icon su__cursor"
                      IconName="Close"
                      width="12"
                      height="12"
                      color={IconColors.AdvanceSearchCrossIcon}
                    />
                  </button>
                  <button
                    type="button"
                    data-trigger-a11y={A11Y_IDS.childTrap}
                    data-name-a11y="clear-all"
                    lang={variables.searchCallVariables.langAttr}
                    tabIndex={tabIndexes.tabIndex_0}
                    className={`su__font-13 su__text-blue su__pr-3 su__cursor su__f-medium a11y-btn su__text-hover-underline su__font-italic ${
                      advancedState.exactPhrase ||
                      advancedState.withOneOrMore ||
                      advancedState.withoutTheWords
                        ? 'd-block'
                        : 'd-none'
                    } `}
                    onClick={openClearFiltersModal}
                  >
                    {t(StaticStrings.CLEARALL)}
                  </button>
                </div>
                {showPopup && (
                  <Fragment>
                    <div
                      role={a11y.ROLES.ALERT}
                      className="su__flex-hcenter su__position-fixed su__trbl-0 su__zindex-4 su__px-sm-1"
                    >
                      <div
                        id={A11Y_IDS.childTrap}
                        className="su__modal-inner su__radius su__animate-fadown su__zindex-1 su__shadow-lg su__bg-white su__radius-1"
                      >
                        <div className="su__bookmark-popup-title su__px-4 su__py-3 su__flex-vcenter">
                          <div
                            lang={variables.searchCallVariables.langAttr}
                            className="su__font-14 su__text-black-shade su__f-regular su__flex-1 su__position-relative su__mb-3"
                          >
                            {t(StaticStrings.reset_changes)}
                          </div>
                        </div>
                        <div className="su__Conform-block su__flex-vcenter su__p-3 su__justify-content-end">
                          <button
                            lang={variables.searchCallVariables.langAttr}
                            tabIndex={tabIndexes.tabIndex_0}
                            className="su__btn su__bg-gray su__bg-gray su__px-4 su__py-2 su__mr-3 su__rtlml-3 su__rtlmr-0 su__radius su__cursor su__color_black "
                            onClick={() => setShowPopup(false)}
                          >
                            {t(StaticStrings.su_no)}
                          </button>
                          <button
                            lang={variables.searchCallVariables.langAttr}
                            tabIndex={tabIndexes.tabIndex_0}
                            className="su__btn su__bg-gray su__px-4 su__py-2 su__radius su__cursor su__color_black "
                            onClick={clearAdvancedFilters}
                          >
                            {t(StaticStrings.su_yes)}
                          </button>
                        </div>
                      </div>
                      <div
                        className="su__overlay su__zindex-1"
                        onClick={() => setShowPopup(false)}
                      />
                    </div>
                  </Fragment>
                )}
                {/*                     <input lang={variables.searchCallVariables.langAttr} className="su__form-control" type="text" name="su__exactPhrase" value={advancedState.exactPhrase} onChange={(e) => advanceSearchKeyword(e.target.value, 'exactPhrase' )} aria-label={t(StaticStrings.WITHTHEEXACTPHRASE)}/> */}
                <div
                  className="u__bookmark-inner su__px-0 su__pb_14px su__kh-input-br-8"
                  role={a11y.ROLES.GROUP}
                  aria-labelledby="su__advanceLabel"
                >
                  <div className="su__w-100 su__advanceLabel">
                    <div className="su__col-md-12 su__mb-2  su__pr_14px su__pl_14px su__font-14 su__f-regular">
                      <label
                        lang={variables.searchCallVariables.langAttr}
                        className={`su__mb-4px  su__f-medium  su__color_black su__d-block ${
                          isDeviceMobile ? 'su__font-12' : 'su__font-13'
                        } `}
                      >
                        {t(StaticStrings.WITHTHEEXACTPHRASE)}
                      </label>
                      <input
                        lang={variables.searchCallVariables.langAttr}
                        className="su__form-control  su__f-regular  su__popup_input_text su__height_40px"
                        type="text"
                        name="su__exactPhrase"
                        value={advancedState.exactPhrase}
                        onChange={(e) => advanceSearchKeyword(e.target.value, 'exactPhrase')}
                        aria-label={t(StaticStrings.WITHTHEEXACTPHRASE)}
                      />
                    </div>
                    <div className="su__col-md-12 su__mb-2  su__pr_14px su__pl_14px su__pt_10px su__font-14 su__f-regular">
                      <label
                        lang={variables.searchCallVariables.langAttr}
                        className={`su__d-block su__mb-4px su__f-medium su__color_black ${
                          isDeviceMobile ? 'su__font-12' : 'su__font-13'
                        }`}
                      >
                        {t(StaticStrings.WITHONEORMOREWORDS)}
                      </label>
                      <input
                        lang={variables.searchCallVariables.langAttr}
                        className="su__form-control su__popup_input_text su__f-bold"
                        type="text"
                        name="su__withOneOrMore"
                        value={advancedState.withOneOrMore}
                        onChange={(e) => advanceSearchKeyword(e.target.value, 'withOneOrMore')}
                        aria-label={t(StaticStrings.WITHONEORMOREWORDS)}
                      />
                    </div>
                    <div className="su__col-md-12 su__mb-2  su__pr_14px su__pl_14px su__pt_10px su__font-14 su__f-regular">
                      <label
                        lang={variables.searchCallVariables.langAttr}
                        className={`su__d-block su__mb-4px su__f-medium su__color_black ${
                          isDeviceMobile ? 'su__font-12' : 'su__font-13'
                        } `}
                      >
                        {t(StaticStrings.WITHOUTTHEWORDS)}
                      </label>
                      <input
                        lang={variables.searchCallVariables.langAttr}
                        className="su__form-control su__f-normal  su__popup_input_text"
                        type="text"
                        name="su__withoutTheWords"
                        value={advancedState.withoutTheWords}
                        onChange={(e) => advanceSearchKeyword(e.target.value, 'withoutTheWords')}
                        aria-label={t(StaticStrings.WITHOUTTHEWORDS)}
                      />
                    </div>
                    <div className="su__col-md-12 su__mb-2  su__pr_14px su__pl_14px su__pt_10px su__font-14 su__f-regular">
                      <label
                        lang={variables.searchCallVariables.langAttr}
                        className={`su__d-block su__mb-4px su__f-medium  su__color_black ${
                          isDeviceMobile ? 'su__font-12' : 'su__font-13'
                        }`}
                      >
                        {t(StaticStrings.WILDCARDSEARCH)}
                      </label>
                      <input
                        lang={variables.searchCallVariables.langAttr}
                        className="su__form-control su__f-normal  su__popup_input_text"
                        type="text"
                        name="su__wildcardSearch"
                        value={advancedState.wildcardSearch}
                        onChange={(e) => advanceSearchKeyword(e.target.value, 'wildcardSearch')}
                        aria-label={t(StaticStrings.WILDCARDSEARCH)}
                      />
                    </div>
                    <div className="su__advance_group su__col-md-12 su__box-sizing su__pr_14px su__pl_14px">
                      <button
                        tabIndex={tabIndexes.tabIndex_0}
                        type="submit"
                        className="su__btn-block su__font-regular su__full-btn su__text-white su__bg-blue-grd su__radius su__font-14 su__refine__search su__w-100 su__p-2 su__mt-12px"
                      >
                        {t(StaticStrings.REFINESEARCH)}
                      </button>
                    </div>
                  </div>
                </div>
              </form>
              <div className="su__overlay su__zindex-1" onClick={() => setIsModalOpen(false)}></div>
            </div>
          </Fragment>
        )}
      </div>
    );
  } catch (e) {
    console.log(`Error in Advance Search Componnent ${e}`);
    /** Handled alternate UI for Advance search on error in above component */
    const [isModalOpen, setIsModalOpen] = useState(false);
    const showPopUp = () => {
      setIsModalOpen(true);
      setTimeout(() => {
        setIsModalOpen(false);
      }, 1500);
    };
    return (
      <Fragment>
        <div className="su__w-100">
          <div className="su__search_advance su__d-xs-none su__d-md-flex  su__font-12 su__radius-1 su__mr-2 su__rtlmr-0 su__rtlml-0">
            <Tooltip
              text={t(StaticStrings.advance_search)}
              position="top"
              className="position-relative"
            >
              <button
                type="button"
                onClick={() => showPopUp()}
                className="su__filters-button su__btn btn_advance-search su__radius-2 su__bg-white su__flex-hcenter su__rtlmr-0 su__rtlml-3"
              >
                <Icons
                  className="su__advance-icon su__bg-white-hover-50"
                  IconName="Advancesearch"
                  width="16"
                  widthInner="18"
                  height="16"
                  heightInner="1.928"
                  rx="0.964"
                  cx="2.785"
                  cy="2.785"
                  r="2.785"
                  transform="translate(0 0)"
                  fill="#919bb0"
                />
                <span
                  lang={variables.searchCallVariables.langAttr}
                  className="su__text-black su__font-13 su__p-1 su__rtlp-1 su__font-regular"
                >
                  {t(StaticStrings.advance_search)}
                </span>
              </button>
            </Tooltip>
          </div>
        </div>
        {isModalOpen && <UnderConstruction component={'Advance Search'} />}
      </Fragment>
    );
  }
};
export default AdvanceSearch;
