import React, { useState, Fragment, useEffect, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import variables from '../../../redux/variables';
import { search } from '../../../redux/ducks';
import StaticStrings from 'StaticStrings';
import { a11y, tabIndexes, A11Y_IDS, focusTrap } from '../../../constants/a11y';
import useDevice from 'function-library/hooks/use-device/use-device';
import { EVENT_NAMES } from 'constants/constants';

const ResultsPerPage = () => {
  try {
    /**
     *  Change Languages state
     */
    const { t } = useTranslation();
    const modalRef = useRef(null);
    const { isDeviceMobile } = useDevice();
    const [showResultsPerPage, setIsModalOpen] = useState(false);
    const [posY, setPosY] = useState(0);
    let searchSuggest = useSelector((state) => state.searchResult);
    /** Update Results per page */
    let [advancedState, setAdvancedState] = useState({
      resultsPerPage: variables.searchCallVariables.resultsPerPage
    });
    const setResultsPerPage = (resultsPerPage) => {
      setIsModalOpen(false);
      setAdvancedState({ resultsPerPage: resultsPerPage });
      variables.searchCallVariables.resultsPerPage = resultsPerPage;
      variables.searchCallVariables.pageSize = resultsPerPage;
      variables.searchCallVariables.from = 0;
      variables.searchCallVariables.pageNo = 1;
      variables.searchSource = 'pagination';
      variables.searchCallVariables.pagingAggregation = [];
      dispatch(search.start(variables.searchCallVariables));
      let focus =
        document.querySelector(`[su-container]`) ||
        document.querySelector(`[ng-app = "searchUnifyApp"]`);
      window.scroll({
        top: focus.offsetTop,
        behavior: 'smooth'
      });
    };

    useEffect(() => {
      setIsModalOpen(false);
      setAdvancedState({ resultsPerPage: variables.searchCallVariables.resultsPerPage });
    }, [searchSuggest]);

    const dispatch = useDispatch();

    useEffect(() => {
      focusTrap(showResultsPerPage);
    }, [showResultsPerPage]);

    useEffect(() => {
      document.addEventListener('keydown', handleHideDropdown);
      document.addEventListener('click', handleClickOutside, true);
      return () => {
        document.removeEventListener('keydown', handleHideDropdown);
        document.removeEventListener('click', handleClickOutside, true);
      };
    });
    const handleHideDropdown = (e) => {
      e.key === EVENT_NAMES.ESCAPE && setIsModalOpen(false);
    };

    const handleClickOutside = (e) => {
      if (modalRef.current && !modalRef.current.contains(e.target)) {
        setIsModalOpen(false);
      }
    };
    /**
     * The function used to calculate position of pagination modal to be opened
     * @param {*} event event contains all info related to button
     */
    const calPosForPaginationPopUp = (event) => {
      let popUpHeight = 112;
      const paginationButton = event.target;
      const paginationButtonRect = paginationButton.getBoundingClientRect();

      // Get distance from bottom, accounting for bottom nav bar
      const bottomNavHeight =
        isDeviceMobile && !variables.isConsoleTypeSC
          ? Math.max(
              window.outerHeight - window.innerHeight,
              56 // Fallback for mobile
            )
          : 0;
      const distanceFromBottom =
        (window.visualViewport?.height || window.innerHeight) -
        paginationButtonRect.bottom -
        bottomNavHeight;

      setIsModalOpen(!showResultsPerPage);

      if (distanceFromBottom > popUpHeight) {
        setPosY(40);
      } else {
        setPosY(-115);
      }
    };

    return (
      <Fragment>
        {searchSuggest && searchSuggest.result ? (
          <div
            className="su__d-inline-block su__position-relative su__align_resultPerPage su__sc-loading"
            ref={modalRef}
          >
            <span
              lang={variables.searchCallVariables.langAttr}
              className="su__color-black su__pr-1 su__font-16 su__f-normal "
            >
              {' '}
              {t(StaticStrings.results_perpage)}{' '}
            </span>
            <button
              data-trigger-a11y={A11Y_IDS.dropdown_trap}
              data-name-a11y="pagination"
              type="button"
              lang={variables.searchCallVariables.langAttr}
              aria-label={`${t(StaticStrings.results_perpage)} ${advancedState.resultsPerPage}`}
              role={a11y.ROLES.BTN}
              tabIndex={tabIndexes.tabIndex_0}
              onClick={calPosForPaginationPopUp}
              className={`su__d-inline-block resultsPerPage su__cursor su__radius-1  su__font-12 su__f-normal su__pr-2 su__pl-2 su__pt-1 su__pb-1 su__w_62 su_h_38 su__sc-loading`}
            >
              <span className="su__pr-2 su__rtlpl-2 su__font-16">
                {advancedState.resultsPerPage}
              </span>
              <i
                className={`${
                  showResultsPerPage ? 'su__arrow-up su__top-2px su__left-0' : 'su__arrow-down'
                }`}
              ></i>
            </button>
            {showResultsPerPage && (
              <div
                id={A11Y_IDS.dropdown_trap}
                className="su__pr-0 resultsPerPage su__right-0 pageSize su__bg-white su__position-absolute su__d-inline-block su__font-12 su__radius-1 su__f-regular su__w_62 resultPerPageBox su__z-index su__right-unset-rtl su__left-0-rtl"
                style={{ top: `${posY}px` }}
              >
                <button
                  type="button"
                  lang={variables.searchCallVariables.langAttr}
                  aria-label={`${t(StaticStrings.results_perpage)} 10`}
                  role={a11y.ROLES.BTN}
                  tabIndex={tabIndexes.tabIndex_0}
                  className={`su__cursor a11y-btn d-block su__font-11 su__line-height-20  su__f-normal su__pl_6 ${
                    advancedState.resultsPerPage === 10 ? 'su__color-blue' : 'su__color_grey '
                  }`}
                  onClick={() => setResultsPerPage(10)}
                  value="10"
                >
                  10
                </button>
                <button
                  type="button"
                  lang={variables.searchCallVariables.langAttr}
                  aria-label={`${t(StaticStrings.results_perpage)} 20`}
                  role={a11y.ROLES.BTN}
                  tabIndex={tabIndexes.tabIndex_0}
                  className={`su__cursor a11y-btn d-block su__font-11 su__line-height-20  su__f-normal su__pl_6 ${
                    advancedState.resultsPerPage === 20 ? 'su__color-blue' : 'su__color_grey '
                  }`}
                  onClick={() => setResultsPerPage(20)}
                  value="20"
                >
                  20
                </button>
                <button
                  type="button"
                  lang={variables.searchCallVariables.langAttr}
                  aria-label={`${t(StaticStrings.results_perpage)} 30`}
                  role={a11y.ROLES.BTN}
                  tabIndex={tabIndexes.tabIndex_0}
                  className={`su__cursor a11y-btn d-block su__font-11 su__line-height-20  su__f-normal su__pl_6 ${
                    advancedState.resultsPerPage === 30 ? 'su__color-blue' : 'su__color_grey'
                  }`}
                  onClick={() => setResultsPerPage(30)}
                  value="30"
                >
                  30
                </button>
                <button
                  type="button"
                  lang={variables.searchCallVariables.langAttr}
                  aria-label={`${t(StaticStrings.results_perpage)} 40`}
                  role={a11y.ROLES.BTN}
                  tabIndex={tabIndexes.tabIndex_0}
                  className={`su__cursor a11y-btn d-block su__font-11 su__line-height-20  su__f-normal su__pl_6 ${
                    advancedState.resultsPerPage === 40 ? 'su__color-blue' : 'su__color_grey'
                  }`}
                  onClick={() => setResultsPerPage(40)}
                  value="40"
                >
                  40
                </button>
                <button
                  type="button"
                  lang={variables.searchCallVariables.langAttr}
                  aria-label={`${t(StaticStrings.results_perpage)} 50`}
                  role={a11y.ROLES.BTN}
                  tabIndex={tabIndexes.tabIndex_0}
                  className={`su__cursor a11y-btn d-block su__font-11 su__line-height-20  su__f-normal su__pl_6 ${
                    advancedState.resultsPerPage === 50 ? 'su__color-blue' : 'su__color_grey'
                  }`}
                  onClick={() => setResultsPerPage(50)}
                  value="50"
                >
                  50
                </button>
              </div>
            )}
          </div>
        ) : null}
      </Fragment>
    );
  } catch (e) {
    console.log('Error in navigation paginaition component', e);
    return <div></div>;
  }
};

export default ResultsPerPage;
