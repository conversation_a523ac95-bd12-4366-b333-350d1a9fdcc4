/* global jest, it, expect, describe, beforeEach, afterEach */
import React from 'react';
import { shallow } from 'enzyme';
import { useSelector, useDispatch } from 'react-redux';
import ResultsPerPage from './';
// import { act } from 'react-dom/test-utils';
// Mock the dependencies
jest.mock('react-i18next', () => ({
  useTranslation: () => ({ t: (key) => key })
}));

jest.mock('../../../redux/variables', () => ({
  searchCallVariables: {
    resultsPerPage: 20,
    langAttr: 'en',
    pageSize: 20,
    from: 0,
    pageNo: 1,
    pagingAggregation: []
  },
  searchSource: ''
}));

jest.mock('../../../redux/ducks', () => ({
  search: {
    start: jest.fn()
  }
}));

jest.mock('StaticStrings', () => ({
  results_perpage: 'Results per page'
}));

// Mock Redux hooks
jest.mock('react-redux', () => ({
  useDispatch: jest.fn(),
  useSelector: jest.fn()
}));

describe('ResultsPerPage Component', () => {
  let mockDispatch;

  beforeEach(() => {
    mockDispatch = jest.fn();
    useDispatch.mockReturnValue(mockDispatch);
    useSelector.mockImplementation(() => ({ result: {} }));

    // Mock window.scroll
    window.scroll = jest.fn();

    // Mock document.querySelector
    document.querySelector = jest.fn(() => ({ offsetTop: 0 }));
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders without crashing', () => {
    const wrapper = shallow(<ResultsPerPage />);
    expect(wrapper.exists()).toBe(true);
  });

  it('initial state is set correctly', () => {
    const wrapper = shallow(<ResultsPerPage />);
    expect(wrapper.find('button').first().props()['aria-label']).toBe('Results per page 20');
  });

  it('opens dropdown when button is clicked', () => {
    const wrapper = shallow(<ResultsPerPage />);
    const mockEvent = {
      target: {
        getBoundingClientRect: () => ({
          bottom: 500
        })
      }
    };
    wrapper.find('button').first().simulate('click', mockEvent);
    expect(wrapper.find('.pageSize').exists()).toBe(true);
  });

  it('updates results per page when an option is selected', () => {
    const wrapper = shallow(<ResultsPerPage />);
    const mockEvent = {
      target: {
        getBoundingClientRect: () => ({
          bottom: 500
        })
      }
    };
    wrapper.find('button').first().simulate('click', mockEvent);
    wrapper.find('.pageSize button').at(2).simulate('click'); // Selecting 30 results per page

    expect(mockDispatch).toHaveBeenCalled();
  });

  it('should handle errors gracefully', () => {
    useSelector.mockImplementation(() => {
      throw new Error('Test error');
    });
    useDispatch.mockImplementation(() => jest.fn());
    const wrapper = shallow(<ResultsPerPage />);
    expect(wrapper.text()).toBe('');
  });
  // it('closes dropdown when Escape key is pressed', async () => {
  //   const wrapper = shallow(<ResultsPerPage />);
  //   const mockEvent = { target: { getBoundingClientRect: jest.fn(() => ({ top: 100 })) } };

  //   await act(async () => {
  //     wrapper.find('button').at(1).simulate('click', mockEvent);
  //     wrapper.find('button').at(1).simulate('keydown', { key: 'Escape' });
  //   });

  //   wrapper.update();

  //   expect(wrapper.find('.modal-class').exists()).toBe(false);
  // });
  // it('closes dropdown when clicking outside', () => {
  //   const wrapper = shallow(<ResultsPerPage />);
  //   wrapper.find('button').first().simulate('click');

  //   const event = new MouseEvent('click', { bubbles: true });
  //   document.dispatchEvent(event);

  //   expect(wrapper.find('.pageSize').exists()).toBe(false);
  // });

  // it('should update state correctly when a new value is selected', () => {
  //   const wrapper = shallow(<ResultsPerPage />);
  //   wrapper.find('button').first().simulate('click');
  //   wrapper.find('.pageSize button').at(1).simulate('click'); // Selecting 20 results per page

  //   expect(mockDispatch).toHaveBeenCalledWith(expect.any(Function));
  // });

  // it('should apply the correct CSS class when a value is selected', () => {
  //   useSelector.mockImplementation(() => ({ result: {} }));
  //   const wrapper = shallow(<ResultsPerPage />);

  //   wrapper.find('button').first().simulate('click');
  //   expect(wrapper.find('.pageSize button').at(2).hasClass('su__color-blue')).toBe(false);

  //   wrapper.find('.pageSize button').at(2).simulate('click'); // Selecting 30
  //   wrapper.update();

  //   expect(wrapper.find('.pageSize button').at(2).hasClass('su__color-blue')).toBe(true);
  // });

  it('should not open dropdown when results are not available', () => {
    useSelector.mockImplementation(() => ({ result: null }));
    const wrapper = shallow(<ResultsPerPage />);
    expect(wrapper.find('button').exists()).toBe(false);
  });

  // it('should not throw errors when clicking without valid event target', () => {
  //   const wrapper = shallow(<ResultsPerPage />);
  //   expect(() => {
  //     wrapper.find('button').first().simulate('click', {});
  //   }).not.toThrow();
  // });
});
