/* global gza */
import React, { useState, useEffect, useRef } from 'react';
import { search, autocomplete, recommendations } from '../../../redux/ducks';
import { useDispatch, useSelector } from 'react-redux';
import AutoSuggestion from 'components/feature-components/auto-suggest-search-app/index.jsx';
import Bookmarks from '../bookmarks/index';
import variables from '../../../redux/variables';
import Icons from '../../../assets/svg-icon/svg';
import useComponentVisible from '../../../event-handler/event';
import { useTranslation } from 'react-i18next';
import utilityMethods from '../../../redux/utilities/utility-methods';
import UnderConstruction404 from 'components/feature-components/under-consruction/error404.jsx';
import LinearProgress from '@material-ui/core/LinearProgress';
import IconColors from '../../../IconColors';
import AdvanceSearch from '../advance-search/index';
import StaticStrings from '../../../StaticStrings';
import { A11Y_IDS, a11y, tabIndexes } from '../../../constants/a11y';
import { useDevice } from 'function-library/hooks';
import useOnClickOutsideRef from 'event-handler/outside-click';
// autosuggestion end
import { SC_IDS, EVENT_CODES } from 'constants/constants';

const OKTA = 'Okta';

const Search = (props) => {
  try {
    let urlOpensInNewTab = props.urlOpensInNewTab;
    const { isDeviceMobile } = useDevice();
    let isAdvanceSearchCallTriggered = props.isAdvanceSearchCall;
    let { SearchboxClicked, isAdvanceSearchCall, setIsAdvanceSearchCall } = props;
    const searchBoxRef = useOnClickOutsideRef(() => props.setSearchboxClicked(false), null, false);
    let setAdvanceSearchCallTriggered = props.setIsAdvanceSearchCall;
    let searchError, validationError;
    [searchError, validationError] = useSelector((state) => {
      return [state.error, state.validationError];
    });

    const topActionsView = utilityMethods.isTopActionsView();

    window.onpopstate = () => {
      variables.onpopstate = true;
      utilityMethods.importQueryParamsToVariables();
      variables.searchSource = 'searchbox';
      variables.searchCallVariables.pagingAggregation = [];
      dispatch(search.start(variables.searchCallVariables));
      if (searchResult.searchClientSettings?.recommendations) {
        dispatch(recommendations.recommendationsStart(variables.searchCallVariables));
      }
      inputValue.current.value = variables.searchCallVariables.searchString;
    };

    let searchResult = useSelector((state) => state.searchResult);
    let autocompleteResult = useSelector((state) => state.autocomplete);

    let instantSearch = searchResult.searchClientSettings?.autoCompleteInstant == 1 ? true : false;
    const { t } = useTranslation();
    const inputValue = useRef(null);
    const [showIcons, setShowIcons] = useState(false);
    const [prevIdCall, setPrevIdCall] = useState();
    const [isComponentShow, setIsComponentShow] = useState();
    const [changeSearchDataOnArrowKey, setChangeSearchDataOnArrowKey] = useState(
      Array(variables.searchCallVariables.resultsPerPage)
        .fill(null)
        .map((_, i) => i)
    );
    const [previousAction, setPreviousAction] = useState('');
    /** it includes data from recent seach , autosuggestiona and hits */
    let [tempData, setTempData] = useState([]);
    const [isPopupVisible, setIsPopupVisible] = useState(false);
    const popupRef = useRef(null);

    useEffect(() => {
      let tempDataInner = [];
      autocompleteResult &&
        autocompleteResult.recentSearchHistory &&
        autocompleteResult.recentSearchHistory.length != 0 &&
        tempDataInner.push(...autocompleteResult.recentSearchHistory);
      autocompleteResult &&
        autocompleteResult.result &&
        tempDataInner.push(...autocompleteResult.result.hits);
      variables.autocompleteCallVariables.searchString &&
        tempDataInner.push({
          title: variables.autocompleteCallVariables.searchString,
          type: 'userTypedData'
        });
      if (autocompleteResult && autocompleteResult.searchClientSettings && tempDataInner.length) {
        setChangeSearchDataOnArrowKey(
          Array(Number(tempDataInner.length - 1))
            .fill(null)
            .map((_, i) => i)
        );
      }
      setTempData(tempDataInner);
    }, [autocompleteResult]);

    /**
     * dispatches the search action.
     * @param {args received from the debounce method} text
     */

    const dispatchSearchAction = (event) => {
      /**on first load , intialing the states */
      let tempDataInner = [];
      props.setSearchboxClicked(true);
      autocompleteResult &&
        autocompleteResult.recentSearchHistory &&
        autocompleteResult.recentSearchHistory.length != 0 &&
        tempDataInner.push(...autocompleteResult.recentSearchHistory);
      autocompleteResult &&
        autocompleteResult.result &&
        tempDataInner.push(...autocompleteResult.result.hits);
      variables.autocompleteCallVariables.searchString &&
        tempDataInner.push({
          title: variables.autocompleteCallVariables.searchString,
          type: 'userTypedData'
        });
      if (tempDataInner && tempDataInner.length != 0) {
        setChangeSearchDataOnArrowKey(
          Array(Number(tempDataInner.length - 1))
            .fill(null)
            .map((_, i) => i)
        );
      }
      setTempData(tempDataInner);
      setPreviousAction('');

      variables.searchCallVariables.from = 0;
      variables.searchCallVariables.pageNo = 1;
      // control,esc,shift, spacebar, 4 arrow key ,Alt
      // no search hit on these keys
      if (
        ![
          EVENT_CODES.CONTROL,
          EVENT_CODES.ESCAPE,
          EVENT_CODES.SHIFT,
          EVENT_CODES.SPACE,
          EVENT_CODES.LEFT_ARROW,
          EVENT_CODES.UP_ARROW,
          EVENT_CODES.RIGHT_ARROW,
          EVENT_CODES.DOWN_ARROW,
          EVENT_CODES.ALT
        ].includes(event.keyCode) &&
        !event.ctrlKey &&
        !event.altKey
      ) {
        if (event.currentTarget.value) {
          setShowIcons(true);
        } else {
          setShowIcons(false);
        }

        if (instantSearch) {
          if (event.keyCode !== EVENT_CODES.ENTER) {
            clearTimeout(prevIdCall);
            variables.searchSource = 'instantSearch';
            searchCall(event);
          }
        } else {
          setIsComponentShow(true);
          if (event.keyCode === EVENT_CODES.ENTER && !isAdvanceSearchCallTriggered) {
            if (tempData[changeSearchDataOnArrowKey[0]]?.href && previousAction) {
              inputValue.current.value = tempData[tempData.length - 1].title;
              window.open(tempData[changeSearchDataOnArrowKey[0]].href);
              resultOpenNewTab(
                tempData[changeSearchDataOnArrowKey[0]],
                changeSearchDataOnArrowKey[0]
              );
            } else {
              variables.searchSource = 'searchbox';
              searchCall(event);
            }
            setIsComponentShow(false);
            props.setSearchboxClicked(false);
          } else {
            setAdvanceSearchCallTriggered(false);
            autocompleteResult.result = null;
            variables.autocompleteCallVariables.searchString = event.currentTarget.value;
            variables.autocompleteCallVariables.aggregations =
              variables.searchCallVariables.aggregations;
            if (event.currentTarget.value !== '') {
              event.persist();
              let hasProp = Object.prototype.hasOwnProperty.call(event.currentTarget, 'value');
              clearTimeout(prevIdCall);
              setPrevIdCall(
                setTimeout(() => {
                  autoComplete(event, hasProp);
                }, 1000)
              );
            } else {
              setIsComponentShow(false);
            }
            if (event.currentTarget.value !== '' && event.keyCode === EVENT_CODES.F5) {
              document.getElementById('search-box-search').value = '';
            }
          }
        }
      } else if ([EVENT_CODES.DOWN_ARROW].includes(event.keyCode)) {
        //down
        autocompleteResult && autocompleteResult.result?.hits.length && keyPressed('down');
      } else if ([EVENT_CODES.UP_ARROW].includes(event.keyCode)) {
        // up
        autocompleteResult && autocompleteResult.result?.hits.length && keyPressed('up');
      } else if ([EVENT_CODES.ESCAPE].includes(event.keyCode)) {
        setIsComponentShow(false);
      }
    };

    const resultOpenNewTab = (item, index) => {
      gza('search', {
        searchString: variables.autocompleteCallVariables.searchString,
        result_count: autocompleteResult.result.hits.length,
        page_no: 0,
        uid: variables.autocompleteCallVariables.uid,
        search_type: searchResult.searchType,
        searchUid: variables.autocompleteCallVariables.searchUid,
        filter: [],
        conversion: [
          {
            rank: index + 1,
            url: item['href'],
            relevance_score: item['_score'],
            subject: item['highlight']['TitleToDisplayString'][0] || '',
            es_id:
              item['sourceName'] + '/' + item['objName'] + '/' + encodeURIComponent(item['_id']),
            sc_analytics_fields: item['trackAnalytics']
          }
        ]
      });
    };

    let keyPressed = (action) => {
      let data = changeSearchDataOnArrowKey;
      if (action == 'down' && previousAction) {
        data.push(data.shift());
      } else if (action == 'up' && previousAction) {
        data.unshift(data.pop());
      }
      if (
        tempData[data[0]] &&
        tempData[data[0]].type &&
        ['userTypedData', 'recentSearch', 'autoSuggestion'].includes(tempData[data[0]].type)
      ) {
        inputValue.current.value = tempData[data[0]].title;
      } else {
        inputValue.current.value =
          tempData[data[0]].highlight.TitleToDisplayString[0] || tempData[data[0]].href;
      }
      tempData[data[0]].type != 'userTypedData' &&
        document.getElementsByClassName('su__suggestions-list')[data[0]].focus();
      document.getElementById('search-box-search').focus();
      setPreviousAction(action);
      setChangeSearchDataOnArrowKey([...data]);
    };

    const autoComplete = (event, hasProp) => {
      if (hasProp) {
        dispatch(autocomplete.start(variables.autocompleteCallVariables));
      }
    };

    const searchCall = (e) => {
      variables.searchCallVariables.searchString = e.currentTarget.value;
      variables.autocompleteCallVariables.smartFacetsClicked = false;
      variables.searchSource = 'searchbox';
      variables.searchCallVariables.pagingAggregation = [];
      dispatch(search.start(variables.searchCallVariables));
      inputValue.current.value = variables.searchCallVariables.searchString;
      if (searchResult?.searchClientSettings?.recommendations) {
        dispatch(recommendations.recommendationsStart(variables.searchCallVariables));
      }
    };

    const { ref, isComponentVisible, setIsComponentVisible } = useComponentVisible(true);

    const dispatch = useDispatch();
    useEffect(() => {
      inputValue.current.focus();
      inputValue.current.value = variables.searchCallVariables.searchString;
    }, [variables.searchCallVariables.searchString]);

    const searchClick = () => {
      setIsComponentVisible(false);
      variables.searchCallVariables.from = 0;
      variables.searchCallVariables.pageNo = 1;
      variables.autocompleteCallVariables.smartFacetsClicked = false;
      variables.searchCallVariables.searchString =
        document.getElementById('search-box-search').value;
      variables.searchSource = 'searchbox';
      variables.searchCallVariables.pagingAggregation = [];
      dispatch(search.start(variables.searchCallVariables));
      if (searchResult.searchClientSettings?.recommendations) {
        dispatch(recommendations.recommendationsStart(variables.searchCallVariables));
      }
    };
    /**
     * clear input box Value.
     * @param { On click clear input box Value}
     */
    const searchCallEventFromAutocomplete = () => {
      setShowIcons(true);
      variables.searchCallVariables.from = 0;
      variables.searchCallVariables.pageNo = 1;
      variables.autocompleteCallVariables.smartFacetsClicked = false;
      variables.searchSource = 'searchbox';
      variables.searchCallVariables.searchString =
        document.getElementById('search-box-search').value;
      variables.searchCallVariables.pagingAggregation = [];
      dispatch(search.start(variables.searchCallVariables));
      let restValue = document.getElementById('search-box-search').value;
      if (restValue === '') {
        setShowIcons(false);
      }
    };

    const ClearIcon = () => {
      let restValue = (document.getElementById('search-box-search').value = '');
      setIsComponentShow(false);
      if (restValue === '') {
        setShowIcons(false);
        variables.searchCallVariables.searchString = '';
        variables.searchSource = 'clear-icon';
        variables.searchCallVariables.from = 0;
        variables.searchCallVariables.pageNo = 1;
        variables.searchCallVariables.pagingAggregation = [];
        if (variables.searchClientType != SC_IDS.ZENDESK_SUPPORT_CONSOLE) {
          dispatch(search.start(variables.searchCallVariables));
        }
        if (searchResult.searchClientSettings?.recommendations) {
          dispatch(recommendations.recommendationsStart(variables.searchCallVariables));
        }
      }
      variables.searchAnalyticsObject = {
        searchString: searchResult.searchQuery,
        result_count: searchResult.result.hits.length,
        page_no: variables.searchCallVariables.pageNo,
        uid: variables.searchCallVariables.uid,
        filter: variables.searchCallVariables.aggregations,
        exactPhrase: variables.searchCallVariables.exactPhrase,
        withOneOrMore: variables.searchCallVariables.withOneOrMore,
        withoutTheWords: variables.searchCallVariables.withoutTheWords,
        dym: undefined,
        searchUid: variables.searchCallVariables.searchUid
      };
    };

    const inputClicked = () => {
      props.setSearchboxClicked(true);
    };

    const closeSearchfocus = () => {
      props.setSearchboxClicked(false);
      inputValue.current.value = null;
    };

    useEffect(() => {
      props.setShowHeaderComponents(
        isComponentVisible &&
          autocompleteResult &&
          autocompleteResult.result &&
          autocompleteResult.result.hits &&
          autocompleteResult.result.hits.length != 0 &&
          inputValue.current?.value &&
          isComponentShow &&
          (!instantSearch || (instantSearch && isComponentShow))
      );
    }, [
      isComponentVisible,
      autocompleteResult,
      autocompleteResult?.result?.hits,
      instantSearch,
      isComponentShow,
      inputValue.current?.value,
      isComponentShow
    ]);

    useEffect(() => {
      window.scrollTo({
        top: 0
      });
      document.body.style.overflow = props.ShowHeaderComponents && isDeviceMobile ? 'hidden' : '';
    }, [props.ShowHeaderComponents]);
    const togglePopup = () => {
      setIsPopupVisible(!isPopupVisible);
    };

    const handleClickOutside = (event) => {
      if (popupRef.current && !popupRef.current.contains(event.target)) {
        setIsPopupVisible(false);
      }
    };

    useEffect(() => {
      document.addEventListener('mousedown', handleClickOutside);
      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
      };
    }, []);

    const removeHsctoken = () => {
      document.cookie = `hscToken_${variables.searchCallVariables.uid}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
      if (window.scConfiguration?.ssoType === OKTA) {
        fetch(
          `${variables.searchClientProps.instanceName}/saml/logout?uid=${variables.searchCallVariables.uid}`,
          {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              user: localStorage.getItem('user') // Fetch user data correctly from localStorage
            })
          }
        )
          .then(async (response) => {
            if (response.ok) {
              const result = await response.json();
              if (result.location != '') {
                window.location.href = result.location;
              }
              console.log('Logout request sent successfully');
            } else {
              console.error('Failed to send logout request', response.status);
            }
          })
          .catch((error) => console.error('Error:', error));
      } else {
        window.location.reload();
      }
    };

    return (
      <section
        className={`su__w-100 su__py-4 su__search_section su__bg-blue-grd ${
          variables.searchClientType == SC_IDS.INTRANET_SEARCH ? 'su__chrome-extension' : ''
        }`}
      >
        <a
          id={A11Y_IDS.skipToMainContent}
          lang={variables.searchCallVariables.langAttr}
          href="#su__skiplink"
          aria-label={t(StaticStrings.skip_to_main_content)}
          tabIndex={tabIndexes.tabIndex_0}
          className="su__skip-link"
        >
          {t(StaticStrings.skip_to_main_content)}
        </a>
        <button
          type="button"
          id="hit-me"
          className="su__d-none"
          onClick={searchCallEventFromAutocomplete}
          value={instantSearch}
        >
          {StaticStrings.SEARCHCALL}
        </button>
        <div
          className={`su__container su__container_custom su__position-relative su__searchBox-header ${
            isDeviceMobile && props.ShowHeaderComponents ? 'su__p-ie-20px' : 'su__pr-1-inline'
          }`}
        >
          <div
            autoComplete="off"
            id="searchForm"
            className={`${
              variables.isConsoleTypeSC ? 'su__pl-10px' : 'su__pl-20px'
            } su__search-forms su__d-flex su__m-0 su__px-3 ${
              !isDeviceMobile ? 'su__px-56px' : null
            }`}
            onSubmit={(e) => {
              e.preventDefault();
            }}
          >
            {props.ShowHeaderComponents && isDeviceMobile ? (
              <div onClick={() => closeSearchfocus()} className="su__close_sb su__bg-color">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  id="arrow-back"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                >
                  <g id="arrow-back-2" data-name="arrow-back">
                    <rect
                      id="Rectangle_37"
                      data-name="Rectangle 37"
                      width="24"
                      height="24"
                      transform="translate(24) rotate(90)"
                      fill="#fff"
                      opacity="0"
                    />
                    <path
                      id="Path_30"
                      data-name="Path 30"
                      d="M19,11H7.14l3.63-4.36A1,1,0,0,0,9.23,5.36l-5,6a1.19,1.19,0,0,0-.09.15.127.127,0,0,1-.07.13.961.961,0,0,0,0,.72.127.127,0,0,0,.07.13,1.19,1.19,0,0,0,.09.15l5,6a1,1,0,1,0,1.54-1.28L7.14,13H19a1,1,0,0,0,0-2Z"
                      fill="#fff"
                    />
                  </g>
                </svg>
              </div>
            ) : null}

            <div
              className={`su_searchbar_box ${
                variables.isConsoleTypeSC && isDeviceMobile && props.SearchboxClicked
                  ? 'su__position-unset'
                  : 'su__position-relative'
              }`}
            >
              <div
                className={`${
                  variables.isConsoleTypeSC
                    ? isDeviceMobile && SearchboxClicked
                      ? 'su__position-relative'
                      : null
                    : 'su__position-relative'
                }`}
                ref={ref}
              >
                {' '}
                {/*work on this to change search box width */}
                <div
                  ref={searchBoxRef}
                  className={`${
                    variables.isConsoleTypeSC && isDeviceMobile && SearchboxClicked
                      ? 'su__search_elements'
                      : ''
                  } ${
                    variables.isConsoleTypeSC &&
                    isDeviceMobile &&
                    SearchboxClicked &&
                    !props.ShowHeaderComponents
                      ? 'su__sb_clicked'
                      : ''
                  } ${
                    variables.isConsoleTypeSC &&
                    isDeviceMobile &&
                    props.SearchboxClicked &&
                    props.ShowHeaderComponents
                      ? 'su__sb_clicked_autocomplete'
                      : ''
                  } su__radius-2 su__d-flex su__position-relative su__search-box-unq`}
                >
                  <button
                    type="button"
                    lang={variables.searchCallVariables.langAttr}
                    role={a11y.ROLES.BTN}
                    tabIndex={tabIndexes.tabIndex_0}
                    aria-label={t(StaticStrings.search)}
                    className={`${
                      variables.isConsoleTypeSC && isDeviceMobile
                        ? 'su__zendeskBox'
                        : 'su__search_box_btn'
                    }  su__btn  su__animate-zoom su__flex-vcenter su__position-absolute su__zindex su__bg-transparent border-0`}
                    onClick={searchClick}
                  >
                    <span>
                      <Icons
                        IconName="SearchBoxSearchBtn"
                        width="24"
                        height="24"
                        color={IconColors.SearchIcon}
                        transform="translate(0 -2)"
                      />
                    </span>
                  </button>

                  <input
                    onClick={() => inputClicked()}
                    lang={variables.searchCallVariables.langAttr}
                    ref={inputValue}
                    id="search-box-search"
                    className={`${
                      variables.isConsoleTypeSC && isDeviceMobile ? 'su__zendesk_searchbox ' : null
                    } su__input-search-box su__w-100 su__su__font-14 su__text-dark su__border-none su__radius-2 su__box-shadow`}
                    type="input"
                    placeholder={t(StaticStrings.search_here) + '...'}
                    onKeyUp={(e) => dispatchSearchAction(e)}
                    aria-label={t(StaticStrings.search_here)}
                    onChange={(e) => (variables.searchCallVariables.searchString = e.target.value)}
                  />
                  {showIcons || variables.searchCallVariables.searchString ? (
                    <button
                      lang={variables.searchCallVariables.langAttr}
                      type="button"
                      tabIndex={tabIndexes.tabIndex_0}
                      aria-label={t(StaticStrings.Clear)}
                      className={`su__mr-3 su__animate-zoom su__position-absolute su__cursor a11y-btn p-0 su__input-cross ${
                        variables.isConsoleTypeSC && isDeviceMobile
                          ? 'su__input-close-console su__right-unset-rtl'
                          : 'su__input-close'
                      }`}
                      onClick={ClearIcon}
                    >
                      <Icons
                        IconName="SearchBoxClose"
                        width="22"
                        height="22"
                        color={IconColors.SearchCrossIcon}
                        transform="translate(-0.812 -0.812)"
                      />
                    </button>
                  ) : null}
                </div>
                {!autocompleteResult.result &&
                variables.autocompleteCallVariables.searchString &&
                !searchError &&
                !validationError ? (
                  <div
                    className={`${
                      variables.isConsoleTypeSC && isDeviceMobile
                        ? SearchboxClicked
                          ? 'su__linear-loader-zendesk-full-width'
                          : 'su__linear-loader-zendesk'
                        : null
                    }  su__linear-loader`}
                  >
                    <LinearProgress />
                  </div>
                ) : (
                  <div className="temp_result">
                    {isComponentVisible &&
                      autocompleteResult &&
                      autocompleteResult.result &&
                      autocompleteResult.result.hits &&
                      autocompleteResult.result.hits.length != 0 &&
                      inputValue.current?.value &&
                      (instantSearch || (!instantSearch && !isComponentShow) ? null : (
                        <AutoSuggestion
                          data={inputValue.current.value}
                          dataType={
                            tempData[changeSearchDataOnArrowKey[0]] &&
                            tempData[changeSearchDataOnArrowKey[0]].type
                              ? 'recentSearch'
                              : 'autosuggest'
                          }
                          changeSearchDataOnArrowKey={changeSearchDataOnArrowKey}
                          tempData={tempData}
                          component="searchbox"
                          urlOpensInNewTab={urlOpensInNewTab}
                          previousAction={previousAction}
                          isComponentVisible={isComponentVisible}
                          setIsComponentVisible={setIsComponentVisible}
                        />
                      ))}
                  </div>
                )}
              </div>
            </div>
            {topActionsView && (
              <div
                className={` ${
                  variables.isConsoleTypeSC ? '' : 'su__mobile_component_none'
                }  su_desktop_content su__d-flex `}
              >
                <AdvanceSearch
                  isAdvanceSearchCall={isAdvanceSearchCall}
                  setIsAdvanceSearchCall={setIsAdvanceSearchCall}
                />
                <Bookmarks
                  bookmarkListIconActive={props.bookmarkListIconActive}
                  limitReached={props.limitReached}
                  resultBookmarkClickedFunc={props.resultBookmarkClickedFunc}
                  getArrAggregation={props.arrayAggregation}
                />
              </div>
            )}
          </div>
          {variables.searchClientType == SC_IDS.INTRANET_SEARCH ? (
            <div className="su__position-relative">
              <div className="su__profile-section su__text-capitalize" onClick={togglePopup}>
                {variables?.searchCallVariables['userName']?.charAt(0)}
              </div>
              <div
                id="su__loggedInPopup"
                ref={popupRef}
                style={{
                  display: isPopupVisible ? 'block' : 'none'
                }}
              >
                <div className="su__d-flex su__align-items-center">
                  <svg
                    id="loggedInCross"
                    className="su__margin-left-auto su__cursor_pointer"
                    xmlns="http://www.w3.org/2000/svg"
                    height="24"
                    viewBox="0 -960 960 960"
                    width="24"
                    fill="#5F6368"
                    onClick={togglePopup}
                  >
                    <path d="m256-200-56-56 224-224-224-224 56-56 224 224 224-224 56 56-224 224 224 224-56 56-224-224-224 224Z"></path>
                  </svg>
                  <span className="su__logged-in-charAt0 su__text-capitalize">
                    {variables?.searchCallVariables['userName']?.charAt(0)}
                  </span>
                  <span className="su__font-16 su__black-color su__lh-24px su__text-capitalize">
                    Hi, {variables?.searchCallVariables['userName'] || ''}
                  </span>
                  <span className="su__font-14 su__font-color-000">{window.su_utm || ''}</span>
                  {window.scConfiguration?.ssoType === OKTA ? (
                    <button
                      id="signOut-btn"
                      className="su__sign-out-btn su__font-14"
                      onClick={removeHsctoken}
                    >
                      <svg
                        width="20"
                        height="20"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      >
                        <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"></path>
                        <polyline points="16 17 21 12 16 7"></polyline>
                        <line x1="21" x2="9" y1="12" y2="12"></line>
                      </svg>
                      Sign Out
                    </button>
                  ) : null}
                </div>
              </div>
            </div>
          ) : null}
        </div>
      </section>
    );
  } catch (e) {
    /**Handle error in Search-box component */
    console.log('Error in Search-box component', e);
    variables.hasError = true;
    /** show 404 svg */
    return (
      <div>
        {' '}
        <UnderConstruction404 />{' '}
      </div>
    );
  }
};

export default Search;
