/* global jest, it, expect, describe, beforeEach */
import React from 'react';
import { shallow, mount } from 'enzyme';
import StickyFacets from './';
import { useDispatch, useSelector } from 'react-redux';

// Mock the dependencies
jest.mock('react-redux', () => ({
  useDispatch: jest.fn(),
  useSelector: jest.fn()
}));

jest.mock('react-i18next', () => ({
  useTranslation: () => ({ t: (key) => key })
}));

jest.mock('../../../redux/variables', () => ({
  searchCallVariables: {
    aggregations: [],
    langAttr: 'en'
  },
  selectedStickyFilters: []
}));

jest.mock('../../../redux/ducks', () => ({
  search: {
    start: jest.fn()
  }
}));

describe('StickyFacets Component', () => {
  let props;
  let mockDispatch;

  beforeEach(() => {
    props = {
      item: {
        key: 'testKey',
        order: 0,
        values: [
          { Contentname: 'Item 1' },
          { Contentname: 'Item 2' },
          { Contentname: 'Item 3' },
          { Contentname: 'Item 4' },
          { Contentname: 'Item 5' }
        ]
      },
      index: 0,
      standOut: false
    };

    mockDispatch = jest.fn();
    useDispatch.mockReturnValue(mockDispatch);
    useSelector.mockReturnValue({
      aggregationsArray: [{ values: [] }]
    });
  });

  it('renders without crashing', () => {
    const wrapper = shallow(<StickyFacets {...props} />);
    expect(wrapper.exists()).toBeTruthy();
  });

  it('renders correct number of sticky filters', () => {
    const wrapper = shallow(<StickyFacets {...props} />);
    expect(wrapper.find('[data-testid="su__sticky__filters"]')).toHaveLength(5);
  });

  it('calls clearReset when cancel button is clicked', () => {
    const wrapper = mount(<StickyFacets {...props} />);
    wrapper.find('.su__cancel__sticky').first().simulate('click');
    expect(mockDispatch).toHaveBeenCalled();
  });

  it('doesn\'t show "see more" button when there are 4 or fewer items', () => {
    props.item.values = props.item.values.slice(0, 3);
    const wrapper = shallow(<StickyFacets {...props} />);
    expect(wrapper.find('.su__see-more-less')).toHaveLength(0);
  });

  // KEEP THE BOTTOM TEST CASE AT THE END OF ALL TEST CASES ALWAYS
  it('should show error when error caught', () => {
    jest.spyOn(require('react-i18next'), 'useTranslation').mockReturnValue({
      t: jest.fn(() => {
        throw new Error('Simulated translation error');
      })
    });

    const consoleLog = jest.spyOn(console, 'log').mockImplementation();
    const wrapper = shallow(<StickyFacets />);
    expect(consoleLog).toHaveBeenCalledWith(
      'Error in stickFacet-facets component',
      expect.any(Error)
    );
    expect(wrapper.html()).toBe('<div></div>');
    consoleLog.mockRestore();
  });
});
