import React, { Fragment } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import variables from '../../../redux/variables';
import { search } from '../../../redux/ducks';
import { mergeFilterClicked, facetClickNested } from '../../../mergeFacet';
import { useTranslation } from 'react-i18next';
import setCookies from '../../../setCookie/setCookie';
import StaticStrings from 'StaticStrings';
import { a11y, tabIndexes } from '../../../constants/a11y';
import { setWildcardToggle } from 'function-library/commonFunctions';
import { htmlSanitizer } from 'function-library/dataFormatter';
import { v4 as uuid } from 'uuid';
import { AGGR_KEYS } from 'constants/constants';

const POST_TIME = 'post_time';

const StickyFacets = (props) => {
  try {
    /**
     *  Change Languages state
     */
    const { t } = useTranslation();
    const dispatch = useDispatch();
    const searchResult = useSelector((state) => state.searchResult);

    let item = props.item;
    let idx = props.index;
    let standOut = props.standOut;
    let bgColor = props.bgColor;
    let borderColor = props.borderColor;
    let check;
    /**
     * Call search result function if filter changes
     * @param {Clear filter} contentName
     * @param {Clear type} Clear_type
     */
    const clearReset = (filter, item) => {
      setCookies.setSmartFacetOff();
      const aggIndex = variables.searchCallVariables.aggregations.findIndex(
        (filterType) => filterType.type === item.key
      );
      variables.currentClickedOrder = searchResult.aggregationsArray.findIndex(
        (f) => f.order === item.order
      );
      if (standOut) {
        setWildcardToggle(false);
      } else if (['exactPhrase', 'withOneOrMore', 'withoutTheWords'].includes(item.key)) {
        variables.searchCallVariables[item.key] = '';
      } else {
        const filterValue = variables.searchCallVariables.aggregations.find(
          (filterType) => filterType.type === item.key
        );
        const parentFacetIndex = variables.searchCallVariables.aggregations.findIndex(
          (filterType) => filterType.type === item.key
        );
        if (
          filterValue &&
          (filterValue.type.indexOf('_nested') > 0 || filterValue.type.indexOf('_navigation') > 0)
        ) {
          facetClickNested(filter, item.key, item.order, filter.level, filter.path, searchResult);
        } else if (filter.Contentname && filter.Contentname.indexOf('merged_') > -1) {
          mergeFilterClicked(
            filter.Contentname,
            variables.searchCallVariables.aggregations[aggIndex].filter,
            searchResult.aggregationsArray[variables.currentClickedOrder].values,
            false
          );
        } else if (filterValue) {
          if (filterValue.type === POST_TIME) {
            filterValue.filter.splice(0, 1);
          } else {
            const index = filterValue.filter.indexOf(filter.Contentname);
            filterValue.filter.splice(index, 1);
          }
          if (filterValue.filter.length === 0) {
            variables.searchCallVariables.aggregations.splice(parentFacetIndex, 1);
          }
        }
        variables.selectedStickyFilters = [];
        for (let element of searchResult.aggregationsArray[0].values) {
          element.selected = false;
        }
      }
      //remove empty aggregations
      variables.searchCallVariables.aggregations =
        variables.searchCallVariables.aggregations.filter(function (facet) {
          if (!facet.sort) {
            if (
              facet.filter &&
              !facet.filter.length &&
              (!facet.children || (facet.children && !facet.children.length))
            ) {
              return false;
            }
          } else {
            if (
              facet.filter &&
              !facet.filter.length &&
              (!facet.children || (facet.children && !facet.children.length))
            ) {
              delete facet.filter;
              delete facet.children;
            }
          }
          return true;
        });
      if (item.merged && item.order == 0) {
        if (item.values) {
          item.values.forEach((element) => {
            if (variables.searchCallVariables.aggregations) {
              variables.searchCallVariables.aggregations.forEach((filter) => {
                if (!check) {
                  check = filter.filter.includes(element.Contentname);
                }
              });
            }
          });
        }
      }

      if (
        (item.key == AGGR_KEYS._INDEX || item.key == AGGR_KEYS._TYPE) &&
        item.order == 0 &&
        !check
      ) {
        variables.allSelected = true;
        variables.activeType = 'all';
      }
      variables.searchSource = 'sticky-facets';
      variables.searchCallVariables.from = 0;
      variables.searchCallVariables.pageNo = 1;
      variables.searchCallVariables.pagingAggregation = [];

      dispatch(search.start(variables.searchCallVariables));
    };

    const formatDate = (timestamp) => {
      let date = new Date(timestamp);
      const year = date.getUTCFullYear();
      const month = date.getUTCMonth() + 1;
      const day = date.getUTCDate();
      return `${year}/${month}/${day}`;
    };

    /**
     *
     * @param {show more sticky facets} facet
     */

    return (
      <Fragment key={idx}>
        {item.values.map((value) => (
          <div
            key={uuid()}
            data-testid="su__sticky__filters"
            className={`su__sticky__filters su__text-nowrap  su__radius-5p su__font-11 su__mr-5px  su__rtlmr-0 su__rtlml-2 ${
              !standOut ? 'su__mb-2' : ''
            } su__color-grey `}
            style={{
              background: bgColor ? bgColor : null,
              borderColor: borderColor ? borderColor : null,
              color: borderColor ? borderColor : null
            }}
          >
            <span
              className="su__sticky__filters__ellipses su__rtlpl-1 su__rtlpr-1"
              dangerouslySetInnerHTML={{
                __html: htmlSanitizer(
                  value.sticky_name ||
                    value.Contentname_short ||
                    (value.selectedFilter === StaticStrings.slider &&
                      value.type === StaticStrings.date &&
                      `${formatDate(value.selectedMinValue)} - ${formatDate(
                        value.selectedMaxValue
                      )}`) ||
                    (value.selectedFilter === StaticStrings.slider &&
                      value.type === StaticStrings.integer &&
                      `${value.selectedMinValue} - ${value.selectedMaxValue}`) ||
                    value.displayName ||
                    value.Contentname
                )
              }}
            ></span>
            <button
              type="button"
              lang={variables.searchCallVariables.langAttr}
              role={a11y.ROLES.BTN}
              tabIndex={tabIndexes.tabIndex_0}
              aria-label={`${t(StaticStrings.remove)} ${
                value.sticky_name ||
                value.Contentname_short ||
                value.displayName ||
                value.Contentname
              }`}
              className="su__cancel__sticky su__cursor a11y-btn  su__font-11"
              onClick={() => clearReset(value, item)}
              style={{
                borderLeft: borderColor ? `1px solid ${borderColor}` : undefined,
                color: borderColor ? borderColor : null
              }}
            >
              &#x2715;
            </button>
          </div>
        ))}
      </Fragment>
    );
  } catch (e) {
    console.log('Error in stickFacet-facets component', e);
    return <div></div>;
  }
};

export default StickyFacets;
