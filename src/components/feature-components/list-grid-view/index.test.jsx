/* global jest, it, expect, describe, beforeEach, afterEach */
import React from 'react';
import { mount } from 'enzyme';
import ToggleView from './';
import { useTranslation } from 'react-i18next';
import variables from '__mocks__/variables';

jest.mock('react-i18next', () => ({
  useTranslation: jest.fn()
}));

describe('ToggleView Component', () => {
  const mockProps = {
    isListView: false,
    setIsListView: jest.fn(),
    isGridView: false,
    setIsGridView: jest.fn(),
    variables
  };

  let wrapper;

  beforeEach(() => {
    useTranslation.mockImplementation(() => ({ t: (key) => key }));
    wrapper = mount(<ToggleView {...mockProps} />);
    wrapper.update();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders without crashing', () => {
    expect(wrapper.exists()).toBe(true);
  });

  it('renders both list view and grid view buttons', () => {
    expect(wrapper.find('.su__list-view').exists()).toBe(true);
    expect(wrapper.find('.su__grid-view').exists()).toBe(true);
  });

  it('calls setIsListView(true) and setIsGridView(false) on switchListView()', () => {
    wrapper.find('.su__list-view').simulate('click');
    expect(mockProps.setIsListView).toHaveBeenCalledWith(true);
    expect(mockProps.setIsGridView).toHaveBeenCalledWith(false);
  });

  it('calls setIsGridView(true) and setIsListView(false) on switchGridView()', () => {
    wrapper.find('.su__grid-view').simulate('click');
    expect(mockProps.setIsGridView).toHaveBeenCalledWith(true);
    expect(mockProps.setIsListView).toHaveBeenCalledWith(false);
  });

  it('applies correct accessibility attributes to list view button', () => {
    const listViewButton = wrapper.find('.su__list-view');
    expect(listViewButton.prop('lang')).toBe('en');
    expect(listViewButton.prop('tabIndex')).toBe(0); // Assuming tabIndex value
    expect(listViewButton.prop('role')).toBe('button');
    expect(listViewButton.prop('aria-label')).toContain('List View');
  });

  it('applies correct accessibility attributes to grid view button', () => {
    const gridViewButton = wrapper.find('.su__grid-view');
    expect(gridViewButton.prop('lang')).toBe('en');
    expect(gridViewButton.prop('tabIndex')).toBe(0);
    expect(gridViewButton.prop('role')).toBe('button');
    expect(gridViewButton.prop('aria-label')).toContain('Grid View');
  });

  it('should handle errors gracefully', () => {
    jest.spyOn(console, 'log').mockImplementation(() => {});
    useTranslation.mockImplementation(() => {
      throw new Error('Test error');
    });
    const wrapper = mount(<ToggleView {...mockProps} />);
    wrapper.update();
    expect(wrapper.html()).toBe('<div></div>');
    expect(console.log).toHaveBeenCalledWith('Error in listGridView component', expect.any(Error));
  });
});
