/* global jest, it, expect, describe, beforeEach, afterEach */
import React from 'react';
import { shallow } from 'enzyme';
import { useSelector, useDispatch } from 'react-redux';
import DidYouMean from './';
import variables from '../../../redux/variables';
import { search } from '../../../redux/ducks';
import { htmlSanitizer } from 'function-library/dataFormatter';
import { v4 as uuid } from 'uuid';

// Mock dependencies
jest.mock('react-redux', () => ({
  useSelector: jest.fn(),
  useDispatch: jest.fn()
}));

jest.mock('react-i18next', () => ({
  useTranslation: jest.fn().mockReturnValue({ t: jest.fn((key) => key) })
}));

jest.mock('uuid', () => ({
  v4: jest.fn()
}));

jest.mock('function-library/dataFormatter', () => ({
  htmlSanitizer: jest.fn()
}));

jest.mock('../../../redux/variables', () => ({
  searchCallVariables: {
    pagingAggregation: [],
    searchString: '',
    langAttr: 'en'
  },
  previousDymSearchString: '',
  searchSource: ''
}));

jest.mock('../../../redux/ducks', () => ({
  search: {
    start: jest.fn()
  }
}));

jest.mock('../../../StaticStrings', () => ({
  DIDYOUMEAN: 'DIDYOUMEAN'
}));

describe('DidYouMean Component', () => {
  let dispatchMock;

  beforeEach(() => {
    dispatchMock = jest.fn();
    useSelector.mockImplementation((selectorFn) => selectorFn());
    useDispatch.mockReturnValue(dispatchMock);
    variables.searchCallVariables.pagingAggregation = [];
    variables.previousDymSearchString = '';
    variables.searchSource = '';
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders without crashing', () => {
    const wrapper = shallow(<DidYouMean />);
    expect(wrapper.exists()).toBe(true);
  });

  it('dispatches search.start when an option is clicked', () => {
    const searchSuggest = {
      result: true,
      suggest: {
        simple_phrase: [
          {
            options: [{ text: 'Option 1' }, { text: 'Option 2' }]
          }
        ]
      }
    };
    useSelector.mockReturnValue(searchSuggest);

    const wrapper = shallow(<DidYouMean />);
    wrapper.find('.su__cursor').first().simulate('click');

    expect(dispatchMock).toHaveBeenCalledWith(search.start(variables.searchCallVariables));
  });

  it('calls htmlSanitizer for each suggestion text', () => {
    const searchSuggest = {
      result: true,
      suggest: {
        simple_phrase: [
          {
            options: [{ text: 'Option 1' }, { text: 'Option 2' }]
          }
        ]
      }
    };
    useSelector.mockReturnValue(searchSuggest);
    htmlSanitizer.mockReturnValueOnce('Sanitized Text 1').mockReturnValueOnce('Sanitized Text 2');

    shallow(<DidYouMean />);
    expect(htmlSanitizer).toHaveBeenCalledWith('Option 1');
    expect(htmlSanitizer).toHaveBeenCalledWith('Option 2');
  });

  it('renders suggestions with unique keys', () => {
    const searchSuggest = {
      result: true,
      suggest: {
        simple_phrase: [
          {
            options: [{ text: 'Option 1' }, { text: 'Option 2' }]
          }
        ]
      }
    };
    useSelector.mockReturnValue(searchSuggest);
    uuid.mockReturnValueOnce('uuid1').mockReturnValueOnce('uuid2');

    const wrapper = shallow(<DidYouMean />);
    expect(wrapper.find('.su__didumean').at(0).key()).toBeTruthy();
    expect(wrapper.find('.su__didumean').at(1).key()).toBeTruthy();
  });

  it('does not render suggestions when searchSuggest result is false', () => {
    const searchSuggest = {
      result: false,
      suggest: {
        simple_phrase: [
          {
            options: [{ text: 'Option 1' }, { text: 'Option 2' }]
          }
        ]
      }
    };
    useSelector.mockReturnValue(searchSuggest);

    const wrapper = shallow(<DidYouMean />);
    expect(wrapper.find('.su__didumean').exists()).toBe(false);
  });

  it('does not render suggestions when searchSuggest has no options', () => {
    const searchSuggest = {
      result: true,
      suggest: {
        simple_phrase: [
          {
            options: []
          }
        ]
      }
    };
    useSelector.mockReturnValue(searchSuggest);

    const wrapper = shallow(<DidYouMean />);
    expect(wrapper.find('.su__didumean').exists()).toBe(false);
  });
});
