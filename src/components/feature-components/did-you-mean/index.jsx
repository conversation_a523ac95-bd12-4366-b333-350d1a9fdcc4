import React from 'react';
import { useSelector, useDispatch } from 'react-redux';
import variables from '../../../redux/variables';
import { search } from '../../../redux/ducks';
import { useTranslation } from 'react-i18next';
import StaticStrings from '../../../StaticStrings';
import { htmlSanitizer } from 'function-library/dataFormatter';
import { v4 as uuid } from 'uuid';
import { useDevice } from 'function-library/hooks';
const DidYouMean = () => {
  try {
    /**
     *  Change Languages state
     */
    const { t } = useTranslation();
    const { isDeviceMobile } = useDevice();
    const searchSuggest = useSelector((state) => state.searchResult);
    const dispatch = useDispatch();
    const searchClick = (text) => {
      variables.searchCallVariables.pagingAggregation = [];
      variables.previousDymSearchString = text;
      variables.searchCallVariables.searchString = text;
      variables.searchSource = 'did-you-mean';
      dispatch(search.start(variables.searchCallVariables));
      variables.searchAnalyticsObject = null;
    };
    return (
      <div className="did-you-mean">
        {searchSuggest.result &&
          Object.keys(searchSuggest.suggest).length > 0 &&
          searchSuggest.suggest.simple_phrase.map((item) => (
            <div key={uuid()}>
              {React.Children.toArray(
                item.options.map((item) => (
                  <div className="su__didumean su__mb-2 su__mt-1" key={uuid()}>
                    <div>
                      <span
                        lang={variables.searchCallVariables.langAttr}
                        className="su__f-normal su__font-16 su__loading-view su__loading-Dnone"
                      >
                        {t(StaticStrings.DIDYOUMEAN)}:{' '}
                      </span>
                      <span className="su__loading-view su__loading-Dnone">
                        <span
                          href="#!"
                          dangerouslySetInnerHTML={{ __html: htmlSanitizer(item.text) }}
                          className={`${
                            !isDeviceMobile ? 'su__font-16' : 'su__font-13'
                          } su__text-decoration ${
                            !isDeviceMobile ? 'su__font-bold' : ''
                          } su__cursor su__text-blue`}
                          onClick={() => searchClick(item.text)}
                        />
                      </span>
                    </div>
                  </div>
                ))
              )}
            </div>
          ))}
      </div>
    );
  } catch (e) {
    console.log('Error in did you mean component', e);
    return <div></div>;
  }
};

export default DidYouMean;
