import React, { useEffect, useRef, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import variables from '../../../redux/variables';
import { search } from '../../../redux/ducks';
import { useTranslation } from 'react-i18next';
import { mergeFilters, mergeFilterClicked } from '../../../mergeFacet';
import Menu from '@material-ui/core/Menu';
import setCookies from '../../../setCookie/setCookie';
import StaticStrings from '../../../StaticStrings';
import { a11y, tabIndexes } from '../../../constants/a11y';
import { useDevice } from 'function-library/hooks';
import { AGGR_KEYS } from 'constants/constants';

const TopFacets = () => {
  try {
    const { t } = useTranslation();
    const searchResult = useSelector((state) => state.searchResult);
    const dispatch = useDispatch();
    const scrollContainerRef = useRef(null);
    const scrollLeftButtonRef = useRef(null);
    const scrollRightButtonRef = useRef(null);
    const [isScrolledToLeft, setIsScrolledToLeft] = useState(true);
    const [isScrolledToRight, setIsScrolledToRight] = useState(false);
    const [mergedtabs, setMergedtabs] = useState(null);
    let parsed_array =
      searchResult && searchResult.result && searchResult.merged_facets
        ? JSON.parse(searchResult.merged_facets)
        : [];
    parsed_array.length &&
      parsed_array.forEach(function (o) {
        mergeFilters(o, searchResult.aggregationsArray);
      });

    /**
     * Get content results based on tab selection.
     * @param {tab name} tab
     */
    const getContentResults = (contentName, label) => {
      setCookies.setSmartFacetOff();
      variables.searchCallVariables.pagingAggregation = [];
      for (let element of searchResult.aggregationsArray[0]?.values) {
        element.selected = false;
        if (contentName == 'All Content') {
          variables.allSelected = true;
        } else {
          variables.allSelected = false;
          if (element.displayName == contentName) {
            element.selected = true;
          }
        }
      }

      if (label === 'all') contentName = 'all';
      variables.searchCallVariables.aggregations = [];
      if (contentName !== 'all') {
        let arr = [];
        if (contentName.indexOf('merged_') > -1) {
          let filterChecked = true;
          mergeFilterClicked(
            contentName,
            arr,
            searchResult.aggregationsArray[0]?.values,
            filterChecked
          );
        } else {
          arr.push(contentName);
        }
        variables.searchCallVariables.aggregations.push({ type: label, filter: arr });
        variables.searchCallVariables.versionResults = false;
      } else {
        variables.searchCallVariables.aggregations = [];
        variables.searchCallVariables.versionResults = true;
      }

      variables.searchCallVariables.from = 0;
      variables.searchCallVariables.pageNo = 1;
      variables.searchSource = 'content-source-tabs';
      variables.activeType = contentName;
      dispatch(search.start(variables.searchCallVariables));
    };

    const isPropsLoaded = useRef(true);
    const { isDeviceMobile } = useDevice();

    const handlemergetabs = (Contentname) => {
      if (Contentname === mergedtabs) {
        setMergedtabs(null);
      } else {
        setMergedtabs(Contentname);
      }
    };

    useEffect(() => {
      if (!isPropsLoaded.current) {
        const { result, aggregationsArray } = searchResult;
        const hasEmptyKeyAndLabel =
          result && aggregationsArray[0]?.key === '' && aggregationsArray[0]?.label === '';
        if (hasEmptyKeyAndLabel && variables.searchCallVariables.aggregations.length) {
          variables.searchCallVariables.aggregations =
            variables.searchCallVariables.aggregations.filter(
              (item) => item.type !== AGGR_KEYS._INDEX
            );
          if (!variables.searchCallVariables.aggregations.length) {
            variables.allSelected = true;
            variables.activeType = 'all';
          }
          isPropsLoaded.current = true;
        }
      } else isPropsLoaded.current = false;
    });

    useEffect(() => {
      const scrollContainer = scrollContainerRef.current;

      const handleScroll = () => {
        const maxScrollLeft = scrollContainer.scrollWidth - scrollContainer.clientWidth;
        if (document.body.classList.contains('su__rtl')) {
          setIsScrolledToLeft(scrollContainer.scrollLeft <= -(maxScrollLeft - 1));
          setIsScrolledToRight(scrollContainer.scrollLeft >= 0);
        } else {
          setIsScrolledToLeft(scrollContainer.scrollLeft === 0);
          setIsScrolledToRight(scrollContainer.scrollLeft >= maxScrollLeft - 1);
        }
      };

      if (scrollContainer) {
        scrollContainer.addEventListener('scroll', handleScroll);
        handleScroll(); // Initial check
      }

      return () => {
        if (scrollContainer) {
          scrollContainer.removeEventListener('scroll', handleScroll);
        }
      };
    });

    const [anchorEl, setAnchorEl] = React.useState(null);
    const handleClick = (event, id) => {
      setAnchorEl(event.currentTarget);
      const checkId = setInterval(function () {
        if (document.getElementById('su__MCS' + id)) {
          clearInterval(checkId);
          let clickedOpen = document.getElementById('su__MCS' + id);
          clickedOpen.classList.add('su__d-block');
        }
      });
    };
    const handleClose = () => {
      setAnchorEl(null);
      setMergedtabs(null);
    };

    /* Horizontal slider */
    useEffect(() => {
      let box = document.querySelector('.box-inner');
      let arrows = document.querySelectorAll('.arrow');

      arrows.forEach(function (arrow) {
        arrow.addEventListener('click', function () {
          let x;
          if (arrow.classList.contains('arrow-right')) {
            x = box.clientWidth / 2 + box.scrollLeft;
            box.scrollTo({
              left: x,
              behavior: 'smooth'
            });
          } else {
            x = box.clientWidth / 2 - box.scrollLeft;
            box.scrollTo({
              left: -x,
              behavior: 'smooth'
            });
          }
        });
      });
    });

    useEffect(() => {
      window.addEventListener('scroll', handleClose);
      return () => {
        window.removeEventListener('scroll', handleClose);
      };
    }, []);

    return (
      <section className="su__contentTabs-section su__w-100 su__position-relative">
        <div className="su__container su__container_custom su__cs_grid_bg su__sc-loading">
          <div
            className={`${
              isDeviceMobile ? 'su__col-md-12__mobile' : ''
            } su__contentTabs-block su__cs-tabs su__cs_sm_tab su__px-56px su__cs_border_bottom su__pos_relative su__top-3px su__sc-loading `}
          >
            {searchResult.result &&
              searchResult.aggregationsArray
                .filter((item, index) => index === 0)
                .map((item) => (
                  <div key={'searchResult.aggregationsArray ' + (item?.key || item?.label)}>
                    {item.key !== '' && item.label !== '' ? (
                      <div className="su__slider-outer su__position-relative">
                        {!isScrolledToLeft && (
                          <button
                            lang={variables.searchCallVariables.langAttr}
                            aria-label={t(StaticStrings.slide_left)}
                            className={`arrow-left arrow su__slider-btn-left su__cursor_pointer su__zindex-custom-2 ${
                              isDeviceMobile ? 'su__slider_btn_position' : ''
                            }`}
                            ref={scrollLeftButtonRef}
                          >
                            {' '}
                          </button>
                        )}
                        <div
                          className={`box-inner su-tabsSection su__bg-white su__w-100 su__text-nowrap su__font-14 su__overflowx-auto su__slider-list su__hideScroller su__cs_grid_bg su__content-tab su__sc-loading ${
                            !isScrolledToLeft ? 'su__showLeft-arrow' : ''
                          } ${!isScrolledToRight ? 'su__showRight-arrow' : ''}`}
                          id="su__top_nav_outer_div"
                          role={a11y.ROLES.TABLIST}
                          ref={scrollContainerRef}
                        >
                          <div id="su__top_nav_inner_div" className="su__cs_grid_bg">
                            <button
                              data-test-id={`su__top_facet_all_btn`}
                              lang={variables.searchCallVariables.langAttr}
                              aria-label={`${t(StaticStrings.ALLCONTENT)}`}
                              role={a11y.ROLES.TAB}
                              aria-selected={'' + !!variables.allSelected}
                              tabIndex={tabIndexes.tabIndex_0}
                              className={
                                variables.allSelected
                                  ? `all-content su__tabs su__active-tab a11y-btn su__height_57px su__pt_22px su__z-index  ${
                                      isDeviceMobile ? 'su__height_40px su__pt_5px' : ''
                                    }`
                                  : `all-content su__tabs su__inactive-type a11y-btn su__height_57px su__pt_22px su__z-index  ${
                                      isDeviceMobile
                                        ? 'su__height_40px su__pt_5px su__tabs-mobile'
                                        : ''
                                    } `
                              }
                              onClick={() => getContentResults('All Content', 'all')}
                            >
                              {t(StaticStrings.ALLCONTENT)}
                            </button>
                            {React.Children.toArray(
                              item.values.map((itemInner, index) => (
                                <button
                                  data-test-id={`su__top_facet_${itemInner.displayName}_btn_${index}`}
                                  lang={variables.searchCallVariables.langAttr}
                                  key={`${itemInner}_${itemInner?.Contentname}_${index}`}
                                  aria-label={`${itemInner.displayName}`}
                                  role={a11y.ROLES.TAB}
                                  aria-selected={
                                    '' +
                                    !!(
                                      itemInner.selected ||
                                      (item.indeterminateFlag &&
                                        item.indeterminateFlag[itemInner.Contentname] === true)
                                    )
                                  }
                                  tabIndex={tabIndexes.tabIndex_0}
                                  className={
                                    itemInner.Contentname +
                                    ` su__tabs a11y-btn su__height_57px su__pt_22px su__z-index  ${
                                      isDeviceMobile ? 'su__height_40px su__pt_5px' : ''
                                    } ` +
                                    (itemInner.selected ||
                                    Boolean(
                                      item.indeterminateFlag &&
                                        item.indeterminateFlag[itemInner.Contentname]
                                    )
                                      ? 'su__active-tab'
                                      : 'su__inactive-type')
                                  }
                                  onClick={() =>
                                    !itemInner.merged ||
                                    (itemInner.merged && itemInner.showChild !== '1')
                                      ? getContentResults(itemInner.Contentname, item.key)
                                      : null
                                  }
                                >
                                  {!itemInner.merged || itemInner.showChild != 1
                                    ? itemInner.displayName
                                    : ''}
                                  {itemInner.merged && itemInner.showChild == 1 ? (
                                    <div className="su__mergedFilter-CS-tab su__d-flex">
                                      <div
                                        data-test-id={'su__merged_filter_svg'}
                                        className="su__mergedFilter-CS-button "
                                        onClick={(e) => {
                                          handleClick(e, itemInner.Contentname);
                                        }}
                                      >
                                        <span
                                          className="su__mergedFilter-CS-svg su__merged_tabs su__position-relative su__pr-4 su__right-0-rtl su__rtlpl-4"
                                          onClick={() => handlemergetabs(itemInner.Contentname)}
                                        >
                                          {itemInner.displayName}
                                          <span
                                            className={` ${
                                              mergedtabs === itemInner.Contentname
                                                ? 'su__arrow-up'
                                                : 'su__arrow-down'
                                            } su__arrow_position su__arrow-align-rtl`}
                                          ></span>
                                        </span>
                                      </div>
                                      <Menu
                                        id={'su__MCS' + itemInner.Contentname}
                                        className="su__merge-options su__d-none"
                                        anchorEl={anchorEl}
                                        open={Boolean(anchorEl)}
                                        onClose={handleClose}
                                        onClick={handleClose}
                                        getContentAnchorEl={null}
                                        transformOrigin={{ horizontal: 'left', vertical: 'top' }}
                                        anchorOrigin={{ horizontal: 'left', vertical: 'bottom' }}
                                        disableScrollLock
                                      >
                                        {React.Children.toArray(
                                          itemInner.childArray.map((child, index) => (
                                            <div
                                              key={
                                                child?.displayName + `${index}` ||
                                                child?.Contentname + `${index}`
                                              }
                                              className={
                                                child.Contentname +
                                                ' su__merge-options-item ' +
                                                (child.selected ? 'su__merge-options-active' : '')
                                              }
                                              onClick={() =>
                                                getContentResults(child.Contentname, item.key)
                                              }
                                            >
                                              {child.displayName || child.Contentname}
                                            </div>
                                          ))
                                        )}
                                      </Menu>
                                    </div>
                                  ) : null}
                                </button>
                              ))
                            )}
                          </div>
                        </div>
                        {!isScrolledToRight && (
                          <button
                            lang={variables.searchCallVariables.langAttr}
                            aria-label={t(StaticStrings.slide_right)}
                            className={`arrow-right arrow su__slider-btn su__slider-button-right su__slider-btn-right su__cursor_pointer su__zindex-custom-2 su__sc-loading ${
                              isDeviceMobile ? 'su__slider_btn_position' : ''
                            }`}
                            ref={scrollRightButtonRef}
                          >
                            {' '}
                          </button>
                        )}
                      </div>
                    ) : null}
                  </div>
                ))}
          </div>
          <div className="su__content-s-border su__position-relative"></div>
        </div>
      </section>
    );
  } catch (e) {
    console.log('Error in component content source tab', e);
    /**Handle error in conyent source tab component */
    return (
      <div data-test-id={'su__top_facet_loading'} className="su__loading1">
        <div className="su-tabsSection su__bg-white su__w-100 su__border-b su__border-t su__text-nowrap su__font-14 su__overflowx-auto su__minscroller">
          <div className="all-content su__tabs  su__loading-view ">All Content</div>
          <div className="su__loading-view su__tabs ">{StaticStrings.CONTENTSOURCE}</div>
          <div className="su__loading-view su__tabs ">{StaticStrings.CONTENTSOURCE}</div>
          <div className="su__loading-view su__tabs ">{StaticStrings.CONTENTSOURCE}</div>
          <div className="su__loading-view su__tabs ">{StaticStrings.CONTENTSOURCE}</div>
          <div className="su__loading-view su__tabs ">{StaticStrings.CONTENTSOURCE}</div>
          <div className="su__loading-view su__tabs ">{StaticStrings.CONTENTSOURCE}</div>
          <div className="su__loading-view su__tabs ">{StaticStrings.CONTENTSOURCE}</div>
        </div>
      </div>
    );
  }
};

export default TopFacets;
