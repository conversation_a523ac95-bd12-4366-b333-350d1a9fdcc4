/* global jest, it, expect, describe, beforeEach, afterEach */
import React from 'react';
import { mount, shallow } from 'enzyme';
import { useSelector, useDispatch } from 'react-redux';
import TopFacets from './';
import variables from '__mocks__/variables';
import { search } from '../../../redux/ducks';
import state from '__mocks__/state';
import StaticStrings from 'StaticStrings';

const SELECTORS = {
  topFacetLoading: '[data-test-id="su__top_facet_loading"]',
  topFacetAllTab: '[data-test-id="su__top_facet_all_btn"]',
  mergeFilterSvg: '[data-test-id="su__merged_filter_svg"]'
};

jest.mock('react-redux', () => ({
  useSelector: jest.fn(),
  useDispatch: jest.fn()
}));

jest.mock('react-i18next', () => ({
  useTranslation: jest.fn().mockReturnValue({ t: (key) => key })
}));

jest.mock('../../../mergeFacet', () => ({
  mergeFilters: jest.fn(),
  mergeFilterClicked: jest.fn()
}));

jest.mock('../../../setCookie/setCookie', () => ({
  setSmartFacetOff: jest.fn()
}));

jest.mock('../../../redux/variables', () => require('__mocks__/variables'));

jest.mock('../../../redux/ducks', () => ({
  search: {
    start: jest.fn()
  }
}));

const getMockedElements = () => {
  /* .box-inner */
  const box = {
    clientWidth: 50,
    scrollLeft: 2,
    scrollTo: jest.fn()
  };
  /** Arrow Buttons */
  const buttonLeft = document.createElement('button');
  buttonLeft.className = 'arrow-left';
  buttonLeft.textContent = '<';
  const buttonRight = document.createElement('button');
  buttonRight.className = 'arrow-right';
  buttonRight.textContent = '>';
  /* simulateSmallerOuterDiv */
  const simulateSmallerOuterDiv = (id) => {
    if (id === 'su__top_nav_outer_div') {
      return { offsetWidth: 100 };
    } else return { offsetWidth: 200 };
  };

  /* mockHiddenMenu */
  const mockHiddenMenu = document.createElement('div');

  /* mockedEvent  */
  const mockedEvent = {
    target: {
      currentElement: null
    }
  };

  /* fakeInterval */
  const fakeInterval = (callback) => {
    callback();
    return 1; // return a fake interval ID
  };

  return {
    box,
    mockHiddenMenu,
    buttonLeft,
    buttonRight,
    mockedEvent,
    simulateSmallerOuterDiv,
    fakeInterval
  };
};

describe('TopFacets', () => {
  let useSelectorMock;
  let dispatchMock;
  let original = state.searchResult.aggregationsArray[0];

  beforeEach(() => {
    state.searchResult.aggregationsArray[0] = original;
    useSelectorMock = useSelector.mockImplementation((selector) => selector(state));
    dispatchMock = jest.fn();
    useDispatch.mockReturnValue(dispatchMock);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should render the tabs based on aggregations', () => {
    const wrapper = mount(<TopFacets />);
    expect(wrapper.find(SELECTORS.topFacetAllTab).text()).toBe(StaticStrings.ALLCONTENT);
    const aggregations = state.searchResult.aggregationsArray.filter((v) => !v.key && !v.label);
    aggregations.forEach((aggregation) => {
      aggregation.values
        .filter((v) => !v.merged)
        .forEach((value, index) => {
          expect(
            wrapper.find(`[data-test-id="su__top_facet_${value.displayName}_btn_${index}"]`).text()
          ).toEqual(value.displayName);
        });
    });
  });

  it('should dispatch search on ALL Content TAB click', () => {
    const wrapper = shallow(<TopFacets />);
    wrapper.find(SELECTORS.topFacetAllTab).simulate('click');
    expect(variables.allSelected).toBe(true);
    expect(dispatchMock).toHaveBeenCalledWith(search.start(variables.searchCallVariables));
  });

  it('should dispatch search on TABS other than All Content except merged', () => {
    const wrapper = shallow(<TopFacets />);
    const aggregations = state.searchResult.aggregationsArray.filter(
      (v, i) => !!v.key && !!v.label && i === 0
    );
    aggregations.forEach((aggregation) => {
      aggregation.values
        .filter((v) => !v.merged)
        .forEach((value, index) => {
          wrapper
            .find(`[data-test-id="su__top_facet_${value.displayName}_btn_${index}"]`)
            .simulate('click');
          expect(dispatchMock).toHaveBeenCalledTimes(index + 1);
          expect(dispatchMock).toHaveBeenCalledWith(search.start(variables.searchCallVariables));
        });
    });
  });

  it('should diplay merged filter svg if top facets are merged', () => {
    useSelectorMock = useSelector.mockImplementation((selector) => selector(state));
    const wrapper = mount(<TopFacets />);
    expect(wrapper.find(SELECTORS.mergeFilterSvg).exists()).toBe(true);
  });

  it('should set diplay block to merged filter list when merged TAB is clicked', () => {
    jest.useFakeTimers();
    const { mockHiddenMenu, mockedEvent, fakeInterval } = getMockedElements();
    const spy = jest.spyOn(document, 'getElementById').mockReturnValue(mockHiddenMenu);
    // Mock setInterval to call the callback immediately
    jest.spyOn(global, 'setInterval').mockImplementation(fakeInterval);
    const aggregations = state.searchResult.aggregationsArray[0];
    const Contentname = aggregations.values.filter((v) => v.merged)[0].Contentname;
    useSelectorMock = useSelector.mockImplementation((selector) => selector(state));
    //mount
    const wrapper = mount(<TopFacets />);
    wrapper.find(SELECTORS.mergeFilterSvg).simulate('click', mockedEvent, Contentname);
    // wait for timers to finish
    jest.runAllTimers();

    expect(spy).toHaveBeenCalledWith('su__MCS' + Contentname);
    // display block should get added
    expect(mockHiddenMenu.classList).toContain('su__d-block');

    //cleanup
    spy.mockRestore();
    jest.spyOn(global, 'setInterval').mockRestore();
    jest.useRealTimers();
  });

  it('should scroll inner div if outer div is smaller on arrow button clicks', () => {
    const { box, buttonLeft, buttonRight, simulateSmallerOuterDiv } = getMockedElements();

    /* simulate a situation where outer div is smaller than inner to trigger arrow buttons */
    const spy = jest.spyOn(document, 'querySelector').mockImplementation(() => box);
    jest.spyOn(document, 'getElementById').mockImplementation(simulateSmallerOuterDiv);
    document.querySelectorAll = jest.fn(() => {
      return [buttonLeft, buttonRight];
    });
    mount(<TopFacets />);

    expect(spy).toHaveBeenCalledWith('.box-inner');

    /* simulate clicks on the arrow buttons to see if scroll called with expected params */
    buttonRight.click();
    expect(box.scrollTo).toHaveBeenCalledWith({
      left: box.clientWidth / 2 + box.scrollLeft,
      behavior: 'smooth'
    });
    buttonLeft.click();
    expect(box.scrollTo).toHaveBeenCalledWith({
      left: -(box.clientWidth / 2 - box.scrollLeft),
      behavior: 'smooth'
    });

    spy.mockRestore();
  });

  it('should select All Content by default in case key/label are empty in aggregations', () => {
    state.searchResult.aggregationsArray[0] = {
      key: '',
      label: '',
      values: [],
      order: 0
    };
    variables.allSelected = false;
    variables.activeType = 'My_Content';
    useSelectorMock = useSelector.mockImplementation((selector) => selector(state));
    mount(<TopFacets />);
    expect(variables.allSelected).toEqual(true);
    expect(variables.activeType).toEqual('all');
  });

  it('should handle errors and show loading screen', () => {
    const consoleSpy = jest.spyOn(console, 'log').mockImplementation(() => {});
    useSelectorMock.mockImplementation(() => {
      throw new Error('Test error');
    });
    const wrapper = shallow(<TopFacets />);
    expect(wrapper.find(SELECTORS.topFacetLoading).exists()).toBe(true);
    expect(consoleSpy).toHaveBeenCalledWith(
      'Error in component content source tab',
      expect.any(Error)
    );
    consoleSpy.mockRestore();
  });
});
