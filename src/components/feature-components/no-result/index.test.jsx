/* global jest, it, expect, describe, beforeEach, afterEach */
import React from 'react';
import { mount } from 'enzyme';
import { useDispatch, useSelector } from 'react-redux';
import NoResult from './';
import variables from '../../../redux/variables';
import { search } from '../../../redux/ducks';
import setCookies from '../../../setCookie/setCookie';
import { setWildcardToggle } from 'function-library/commonFunctions';
import { useTranslation } from 'react-i18next';

// Mock dependencies
jest.mock('react-redux', () => ({
  useDispatch: jest.fn(),
  useSelector: jest.fn()
}));

jest.mock('react-i18next', () => ({
  useTranslation: jest.fn()
}));

jest.mock('uuid', () => ({
  v4: jest.fn()
}));

jest.mock('../../../setCookie/setCookie', () => ({
  setSmartFacetOff: jest.fn()
}));

jest.mock('function-library/commonFunctions', () => ({
  setWildcardToggle: jest.fn()
}));

jest.mock('../../../redux/variables', () => require('__mocks__/variables'));

jest.mock('../../../redux/ducks', () => ({
  search: {
    start: jest.fn()
  }
}));

jest.mock('../../../StaticStrings', () => ({
  CLICK_HERE_TO_CLEAR_FILTERS: 'CLICK_HERE_TO_CLEAR_FILTERS',
  SORRY_NO_RESULTS_FOUND: 'SORRY_NO_RESULTS_FOUND',
  SHOWING_RESULTS_USING_SOME_OF_YOUR_RECENT_SEARCH_TERMS:
    'SHOWING_RESULTS_USING_SOME_OF_YOUR_RECENT_SEARCH_TERMS',
  kudos: 'kudos',
  replies: 'replies',
  views: 'views'
}));

describe('NoResult Component', () => {
  let dispatchMock, original;

  beforeEach(() => {
    dispatchMock = jest.fn();
    useTranslation.mockImplementation(() => ({ t: (key) => key }));
    useDispatch.mockReturnValue(dispatchMock);
    original = variables.previousSearches;
    useSelector.mockReturnValue([false, false]);
  });

  afterEach(() => {
    jest.clearAllMocks();
    variables.previousSearches = original;
  });

  it('renders without crashing', () => {
    const wrapper = mount(<NoResult />);
    wrapper.update();
    expect(wrapper.exists()).toBe(true);
    expect(wrapper.find('.su__sorry-no-result').exists()).toBe(true);
    expect(wrapper.find('.su__clear-filters-btn').exists()).toBe(true);
  });

  it('calls clearReset function when button is clicked', () => {
    const wrapper = mount(<NoResult />);
    useSelector.mockReturnValue(false);
    wrapper.update();
    wrapper.find('.su__clear-filters-btn').simulate('click');
    expect(variables.searchSource).toBe('no-result');
    expect(setCookies.setSmartFacetOff).toHaveBeenCalled();
    expect(setWildcardToggle).toHaveBeenCalledWith(false);
    expect(dispatchMock).toHaveBeenCalledWith(search.start(variables.searchCallVariables));
  });

  it('renders the correct number of previous search items', () => {
    const wrapper = mount(<NoResult />);
    wrapper.update();
    expect(wrapper.find('.su__list-items')).toHaveLength(2);
  });

  it('does not render previous searches when none available', () => {
    variables.previousSearches = [];
    const wrapper = mount(<NoResult />);
    wrapper.update();
    expect(wrapper.find('.su__list-items').exists()).toBe(false);
  });

  it('should handle errors gracefully', () => {
    jest.spyOn(console, 'log').mockImplementation(() => {});
    useTranslation.mockImplementation(() => {
      throw new Error('Test error');
    });
    const wrapper = mount(<NoResult />);
    wrapper.update();
    expect(wrapper.html()).toBe('<div></div>');
    expect(console.log).toHaveBeenCalledWith('Error in no result component', expect.any(Error));
  });
});
