/* global jest, it, expect, describe, beforeEach, afterEach */
import React from 'react';
import { shallow, mount } from 'enzyme';
import Advertisement from './';
import { useSelector } from 'react-redux';
import variables from '__mocks__/variables';
import state from '__mocks__/state';

const SELECTORS = {
  adv: "[data-test-id='advertisement-link']"
};
const mockURL = 'https://www.google.com';
const mockHtmlString = `<a class="testurl" href="${mockURL}">Advertisement Content</a>`;
const event = {
  target: {
    closest: jest.fn(() => ({
      getAttribute: jest.fn(() => mockURL)
    }))
  }
};

jest.mock('react-redux', () => ({
  useSelector: jest.fn()
}));

jest.mock('../../../redux/variables', () => require('__mocks__/variables'));

describe('Advertisement Component', () => {
  let useSelectorMock;

  beforeEach(() => {
    useSelectorMock = useSelector;
    window.gza = jest.fn();
  });

  afterEach(() => {
    useSelectorMock.mockClear();
  });

  it('renders advertisement HTML if available', () => {
    state.adHtml.htmlString = mockHtmlString;
    useSelectorMock.mockReturnValue(state);
    const wrapper = mount(<Advertisement />);
    expect(wrapper.exists(SELECTORS.adv)).toBe(true);
    expect(wrapper.find(SELECTORS.adv).html()).toContain(state.adHtml.htmlString);
  });

  it('does not render advertisement HTML if not available', () => {
    state.adHtml.htmlString = '';
    useSelectorMock.mockReturnValue(state);
    const wrapper = shallow(<Advertisement />);
    expect(wrapper.exists(SELECTORS.adv)).toBe(false);
  });

  it('tracks analytics when advertisements are viewed', () => {
    state.adHtml.htmlString = mockHtmlString;
    useSelectorMock.mockReturnValue(state);
    mount(<Advertisement />);
    expect(window.gza).toHaveBeenCalledTimes(1);
    expect(window.gza).toHaveBeenCalledWith('adv_view', {
      textSearched: variables.searchCallVariables.searchString,
      advId: variables.searchCallVariables.searchString,
      url: window.location.href,
      page_no: variables.searchCallVariables.pageNo
    });
  });

  it('tracks analytics when advertisement opened or clicked', () => {
    state.adHtml.htmlString = mockHtmlString;
    useSelectorMock.mockReturnValue(state);
    const wrapper = mount(<Advertisement />);

    wrapper.find(SELECTORS.adv).simulate('click', event);

    expect(window.gza).toHaveBeenCalledTimes(2); // 1 for view 1 for click
    expect(window.gza).toHaveBeenCalledWith('adv_click', {
      textSearched: variables.searchCallVariables.searchString,
      advId: variables.searchCallVariables.searchString,
      url: window.location.href,
      advUrl: mockURL,
      page_no: variables.searchCallVariables.pageNo
    });
  });

  it('catches component error when crashed and renders empty div', () => {
    // Mock useSelector to throw an error
    useSelectorMock.mockImplementation(() => {
      throw new Error('Test error');
    });
    const consoleSpy = jest.spyOn(console, 'log').mockImplementation(() => {});

    const wrapper = mount(<Advertisement />);
    expect(wrapper.isEmptyRender()).toBe(false);
    expect(wrapper.html()).toBe('<div></div>');
    expect(consoleSpy).toHaveBeenCalledWith('Error in advertisment component', expect.any(Error));
    consoleSpy.mockRestore();
  });
});
