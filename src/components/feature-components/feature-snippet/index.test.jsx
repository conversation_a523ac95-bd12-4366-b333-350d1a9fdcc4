/* global jest, it, expect, describe, beforeEach, afterEach */
import React from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { mount } from 'enzyme';
import FeaturedSnippet from './';
import variables from '__mocks__/variables';
import state from '__mocks__/state';

const testData = {
  sourceName: 'Test',
  objName: 'Test',
  _id: 'Test',
  href: 'test.com',
  highlight: { TitleToDisplayString: ['Test'] },
  trackAnalytics: 'TestFields'
};

const cmpGenerator = (cmpName) => {
  const DummyChild = (props) => (
    // eslint-disable-next-line react/prop-types
    <div className={cmpName}>
      <button
        className="steps_btn"
        onClick={() => {
          // eslint-disable-next-line react/prop-types
          props.linkOpened(testData, 1, '', '');
        }}
      >
        Test
      </button>
    </div>
  );
  return DummyChild;
};

// Mock the sub-components
jest.mock('components/feature-components/feature-snippets-steps/index.jsx', () =>
  cmpGenerator('f_steps')
);
jest.mock('components/feature-components/feature-snippet-QA/index.jsx', () => cmpGenerator('f_qa'));
jest.mock('components/feature-components/feature-snippet-multimedia/index.jsx', () =>
  cmpGenerator('f_multi')
);

jest.mock('react-redux', () => ({
  useSelector: jest.fn(),
  useDispatch: jest.fn()
}));

jest.mock('../../../redux/variables', () => require('__mocks__/variables'));

describe('FeaturedSnippet', () => {
  beforeEach(() => {
    window.gza = jest.fn();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders FeaturedSnippetSteps when steps are present and mergeSources is false', () => {
    useSelector.mockReturnValueOnce(state.searchResult.featuredSnippetResult);
    variables.searchCallVariables.mergeSources = false;
    const wrapper = mount(<FeaturedSnippet />);
    wrapper.update();

    expect(wrapper.find('div.f_steps')).toHaveLength(1);
    expect(wrapper.find('div.f_qa')).toHaveLength(0);
    expect(wrapper.find('div.f_multi')).toHaveLength(0);
  });

  it('renders FeaturedSnippetQA when questionAnswer is present and mergeSources is false', () => {
    useSelector.mockReturnValueOnce({
      ...state.searchResult.featuredSnippetResult,
      steps: []
    });
    variables.searchCallVariables.mergeSources = false;
    const wrapper = mount(<FeaturedSnippet />);
    wrapper.update();

    expect(wrapper.find('div.f_steps')).toHaveLength(0);
    expect(wrapper.find('div.f_qa')).toHaveLength(1);
    expect(wrapper.find('div.f_multi')).toHaveLength(0);
  });

  it('renders FeaturedSnippetImages when multiMedia is present and mergeSources is false', () => {
    useSelector.mockReturnValueOnce({
      ...state.searchResult.featuredSnippetResult,
      steps: [],
      questionAnswer: {}
    });
    variables.searchCallVariables.mergeSources = false;
    const wrapper = mount(<FeaturedSnippet />);
    wrapper.update();
    expect(wrapper.find('div.f_steps')).toHaveLength(0);
    expect(wrapper.find('div.f_qa')).toHaveLength(0);
    expect(wrapper.find('div.f_multi')).toHaveLength(1);
  });

  it('renders empty fragment when no relevant data is present', () => {
    useSelector.mockImplementation(() => {
      throw new Error('Test error');
    });
    useDispatch.mockImplementation(() => jest.fn());
    const wrapper = mount(<FeaturedSnippet />);
    expect(wrapper.text()).toBe('');
  });

  it('should track analytics when feature snippet link is opened', () => {
    useSelector.mockReturnValueOnce(state.searchResult.featuredSnippetResult);
    const wrapper = mount(<FeaturedSnippet />);
    wrapper.update();
    wrapper.find('button.steps_btn').simulate('click', {});
    expect(window.gza).toHaveBeenCalledTimes(1);
    expect(window.gza).toHaveBeenCalledWith('conversion', {
      index: 'Test',
      type: 'Test',
      id: 'Test',
      rank: 1,
      convUrl: 'test.com',
      convSub: 'Test',
      pageSize: variables.searchCallVariables.pageSize,
      page_no: variables.searchCallVariables.pageNo,
      sc_analytics_fields: 'TestFields'
    });
  });
});
