/* global jest, it, expect, describe */
import React from 'react';
import { shallow } from 'enzyme';
import Bookmarks from './';
import SaveBookmark from 'components/feature-components/save-bookmarks/index.jsx';
import ListBookmark from 'components/feature-components/list-bookmarks/index.jsx';

// Mock the components used within Bookmarks
jest.mock('components/feature-components/save-bookmarks/index.jsx', () => {
  const SaveBookmark = () => <div>SaveBookmark</div>;
  return SaveBookmark;
});
jest.mock('components/feature-components/list-bookmarks/index.jsx', () => {
  const ListBookmark = () => <div>ListBookmark</div>;
  return ListBookmark;
});

const props = {
  getArrAggregation: [],
  resultBookmarkClickedFunc: jest.fn(),
  limitReached: false,
  bookmarkListIconActive: false
};

describe('Bookmarks Component', () => {
  it('renders without crashing', () => {
    const wrapper = shallow(<Bookmarks {...props} />);
    expect(wrapper.exists()).toBe(true);
  });

  it('calls resultBookmarkClickedFunc when savedResultBookmark is invoked', () => {
    const wrapper = shallow(<Bookmarks {...props} />);
    const { savedResultBookmark } = wrapper.find(ListBookmark).props();
    savedResultBookmark();
    expect(props.resultBookmarkClickedFunc).toHaveBeenCalled();
  });

  it('passes the correct props to SaveBookmark and ListBookmark', () => {
    const wrapper = shallow(<Bookmarks {...props} />);

    const saveBookmarkProps = wrapper.find(SaveBookmark).props();
    const listBookmarkProps = wrapper.find(ListBookmark).props();

    expect(saveBookmarkProps.callBack).toBeInstanceOf(Function);
    expect(saveBookmarkProps.list).toEqual([]);

    expect(listBookmarkProps.bookmarkListIconActive).toBe(props.bookmarkListIconActive);
    expect(listBookmarkProps.limitReached).toBe(props.limitReached);
    expect(listBookmarkProps.savedResultBookmark).toBeInstanceOf(Function);
    expect(listBookmarkProps.getArrAggregation).toBe(props.getArrAggregation);
    expect(listBookmarkProps.bookmark).toEqual([]);
    expect(listBookmarkProps.callBack).toBeInstanceOf(Function);
  });
});
