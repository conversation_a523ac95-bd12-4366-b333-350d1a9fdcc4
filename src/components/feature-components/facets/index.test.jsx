/* global jest, it, expect, describe, beforeEach, afterEach */
import React from 'react';
import { mount, shallow } from 'enzyme';
import Facets from './';
import FacetAlternateUI from 'components/feature-components/under-consruction/dummy-factes-UI.jsx';
import state from '__mocks__/state';
import variables from '__mocks__/variables';
import { defaultThemeDataMock, useBrowserStorageMock } from '__mocks__/storage-mock';

// Create a mock selector function
const mockSelector = jest.fn();

// Mock the dependencies
jest.mock('react-redux', () => ({
  useSelector: (selector) => mockSelector(selector)
}));

jest.mock('components/feature-components/filter-list/index.jsx', () => {
  const FilterList = () => <div>Mocked FilterList</div>;
  return FilterList;
});
jest.mock('components/feature-components/under-consruction/dummy-factes-UI.jsx', () => {
  const FacetAlternateUI = () => <div>Mocked FacetAlternateUI</div>;
  return FacetAlternateUI;
});

jest.mock('../../../redux/variables', () => require('__mocks__/variables'));
const [startLocalStorageMock, clearLocalStorageMock] = useBrowserStorageMock('local');

describe('Facets Component', () => {
  let props = {
    smartFacetsAggregation: [],
    setIsButtonActive: jest.fn()
  };

  beforeEach(() => {
    startLocalStorageMock();
    // Reset the mock before each test
    mockSelector.mockReset();
  });

  afterEach(() => {
    clearLocalStorageMock();
    jest.clearAllMocks();
  });

  it('renders FacetAlternateUI components when there is an error', () => {
    // Mock selector to throw an error
    mockSelector.mockImplementation(() => {
      throw new Error('Error fetching data');
    });

    const wrapper = shallow(<Facets smartFacetsAggregation={[]} />);
    expect(wrapper.find(FacetAlternateUI)).toHaveLength(3);
  });

  it('renders FacetAlternateUI components when search results are not available', () => {
    mockSelector.mockImplementation((selector) => {
      if (selector.toString().includes('inProgress')) {
        return false;
      }
      return {};
    });

    const wrapper = shallow(<Facets smartFacetsAggregation={[]} />);
    expect(wrapper.find(FacetAlternateUI)).toHaveLength(3);
  });

  it('should update hideEye option of searchresult as per preferences stored in localstorage', () => {
    const stackQuestionKey = '2_153_dummy___stack_question___is_answered';
    mockSelector.mockImplementation((selector) => {
      if (selector.toString().includes('firstLoad')) {
        return false;
      }
      return [state.searchResult];
    });

    /* Scenario - hideEye preference is true */
    defaultThemeDataMock.facetsOrder[2].hideEye = true;
    localStorage.setItem(
      'theme' + variables.searchCallVariables.uid,
      JSON.stringify(defaultThemeDataMock)
    );
    let wrapper = mount(<Facets {...props} />);
    wrapper.update();
    let [stackQuestion] = state.searchResult.aggregationsArray.filter(
      (i) => i.key === stackQuestionKey
    );
    expect(stackQuestion.hideEye).toEqual(true);
  });

  it('should turn hideEye off for fields common between smartFacets and search aggregations', () => {
    const stackQuestionKey = '2_153_dummy___stack_question___is_answered';
    const props = {
      smartFacetsAggregation: state.autocomplete.smartAggregations,
      setIsButtonActive: jest.fn()
    };
    mockSelector.mockImplementation((selector) => {
      if (selector.toString().includes('firstLoad')) {
        return false;
      }
      return [state.searchResult];
    });

    defaultThemeDataMock.facetsOrder[2].hideEye = true;
    localStorage.setItem(
      'theme' + variables.searchCallVariables.uid,
      JSON.stringify(defaultThemeDataMock)
    );
    let wrapper = mount(<Facets {...props} />);
    wrapper.update();
    let [stackQuestion] = state.searchResult.aggregationsArray.filter(
      (i) => i.key === stackQuestionKey
    );
    expect(stackQuestion.hideEye).toEqual(false);
  });

  it('should have loading state as false', () => {
    mockSelector.mockImplementation((selector) => {
      if (selector.toString().includes('inProgress')) {
        return false;
      }
      return [state.searchResult];
    });
    const loading = mockSelector((state) => state.inProgress);
    expect(loading).toBe(false);
  });
});
