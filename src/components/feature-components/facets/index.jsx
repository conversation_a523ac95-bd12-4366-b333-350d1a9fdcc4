import React, { Fragment, useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import variables from '../../../redux/variables';
import FilterList from 'components/feature-components/filter-list/index.jsx';
import FacetAlternateUI from 'components/feature-components/under-consruction/dummy-factes-UI.jsx';
import { mergeFilters } from '../../../mergeFacet';
import ClearFilter from '../clear-filter/index';
import { tabIndexes } from '../../../constants/a11y';
import { useTranslation } from 'react-i18next';
import StaticStrings from '../../../StaticStrings';
import useDevice from 'function-library/hooks/use-device/use-device';
import { dummySearchResult } from 'constants/constants';

const Facets = (props) => {
  try {
    let [searchResult] = useSelector((state) => {
      return [state.searchResult];
    });
    const { t } = useTranslation();
    const { isDeviceDesktop } = useDevice();

    const firstLoad = useSelector((state) => state.firstLoad);

    // FOR SKELETON LOADING
    if (firstLoad) {
      searchResult = dummySearchResult.searchResult;
    }
    let smartAggregation = props.smartFacetsAggregation;
    const [clearfilter, setClearfilter] = useState(false);
    let parsed_array =
      searchResult?.result && searchResult.merged_facets
        ? JSON.parse(searchResult.merged_facets)
        : [];
    parsed_array.length &&
      parsed_array.forEach(function (o) {
        mergeFilters(o, searchResult.aggregationsArray);
      });

    let a = JSON.parse(localStorage.getItem('theme' + variables.searchCallVariables.uid));
    if (a?.facetsOrder && searchResult?.aggregationsArray) {
      for (let element of a.facetsOrder) {
        if (element.hideEye) {
          for (let i of searchResult.aggregationsArray) {
            if (i.label == element.label) {
              i.hideEye = element.hideEye;
            }
          }
        }
      }

      for (let i = 0; i < a.facetsOrder.length; i++) {
        for (let element of searchResult.aggregationsArray) {
          if (a.facetsOrder[i].label == element.label) {
            element.index = i;
          }
        }
      }
    }

    const addPathName = (parent, childArray) => {
      childArray.forEach((o) => {
        o.path = JSON.parse(JSON.stringify(parent));
        if (o.childArray && o.childArray.length) {
          let p = JSON.parse(JSON.stringify(parent));
          p.push(o.Contentname);
          addPathName(p, o.childArray);
        }
      });
    };

    searchResult.aggregationsArray &&
      searchResult.aggregationsArray.filter(function (o) {
        if (o.key.indexOf('_nested') > -1 || o.key.indexOf('_navigation') > -1) {
          o.values.forEach((l) => {
            if (l.childArray && l.childArray.length) {
              let parent = [];
              parent.push(l.Contentname);
              addPathName(parent, l.childArray);
            }
          });
        }
      });

    variables.searchCallVariables.smartFacets &&
      smartAggregation &&
      smartAggregation.forEach((hide) => {
        searchResult.aggregationsArray.forEach((item) => {
          if (item.key == hide.key) {
            item.hideEye = false;
          }
        });
      });

    if (searchResult.searchClientSettings?.hiddenFacet && variables.allSelected) {
      searchResult.aggregationsArray.forEach((item) => {
        searchResult.searchClientSettings.hiddenFacet.forEach((y) => {
          if (item.key == y) {
            item.hideFacetAdmin = true;
          }
        });
      });
    }
    const updateClearbutton = () => {
      if (variables.searchCallVariables.aggregations.length) {
        for (let i of variables.searchCallVariables.aggregations) {
          if (i.filter) {
            props.setIsButtonActive(true);
            setClearfilter(true);
            return;
          } else {
            props.setIsButtonActive(false);
            setClearfilter(false);
          }
        }
      } else {
        props.setIsButtonActive(false);
        setClearfilter(false);
      }
    };
    useEffect(() => {
      updateClearbutton();
    }, [variables.searchCallVariables.aggregations]);

    return (
      <div
        id="facets-section"
        className={`su__left-sidebar  su__border-t-none su__bg-bright-white  ${
          !isDeviceDesktop ? 'su__h-100' : 'su__box-shadow-bl-2'
        } ${!searchResult.results && !searchResult.aggregationsArray ? 'su__sc-loading' : ''} `}
      >
        <div className="su__position-relative su__flex-vcenter su__justify-content-between su__sc-loading">
          <div
            tabIndex={tabIndexes.tabIndex_0}
            className=" su__px-3_25 su__pt-3  su__font-16 su__line-height-22 su__f-normal su__my-0 su__d-inline-block  su__dark-gray su__pb-new"
          >
            {t(StaticStrings.filters)}
          </div>
          <div className="su__filter-appear su__px-3_25 ">
            {clearfilter ? (
              <ClearFilter color={'su__cb-blue'} />
            ) : (
              <button
                lang={variables.searchCallVariables.langAttr}
                aria-label={t(StaticStrings.CLEARALL)}
                type="button"
                className="su__clear-all-btn su__border-none su__cursor su__bg-transparent su__f-normal su__font-14 su__line-height-22  su__text-underline  su__mb-1 su__p-0  su__loading-view su__clear-filter su__filter-appear"
                disabled
              >
                {t(StaticStrings.CLEARALL)}
              </button>
            )}
          </div>
        </div>
        <div className={`${!isDeviceDesktop ? 'su__pb-5 su__h-100 su__y-scroll ' : ''}`}>
          {searchResult.result &&
            [...searchResult.aggregationsArray]
              .sort((a, b) => {
                return a.index - b.index;
              })
              .filter(
                (item) =>
                  item.order !== 0 &&
                  !item.hideEye &&
                  !item.hideFacetAdmin &&
                  item.values.length !== 0
              )
              .map((item, index, filterListArray) => (
                <Fragment key={index}>
                  {item.values.length != 0 && (
                    <FilterList
                      item={item}
                      index={index}
                      updateClearbutton={updateClearbutton}
                      filterListArray={filterListArray}
                    />
                  )}
                </Fragment>
              ))}
        </div>
      </div>
    );
  } catch (e) {
    console.log('Error in Facets component', e);
    return (
      <div>
        <FacetAlternateUI />
        <FacetAlternateUI />
        <FacetAlternateUI />
      </div>
    );
  }
};

export default Facets;
