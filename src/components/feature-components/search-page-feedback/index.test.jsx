/* global jest, it, expect, describe, beforeEach, afterEach */
import React from 'react';
import { mount, shallow } from 'enzyme';
import { useSelector } from 'react-redux';
import SearchPageFeedback from './index';
import Tooltip from 'components/feature-components/tooltip/index';
import variables from '__mocks__/variables';
import Icons from '../../../assets/svg-icon/svg';
import { a11y } from '../../../constants/a11y';
import state from '__mocks__/state';

// Mock the required modules
jest.mock('react-redux', () => ({
  useSelector: jest.fn()
}));

jest.mock('react-i18next', () => ({
  useTranslation: () => ({ t: (key) => key })
}));

jest.mock('../../../redux/variables', () => require('__mocks__/variables'));

jest.mock('react-redux', () => ({
  useSelector: jest.fn()
}));

// Mock sessionStorage and localStorage
const mockSessionStorage = {};
const mockLocalStorage = {};
Object.defineProperty(window, 'sessionStorage', {
  value: {
    getItem: jest.fn((key) => mockSessionStorage[key]),
    setItem: jest.fn((key, value) => {
      mockSessionStorage[key] = value;
    }),
    removeItem: jest.fn((key) => {
      delete mockSessionStorage[key];
    })
  }
});

Object.defineProperty(window, 'localStorage', {
  value: {
    getItem: jest.fn((key) => mockLocalStorage[key]),
    setItem: jest.fn((key, value) => {
      mockLocalStorage[key] = value;
    }),
    removeItem: jest.fn((key) => {
      delete mockLocalStorage[key];
    })
  }
});

// Mock utility functions
global._gr_utility_functions = {
  getCookie: jest.fn(() => 'mock-session-id')
};

describe('SearchPageFeedback Component', () => {
  const props = {
    isOpenFeedback: false,
    clickBasedAutoTrigger: 0,
    isFeedbackGiven: false,
    isMultiStepQuestions: {
      isMultiForm: false,
      step: 0
    },
    isThanksModel: {
      isOpen: false,
      message: ''
    },
    isPopupDismissed: false,
    isTooEarlyFeedbackClicked: false,
    setclickBasedAutoTrigger: jest.fn(),
    setOpenFeedback: jest.fn(),
    setThanksModel: jest.fn()
  };
  let useSelectorMock;

  beforeEach(() => {
    jest.resetModules();
    jest.clearAllMocks();
    useSelectorMock = useSelector;
    useSelectorMock.mockReturnValue(state.pageRatingResult);
  });

  afterEach(() => {
    jest.clearAllMocks();
    Object.keys(mockSessionStorage).forEach((key) => delete mockSessionStorage[key]);
    Object.keys(mockLocalStorage).forEach((key) => delete mockLocalStorage[key]);
  });
  it('Check mocked variables', () => {
    expect(variables.searchCallVariables.langAttr).toBe('en');
    expect(a11y.ROLES.BTN).toBe('button');
  });
  it('should update the searchFeedbackIconToggle based on searchFeedback', () => {
    const wrapper = mount(<SearchPageFeedback {...props} />);
    // After the effect runs, the state should be updated
    expect(wrapper.find(SearchPageFeedback)).toHaveLength(1);
  });

  it('should render the component correctly', () => {
    const wrapper = mount(<SearchPageFeedback {...props} />);
    expect(wrapper.find('.su__feedback-text')).toHaveLength(1);
    expect(wrapper.find(Tooltip)).toHaveLength(1);
  });

  it('should handle click on the feedback button', () => {
    const wrapper = mount(<SearchPageFeedback {...props} />);
    wrapper.find('.su__feedback-text').simulate('click');
    expect(props.setOpenFeedback).toHaveBeenCalledWith(!props.isOpenFeedback);
  });

  it('should handle session ID changes', () => {
    mount(<SearchPageFeedback {...props} />);
    expect(global._gr_utility_functions.getCookie).toHaveBeenCalledWith('_gz_sid');
    expect(mockSessionStorage.previousSidSession).toEqual('mock-session-id');
  });

  it('should reset feedback when isPopupDismissed is true', () => {
    const localProps = { ...props, isPopupDismissed: true };
    shallow(<SearchPageFeedback {...localProps} />);
    expect(mockSessionStorage).toEqual({});
  });

  it('should handle timer setup for feedback popup', () => {
    mount(<SearchPageFeedback {...props} />);
    expect(mockSessionStorage.popupTimeout).toBeDefined();
  });

  it('should handle feedback modal logic', () => {
    const wrapper = mount(<SearchPageFeedback {...props} />);
    wrapper.find('.su__feedback-text button').simulate('click');
    expect(props.setOpenFeedback).toHaveBeenCalledWith(!props.isOpenFeedback);
  });

  it('renders the Icons component', () => {
    const wrapper = mount(<SearchPageFeedback {...props} />);
    expect(wrapper.find(Icons).exists()).toBe(true);
  });

  it('should parse searchFeedback and configure feedback options', () => {
    mount(<SearchPageFeedback {...props} />);
    expect(props.setclickBasedAutoTrigger).not.toHaveBeenCalled();
  });

  it('should clear timers on unmount', () => {
    jest.useFakeTimers();
    const clearTimeoutSpy = jest.spyOn(window, 'clearTimeout');
    const wrapper = mount(<SearchPageFeedback {...props} />);
    expect(clearTimeoutSpy).toHaveBeenCalledTimes(2);
    wrapper.unmount();
    expect(clearTimeoutSpy).toHaveBeenCalledTimes(3);
    clearTimeoutSpy.mockRestore();
  });
});
