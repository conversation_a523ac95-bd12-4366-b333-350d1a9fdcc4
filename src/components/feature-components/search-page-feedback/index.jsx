/* global _gr_utility_functions */
import React, { useEffect, useState, useRef } from 'react';
import Tooltip from 'components/feature-components/tooltip/index';
import { useTranslation } from 'react-i18next';
import variables from '../../../redux/variables';
import StaticStrings from 'StaticStrings';
import { a11y, tabIndexes } from '../../../constants/a11y';
import { bool, func, number } from 'prop-types';
import Icons from '../../../assets/svg-icon/svg';
import { useSelector } from 'react-redux';
const SearchPageFeedback = (props) => {
  const { t } = useTranslation();
  const {
    clickBasedAutoTrigger,
    isOpenFeedback,
    setOpenFeedback,
    setclickBasedAutoTrigger,
    setThanksModel,
    isPopupDismissed,
    setIsPopupDismissed,
    setisMultiStepQuestions,
    setIsTooEarlyFeedbackClicked
  } = props;
  const { searchFeedback } = useSelector((state) => state.pageRatingResult);
  const { selectedMinuteAutoTrigger, searchFeedbackAutoTriggerSurvey } = searchFeedback
    ? JSON.parse(searchFeedback)
    : {};
  const [searchFeedbackIconToggle, setSearchFeedbackIconToggle] = useState(false);
  const isTooEarlyFeedback = sessionStorage.getItem('isTooEarlyFeedback') === 'true';
  const cookieValue = _gr_utility_functions.getCookie('_gz_sid');
  const [currentSessionId, setCurrentSessionId] = useState(cookieValue ? cookieValue : null);
  let isFeedbackSubmitted = sessionStorage.getItem('isFeedbackSubmitted') === 'true';
  let storedPreviousSid = sessionStorage.getItem('previousSidSession');
  let previousSid = storedPreviousSid ? storedPreviousSid : null;
  const popupTimeoutRef = useRef(null); // Ref for popup timeout
  const sessionIntervalRef = useRef(null); // Ref for session interval
  const thankYouPopUpRef = useRef(null); // Ref for closing thank you pop up
  let timeoutInMs = selectedMinuteAutoTrigger * 60 * 1000;
  const popupTriggerTime = Date.now() + timeoutInMs;
  const timerStartForPopup = Date.now();
  const readableTimerStartForPopup = new Date(timerStartForPopup).toLocaleString();
  // Create a readable format for the timer
  const readablepopupTriggerTime = new Date(popupTriggerTime).toLocaleString();

  /**
   * Resets all feedback-related state and session storage items.
   * No parameters.
   * No return value.
   */
  const resetFeedback = () => {
    setclickBasedAutoTrigger(0); // Reset click-based trigger count
    setIsPopupDismissed(false);
    setIsTooEarlyFeedbackClicked(false);
    setisMultiStepQuestions((prev) => {
      return { ...prev, isMultiForm: false, step: 0 };
    });
    sessionStorage.removeItem('isFeedbackSubmitted'); // Remove feedback submission state
    localStorage.removeItem('isSearchExpFeedback'); // Remove feedback submission state from local storage
    localStorage.removeItem('isFeedbackPopupDismissed'); // Remove feedback dismissed state from local storage
    sessionStorage.removeItem('isFeedbackPopupDismissed'); // Remove popup dismissed state
    sessionStorage.removeItem('popupTimeout'); // Remove popup timeout data
    sessionStorage.removeItem('clickBasedAutoTrigger'); // Remove click trigger data
    sessionStorage.removeItem('rating'); // Remove stored rating from session
    sessionStorage.removeItem('isTooEarlyFeedback'); // Remove too early radio buuton stored state
    if (!isPopupDismissed) {
      sessionStorage.removeItem('previousSidSession'); // Remove stored session ID
    }
    clearTimers(); // Clear any active timers
    setThanksModel((prev) => {
      return { ...prev, isOpen: false, message: '' };
    }); // Reset the "Thank you" modal state
  };

  /**
   * Handles changes in session ID, updates the current session state,
   * and resets feedback if the session ID is cleared.
   * No parameters.
   * No return value.
   */
  const handleChangeInSessionId = () => {
    const newSessionId = _gr_utility_functions.getCookie('_gz_sid'); // Fetch the latest session ID from the cookie

    if (newSessionId && newSessionId !== currentSessionId && currentSessionId !== '') {
      // If session ID is valid and has changed, update the session I
      setCurrentSessionId(newSessionId); // Update state
      clearInterval(sessionIntervalRef.current); // Stop session change tracking
    } else if (
      newSessionId === '' &&
      newSessionId !== currentSessionId &&
      currentSessionId !== ''
    ) {
      // If session ID is cleared, reset the feedback state
      resetFeedback();
    }
  };

  /**
   * Starts a timeout for displaying the feedback popup.
   * No parameters.
   * No return value.
   */
  const startPopUpTimeOut = () => {
    const isPopUpTimerExists = !!sessionStorage.getItem('popupTimeout'); // Check if a timer already exists

    if (!isPopUpTimerExists) {
      clearTimers(); // Clear existing timers
      sessionStorage.setItem(
        'popupTimeout',
        JSON.stringify({ popupTriggerTime, readableTimerStartForPopup, readablepopupTriggerTime })
      ); // Store timer data in session storage

      popupTimeoutRef.current = setTimeout(() => {
        openFeedbackModal(); // Trigger feedback modal after timeout
      }, timeoutInMs);
    }
  };

  /**
   * Clears all active timers for feedback popup.
   * No parameters.
   * No return value.
   */
  const clearTimers = () => {
    clearTimeout(popupTimeoutRef.current); // Clear the timeout for feedback popup
    popupTimeoutRef.current = null;
    sessionStorage.removeItem('popupTimeout'); // Remove popup timeout data
  };

  useEffect(() => {
    // Poll every 1 second as a fallback
    const interval = setInterval(() => {
      const storedValue = localStorage.getItem('isFeedbackPopupDismissed') === 'true';
      if (storedValue) {
        sessionStorage.setItem('isFeedbackPopupDismissed', 'true');
        setIsPopupDismissed(storedValue);
      }
    }, 1000);

    return () => {
      clearInterval(interval);
    };
  }, []);

  /**
   * Monitors changes in the `currentSessionId` and manages timers.
   * Automatically invoked when `currentSessionId` changes.
   */
  useEffect(() => {
    if (
      (previousSid ?? null) !== (currentSessionId || null) &&
      searchFeedbackAutoTriggerSurvey &&
      !variables.isConsoleTypeSC
    ) {
      sessionStorage.setItem('previousSidSession', currentSessionId); // Store the new session ID
      clearTimers(); // Clear existing timers
      startPopUpTimeOut(); // Start a new feedback popup timer
    }

    sessionStorage.setItem('previousSidSession', currentSessionId); // Persist current session ID
  }, [currentSessionId]);

  /**
   * Tracks changes in session ID at regular intervals (1 second).
   * Starts the interval on component mount and cleans it up on unmount.
   */
  useEffect(() => {
    sessionIntervalRef.current = setInterval(handleChangeInSessionId, 1000); // Check session ID every second

    return () => {
      clearInterval(sessionIntervalRef.current); // Clear interval on unmount
    };
  }, [currentSessionId]);

  /**
   * Manages feedback popup logic based on user activity, feedback parameters, and session state.
   * Automatically invoked when `clickBasedAutoTrigger` or `searchFeedback` changes.
   */
  useEffect(() => {
    if (!searchFeedback) return; // Exit if no search feedback configuration

    try {
      const searchExperienceObj = JSON.parse(searchFeedback); // Parse search feedback configuration
      const {
        searchFeedbackIconToggle,
        searchFeedbackAutoTriggerSurvey,
        selectedBasedActivityAutoTrigger
      } = searchExperienceObj;
      // Handle icon toggle
      setSearchFeedbackIconToggle(!!searchFeedbackIconToggle); // Enable or disable feedback icons

      const shouldTriggerFeedback =
        searchFeedbackAutoTriggerSurvey &&
        clickBasedAutoTrigger === Number(selectedBasedActivityAutoTrigger) &&
        !isFeedbackSubmitted &&
        !isPopupDismissed &&
        (previousSid ?? null) === (currentSessionId || null);

      if (shouldTriggerFeedback) {
        openFeedbackModal(); // Immediately trigger feedback modal if conditions are met
      } else if (!isFeedbackSubmitted && !isPopupDismissed && searchFeedbackAutoTriggerSurvey) {
        startPopUpTimeOut(); // Start timeout if feedback is not yet submitted
      } else if (isPopupDismissed === true) {
        clearTimers();
        sessionStorage.removeItem('clickBasedAutoTrigger'); // Remove click trigger data
      }

      return () => {
        clearTimeout(popupTimeoutRef.current); // Clear timeout on unmount
      };
    } catch (error) {
      console.error('Error parsing searchFeedback:', error); // Log any errors during parsing
    }
  }, [clickBasedAutoTrigger, searchFeedback, isPopupDismissed]);

  const handleClick = () => {
    variables.searchResultClicked = false;
  };
  const closeThanksPopAfterTwoSeconds = () => {
    // Properly clear the previous timeout before setting a new one
    if (thankYouPopUpRef.current) {
      clearTimeout(thankYouPopUpRef.current);
    }
    // Store the timeout ID correctly
    thankYouPopUpRef.current = setTimeout(() => {
      setThanksModel((prev) => ({
        ...prev,
        isOpen: false,
        message: "You've already shared your Feedback."
      }));

      // Once the modal is closed, clear the ref to avoid stale timeouts
      thankYouPopUpRef.current = null;
    }, 2000);
  };

  /**
   * Opens the feedback modal or the "Thank You" modal based on session and feedback state.
   * No parameters.
   * No return value.
   */
  const openFeedbackModal = () => {
    // Retrieve the previously stored session ID from session storage
    let storedPreviousSid = sessionStorage.getItem('previousSidSession');

    // Set `previousSid` to the stored value or null if it doesn't exist
    let previousSid = storedPreviousSid ? storedPreviousSid : null;
    // check if feedback isalready submitted from page rating widget
    const isSearchExpFeedback = localStorage.getItem('isSearchExpFeedback') === 'true';
    if (isSearchExpFeedback === true) {
      sessionStorage.setItem('isFeedbackSubmitted', true);
      isFeedbackSubmitted = sessionStorage.getItem('isFeedbackSubmitted') === 'true';
    } else {
      sessionStorage.removeItem('isFeedbackSubmitted');
      isFeedbackSubmitted = false;
    }

    // Check if feedback has already been submitted, session IDs match, and it's not too early for feedback
    if (
      isFeedbackSubmitted && // Ensure feedback was submitted
      (previousSid ?? null) === (currentSessionId ?? null) && // Match session IDs
      !isTooEarlyFeedback // Ensure it's not too early to show feedback
    ) {
      setThanksModel((prev) => {
        return { ...prev, isOpen: true, message: "You've already shared your Feedback." };
      }); // Open the "Thank You" modal
      closeThanksPopAfterTwoSeconds();
    } else {
      // Toggle the feedback modal's open state
      setOpenFeedback(!isOpenFeedback);
    }
  };

  return (
    <>
      {searchFeedbackIconToggle && !variables.isConsoleTypeSC && (
        <div className="su__feedback-text" onClick={() => openFeedbackModal()}>
          <Tooltip
            text={t(StaticStrings.give_search_feedback)}
            position="left"
            className="position-relative"
          >
            <button
              type="button"
              lang={variables.searchCallVariables.langAttr}
              aria-label={t(StaticStrings.GIVEFEEDBACK)}
              role={a11y.ROLES.BTN}
              tabIndex={tabIndexes.tabIndex_0}
              onClick={handleClick}
              className="su__feedback-searchsvg su__font-12 su__position-fixed su__zindex su__cursor a11y-btn su__searchPageFeedback su__sc-loading"
            >
              <Icons IconName="SearchFeedbackBtn" width="32" height="32" color="#fff" />
            </button>
          </Tooltip>
        </div>
      )}
    </>
  );
};

SearchPageFeedback.propTypes = {
  setOpenFeedback: func,
  isOpenFeedback: bool,
  clickBasedAutoTrigger: number,
  setThanksModel: func,
  setclickBasedAutoTrigger: func,
  isPopupDismissed: bool,
  setIsPopupDismissed: func,
  setisMultiStepQuestions: func,
  setIsTooEarlyFeedbackClicked: func
};

SearchPageFeedback.defaultProps = {
  setOpenFeedback: () => {},
  isOpenFeedback: false,
  setclickBasedAutoTrigger: () => {},
  clickBasedAutoTrigger: 0
};

export default SearchPageFeedback;
