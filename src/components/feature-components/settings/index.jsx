import React, { useState, Fragment, useEffect, useRef } from 'react';
import Icons from '../../../assets/svg-icon/svg';
import { useTranslation } from 'react-i18next';
import Tooltip from 'components/feature-components/tooltip/index.jsx';
import FacetPreference from 'components/feature-components/facet-preference/index.jsx';
import variables from '../../../redux/variables';
import StaticStrings from 'StaticStrings';
import IconColors from 'IconColors';
import setCookies from '../../../setCookie/setCookie';
import { a11y, A11Y_IDS, tabIndexes } from '../../../constants/a11y';
import { useDevice } from 'function-library/hooks';
import { SC_IDS, EVENT_NAMES } from 'constants/constants';

const Settings = (props) => {
  try {
    let dataModalA11y = props.dataModalA11y;
    let facetsData = props.data;
    let mergedFacet = props.mergedFacet;
    let isSmartFacets = props.isSmartFacets;
    let smartFacetsAggregation = props.smartFacetsAggregation;
    let isAllContentShow = props.allContentHide;
    const { isDeviceMobile } = useDevice();
    const { t } = useTranslation();
    const [isComponentVisible, setIsComponentVisible] = useState(false);
    const [showSearchTips, setComponentVisible] = useState(false);
    const [editPageLayout, toggleEditMode] = useState(false);
    const [isAutoLearning, setIsAutoLearning] = useState(true);
    const ref = useRef(null);
    const [boxVisible, setBoxVisible] = useState(false);
    const handleHideDropdown = (e) => {
      if (e.key === EVENT_NAMES.ESCAPE) {
        setIsComponentVisible(false);
        setComponentVisible(false);
        toggleEditMode(false);
      }
    };

    const handleClickOutside = (e) => {
      if (ref.current && !ref.current.contains(e.target)) {
        setIsComponentVisible(false);
      }
    };

    useEffect(() => {
      document.addEventListener('keydown', handleHideDropdown);
      document.addEventListener('click', handleClickOutside, true);
      if (
        localStorage.getItem('AutoLearning') == null ||
        localStorage.getItem('AutoLearning') == undefined
      ) {
        localStorage.setItem('AutoLearning', true);
      }
      let isAutoLearning = localStorage.getItem('AutoLearning') === 'false' ? false : true;
      setIsAutoLearning(isAutoLearning);
      return () => {
        document.removeEventListener('keydown', handleHideDropdown);
        document.removeEventListener('click', handleClickOutside, true);
      };
    }, []);

    useEffect(() => {
      if (!isDeviceMobile) {
        setBoxVisible(true);
      } else if (
        isSmartFacets ||
        !variables.mergeResults ||
        (variables.mergeResults && !variables.resultsInAllContentSources)
      ) {
        setBoxVisible(true);
      } else {
        setBoxVisible(false);
      }
    }, [
      isDeviceMobile,
      isSmartFacets,
      variables.mergeResult,
      variables.resultsInAllContentSources
    ]);

    const onChangeAutoLearning = (e) => {
      !e && setCookies.setSmartFacetOff();
      localStorage.setItem('AutoLearning', e);
      setIsAutoLearning(e);
    };

    return (
      <Fragment>
        <button
          type="button"
          lang={variables.searchCallVariables.langAttr}
          aria-label={t(StaticStrings.tips)}
          role={a11y.ROLES.BTN}
          tabIndex={tabIndexes.tabIndex_0}
          className={`su__mobile-child-block su__d-md-none su__d-xs-block su__col su__searchTips su__text-center su__font-9 su__font-bold a11y-btn p-0  ${
            isDeviceMobile ? 'su__loading-Dnone' : ''
          }`}
          onClick={() => {
            setComponentVisible(!showSearchTips);
          }}
        >
          <div className={`su__mob-search-iner ${showSearchTips ? 'su__mob-active' : ''}`}>
            <div className="su__mob-icon su__mobile_setting_icon su__mobile_arabic_header">
              <svg
                id="tips_and_updates_black_24dp_1_"
                data-name="tips_and_updates_black_24dp (1)"
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
              >
                <rect
                  id="Rectangle_25"
                  data-name="Rectangle 25"
                  width="24"
                  height="24"
                  fill="none"
                />
                <path
                  id="Path_23"
                  data-name="Path 23"
                  d="M7,20h4a2,2,0,0,1-4,0ZM5,19h8V17H5ZM16.5,9.5A7.536,7.536,0,0,1,12.73,16H5.27A7.5,7.5,0,1,1,16.5,9.5Zm-2,0a5.5,5.5,0,0,0-11,0A5.436,5.436,0,0,0,5.85,14h6.3A5.436,5.436,0,0,0,14.5,9.5Zm6.87-2.13L20,8l1.37.63L22,10l.63-1.37L24,8l-1.37-.63L22,6ZM19,6l.94-2.06L22,3l-2.06-.94L19,0l-.94,2.06L16,3l2.06.94Z"
                  fill="#fff"
                />
              </svg>
            </div>
          </div>
        </button>
        <div
          className={` setting-container ${isComponentVisible ? 'd-none' : null} ${
            boxVisible ? null : 'd-none'
          } su__kh-btn-lh-0  su__loading-Dnone`}
        >
          {' '}
          {/* does not show gear icon in zendesk mobile view */}
          <Tooltip text={t(StaticStrings.settings)} position="left" className="position-relative">
            <button
              type="button"
              data-trigger-a11y={A11Y_IDS.trap}
              data-name-a11y={`settings${dataModalA11y ? '-mobile' : '-desktop'}`}
              lang={variables.searchCallVariables.langAttr}
              aria-label={t(StaticStrings.settings)}
              role={a11y.ROLES.BTN}
              tabIndex={tabIndexes.tabIndex_0}
              id="customizedSettings"
              className={`su__side-Search-tips su__rtlleft su__d-md-block su__bg-blue-grd su__position-fixed su__cursor su__zindex-2 su__d-lg-flex su__minscroller border-0 su__hide-settings_gear su__setting-br su__bs-cyan-blue su__setting-bs su__pt-4px su__w-35px  su__pl-1 ${
                isComponentVisible ? 'su__hide-gear' : ''
              }`}
              onClick={() => {
                setIsComponentVisible(!isComponentVisible);
              }}
            >
              <span id="gear">
                <Icons
                  className="gear-icon"
                  IconName="settings"
                  width="27"
                  height="27"
                  color={IconColors.SettingsGearIcon}
                />
              </span>
            </button>
          </Tooltip>
        </div>

        {isComponentVisible && (
          <div
            id={A11Y_IDS.trap}
            ref={ref}
            className={`sidebar su__customized__settings su__position-fixed ${
              !variables.mergeResults ||
              (variables.mergeResults && !variables.resultsInAllContentSources)
                ? 'su__side-customized-gear'
                : 'su__side-customized-gear-all-cs'
            } ${isComponentVisible ? 'su__show-gear' : ''}`}
          >
            {isSmartFacets && (
              <>
                <div className="su__customized__settings-inner">
                  <div
                    className="su__customized_switch su__gear-min-width"
                    onClick={() => onChangeAutoLearning(!isAutoLearning)}
                  >
                    <input
                      tabIndex={tabIndexes.tabIndex_0}
                      id="auto-learning"
                      type="checkbox"
                      value={isAutoLearning}
                      checked={!!isAutoLearning}
                      readOnly
                    />
                    <span className="su__customized_switch_slider round"></span>
                  </div>
                  <label
                    lang={variables.searchCallVariables.langAttr}
                    htmlFor="auto-learning"
                    className="su__font-13 su__pl-2 su__rtlpl-0 su__rtlpr-2 su___autolearning"
                  >
                    {t(StaticStrings.AUTOLEARNING)}
                  </label>
                  <span className="su__mt-1 su__tooltip-gear">
                    <Tooltip
                      text={t(StaticStrings.autolearning_facet_preselect)}
                      position="left"
                      className="position-relative"
                    >
                      <span
                        tabIndex={tabIndexes.tabIndex_0}
                        className="su___show-more-summary su__ml-2"
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="14"
                          height="14"
                          viewBox="0 0 14 14"
                        >
                          <defs>
                            <clipPath id="clip-Info">
                              <rect width="14" height="14" />
                            </clipPath>
                          </defs>
                          <g id="Info" clipPath="url(#clip-Info)">
                            <g id="info_black_24dp_2_" data-name="info_black_24dp (2)">
                              <path
                                id="Path_10984"
                                data-name="Path 10984"
                                d="M0,0H14V14H0Z"
                                fill="none"
                              />
                              <path
                                id="Path_10985"
                                data-name="Path 10985"
                                d="M7.833,2a5.833,5.833,0,1,0,5.833,5.833A5.835,5.835,0,0,0,7.833,2Zm0,8.75a.585.585,0,0,1-.583-.583V7.833a.583.583,0,0,1,1.167,0v2.333A.585.585,0,0,1,7.833,10.75Zm.583-4.667H7.25V4.917H8.417Z"
                                transform="translate(-0.833 -0.833)"
                                fill={IconColors.autolearn_svg_fill}
                              />
                            </g>
                          </g>
                        </svg>
                      </span>
                    </Tooltip>
                  </span>
                </div>
                <hr className="su-customize-hr" />
              </>
            )}
            {(!variables.mergeResults ||
              (variables.mergeResults && !variables.resultsInAllContentSources)) && (
              <Tooltip
                text={t(StaticStrings.CUSTOMIZE)}
                position="left"
                className="position-relative"
              >
                <button
                  type="button"
                  data-trigger-a11y={A11Y_IDS.childTrap}
                  data-name-a11y="customized__settings"
                  lang={variables.searchCallVariables.langAttr}
                  role={a11y.ROLES.BTN}
                  aria-label={t(StaticStrings.CUSTOMIZED)}
                  tabIndex={tabIndexes.tabIndex_0}
                  className="su__customized__settings-inner a11y-btn p-0"
                  onClick={() => {
                    toggleEditMode(!editPageLayout);
                    setIsComponentVisible(!isComponentVisible);
                  }}
                >
                  <svg
                    className="su__gear-min-width  su___show-more-summary"
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                  >
                    <defs>
                      <clipPath id="clip-Customize">
                        <rect width="24" height="24" />
                      </clipPath>
                    </defs>
                    <g id="Customize" clipPath="url(#clip-Customize)">
                      <g id="edit_note_black_24dp_1_" data-name="edit_note_black_24dp (1)">
                        <rect
                          id="Rectangle_5709"
                          data-name="Rectangle 5709"
                          width="24"
                          height="24"
                          fill="none"
                        />
                        <path
                          id="Path_10983"
                          data-name="Path 10983"
                          d="M14,11a1,1,0,0,1-1,1H4a1,1,0,0,1,0-2h9A1,1,0,0,1,14,11ZM3,7A1,1,0,0,0,4,8h9a1,1,0,0,0,0-2H4A1,1,0,0,0,3,7Zm7,8a1,1,0,0,0-1-1H4a1,1,0,0,0,0,2H9A1,1,0,0,0,10,15Zm8.01-2.13.71-.71a1,1,0,0,1,1.41,0l.71.71a1,1,0,0,1,0,1.41l-.71.71Zm-.71.71-5.16,5.16a.483.483,0,0,0-.14.35V20.5a.5.5,0,0,0,.5.5h1.41a.469.469,0,0,0,.35-.15l5.16-5.16Z"
                          fill={IconColors.settings_svg_fill}
                        />
                      </g>
                    </g>
                  </svg>
                  <p
                    lang={variables.searchCallVariables.langAttr}
                    className="su__font-13 su__pl-2 su___show-more-summary"
                  >
                    {t(StaticStrings.CUSTOMIZED)}
                  </p>
                </button>
              </Tooltip>
            )}
            {!isDeviceMobile && (
              <Tooltip
                text={t(StaticStrings.search_tips)}
                position="left"
                className="position-relative"
              >
                <button
                  type="button"
                  data-trigger-a11y={A11Y_IDS.childTrap}
                  data-name-a11y="search_tips"
                  lang={variables.searchCallVariables.langAttr}
                  role={a11y.ROLES.BTN}
                  aria-label={t(StaticStrings.SEARCHTIPS)}
                  tabIndex={tabIndexes.tabIndex_0}
                  className="su__customized__settings-inner a11y-btn p-0"
                  onClick={() => {
                    setComponentVisible(!showSearchTips);
                    setIsComponentVisible(!isComponentVisible);
                  }}
                >
                  <svg
                    className="su__gear-min-width  su___show-more-summary"
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                  >
                    <defs>
                      <clipPath id="clip-Tips1">
                        <rect width="24" height="24" />
                      </clipPath>
                    </defs>
                    <g id="Tips1" clipPath="url(#clip-Tips1)">
                      <g id="tips_and_updates_black_24dp" transform="translate(1.816 1.909)">
                        <rect
                          id="Rectangle_5707"
                          data-name="Rectangle 5707"
                          width="20"
                          height="20"
                          transform="translate(0.183 0.183)"
                          fill="none"
                        />
                        <path
                          id="Path_10981"
                          data-name="Path 10981"
                          d="M6.167,16.971H9.561a1.7,1.7,0,1,1-3.394,0Zm-1.7-.849h6.788v-1.7H4.47Zm9.758-8.061a6.394,6.394,0,0,1-3.2,5.516H4.7a6.362,6.362,0,1,1,9.529-5.516Zm4.132-1.807L17.2,6.788l1.163.535L18.9,8.486l.535-1.163,1.163-.535L19.43,6.254,18.9,5.091ZM16.35,5.091l.8-1.748,1.748-.8-1.748-.8L16.35,0l-.8,1.748-1.748.8,1.748.8Z"
                          transform="translate(-0.227)"
                          fill={IconColors.search_tip_svg_fill}
                        />
                      </g>
                    </g>
                  </svg>
                  <p
                    lang={variables.searchCallVariables.langAttr}
                    className={`su__font-13 su__pl-2  su___show-more-summary su__text-left `}
                  >
                    {t(StaticStrings.search_tips)}
                  </p>
                </button>
              </Tooltip>
            )}
          </div>
        )}
        {showSearchTips && (
          <React.Fragment>
            <div className=" su__flex-hcenter su__position-fixed su__px-xs-2 su__trbl-0 su__zindex-3">
              <div
                id={A11Y_IDS.childTrap}
                className={`su__overflow-hide searchTips-sidenav su__with-overlay su__Search-tips  su__position-fixed su__zindex-3 su__bg-white div-right md-whiteframe-4dp ${
                  showSearchTips ? 'su__searchTips-show su__animate-fadown' : ''
                }`}
              >
                <div
                  className={`su__searchTip-header su__p-3 su__scTips-header-color su__rtltext-right su__position-relative ${
                    variables.searchClientType === SC_IDS.KHOROS &&
                    variables.searchCallVariables.language === 'ar'
                      ? 'su__kh_scTips_head'
                      : ''
                  }`}
                >
                  <h2
                    lang={variables.searchCallVariables.langAttr}
                    className="su__m-0 su__font-18 su__searchTips_font su__f-normal"
                  >
                    {' '}
                    {t(StaticStrings.search_tips)}{' '}
                  </h2>
                </div>
                <div className="su__searchTip-content su__bg-white">
                  <ul
                    className={`su__searchTip-list su__m-0 su__pr-3 su__rtlpr-5  su__pl-5 su__rtlpl-3 su__font-12 ${
                      variables.searchCallVariables.language === 'ar' ? 'sc__tips_arabicBox' : ''
                    } ${variables.searchClientType === SC_IDS.KHOROS ? 'su__khTips' : ''}`}
                  >
                    <li lang={variables.searchCallVariables.langAttr}>
                      {t(
                        StaticStrings.Start_your_search_with_a_few_essential_terms_directly_related_to_your_query_or_issue
                      )}
                    </li>
                    <li lang={variables.searchCallVariables.langAttr}>
                      {t(
                        StaticStrings.Enhance_your_search_gradually_by_adding_specific_keywords_to_narrow_the_results
                      )}
                    </li>
                    <li lang={variables.searchCallVariables.langAttr}>
                      {t(StaticStrings.Omit_punctuation_marks_from_your_search)}
                    </li>
                    <li lang={variables.searchCallVariables.langAttr}>
                      {t(
                        StaticStrings.Remember_that_the_search_function_does_not_differentiate_between_uppercase_and_lowercase_letters
                      )}
                    </li>
                    <li lang={variables.searchCallVariables.langAttr}>
                      {t(StaticStrings.Exclude_vague_filler_words)}
                    </li>

                    <li lang={variables.searchCallVariables.langAttr}>
                      {t(StaticStrings.If_your_initial_search_does_not_yield_the_desired_results)}
                    </li>
                  </ul>
                </div>
                <div className="su__scTips-closeBtn-section su__mb-14 su__mt-20 su__position-absolute su__w-100 su__bottom-0">
                  <button
                    type="button"
                    lang={variables.searchCallVariables.langAttr}
                    role={a11y.ROLES.BTN}
                    aria-label={t(StaticStrings.close_popup)}
                    tabIndex={tabIndexes.tabIndex_0}
                    onClick={() => setComponentVisible(!showSearchTips)}
                    className={`su__scTips-closeBtn su__cursor a11y-btn su__border-r-4 su__color-white su__fontsize-14 su__f-normal `}
                  >
                    {t(StaticStrings.Close)}
                  </button>
                </div>
              </div>
              <div
                className="su__bg-overlay su__zindex-1"
                onClick={() => setComponentVisible(!showSearchTips)}
              ></div>
            </div>
          </React.Fragment>
        )}
        {editPageLayout ? (
          <FacetPreference
            isAllContentShow={isAllContentShow}
            data={facetsData}
            toggleEditMode={toggleEditMode}
            smartFacetsAggregation={smartFacetsAggregation}
            mergedFacet={mergedFacet}
            editPageLayout={editPageLayout}
          />
        ) : null}
      </Fragment>
    );
  } catch (e) {
    console.log('Error in facet-preference component', e);
    return <div></div>;
  }
};

export default Settings;
