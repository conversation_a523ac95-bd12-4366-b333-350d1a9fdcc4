import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import Tooltip from 'components/feature-components/tooltip/index.jsx';
import variables from '../../../redux/variables';
import StaticStrings from '../../../StaticStrings';
import { a11y } from '../../../constants/a11y';
import Dropdown from '../dropdown/index';

const setLocalStorage = (languages) => {
  if (!languages) {
    localStorage.setItem('language', 'en');
  } else if (languages && languages?.config) {
    let storedLanguage = localStorage.getItem('language');
    if (!storedLanguage) {
      localStorage.setItem('language', languages.config.defaultLanguage.code || 'en');
      variables.searchCallVariables.language = localStorage.getItem('language');
    } else if (!languages.config.selectedLanguages.some((lang) => lang.code === storedLanguage)) {
      localStorage.setItem('language', languages.config.defaultLanguage.code);
      variables.searchCallVariables.language = languages.config.defaultLanguage.code;
    }
  } else {
    localStorage.setItem('language', 'en');
  }
};

const Language = () => {
  try {
    const { i18n } = useTranslation();
    const { t } = useTranslation();
    let languages = variables.languages;
    setLocalStorage(languages);
    const [showOvarlay, setShowoverlay] = useState(false);
    const getlanges = localStorage.getItem('language');
    const geti18langes = localStorage.getItem('i18nextLng') || 'en';
    if (getlanges !== geti18langes) {
      i18n.changeLanguage(getlanges);
    }
    let changeLanguage = (e) => {
      let value = e?.target?.getAttribute?.('value')
        ? e?.target?.getAttribute?.('value')
        : e.target.value;
      i18n.changeLanguage(value);
      variables.searchCallVariables.langAttr = value;
      localStorage.setItem('language', value);
      variables.searchCallVariables.language = localStorage.getItem('language');
      if (languages[value] && languages[value].type === 'RTL') {
        document.getElementsByTagName('body')[0]?.classList.add('su__rtl');
        document.getElementsByTagName('body')[0]?.classList.remove('su__ltr');
      } else {
        document.getElementsByTagName('body')[0]?.classList.remove('su__rtl');
        document.getElementsByTagName('body')[0]?.classList.add('su__ltr');
      }
    };
    useEffect(() => {
      if (languages) {
        let e = {
          target: {
            value: localStorage.getItem('language')
          }
        };
        changeLanguage(e);
      }
    }, [localStorage.getItem('language')]);
    return (
      <div className="su__w-auto su__Language-boxs su__flex-vcenter su__mt-14px-mobile position-relative su__sc-loading">
        <Tooltip
          text={t(StaticStrings.LANGUAGE)}
          position="bottom"
          className="position-relative "
          role={a11y.ROLES.GROUP}
          aria-labelledby="su__LanguageLabel"
          tipClassName="su__position-absolute  su__zindex-1 su__bottom-unset su__min-w-80"
          tipMinWidth="su__min-w-80"
        >
          <Dropdown
            dropDownTitle={getlanges}
            dropdownItems={languages?.config.selectedLanguages}
            handleChange={changeLanguage}
            language={true}
            value={getlanges}
            setShowoverlay={setShowoverlay}
          />
        </Tooltip>
        {showOvarlay && <div className="su__overlay-transparent su__zindex-1"></div>}
      </div>
    );
  } catch (e) {
    console.log('Error in language component', e);
    return <div></div>;
  }
};

export default Language;
