/* global jest, it, expect, describe, beforeEach, afterEach */
import React from 'react';
import { mount } from 'enzyme';
import Language from './';
import Tooltip from 'components/feature-components/tooltip/index.jsx';
import { useTranslation } from 'react-i18next';
import Dropdown from '../dropdown/index';
import { useBrowserStorageMock } from '__mocks__/storage-mock';
import variables from '__mocks__/variables';

// Mock the uuid function
jest.mock('uuid', () => ({
  v4: jest.fn(() => 'unique-id')
}));

jest.mock('react-i18next', () => ({
  useTranslation: jest.fn()
}));

jest.mock('../../../redux/variables', () => require('__mocks__/variables'));

describe('Language Component', () => {
  let wrapper, original;
  const mockChangeLanguage = jest.fn();
  const [startLoaclStorageMock, clearLocalStorageMock] = useBrowserStorageMock('local');

  beforeEach(() => {
    useTranslation.mockImplementation(() => ({
      i18n: {
        changeLanguage: mockChangeLanguage
      },
      t: (key) => key
    }));
    startLoaclStorageMock();
    localStorage.setItem('language', 'en');
    localStorage.setItem('i18nextLng', 'en');
    original = variables.languages;
    wrapper = mount(<Language />);
  });

  afterEach(() => {
    variables.languages = original;
    clearLocalStorageMock();
    jest.clearAllMocks();
  });

  it('should render correctly', () => {
    expect(wrapper).toBeTruthy();
  });

  it('should set default language to "en" if languages is undefined', () => {
    localStorage.removeItem('language');
    mount(<Language />);
    expect(localStorage.getItem('language')).toBe('en');
  });

  it('should change language on select change', () => {
    wrapper
      .find(Dropdown)
      .props()
      .handleChange({ target: { value: 'es' } });
    expect(mockChangeLanguage).toHaveBeenCalledWith('es');
    expect(localStorage.getItem('language')).toBe('es');
  });

  it('should render Tooltip component', () => {
    expect(wrapper.find(Tooltip).exists()).toBe(true);
  });

  it('should call changeLanguage on useEffect when language changes', () => {
    localStorage.setItem('language', 'es');
    mount(<Language />);
    expect(mockChangeLanguage).toHaveBeenCalledWith('es');
  });

  it('should remove RTL class from body for non-RTL languages', () => {
    wrapper
      .find(Dropdown)
      .props()
      .handleChange({ target: { value: 'en' } });
    expect(document.body.classList.contains('su__rtl')).toBe(false);
  });

  // it('should add RTL class from body for non-RTL languages', () => {
  //   wrapper
  //     .find(Dropdown)
  //     .props()
  //     .handleChange({ target: { value: 'ar' } });
  //   wrapper.update();
  //   expect(document.body.classList.contains('su__rtl')).toBe(true);
  // });

  it('should set language in localstorage as english if nothing is found in variables or if the config is empty', () => {
    variables.languages = null;
    wrapper = mount(<Language />);
    wrapper.update();
    expect(localStorage.setItem).toHaveBeenCalledWith('language', 'en');
    variables.languages = { config: null };
    wrapper = mount(<Language />);
    wrapper.update();
    expect(localStorage.setItem).toHaveBeenCalledWith('language', 'en');
  });

  it('should set default language from languages in variables in case an alien (means not in cofig) language is present in storage', () => {
    localStorage.setItem('language', 'alien');
    wrapper = mount(<Language />);
    wrapper.update();
    expect(localStorage.setItem).toHaveBeenCalledWith(
      'language',
      variables.languages.config.defaultLanguage.code
    );
  });

  it('should handle errors gracefully', () => {
    jest.spyOn(console, 'log').mockImplementation(() => {});
    useTranslation.mockImplementation(() => {
      throw new Error('Test error');
    });

    const wrapper = mount(<Language />);
    wrapper.update();
    expect(wrapper.html()).toBe('<div></div>');
    expect(console.log).toHaveBeenCalledWith('Error in language component', expect.any(Error));
  });
});
