/* global jest, it, expect, describe, beforeEach */
import React from 'react';
import { shallow, mount } from 'enzyme';
import SaveBookmarks from './index';

// Mocking the i18n translation hook
jest.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key) => key
  })
}));

// Mocking the variables module
jest.mock('../../../redux/variables', () => ({
  searchCallVariables: {
    uid: 'test_uid',
    langAttr: 'en'
  }
}));

describe('SaveBookmarks Component', () => {
  let wrapper;
  const mockProps = {
    list: [],
    callBack: jest.fn()
  };

  beforeEach(() => {
    wrapper = shallow(<SaveBookmarks {...mockProps} />);
  });

  it('should render without crashing', () => {
    expect(wrapper.exists()).toBe(true);
  });

  it('should open the modal when the button is clicked', () => {
    wrapper.find('button.su__mob-search-iner').simulate('click');
    expect(wrapper.find('.su__modal-inner').exists()).toBe(true);
  });

  it('should close the modal when the close button is clicked', () => {
    wrapper.find('button.su__mob-search-iner').simulate('click');
    wrapper.find('button.su__close_bookmark').simulate('click');
    expect(wrapper.find('.su__modal-inner').exists()).toBe(false);
  });

  it('should call saveBookmark on button click', () => {
    wrapper.find('button.su__mob-search-iner').simulate('click');
    const mockEvent = { preventDefault: jest.fn() };
    wrapper.find('form').simulate('submit', mockEvent);
    expect(mockEvent.preventDefault).toHaveBeenCalled();
    expect(mockProps.callBack).toHaveBeenCalled();
  });

  it('should set modal open true', () => {
    wrapper.find('[data-test-id="su__bookmark_btn_test"]').simulate('click');
    wrapper.update();
    expect(wrapper.find('[data-test-id="su__test_modal_open"]').exists()).toBe(true);
  });

  it('should set modalopen false on escape key press', () => {
    const wrapper = mount(<SaveBookmarks {...mockProps} />);
    // Initially opening modal
    wrapper.find('[data-test-id="su__bookmark_btn_test"]').simulate('click');
    wrapper.update();
    expect(wrapper.find('[data-test-id="su__test_modal_open"]').exists()).toBe(true);
    const escapeEvent = new KeyboardEvent('keydown', { key: 'Escape' });
    document.dispatchEvent(escapeEvent);
    wrapper.update();
    expect(wrapper.find('[data-test-id="su__test_modal_open"]').exists()).toBe(false);
  });
});
