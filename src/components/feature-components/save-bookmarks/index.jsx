import React, { useState, Fragment, useEffect } from 'react';
import Icons from '../../../assets/svg-icon/svg';
import variables from '../../../redux/variables';
import { useTranslation } from 'react-i18next';
import Tooltip from 'components/feature-components/tooltip/index.jsx';
import UnderConstruction from 'components/feature-components/under-consruction/index.jsx';
import IconColors from '../../../IconColors';
import StaticStrings from '../../../StaticStrings';
import { a11y, useFocusTrap, tabIndexes, A11Y_IDS } from '../../../constants/a11y';
import { getWildcardToggle } from 'function-library/commonFunctions';
import { useDevice } from 'function-library/hooks';
import { EVENT_NAMES, SC_IDS } from 'constants/constants';
import utilityMethods from 'redux/utilities/utility-methods';
const SaveBookmarks = (props) => {
  const { t } = useTranslation();
  const setListProp = typeof props.setList !== 'function' ? () => {} : props.setList;
  try {
    /**
     *  Change Languages state
     */
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [bookmarkListing, setList] = useState(
      JSON.parse(
        window.localStorage.getItem('bookmark_searches_' + variables.searchCallVariables.uid) ||
          '[]'
      )
    );
    const [name, setBookmarkName] = useState('');
    const [DuplicateBookmark, setDuplicateBookmark] = useState('');
    const [focusTrap] = useFocusTrap();
    const { isDeviceMobile } = useDevice();
    const topActionsView = utilityMethods.isTopActionsView();
    const bookmarkClass =
      variables.isConsoleTypeSC && isDeviceMobile
        ? 'su__zendesk_bookmark_wd_ht'
        : topActionsView
        ? 'su__save-bookmarks'
        : 'su__save-bookmarks-zendesk';

    useEffect(() => {
      focusTrap(isModalOpen);
    }, [isModalOpen]);

    const handleHideDropdown = (e) => {
      e.key === EVENT_NAMES.ESCAPE && setIsModalOpen(false);
    };
    useEffect(() => {
      if (props.list.length) {
        setList(props.list);
      }
      document.addEventListener('keydown', handleHideDropdown);
      return () => {
        document.removeEventListener('keydown', handleHideDropdown);
      };
    }, [props.list]);

    let bookmarks = (name) => {
      setBookmarkName(name.target.value.trim());
    };

    /**
     * Save Bookmark in local Storage
     */
    const saveBookmark = (e) => {
      variables.searchCallVariables.pagingAggregation = [];
      e.preventDefault();
      let list = JSON.parse(
        localStorage.getItem('bookmark_searches_' + variables.searchCallVariables.uid) || '[]'
      );
      let isBookmarkDuplicate = false;
      list.length &&
        list.forEach((listItem) => {
          if (listItem?.title.trim().toLowerCase() === name?.trim().toLowerCase())
            isBookmarkDuplicate = true;
        });
      if (isBookmarkDuplicate) {
        setDuplicateBookmark(StaticStrings.BOOKMARKALREADYEXISTS);
      } else {
        list.push({
          title: name,
          href: variables.searchCallVariables,
          toggle: getWildcardToggle(),
          bookmark: false
        });
        localStorage.setItem(
          'bookmark_searches_' + variables.searchCallVariables.uid,
          JSON.stringify(list)
        );
        props.callBack(list);
        setBookmarkName('');
        setIsModalOpen(false);
        setList(list);
        setListProp(list);
        setDuplicateBookmark('');
      }
    };

    const closeSaveBookmark = () => {
      setIsModalOpen(false);
      setBookmarkName('');
    };

    return (
      <Fragment>
        <div
          className={` ${
            variables.isConsoleTypeSC ? 'su__d-none' : ''
          } su__d-md-none su__mob-search su__mobile-child-block su__text-center su__font-9 su__font-bold su__kh-btn-fw-n su__word-normal-kh`}
        >
          <button
            type="button"
            lang={variables.searchCallVariables.langAttr}
            aria-label={t(StaticStrings.SAVEBOOKMARK)}
            role={a11y.ROLES.BTN}
            tabIndex={tabIndexes.tabIndex_0}
            className={`su__mob-search-iner a11y-btn p-0  su__color-black ${
              isModalOpen ? 'su__mob-active' : ''
            } ${!isDeviceMobile ? 'su__sc-loading' : 'su__loading-Dnone'}`}
            onClick={() => {
              setIsModalOpen(true);
              setDuplicateBookmark('');
            }}
          >
            <div className="su__mob-icon">
              <Icons
                className="su__bookmark-icon "
                IconName="Bookmark"
                width="24"
                height="24"
                color={IconColors.SaveBookmarksIcon}
                IconClass="su__active-path"
              />
            </div>
            <div
              lang={variables.searchCallVariables.langAttr}
              className="su__mob-txt su__line-height-n su__active-text su__font-12px su__w-80px"
            >
              {t(StaticStrings.SAVEBOOKMARK)}
            </div>
          </button>
        </div>
        <div
          className={`${
            variables.searchClientType == SC_IDS.ZENDESK_SUPPORT_CONSOLE
              ? ''
              : 'su__d-xs-none su__mobile-child-block '
          }  su__d-md-block `}
        >
          <div className={`${!isDeviceMobile ? 'su__mr_10px' : ''} su__kh-btn-lh-0`}>
            <Tooltip
              text={t(StaticStrings.SAVEBOOKMARK)}
              position="bottom"
              className="position-relative"
            >
              <button
                type="button"
                data-trigger-a11y={A11Y_IDS.trap}
                data-name-a11y="save_bookmark"
                lang={variables.searchCallVariables.langAttr}
                tabIndex={tabIndexes.tabIndex_0}
                role={a11y.ROLES.BTN}
                aria-label={t(StaticStrings.SAVEBOOKMARK)}
                data-test-id="su__bookmark_btn_test"
                className={`${bookmarkClass} su__flex-hcenter a11y-btn p-0 su__position-relative su__sc-loading`}
                onClick={() => {
                  setIsModalOpen(true);
                  setDuplicateBookmark('');
                }}
              >
                <span className="su__bookmark-list  su__cursor su__outline-none  su__radius-50 bookmark-list-dots su__loading-view">
                  {topActionsView && (
                    <Icons
                      tabIndex={tabIndexes.tabIndex_0}
                      className={`su__bokmark-icon su__position-absolute su__bookmark-svg-align ${
                        variables.isConsoleTypeSC && isDeviceMobile
                          ? 'su__bookmark-svg-align-console'
                          : null
                      }`}
                      IconName="Bookmark"
                      width={`${variables.isConsoleTypeSC && isDeviceMobile ? '17' : '24'}`}
                      height={`${variables.isConsoleTypeSC && isDeviceMobile ? '17' : '24'}`}
                      color={IconColors.ListBookmarksInModalIcon}
                    />
                  )}
                  {!topActionsView && (
                    <Icons
                      tabIndex={tabIndexes.tabIndex_0}
                      className={`su__bokmark-icon su__position-absolute su__bookmark-svg-align ${
                        variables.isConsoleTypeSC && isDeviceMobile
                          ? 'su__bookmark-svg-align-console'
                          : null
                      }`}
                      IconName="Bookmark"
                      width={`${variables.isConsoleTypeSC && isDeviceMobile ? '17' : '24'}`}
                      height={`${variables.isConsoleTypeSC && isDeviceMobile ? '17' : '24'}`}
                      color={IconColors.save_and_list_bookmark_Icon}
                    />
                  )}
                </span>
              </button>
            </Tooltip>
          </div>
        </div>
        {isModalOpen && (
          <Fragment>
            <div
              className="su__flex-hcenter su__position-fixed su__trbl-0 su__zindex-3 su__px-sm-1"
              id={A11Y_IDS.trap}
              role={a11y.ROLES.DIALOG}
              aria-labelledby="dialog1_label"
              aria-modal="true"
              data-test-id="su__test_modal_open"
            >
              <div className="su__modal-inner su__radius su__animate-fadown su__zindex-1 su__shadow-lg su__bg-white su__radius-1">
                <div className="su__bookmark-popup-title su__pr_14px su__pl_14px su__pt_14px su__flex-vcenter">
                  <h2
                    id={'dialog1_label'}
                    lang={variables.searchCallVariables.langAttr}
                    className="su__font-17 su__text-white su__f-medium su__flex-1 su__position-relative su__my-0 su__font_save_bookmark su__text-blue "
                  >
                    {t(StaticStrings.SAVEASBOOKMARK)}
                  </h2>
                  <button
                    type="button"
                    lang={variables.searchCallVariables.langAttr}
                    className="a11y-btn p-0 su__close_bookmark"
                    aria-label={t(StaticStrings.close_popup)}
                    role={a11y.ROLES.BTN}
                    tabIndex={tabIndexes.tabIndex_0}
                    data-test-id="su__closeSaveBookmark_test"
                    onClick={() => closeSaveBookmark()}
                  >
                    {' '}
                    <Icons
                      className="su__close-icon su__cursor"
                      IconName="Close"
                      width="12"
                      height="12"
                      color={IconColors.SaveBookmarksCrossIcon}
                    />
                  </button>
                </div>
                <form onSubmit={saveBookmark} className="su__m-0">
                  <div className="su__bookmark-inner su__pr_14px su__pl_14px su__pb_14px su__pt-20px">
                    <div className="su__w-100 su__word-normal-kh su__kh-input-br-8 su__kh-placeholder-clr su__save_bookmark_unq">
                      <div className="su__w-100 su__text-center">
                        {/* <Icons className="su__star-icon" IconName="Savebookmark" width="71" height="75" color={IconColors.SaveBookmarksIcon}/> */}
                      </div>
                      <label
                        lang={variables.searchCallVariables.langAttr}
                        className={`su__d-block su__mb-1 su__f-medium su__color_black ${
                          isDeviceMobile ? 'su__font-12' : 'su__font-13'
                        } `}
                      >
                        {t(StaticStrings.TITLEFORTHISSEARCH)}
                      </label>
                      <input
                        lang={variables.searchCallVariables.langAttr}
                        id="Save-bookmark"
                        className={`su__bookmark-input su__form-control su__mb-3 su__mw-100  su__popup_input_text su__popup_placeholder ${
                          name.length ? 'su__form_styles_enable' : 'su__form_styles_disable'
                        }`}
                        type="text"
                        onChange={bookmarks}
                        aria-label={t(StaticStrings.TITLEFORTHISSEARCH)}
                        placeholder={t(StaticStrings.TYPEHERE) + '...'}
                      />
                      {DuplicateBookmark ? (
                        <h5 role={a11y.ROLES.ALERT} className="su__duplicate-bookmark">
                          {DuplicateBookmark}
                        </h5>
                      ) : null}
                      {bookmarkListing.length > 19 ? (
                        <h5 role={a11y.ROLES.ALERT} className="su__duplicate-bookmark">
                          You can bookmark upto 20 searches
                        </h5>
                      ) : null}
                      <button
                        type="submit"
                        lang={variables.searchCallVariables.langAttr}
                        className={`su__btn su__bg-blue-grd su__w-100 su__radius su__bookmark-save su__font-14  su__border-none ${
                          name.length ? 'su__cursor_pointer' : 'su__opacity_pointThree'
                        } ${
                          !name.length ||
                          (JSON.parse(
                            localStorage.getItem(
                              'bookmark_searches_' + variables.searchCallVariables.uid
                            )
                          )?.length || bookmarkListing.length) > 19
                            ? 'su__opacity_pointThree'
                            : 'su__opacity_one'
                        }`}
                        onClick={saveBookmark}
                        disabled={
                          !name.length ||
                          (JSON.parse(
                            localStorage.getItem(
                              'bookmark_searches_' + variables.searchCallVariables.uid
                            )
                          )?.length || bookmarkListing.length) > 19
                        }
                      >
                        {t(StaticStrings.su__save)}
                      </button>
                    </div>
                  </div>
                </form>
              </div>
              <div className="su__overlay su__zindex-1" onClick={() => closeSaveBookmark()} />
            </div>
          </Fragment>
        )}
      </Fragment>
    );
  } catch (e) {
    console.log('Error in Save bookmark Component', e);
    const [isModalOpen, setIsModalOpen] = useState(false);
    const showPopUp = () => {
      setIsModalOpen(true);
      setTimeout(() => {
        setIsModalOpen(false);
      }, 1500);
    };
    return (
      <Fragment>
        <div className="su__d-md-none su__d-xs-block su__col su__mob-search su__text-center su__font-9 su__font-bold">
          <div
            className={`su__mob-search-iner ${isModalOpen ? 'su__mob-active' : ''}`}
            onClick={() => showPopUp()}
          >
            <div className="su__mob-icon">
              <Icons
                className="su__bokmark-icon"
                IconName="Bookmark"
                width="24"
                height="24"
                color={IconColors.AdvanceSearchIconFill}
                IconClass="su__active-path"
              />
            </div>
            <div
              lang={variables.searchCallVariables.langAttr}
              className="su__mob-txt su__line-height-n su__active-text"
            >
              {t(StaticStrings.add_bookmark)}
            </div>
          </div>
        </div>
        <div className="su__d-md-block su__d-xs-none">
          <div className="su__save-bookmarks su__flex-hcenter" onClick={() => showPopUp()}>
            <span className="su__bookmark-list su__mt-1 su__ml-2 su__rtlmr-2 su__rtlml-0 su__cursor su__outline-none su__radius-50 bookmark-list-dots su__loading-view su__sc-loading">
              <Tooltip
                text={t(StaticStrings.add_bookmark)}
                position="top"
                className="position-relative"
              >
                <Icons
                  className="su__bookmark-icon "
                  IconName="Bookmark"
                  width="24"
                  height="24"
                  color={IconColors.AdvanceSearchIconFill}
                />
              </Tooltip>
            </span>
          </div>
        </div>
        {isModalOpen && <UnderConstruction component="Save Bookmarks" />}
      </Fragment>
    );
  }
};
export default SaveBookmarks;
