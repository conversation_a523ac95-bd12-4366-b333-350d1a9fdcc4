/* global gza */
import React, { useState, useEffect, useRef } from 'react';
import Href from 'components/section-components/href/index.jsx';
import Title from 'components/section-components/title/index.jsx';
import Icons from '../../../assets/svg-icon/svg';
import variables from '../../../redux/variables';
import { useTranslation } from 'react-i18next';
import IconColors from '../../../IconColors';
import StaticStrings from 'StaticStrings';
import { a11y, tabIndexes } from '../../../constants/a11y';
import { v4 as uuid } from 'uuid';
const FeaturedSnippetImages = ({ featuredSnippetResult, linkOpened, isDeviceMobile }) => {
  try {
    /**
     *  Change Languages state
     */
    const { t } = useTranslation();
    const [featuredSnippetGraphResponseRecorded, setFeaturedSnippetGraphFeedback] = useState(false);
    const [timeoutResponse, setTimeoutResponse] = useState(true);
    const [updateComponent, setUpdateComponent] = useState(true);
    const feedbackshow = useRef(null);
    const scrollSnippetContainerRef = useRef(null);
    const [isscrollsnippetLeft, setIsscrollsnippetLeft] = useState(false);
    const [isscrollsnippetRight, setIsscrollsnippetRight] = useState(true);

    useEffect(() => {
      setFeaturedSnippetGraphFeedback(false);
      setTimeoutResponse(true);
    }, [featuredSnippetResult]);
    const sendFeaturedSnippetGraphFeedback = (feedback, result, index, item) => {
      gza('featuredSnippet', {
        searchString: variables.searchCallVariables.searchString,
        url: (item && item.href) || result.href,
        t:
          (item && item.title[0]?.substring(0, 300)) ||
          result.highlight.TitleToDisplayString[0]?.substring(0, 300) ||
          result.href,
        uid: variables.searchCallVariables.uid,
        feedback: feedback
      });
      if (item) item.isFeedbackHidden = true;
      setFeaturedSnippetGraphFeedback(true);

      if (index >= 0) {
        document.getElementById(index + '_hiddenSnippet').style.display = 'block';
        setTimeout(function () {
          document.getElementById(index + '_hiddenSnippet').style.display = 'none';
          feedbackshow.current = null;
        }, 5000);
      }
      if (index < 0 || index == undefined) {
        setTimeout(function () {
          setTimeoutResponse(false);
        }, 5000);
      }
      feedbackshow.current = index;
      setUpdateComponent(!updateComponent);
    };
    useEffect(() => {
      const scrollContainer = scrollSnippetContainerRef.current;

      const handleScroll = () => {
        const maxScrollLeft = scrollContainer.scrollWidth - scrollContainer.clientWidth;
        if (document.body.classList.contains('su__rtl')) {
          setIsscrollsnippetLeft(scrollContainer.scrollLeft > -(maxScrollLeft - 1));
          setIsscrollsnippetRight(scrollContainer.scrollLeft < 0);
        } else {
          setIsscrollsnippetLeft(scrollContainer.scrollLeft > 0);
          setIsscrollsnippetRight(scrollContainer.scrollLeft <= maxScrollLeft - 1);
        }
      };

      if (scrollContainer) {
        scrollContainer.addEventListener('scroll', handleScroll);
        handleScroll(); // Initial check
      }

      return () => {
        if (scrollContainer) {
          scrollContainer.removeEventListener('scroll', handleScroll);
        }
      };
    }, [
      scrollSnippetContainerRef.current,
      featuredSnippetResult.multiMedia,
      variables.searchCallVariables.language
    ]);

    const scrollLeft = () => {
      const box = scrollSnippetContainerRef.current;
      box.scrollTo({
        left: box.scrollLeft - box.clientWidth / 2,
        behavior: 'smooth'
      });
    };

    const scrollRight = () => {
      const box = scrollSnippetContainerRef.current;
      box.scrollTo({
        left: box.scrollLeft + box.clientWidth / 2,
        behavior: 'smooth'
      });
    };
    return (
      <>
        {featuredSnippetResult.multiMedia && featuredSnippetResult.multiMedia.length > 1 && (
          <div className="su__fs-multimedia-main  su__slider-outer su__w-100 su__position-relative su__radius-1 ">
            {isscrollsnippetLeft && (
              <div className="su__z-index">
                <button
                  lang={variables.searchCallVariables.langAttr}
                  aria-label={t(StaticStrings.slide_left)}
                  className={`snippetArrows  su__slider-btn-left su__r-auto su__cursor_pointer ${
                    isDeviceMobile ? 'su__left-0' : 'su__left-m14 '
                  }`}
                  onClick={scrollLeft}
                >
                  {' '}
                </button>
                {isDeviceMobile && (
                  <div className="su__blur-effect-btn su__blur-effect-btn-left"> </div>
                )}
              </div>
            )}

            <div
              ref={scrollSnippetContainerRef}
              className="box-inner su-tabsSection su__text-nowrap su__d-flex su__fs-mediaInnerBox su__fs-media-box "
            >
              {featuredSnippetResult.multiMedia.slice(0, 4).map((item, index) => {
                return (
                  <div
                    className={`${
                      (item.thumbnail && item.video_url) || item.image_urls
                        ? 'su__fs-media su__mt-2 su__position-relative su__rtlmr-0 su__rtlml-3 su__rtlmb-3 '
                        : 'su__fs-media su__mt-2 su__position-relative su__rtlmr-0 su__rtlml-3 su__rtlmb-3'
                    }`}
                    key={uuid()}
                  >
                    <div
                      className={`su__featured-thumbnail su__position-relative su__fs-img-container su__overflow-hide ${
                        (item.thumbnail && item.video_url) || item.image_urls
                          ? ' '
                          : 'su_background_grey'
                      }`}
                    >
                      <a
                        href={item.href || item.video_url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="su__position-absolute su__h-100 su__w-100"
                        onMouseDown={(event) => {
                          if (event.button == 1)
                            linkOpened(featuredSnippetResult, index + 1, item.href, item.title[0]);
                        }}
                        onContextMenu={() =>
                          linkOpened(featuredSnippetResult, index + 1, item.href, item.title[0])
                        }
                        onClick={() =>
                          linkOpened(featuredSnippetResult, index + 1, item.href, item.title[0])
                        }
                      >
                        {item.video_url ? (
                          <>
                            {(item.href || item.video_url) && (
                              <Icons
                                IconName="Redirect_Link"
                                width="24"
                                height="24"
                                color={IconColors.FeaturedSnippetThumbsup_down}
                                widthInner="24"
                                heightInner="24"
                                // transform='translate(3,4)'
                                className="su__fs-redirect su__m-2"
                              />
                            )}
                            {item.thumbnail ? (
                              <Icons
                                IconName="Play_Button"
                                width="30"
                                height="30"
                                className="su__fs-play-btn  su__cursor_pointer"
                                transform="scale(.9) "
                              />
                            ) : (
                              <Icons
                                IconName="Play_Button"
                                width="30"
                                height="30"
                                className="su__align_icon_play_grey  su__fs-play-btn  su__cursor_pointer"
                                transform="scale(.9) "
                              />
                            )}
                          </>
                        ) : null}

                        {isDeviceMobile && (
                          <img
                            src={item.image_urls || item.thumbnail}
                            className="su__img-featured"
                            alt={item.alt_attributes}
                            role={a11y.ROLES.IMAGE}
                            aria-label={item.image_urls || item.thumbnail}
                          />
                        )}
                      </a>
                      {!isDeviceMobile && (
                        <img
                          src={item.image_urls || item.thumbnail}
                          className="su__img-featured"
                          alt={item.alt_attributes}
                          role={a11y.ROLES.IMAGE}
                          aria-label={item.image_urls || item.thumbnail}
                        />
                      )}
                      <div
                        className={`su__d-flex su__right-0 su__feedback-icon-above su__position-absolute su__feedback_icon_align  su__w-100 ${
                          item.isFeedbackHidden ? 'su__p-1' : 'su__p-2'
                        }`}
                      >
                        <div
                          className={`${
                            item.isFeedbackHidden
                              ? 'su__snippet-none'
                              : 'su__feedback-row su__d-flex su__pt-2  su__fs-feedback-btns'
                          } `}
                        >
                          <button
                            type="button"
                            lang={variables.searchCallVariables.langAttr}
                            role={a11y.ROLES.BTN}
                            aria-label={t(StaticStrings.THUMBSDOWNBUTTON)}
                            tabIndex={tabIndexes.tabIndex_0}
                            className="su__featured-feedback su__cursor su__loading-view su__bg-softblack su__featured-btn a11y-btn p-0 su__mr-2 su__rtlmr-0"
                            onClick={() =>
                              sendFeaturedSnippetGraphFeedback(
                                0,
                                featuredSnippetResult,
                                index,
                                item
                              )
                            }
                          >
                            <Icons
                              IconName="FtSnippet__ThumbsDown"
                              width="24"
                              height="24"
                              color={IconColors.FeaturedSnippetThumbsup_down}
                              widthInner="24"
                              heightInner="24"
                              transform="translate(3,4)"
                            />
                          </button>
                          <button
                            type="button"
                            lang={variables.searchCallVariables.langAttr}
                            role={a11y.ROLES.BTN}
                            aria-label={t(StaticStrings.THUMBSUPBUTTON)}
                            tabIndex={tabIndexes.tabIndex_0}
                            className="su__featured-feedback su__cursor su__loading-view su__bg-softblack su__featured-btn a11y-btn p-0 su__rtlmr-2 su__ml-0"
                            onClick={() =>
                              sendFeaturedSnippetGraphFeedback(
                                1,
                                featuredSnippetResult,
                                index,
                                item
                              )
                            }
                          >
                            <Icons
                              IconName="FtSnippet__ThumbsUp"
                              width="24"
                              height="24"
                              color={IconColors.FeaturedSnippetThumbsup_down}
                              transform="scale(.549) translate(8, 4)"
                            />
                          </button>
                        </div>
                        <div
                          id={`${index}_hiddenSnippet`}
                          className={`${
                            item.isFeedbackHidden && feedbackshow.current == index
                              ? 'su__feedback-thankyou su__font-14 su__mb-1 su__position-relative su__loading-view'
                              : 'su__snippet-none'
                          }`}
                        >
                          <div className="su__flex-vcenter su__font-14 su__f-regular">
                            <span
                              lang={variables.searchCallVariables.langAttr}
                              className="su__thankyou-text su__whitespace-no"
                            >
                              {t(StaticStrings.thanks_response)}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="su__href-txt su__text-decoration su__color-lgray su__font-14 su__line-height-22 su__f-regular su__align_url su__text-truncate su__p-2 su__royal-blue">
                      {' '}
                      <a
                        className="su__text-decoration su__royal-blue"
                        href={item.href || item.video_url}
                        target="_blank"
                        rel="noopener noreferrer"
                        onMouseDown={(event) => {
                          if (event.button == 1)
                            linkOpened(featuredSnippetResult, index + 1, item.href, item.title[0]);
                        }}
                        onContextMenu={() =>
                          linkOpened(featuredSnippetResult, index + 1, item.href, item.title[0])
                        }
                        onClick={() =>
                          linkOpened(featuredSnippetResult, index + 1, item.href, item.title[0])
                        }
                      >
                        {item.href || item.video_url}
                      </a>
                    </div>
                  </div>
                );
              })}
            </div>
            {isscrollsnippetRight && (
              <div className="su__z-index">
                <button
                  lang={variables.searchCallVariables.langAttr}
                  aria-label={t(StaticStrings.slide_right)}
                  className="snippetArrows  su__slider-btn-right su__cursor_pointer"
                  onClick={scrollRight}
                >
                  {' '}
                </button>
                {isDeviceMobile && (
                  <div className="su__blur-effect-btn su__blur-effect-btn-right"> </div>
                )}
              </div>
            )}
          </div>
        )}
        {featuredSnippetResult.multiMedia && featuredSnippetResult.multiMedia.length == 1 && (
          <div className="su__fs-media-content su__box-shadow-bl-6 su__border-light-gray su__bg-white  su__radius-1 su__mb-10 su__sm_mt-10 su__margin_top_15">
            <div
              className={`su__fs-inner-media su__d-md-flex su__overflow-hide su__fs-singleImage ${
                isDeviceMobile ? 'su__pt-2' : 'su__d-flex su__justify-content-between'
              } `}
            >
              <div
                className={`su__d-flex su__flex-column su__pr-3 su__word-break su__text-truncate ${
                  isDeviceMobile ? 'su__w-100' : 'su__flex-1'
                }`}
              >
                <Title item={featuredSnippetResult} linkOpened={linkOpened} index={1} />
                <Href item={featuredSnippetResult} />
              </div>
              <div
                className={`su__d-flex su__fsfeedback ${
                  isDeviceMobile ? ' su__flex-hcenter su__pt-3 ' : ''
                }`}
              >
                <div className="su__featured-thumbnail  su__position-relative">
                  <a
                    className="su__d-flex su__position-relative"
                    onClick={() => linkOpened(featuredSnippetResult, 1)}
                    target="_blank"
                    href={
                      featuredSnippetResult.multiMedia[0]?.href ||
                      featuredSnippetResult.multiMedia[0]?.video_url
                    }
                    rel="noopener noreferrer"
                  >
                    {featuredSnippetResult.multiMedia[0]?.thumbnail ? (
                      <Icons
                        IconName="Play_Button"
                        width="30"
                        height="30"
                        className="su__fs-play-btn"
                        transform="scale(.9) "
                      />
                    ) : null}
                    <img
                      src={
                        featuredSnippetResult.multiMedia[0]?.image_urls ||
                        featuredSnippetResult.multiMedia[0]?.thumbnail
                      }
                      className="su__img-featured su__img-singlefsmedia"
                      alt={featuredSnippetResult.multiMedia[0]?.alt_attributes}
                      role={a11y.ROLES.IMAGE}
                      aria-label={
                        featuredSnippetResult.multiMedia[0]?.image_urls ||
                        featuredSnippetResult.multiMedia[0]?.thumbnail
                      }
                    />
                  </a>
                  <div
                    className={`su__d-flex su__w-100 su__mt-2  su__position-absolute su__bottom-0  ${
                      isDeviceMobile ? '' : 'su__featureSnippet-response'
                    }
                     ${
                       !featuredSnippetGraphResponseRecorded
                         ? 'su__justify-content-end'
                         : ' su__justify-content-center'
                     }
                    `}
                  >
                    {!featuredSnippetGraphResponseRecorded ? (
                      <div className="su__d-flex su__p-1  su__pr-2 su__rtlpl-2 su__rtlpr-0 ">
                        <span
                          className="su__featured-feedback su__cursor su__loading-view su__bg-softblack su__mr-2 su__rtlmr-0"
                          onClick={() => sendFeaturedSnippetGraphFeedback(0, featuredSnippetResult)}
                        >
                          <Icons
                            IconName="FtSnippet__ThumbsDown"
                            width="24"
                            height="24"
                            color={IconColors.FeaturedSnippetThumbsup_down}
                            widthInner="24"
                            heightInner="24"
                            transform="translate(3,4)"
                          />
                        </span>
                        <span
                          className="su__featured-feedback su__cursor su__loading-view su__bg-softblack su__ml-0 su__rtlmr-2 "
                          onClick={() => sendFeaturedSnippetGraphFeedback(1, featuredSnippetResult)}
                        >
                          <Icons
                            IconName="FtSnippet__ThumbsUp"
                            width="24"
                            height="24"
                            color={IconColors.FeaturedSnippetThumbsup_down}
                            transform="scale(.549) translate(8, 4)"
                          />
                        </span>
                      </div>
                    ) : null}
                    {featuredSnippetGraphResponseRecorded && timeoutResponse ? (
                      <div
                        id="single_hiddenSnippet"
                        className="su__font-14 su__mb-2 su__position-relative su__loading-view"
                      >
                        <div className="su__flex-vcenter su__font-14 su__f-regular">
                          <span
                            lang={variables.searchCallVariables.langAttr}
                            className="su__thankyou-text su__whitespace-no su__w-100 su__p-1"
                          >
                            {t(StaticStrings.thanks_response)}
                          </span>
                        </div>
                      </div>
                    ) : null}
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </>
    );
  } catch (e) {
    console.log('Error in feature-snippet component', e);
    return <div></div>;
  }
};

export default FeaturedSnippetImages;
