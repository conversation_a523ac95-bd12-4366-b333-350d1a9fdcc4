/* global jest, it, expect, describe, beforeEach, afterEach */
import React from 'react';
import { mount } from 'enzyme';
import { useSelector, useDispatch } from 'react-redux';
import { useTranslation } from 'react-i18next';
import FeaturedSnippetImages from './';
import state from '__mocks__/state';
import variables from '__mocks__/variables';

jest.mock('react-i18next', () => ({
  useTranslation: jest.fn()
}));

jest.mock('react-redux', () => ({
  useSelector: jest.fn(),
  useDispatch: jest.fn()
}));

jest.mock('../../../redux/variables', () => require('__mocks__/variables'));

describe('FeaturedSnippetImages', () => {
  const defaultProps = {
    featuredSnippetResult: state.searchResult.featuredSnippetResult,
    linkOpened: jest.fn(),
    isDeviceMobile: false
  };

  const multiProps = {
    ...defaultProps,
    featuredSnippetResult: {
      multiMedia: [
        { ...defaultProps.featuredSnippetResult.multiMedia[0] },
        { ...defaultProps.featuredSnippetResult.multiMedia[0] },
        { ...defaultProps.featuredSnippetResult.multiMedia[0] }
      ]
    }
  };

  const mockProps = {
    featuredSnippetResult: null
  };

  beforeEach(() => {
    useTranslation.mockImplementation(() => ({ t: (key) => key }));
    window.gza = jest.fn();
    jest.useFakeTimers();
    document.getElementById = jest.fn(() => ({ style: { display: '' } }));
  });

  afterEach(() => {
    jest.clearAllTimers();
    jest.clearAllMocks();
  });

  it('renders without crashing', () => {
    const wrapper = mount(<FeaturedSnippetImages {...defaultProps} />);
    wrapper.update();
    expect(wrapper.exists()).toBe(true);
    expect(wrapper.find('.su__fs-media-content').length).toBe(1);
  });

  it('renders multiple media items', () => {
    const wrapper = mount(<FeaturedSnippetImages {...multiProps} />);
    wrapper.update();
    expect(wrapper.find('.su__fs-media').length).toBe(3);
  });

  it('should handle link clicks for single media item', () => {
    const wrapper = mount(<FeaturedSnippetImages {...defaultProps} />);
    wrapper.update();
    const links = wrapper.find('a');
    links.forEach((link) => {
      link.simulate('click');
      link.simulate('mouseDown', { button: 1 });
      link.simulate('contextMenu');
      expect(defaultProps.linkOpened).toHaveBeenCalledWith(defaultProps.featuredSnippetResult, 1);
    });
  });

  it('should handle link clicks for multiple media item', () => {
    const wrapper = mount(<FeaturedSnippetImages {...multiProps} />);
    wrapper.update();
    const links = wrapper.find('a');
    links.forEach((link) => {
      link.simulate('click');
      link.simulate('mouseDown', { button: 1 });
      link.simulate('contextMenu');
      expect(multiProps.linkOpened).toHaveBeenCalledWith(
        multiProps.featuredSnippetResult,
        1,
        multiProps.featuredSnippetResult.multiMedia[0].href,
        multiProps.featuredSnippetResult.multiMedia[0].title[0]
      );
    });
  });

  it('should track analytics for feedback for single mm', () => {
    const wrapper = mount(<FeaturedSnippetImages {...defaultProps} />);
    wrapper.update();
    const thumbsDown = wrapper.find('span.su__featured-feedback').at(0);
    const thumbsUp = wrapper.find('span.su__featured-feedback').at(1);
    const feedbackbtns = [
      { btn: thumbsDown, feedbackVal: 0 },
      { btn: thumbsUp, feedbackVal: 1 }
    ];
    feedbackbtns.forEach((item) => {
      item.btn.simulate('click', {});
      jest.advanceTimersByTime(5000);
      expect(window.gza).toHaveBeenCalled();
      expect(window.gza).toHaveBeenLastCalledWith('featuredSnippet', {
        feedback: item.feedbackVal,
        searchString: variables.searchCallVariables.searchString,
        t: state.searchResult.featuredSnippetResult.highlight.TitleToDisplayString[0],
        uid: variables.searchCallVariables.uid,
        url: state.searchResult.featuredSnippetResult.href
      });
    });
  });

  it('should track analytics for feedback for multi mm', () => {
    const wrapper = mount(<FeaturedSnippetImages {...multiProps} />);
    wrapper.update();
    const thumbsDown = wrapper.find('button.su__featured-feedback').at(0);
    const thumbsUp = wrapper.find('button.su__featured-feedback').at(1);
    const feedbackbtns = [
      { btn: thumbsDown, feedbackVal: 0 },
      { btn: thumbsUp, feedbackVal: 1 }
    ];
    feedbackbtns.forEach((item) => {
      item.btn.simulate('click', {});
      jest.advanceTimersByTime(5000);
      expect(window.gza).toHaveBeenCalled();
      expect(window.gza).toHaveBeenLastCalledWith('featuredSnippet', {
        feedback: item.feedbackVal,
        searchString: variables.searchCallVariables.searchString,
        t: state.searchResult.featuredSnippetResult.highlight.TitleToDisplayString[0],
        uid: variables.searchCallVariables.uid,
        url: state.searchResult.featuredSnippetResult.href
      });
    });
  });

  it('handles errors gracefully', () => {
    const consoleSpy = jest.spyOn(console, 'log').mockImplementation(() => {});
    useSelector.mockImplementation(() => {
      throw new Error('Test error');
    });
    useDispatch.mockImplementation(() => jest.fn());
    const wrapper = mount(<FeaturedSnippetImages {...mockProps} />);
    wrapper.update();
    expect(wrapper.text()).toBe('');
    expect(consoleSpy).toHaveBeenCalledWith(
      'Error in feature-snippet component',
      expect.any(Error)
    );
    consoleSpy.mockRestore();
  });
});
