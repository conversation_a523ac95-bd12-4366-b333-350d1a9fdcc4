/* eslint-disable react/jsx-key */
import React from 'react';
import { useTranslation } from 'react-i18next';
import StaticStrings from '../../../StaticStrings';
import { tabIndexes, a11y } from '../../../constants/a11y';
import variables from '../../../redux/variables';
import Icons from '../../../assets/svg-icon/svg';
const Recommendations = ({ recommendationsResults, redirectURL }) => {
  try {
    /**
     *  Change Languages state
     */
    const { t } = useTranslation();

    return (
      <>
        {recommendationsResults &&
        recommendationsResults.result &&
        recommendationsResults.result.hits.length !== 0 ? (
          <div className="su__Recommended_Article-section su__d-block su__border-radius">
            <div className="su__w-100">
              <div className=" su__Recommended_Articles-R  su__w-100 su__pt-3 su__pb-1 ">
                <div className="su__position-relative su__d-flex su__align-items-center su__recommdation-heading">
                  <Icons
                    IconName="FtSnippet__ThumbsUp"
                    width="24"
                    height="24"
                    color="#166FD2"
                    transform="scale(0.6) translate(3, 2)"
                  />
                  <h2
                    lang={variables.searchCallVariables.langAttr}
                    tabIndex={tabIndexes.tabIndex_0}
                    className="su__Recommended_Article su__px-2 su__font-weight-bold su__font-17 su__my-0"
                  >
                    {t(StaticStrings.recommended_articles)}
                  </h2>
                </div>
                <div className="su__px-3  su__pb-2 su__recommendation-inner-div su__mt-3 ">
                  {React.Children.toArray(
                    recommendationsResults.result.hits.map((item) => (
                      <>
                        {item.highlight.TitleToDisplay[0] ? (
                          <div
                            className={`su__py-1 su__d-block ${
                              redirectURL
                                ? 'su__Recomended_border-widget-b'
                                : 'su__Recomended_border-b'
                            }`}
                          >
                            <div className="su__py-1">
                              <div className="su__search-title su__list-item-title su__text-truncate su__m-0 su__loading-view su__d-xl-flex  su__flex-column su__justify-content-between">
                                {item.contentTag && (
                                  <div className="su__recommendations-tag-content">
                                    <p
                                      title={item.contentTag}
                                      className="su__recommendation-label su__text-truncate su__text-center su__whitespace-initial"
                                    >
                                      {' '}
                                      {item.contentTag}
                                    </p>
                                  </div>
                                )}
                                <div
                                  className={`su__whitespace-initial su__recommendations-title su__truncate-two-lines ${
                                    item.contentTag ? 'su__Recommended_Article-max-width' : ''
                                  }`}
                                >
                                  <a
                                    tabIndex={tabIndexes.tabIndex_minus_1}
                                    className="su__text-decoration su__recommendations-content "
                                    href={item.href}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                  >
                                    <h3
                                      role={a11y.ROLES.LNK}
                                      tabIndex={tabIndexes.tabIndex_0}
                                      className="su__my-0 su__font-14 su__f-normal su__line-height-22 su__recommdation-article su__text-hover-underline su__text-decoration su__hover-color-black su__line_clamp_1"
                                      dangerouslySetInnerHTML={{
                                        __html: item.highlight.TitleToDisplay[0] || item.href
                                      }}
                                    ></h3>
                                  </a>
                                </div>
                              </div>
                            </div>
                          </div>
                        ) : null}
                      </>
                    ))
                  )}
                </div>
                {redirectURL && (
                  <div className="su__recommendations-ViewMore">
                    <a
                      href={redirectURL}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="su__text-decoration"
                    >
                      <span className="su__recommendations-ViewMore-text">View more articles</span>
                    </a>
                  </div>
                )}
              </div>
            </div>
          </div>
        ) : null}
      </>
    );
  } catch (e) {
    console.log('Error in recommendation component', e);
    return <div></div>;
  }
};

export default Recommendations;
