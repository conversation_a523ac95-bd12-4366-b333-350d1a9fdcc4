/* global jest, it, expect, describe, beforeEach, afterEach */
import React from 'react';
import { mount } from 'enzyme';
import Recommendations from './';
import { useTranslation } from 'react-i18next';

jest.mock('../../../redux/variables', () => ({
  searchCallVariables: {
    aggregations: [],
    exactPhrase: '',
    withOneOrMore: '',
    withoutTheWords: '',
    from: 0,
    pageNo: 1,
    langAttr: 'en'
  },
  selectedStickyFilters: [],
  allSelected: true,
  activeType: 'all',
  searchSource: ''
}));

jest.mock('react-i18next', () => ({
  useTranslation: jest.fn()
}));

describe('Recommendations', () => {
  let wrapper;
  const props = {
    recommendationsResults: {
      result: {
        hits: [
          {
            highlight: {
              TitleToDisplay: ['Article 1']
            },
            href: 'https://example.com/article1',
            contentTag: 'Tag 1'
          },
          {
            highlight: {
              TitleToDisplay: ['Article 2']
            },
            href: 'https://example.com/article2',
            contentTag: 'Tag 2'
          }
        ]
      }
    },
    redirectURL: 'https://example.com/more-articles'
  };

  beforeEach(() => {
    useTranslation.mockImplementation(() => ({ t: (key) => key }));
    wrapper = mount(<Recommendations {...props} />);
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.clearAllTimers();
  });

  it('should render without errors', () => {
    expect(wrapper.exists()).toBe(true);
  });

  it('should render the recommended articles', () => {
    expect(wrapper.find('.su__Recommended_Article-section')).toHaveLength(1);
    expect(wrapper.find('.su__search-title')).toHaveLength(2);
  });

  it('should render the redirect link if redirectURL is provided', () => {
    expect(wrapper.find('.su__recommendations-ViewMore')).toHaveLength(1);
    expect(wrapper.find('.su__recommendations-ViewMore a')).toHaveLength(1);
    expect(wrapper.find('.su__recommendations-ViewMore a').prop('href')).toBe(props.redirectURL);
  });

  it('should not render the component if recommendationsResults is empty', () => {
    wrapper.setProps({ recommendationsResults: null });
    expect(wrapper.find('.su__Recommended_Article-section')).toHaveLength(0);
  });

  it('should render the article title with the correct formatting', () => {
    const articleTitle = wrapper.find('.su__search-title h3');
    expect(articleTitle).toHaveLength(2);
    expect(articleTitle.at(0).prop('dangerouslySetInnerHTML').__html).toBe(
      props.recommendationsResults.result.hits[0].highlight.TitleToDisplay[0]
    );
  });

  it('should render the content tag if available', () => {
    const contentTag = wrapper.find('.su__recommendation-label');
    expect(contentTag).toHaveLength(2);
    expect(contentTag.at(0).text().trim()).toBe(
      props.recommendationsResults.result.hits[0].contentTag
    );
  });

  it('should catch error in case component encounters one', () => {
    wrapper.unmount();
    const consoleSpy = jest.spyOn(console, 'log').mockImplementation(() => {});
    useTranslation.mockImplementation(() => {
      throw new Error('error');
    });
    wrapper.mount();
    wrapper.update();
    expect(wrapper.html()).toBe('<div></div>');
    expect(consoleSpy).toHaveBeenCalledWith('Error in recommendation component', expect.any(Error));
    consoleSpy.mockRestore();
  });
});
