import React, { useState, Fragment, useEffect } from 'react';
import Icons from '../../../assets/svg-icon/svg';
import { useTranslation } from 'react-i18next';
import Facets from 'components/feature-components/facets/index.jsx';
import StaticStrings from '../../../StaticStrings';
import variables from '../../../redux/variables';
import { A11Y_IDS, focusTrap, a11y, tabIndexes } from '../../../constants/a11y';
import { useDispatch } from 'react-redux';
import { search } from '../../../redux/ducks';
import useDevice from 'function-library/hooks/use-device/use-device';
const mobileFacets = (props) => {
  const { aggregations } = props;
  try {
    /**
     *  Change Languages state
     */
    const { t } = useTranslation();
    const dispatch = useDispatch();
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [noFiltersMessage, setNoFiltersMessage] = useState(true);
    const { isDeviceMobile, isDeviceIpad } = useDevice();
    useEffect(() => {
      setNoFiltersMessage(true);
      if (aggregations && aggregations.length) {
        let arrayToCheck = aggregations.filter((item, index) => index > 0);
        arrayToCheck.map((item) => {
          if (item.values && item.values.length) {
            setNoFiltersMessage(false);
          }
        });
      }
    }, [aggregations]);
    /**
     * This hook is used to remove homepage scrollbar when filter section is opened.
     * @param- null
     */
    useEffect(() => {
      if (isModalOpen) {
        document.documentElement.style.overflow = 'hidden';
      } else {
        document.documentElement.style.overflow = 'unset';
      }
    }, [isModalOpen]);

    useEffect(() => {
      focusTrap(isModalOpen);
    }, [!isModalOpen]);

    const updateConsoleButtons = () => {
      if (variables.searchCallVariables.aggregations.length && variables.isConsoleTypeSC) {
        for (let i of variables.searchCallVariables.aggregations) {
          if (i.filter) {
            props.setIsButtonActive(true);
            return;
          } else {
            props.setIsButtonActive(false);
          }
        }
      } else {
        props.setIsButtonActive(false);
      }
    };

    useEffect(() => {
      updateConsoleButtons();
    }, [props.isButtonActive]);

    return (
      <Fragment>
        <div className="su__d-xl-none su__d-xs-block su__mob-facetshow su__d-lg-block su__font-12 su__font-bold su__sm-flex-1">
          <div
            className={`su__flex-vcenter su__h-100 su__word-normal-kh ${
              isModalOpen ? 'su__mob-active' : ''
            }`}
          >
            <button
              type="button"
              lang={variables.searchCallVariables.langAttr}
              aria-expanded={isModalOpen ? 'true' : 'false'}
              role={a11y.ROLES.BTN}
              tabIndex={tabIndexes.tabIndex_0}
              onClick={() => setIsModalOpen(true)}
              className="su__flex-vcenter su__text-primary su__facet-icon su__active-text su__p-2 su__p-sm-2  su__border su__shadow su__py-1 su__filter-icon su__mr-il-20 su__mr-0-mobile su__sm_mr-il-0 su__sc-loading"
              data-test-id="su__filter-button"
            >
              <Icons
                className="su__mr-1"
                IconName="Filter"
                width="17"
                height="17"
                color="#1770d4"
                IconClass="su__active-path"
                transform="translate(1, -1)"
              />{' '}
              {t(StaticStrings.filter)}
            </button>
          </div>
        </div>
        {isModalOpen && (
          <Fragment>
            <div
              className="su__position-fixed su__trbl-0 su__zindex-2"
              data-test-id="su__filter-popup"
            >
              <div
                id={A11Y_IDS.trap}
                className={`su__radius su__position-absolute su__h-100 su__zindex-1 su__shadow-lg su__bg-white su__modal_fullwidth  su__w-100 ${
                  isModalOpen ? 'su__animate-fadeLeft' : ''
                } ${isDeviceMobile ? 'su__mobile-facet' : 'su__modal-inner'}`}
              >
                {noFiltersMessage && (
                  <div className="su_no-filters-mobile">
                    <span className="no_filter-msg"> {'No Filters available.'}</span>
                  </div>
                )}
                <div className="su__filter-inner su__h-100 su__position-relative  su__bg-bright-white su__box-shadow-bl-99  su__border-bottom  su__pb-5 ">
                  <Facets
                    isButtonActive={props.isButtonActive}
                    setIsButtonActive={props.setIsButtonActive}
                  />
                </div>
                {isDeviceMobile || isDeviceIpad || variables.isConsoleTypeSC ? (
                  <div
                    className={`su__position-fixed su__bottom-0 su__flex-vcenter   su__w-100 su__h-44px su__bg-bright-white su__box-shadow ${
                      variables.isConsoleTypeSC ? 'su__dropdowns-gaps su__w-97' : null
                    }`}
                  >
                    <button
                      lang={variables.searchCallVariables.langAttr}
                      aria-label={t(StaticStrings.Close)}
                      type="button"
                      role={a11y.ROLES.BTN}
                      tabIndex={tabIndexes.tabIndex_0}
                      className={`su__border-none su__cursor   su__flex-1  su__h-100 su__bottom-buttons  ${
                        variables.isConsoleTypeSC
                          ? 'su__console-btns su__apply-btn'
                          : 'su__dark-gray'
                      }`}
                      data-test-id="su__filter-close-btn"
                      onClick={() => setIsModalOpen(false)}
                    >
                      {t(StaticStrings.CLOSE)}
                    </button>
                    {!variables.isConsoleTypeSC && <div className="su__vertical-line"></div>}
                    <button
                      lang={variables.searchCallVariables.langAttr}
                      aria-label={t(StaticStrings.Apply_Button)}
                      type="button"
                      role={a11y.ROLES.BTN}
                      tabIndex={tabIndexes.tabIndex_0}
                      className={`su__border-none su__cursor  su__flex-1 su__h-100 su__bottom-buttons ${
                        variables.isConsoleTypeSC
                          ? 'su__console-btns su__apply-btn'
                          : 'su__sky-blue'
                      } ${
                        variables.isConsoleTypeSC
                          ? props.isButtonActive
                            ? 'su__console-btn-active'
                            : 'disabled-btn su__bookmark_SavedResult_opacity'
                          : null
                      } `}
                      onClick={() => {
                        dispatch(search.start(variables.searchCallVariables));
                        setIsModalOpen(false);
                      }}
                    >
                      {' '}
                      {t(StaticStrings.Apply_Button)}
                    </button>
                  </div>
                ) : null}
              </div>
              <div className="su__overlay su__zindex-1" onClick={() => setIsModalOpen(false)} />
            </div>
          </Fragment>
        )}
      </Fragment>
    );
  } catch (e) {
    console.log('Error in Mobile Facets', e);
    return <div></div>;
  }
};
export default mobileFacets;
