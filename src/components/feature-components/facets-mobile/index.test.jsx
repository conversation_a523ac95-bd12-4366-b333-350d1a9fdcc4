/* global jest, it, expect, describe, beforeEach, afterEach */
import React from 'react';
import { mount } from 'enzyme';
import state from '__mocks__/state';
import MobileFacets from './index';
const SELECTORS = {
  filterButton: "[data-test-id='su__filter-button']",
  filterPopUp: "[data-test-id='su__filter-popup']",
  filterCloseButton: "[data-test-id='su__filter-close-btn']"
};
jest.mock('react-redux', () => ({
  useSelector: jest.fn(),
  useDispatch: jest.fn()
}));
jest.mock('../../../redux/variables', () => require('__mocks__/variables'));

// Create a mock of the Facets component
jest.mock('components/feature-components/facets/index.jsx', () => {
  const FacetsMock = () => <div className="facets-mock">Mocked Facets Component</div>;
  return FacetsMock;
});

describe('MobileFacets Component', () => {
  let wrapper;
  const props = {
    isButtonActive: false,
    isModalOpen: false,
    setIsButtonActive: jest.fn(),
    setIsModalOpen: jest.fn()
  };
  beforeEach(() => {
    wrapper = mount(<MobileFacets {...props} />);
  });
  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should render without crashing', () => {
    wrapper = mount(
      <MobileFacets aggregations={state.searchResult.aggregationsArray} {...props} />
    );
    wrapper.update();
    expect(wrapper.exists()).toBe(true);
  });

  it('should open the modal on button click', () => {
    let newProps = {
      ...props,
      isModalOpen: true
    };
    wrapper = mount(
      <MobileFacets aggregations={state.searchResult.aggregationsArray} {...newProps} />
    );
    expect(wrapper.find('.su__modal-inner').exists()).toBe(false);
    wrapper.find(SELECTORS.filterButton).simulate('click');
    console.log(wrapper.debug(), 'after update777');
    expect(wrapper.find(SELECTORS.filterPopUp).exists()).toBe(true);
  });

  it('should close the modal on close button click', () => {
    let newProps = {
      ...props,
      isModalOpen: true
    };
    wrapper = mount(
      <MobileFacets aggregations={state.searchResult.aggregationsArray} {...newProps} />
    );
    wrapper.find(SELECTORS.filterButton).simulate('click');
    expect(wrapper.find('.su__modal-inner').exists()).toBe(true);
    wrapper.find(SELECTORS.filterCloseButton).simulate('click');
    expect(wrapper.find(SELECTORS.filterPopUp).exists()).toBe(false);
  });

  it('should show no filters message if no aggregations have values', () => {
    const aggregationsWithNoValues = [{ values: [] }, { values: [] }];
    wrapper.setProps({ aggregations: aggregationsWithNoValues });
    wrapper.find(SELECTORS.filterButton).simulate('click');
    expect(wrapper.find('.no_filter-msg').exists()).toBe(true);
  });
});
