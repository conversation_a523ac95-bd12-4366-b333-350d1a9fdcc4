/* eslint-disable no-inner-declarations */
/* eslint-disable no-undef */
import variables from './variables';
import utilityMethods from './utilities/utility-methods';
import { SC_IDS } from 'constants/constants';

let jwtBearer;
let BASEURL;
let initializeAuthPromise;
let dataSentToGptCall;

/**
 * search call and recomendations to backend on the basis of search client type.
 * @param {} queryPassed
 */

/** Refs */
const memoryRefs = [];

function getEmail() {
  return new Promise((resolve, reject) => {
    let email = '';
    let check = 0;
    const clientType = variables.searchClientType;

    switch (clientType) {
      case SC_IDS.HIGHER_LOGIC_VANILLA:
        window.onVanillaReady(function (vanilla) {
          const { email = '' } = vanilla?.getCurrentUser();
          GzAnalytics.setUser(email);
          if (email) {
            variables.searchCallVariables['email'] = window.su_utm;
            variables.autocompleteCallVariables['email'] = window.su_utm;
            resolve(email);
          }
        });
        break;

      case SC_IDS.ZENDESK_GUIDE:
        const intervalId = setInterval(() => {
          check++;
          email = HelpCenter && HelpCenter.user && HelpCenter.user.email;
          if (email) {
            clearInterval(intervalId);
            resolve(email);
          } else if (check > 18) {
            clearInterval(intervalId);
          }
        }, 10 * 100);
        break;

      case SC_IDS.ZENDESK_SUPPORT:
      case SC_IDS.ZENDESK_SUPPORT_CONSOLE:
        client.get('currentUser').then(function (user) {
          email = user.currentUser.email;
          resolve(email);
        });
        break;

      case SC_IDS.FRESHSERVICE:
        const interval = setInterval(() => {
          check++;
          email = window.USER_INFO && window.USER_INFO.email;
          if (email) {
            clearInterval(interval);
            resolve(email);
          } else if (check > 50) {
            clearInterval(interval);
            reject(undefined);
          }
        }, 3000);
    }
  });
}

(async function setEmail() {
  const clientType = variables.searchClientType;
  switch (clientType) {
    case SC_IDS.SERVICENOW:
      let getLoggedInUser = new GlideAjax('x_312362_c_a_su.getLoggedInUser');
      getLoggedInUser.addParam('sysparm_name', 'getUserEmail');
      getLoggedInUser.getXML(callback);

      function callback(result) {
        let data = result.responseXML.documentElement.getAttribute('answer');
        suGlobals.loggedInUser = data;
        if (data) {
          GzAnalytics.setUser(data);
          variables.searchCallVariables['email'] = window.su_utm;
          variables.autocompleteCallVariables['email'] = window.su_utm;
        }
      }
      break;

    case SC_IDS.ZENDESK_GUIDE:
    case SC_IDS.ZENDESK_SUPPORT:
    case SC_IDS.ZENDESK_SUPPORT_CONSOLE:
      const fetchEmail = await getEmail();
      if (fetchEmail) {
        GzAnalytics.setUser(fetchEmail);
        variables.searchCallVariables['email'] = window.su_utm;
        variables.autocompleteCallVariables['email'] = window.su_utm;
      } else {
        GzAnalytics.setUser('');
      }
      break;

    /** Vanilla platform */
    case SC_IDS.HIGHER_LOGIC_VANILLA:
      utilityMethods.clearIntervalsAndTimeouts(memoryRefs); // cleanup
      if (gdn?.meta?.roleToken != '') {
        const fetchEmail = await getEmail();
        if (fetchEmail) {
          memoryRefs.push(utilityMethods.withInterval(getEmail, 60 * 100)); // trigger interval
          utilityMethods.clearIntervalsAndTimeouts(memoryRefs); // cleanup
        }
      } else {
        GzAnalytics.setUser('');
      }
      break;

    case SC_IDS.KHOROS_AURORA:
      try {
        const backendEndpointUrl = window.location.origin + '/endpoints/userMail/';
        const response = await fetch(backendEndpointUrl, {
          headers: {
            action: 'getUserEmail'
          }
        });
        const data = await response.json();
        if (data && data.email) {
          const email = data.email;
          GzAnalytics.setUser(email);
          variables.searchCallVariables['email'] = window.su_utm;
          variables.autocompleteCallVariables['email'] = window.su_utm;
        } else {
          console.error('Email not found in the response');
        }
      } catch (error) {
        console.error('Error fetching user email:', error);
      }
      break;
    case SC_IDS.FRESHSERVICE:
      try {
        const email = await getEmail();
        if (email) {
          GzAnalytics.setUser(email);
          variables.searchCallVariables.email = window.su_utm;
          variables.autocompleteCallVariables.email = window.su_utm;
        } else {
          console.error('Email not found');
        }
      } catch (error) {
        console.error('Error fetching user email:', error);
      }
      break;
    default:
      let emailInterval = setInterval(function () {
        if (window.user) {
          if (window.isGuestUser) window.user = '';
          clearInterval(emailInterval);
          GzAnalytics.setUser(window.user);
          variables.searchCallVariables['email'] = window.su_utm;
          variables.autocompleteCallVariables['email'] = window.su_utm;
        }
      }, 509);
  }
})();

/** Get Auth JWT token */
async function getAuthToken(url) {
  try {
    const response = await fetch(url);
    if (response.ok) {
      let authtoken = await response.text();
      const searchClientType = variables.searchClientType;
      try {
        switch (searchClientType) {
          case SC_IDS.WORDPRESS:
          case SC_IDS.DRUPAL_7:
          case SC_IDS.DRUPAL_10:
            try {
              authtoken = JSON.parse(authtoken);
            } catch (error) {
              console.error('[ error ]', error);
            }
            jwtBearer = authtoken.token;
            GzAnalytics.setUser(authtoken.email);
            variables.searchCallVariables['email'] = window.su_utm;
            variables.autocompleteCallVariables['email'] = window.su_utm;
            break;
          case SC_IDS.HIGHER_LOGIC_THRIVE:
            let tokenHigher =
              decodeURIComponent(authtoken) &&
              decodeURIComponent(authtoken).split('authtoken=') &&
              decodeURIComponent(authtoken).split('authtoken=')[1];
            jwtBearer = tokenHigher;
            break;
          case SC_IDS.AEM:
            window.email = window.su__aem_useremail;
            jwtBearer = authtoken;
        }
      } catch (error) {
        console.error('[ error ]', error);
      }
    } else {
      console.error('Failed to fetch JWT token:', response.status, response.statusText);
    }
  } catch (error) {
    console.error('[ error ]', error);
  }
}

const getPlatformUrl = async (searchClientType) => {
  let check = 0;
  return new Promise((resolve, reject) => {
    let checkPlatformUrl = setInterval(async () => {
      check++;
      switch (searchClientType) {
        case SC_IDS.WORDPRESS:
          if (document.getElementById('wpBaseUrl') && document.getElementById('wpBaseUrl').value) {
            clearInterval(checkPlatformUrl);
            await getAuthToken(
              document.getElementById('wpBaseUrl').value + '/wp-json/search-unify/v1/search_jwt'
            );
            resolve();
          }
          break;
        case SC_IDS.DRUPAL_7:
          if (typeof Drupal != 'undefined' && Drupal.settings && Drupal.settings.basePath) {
            clearInterval(checkPlatformUrl);
            BASEURL = window.location.origin + Drupal.settings.basePath + 'search-unify/v1';
            resolve();
          }
          break;
        case SC_IDS.DRUPAL_10:
          if (typeof Drupal != 'undefined' && drupalSettings.path && drupalSettings.path.baseUrl) {
            clearInterval(checkPlatformUrl);
            await getAuthToken(
              window.location.origin + drupalSettings.path.baseUrl + 'search-unify/v1/search_jwt'
            );
            resolve();
          }
          break;
        case SC_IDS.INTRANET_SEARCH:
          if (window.jwtBearer) {
            clearInterval(checkPlatformUrl);
            resolve();
          }
          break;
        default:
          if (check > 350) {
            clearInterval(checkPlatformUrl);
            reject(new Error(`Can't read platform dependent variable. Something went wrong!!`));
          }
      }
    }, 30);
  });
};

const initializeAuth = async () => {
  if (!initializeAuthPromise) {
    initializeAuthPromise = (async () => {
      if (
        [SC_IDS.WORDPRESS, SC_IDS.DRUPAL_7, SC_IDS.DRUPAL_10, SC_IDS.INTRANET_SEARCH].includes(
          variables.searchClientType
        )
      ) {
        await getPlatformUrl(variables.searchClientType);
      } else if ([SC_IDS.HIGHER_LOGIC_THRIVE, SC_IDS.AEM].includes(variables.searchClientType)) {
        await getAuthToken(scConfiguration.jwt_href);
        setInterval(() => {
          getAuthToken(scConfiguration.jwt_href);
        }, parseInt(scConfiguration.jwt_expiry) || 300000);
      }
    })();
  }
  return initializeAuthPromise;
};

initializeAuth();

const getPayloadWithWildcard = (queryPassed) => {
  const payload = JSON.parse(JSON.stringify(queryPassed));
  let wildCardSearch = !!sessionStorage.getItem('toggleWildcardSearch');
  if (wildCardSearch && queryPassed.searchString.charAt(0) !== '#') {
    // if session storage is true
    payload.searchString = '#' + queryPassed.searchString;
  }
  return JSON.stringify(payload);
};

export async function savedResultReq(searchClientType, queryPassed) {
  let instanceName = variables.searchClientProps.instanceName;
  let url = '';
  let req = {};
  switch (searchClientType) {
    case SC_IDS.WEB_APP:
    case SC_IDS.ZENDESK_GUIDE:
    case SC_IDS.ZENDESK_SUPPORT:
    case SC_IDS.ZENDESK_SUPPORT_CONSOLE:
    case SC_IDS.NICE_CXONE:
    case SC_IDS.SHAREPOINT:
    case SC_IDS.JOOMLA:
    case SC_IDS.HIGHER_LOGIC_VANILLA:
    case SC_IDS.FRESHSERVICE:
      let searchEndpoint = '/search/getSavedResultsForIds';
      url = `${instanceName}${searchEndpoint}`;
      req = {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(queryPassed)
      };
      break;

    case SC_IDS.HIGHER_LOGIC_THRIVE:
      await initializeAuth();
      queryPassed.authtoken = localStorage.getItem('authtoken');
      queryPassed.higherlogic = true;
      queryPassed.HLAuthToken = document.cookie.split('HLAuthToken=')[1].split(';')[0] || '';
      let searchEndpointHL = '/search/SUSavedResultsForIds';

      url = `${instanceName}${searchEndpointHL}`;
      req = {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(queryPassed)
      };
      break;

    case SC_IDS.WORDPRESS:
    case SC_IDS.DRUPAL_10:
      await initializeAuth();
      searchClientType == SC_IDS.WORDPRESS || searchClientType == SC_IDS.DRUPAL_10
        ? (queryPassed.JWTSecureGroup1 = true)
        : (queryPassed.aem = true);
      searchEndpoint = '/search/SUSavedResultsForIds';
      url = `${instanceName}${searchEndpoint}`;

      req = {
        method: 'POST',
        headers: {
          authorization: 'bearer ' + jwtBearer,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(queryPassed)
      };
      break;

    case SC_IDS.JIVE_TILE:
    case SC_IDS.JIVE:
      //Jive
      break;

    case SC_IDS.SALESFORCE_VISUALFORCE_COMMUNITY:
    case SC_IDS.SALESFORCE_VISUALFORCE_INTERNAL:
    case SC_IDS.AEM:
    case SC_IDS.INTRANET_SEARCH:
      await initializeAuth();
      let searchEndpointSFInternal = '/search/SUSavedResultsForIds';
      url = `${instanceName}${searchEndpointSFInternal}`;
      req = {
        method: 'POST',
        headers: {
          authorization: 'bearer ' + window.jwtBearer,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(queryPassed)
      };
      break;
    case SC_IDS.KHOROS:
      queryPassed.recordIds = JSON.stringify(queryPassed.recordIds);

      const searchParams = Object.keys(queryPassed)
        .map((key) => {
          return encodeURIComponent(key) + '=' + encodeURIComponent(queryPassed[key]);
        })
        .join('&');
      let getSearchResultsEndpoint = window.su_savedResults_path;
      url = getSearchResultsEndpoint;
      req = {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8'
        },
        body: searchParams
      };
      queryPassed.recordIds = JSON.parse(queryPassed.recordIds);
      break;
    case SC_IDS.DRUPAL_7:
      let searchResultByPost = '/SUSavedResultsForIds';
      url = BASEURL + searchResultByPost;
      req = {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(queryPassed)
      };

      break;
    case SC_IDS.MICROSOFT_DYNAMICS: // need to check
      break;
    case SC_IDS.SERVICENOW: // need to check
      break;
    case SC_IDS.HOSTED_SEARCH_CLIENT: // need to check
      break;
    case SC_IDS.KHOROS_AURORA:
      const backendEndpointUrl = window.location.origin + '/endpoints/searches/';
      url = backendEndpointUrl;
      req = {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Action: 'savedResults',
          Instance: instanceName
        },
        body: JSON.stringify(queryPassed)
      };
      break;
  }
  return { url, req };
}

export async function searchUrlReq(searchClientType, queryPassed) {
  let instanceName = variables.searchClientProps.instanceName;
  let url = '';
  let req = {};

  let searchEndpoint;
  switch (searchClientType) {
    case SC_IDS.WEB_APP:
    case SC_IDS.ZENDESK_GUIDE:
    case SC_IDS.ZENDESK_SUPPORT:
    case SC_IDS.ZENDESK_SUPPORT_CONSOLE:
    case SC_IDS.NICE_CXONE:
    case SC_IDS.SHAREPOINT:
    case SC_IDS.JOOMLA:
    case SC_IDS.HIGHER_LOGIC_VANILLA:
    case SC_IDS.FRESHSERVICE:
      if (searchClientType == SC_IDS.ZENDESK_SUPPORT_CONSOLE)
        queryPassed.searchString =
          queryPassed.searchString == '' ? window.caseSubject : queryPassed.searchString;
      searchEndpoint = '/search/searchResultByPost';
      url = `${instanceName}${searchEndpoint}`;
      req = {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: getPayloadWithWildcard(queryPassed)
      };
      break;

    case SC_IDS.HIGHER_LOGIC_THRIVE:
      await initializeAuth();
      queryPassed.authtoken = jwtBearer;
      queryPassed.higherlogic = true;
      queryPassed.HLAuthToken = document.cookie.split('HLAuthToken=')[1].split(';')[0] || '';
      let searchEndpointHL = '/search/SUSearchResults';

      url = `${instanceName}${searchEndpointHL}`;
      req = {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: getPayloadWithWildcard(queryPassed)
      };
      break;
    case SC_IDS.WORDPRESS:
    case SC_IDS.DRUPAL_10:
      await initializeAuth();
      searchClientType == SC_IDS.WORDPRESS || searchClientType == SC_IDS.DRUPAL_10
        ? (queryPassed.JWTSecureGroup1 = true)
        : (queryPassed.aem = true);
      searchEndpoint = '/search/SUSearchResults';
      url = `${instanceName}${searchEndpoint}`;

      req = {
        method: 'POST',
        headers: {
          authorization: 'bearer ' + jwtBearer,
          'Content-Type': 'application/json'
        },
        body: getPayloadWithWildcard(queryPassed)
      };
      break;

    case SC_IDS.JIVE_TILE:
    case SC_IDS.JIVE:
      break;

    case SC_IDS.SALESFORCE_VISUALFORCE_COMMUNITY:
    case SC_IDS.SALESFORCE_VISUALFORCE_INTERNAL:
    case SC_IDS.AEM:
    case SC_IDS.INTRANET_SEARCH:
      await initializeAuth();
      let searchEndpointSFInternal = '/search/SUSearchResults';
      url = `${instanceName}${searchEndpointSFInternal}`;
      req = {
        method: 'POST',
        headers: {
          authorization: 'bearer ' + window.jwtBearer,
          'Content-Type': 'application/json'
        },
        body: getPayloadWithWildcard(queryPassed)
      };
      break;

    case SC_IDS.KHOROS:
      queryPassed = getPayloadWithWildcard(queryPassed);
      queryPassed = JSON.parse(queryPassed);
      queryPassed.aggregations = JSON.stringify(queryPassed.aggregations);
      if (queryPassed.pagingAggregation && typeof queryPassed.pagingAggregation != 'string') {
        queryPassed.pagingAggregation = JSON.stringify(queryPassed.pagingAggregation);
      }
      queryPassed.searchString = encodeURIComponent(queryPassed.searchString);
      const searchParams = Object.keys(queryPassed)
        .map((key) => {
          return encodeURIComponent(key) + '=' + encodeURIComponent(queryPassed[key]);
        })
        .join('&');
      let getSearchResultsEndpoint = window.su_community_path;
      url = getSearchResultsEndpoint;
      req = {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8'
        },
        body: searchParams
      };
      queryPassed.aggregations = JSON.parse(queryPassed.aggregations);
      queryPassed.searchString = decodeURIComponent(queryPassed.searchString);
      break;

    case SC_IDS.DRUPAL_7:
      let BASEURL;
      let searchResultByPost = '/searchResultByPost';
      url = BASEURL + searchResultByPost;
      req = {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: getPayloadWithWildcard(queryPassed)
      };
      break;

    case SC_IDS.MICROSOFT_DYNAMICS:
      let queryToBePassed = JSON.stringify(queryPassed);
      queryToBePassed = queryToBePassed.replace(/"/g, "'");
      let finalQueryPassed = { agre: queryToBePassed };
      let serverURL = location.protocol + '//' + location.host;
      let query = 'ser_Custom_Action';
      url = serverURL + '/api/data/v8.2/' + query;
      req = {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: getPayloadWithWildcard(finalQueryPassed)
      };
      break;

    case SC_IDS.SERVICENOW: // serviceNow
      break;
    case SC_IDS.HOSTED_SEARCH_CLIENT:
      req = {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(queryPassed)
      };
      break;
    case SC_IDS.KHOROS_AURORA:
      const backendEndpointUrl = window.location.origin + '/endpoints/searches/';
      queryPassed['accessToken'] = '';
      queryPassed['fieldsToReturn'] = ['accessToken', 'boardsArr'];
      url = backendEndpointUrl;
      req = {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Action: 'searchAurora',
          Instance: instanceName
        },
        body: getPayloadWithWildcard(queryPassed)
      };
      break;
  }
  const payload =
    typeof req.body === 'string'
      ? req.headers['Content-Type']?.includes('application/x-www-form-urlencoded')
        ? Object.fromEntries(new URLSearchParams(req.body))
        : JSON.parse(req.body)
      : req.body;
  if (!payload.autocomplete) {
    dataSentToGptCall = { searchBody: req.body, searchHeaders: req.headers };
  }
  return { url, req };
}

export async function recomendationsUrlReq(searchClientType, data) {
  // http request for different searchClientType
  let instanceName = variables.searchClientProps.instanceName;
  let url = '';
  let queryPassed = {
    uid: data?.uid,
    searchString: variables.searchCallVariables.searchString,
    sid:
      typeof _gr_utility_functions !== 'undefined'
        ? _gr_utility_functions.getCookie('_gz_taid')
        : '',
    language: localStorage.getItem('language') || 'en',
    useremail: variables.searchCallVariables.email || ''
  };

  if (
    [
      SC_IDS.JIVE,
      SC_IDS.WEB_APP,
      SC_IDS.JIVE_TILE,
      SC_IDS.ZENDESK_GUIDE,
      SC_IDS.ZENDESK_SUPPORT,
      SC_IDS.NICE_CXONE,
      SC_IDS.SHAREPOINT,
      SC_IDS.JOOMLA,
      SC_IDS.HIGHER_LOGIC_VANILLA,
      SC_IDS.FRESHSERVICE
    ].includes(searchClientType)
  ) {
    queryPassed['accessToken'] = data.accessToken;
  }
  if (data.isRecommendationsWidget) {
    queryPassed['recommendationType'] = 2;
  }

  let req = {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(queryPassed)
  };

  switch (searchClientType) {
    case SC_IDS.KHOROS:
      url = window.su_recommendations;
      const searchParams = Object.keys(queryPassed)
        .map((key) => {
          return encodeURIComponent(key) + '=' + encodeURIComponent(queryPassed[key]);
        })
        .join('&');
      req = {
        ...req,
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8'
        },
        body: searchParams
      };
      break;

    case SC_IDS.JIVE:
    case SC_IDS.JIVE_TILE:
    case SC_IDS.WEB_APP:
    case SC_IDS.ZENDESK_GUIDE:
    case SC_IDS.ZENDESK_SUPPORT:
    case SC_IDS.NICE_CXONE:
    case SC_IDS.SHAREPOINT:
    case SC_IDS.JOOMLA:
    case SC_IDS.HIGHER_LOGIC_VANILLA:
    case SC_IDS.FRESHSERVICE:
      url = instanceName + '/ai/getRecommendedResult';
      break;

    case SC_IDS.HIGHER_LOGIC_THRIVE:
      await initializeAuth();
      queryPassed.authtoken = jwtBearer;
      queryPassed.higherlogicRecomm = true;
      queryPassed.HLAuthToken = document.cookie.split('HLAuthToken=')[1].split(';')[0] || '';
      let searchEndpointHL = '/search/getRecommendedResult';

      url = `${instanceName}${searchEndpointHL}`;
      req = {
        ...req,
        body: JSON.stringify(queryPassed)
      };

      break;

    case SC_IDS.DRUPAL_7:
      url = BASEURL + '/ai/getRecommendedResult';
      break;

    case SC_IDS.ZENDESK_SUPPORT_CONSOLE:
    case SC_IDS.MICROSOFT_DYNAMICS:
      url = instanceName + '/ai/getRecommendedResult';
      break;

    case SC_IDS.SALESFORCE_VISUALFORCE_COMMUNITY:
    case SC_IDS.SALESFORCE_VISUALFORCE_INTERNAL:
    case SC_IDS.AEM:
    case SC_IDS.INTRANET_SEARCH:
      await initializeAuth();
      queryPassed.orgId = window.orgId;
      url = instanceName + '/ai/authSURecommendation';
      req = {
        ...req,
        headers: {
          ...req.headers,
          authorization: 'bearer ' + window.jwtBearer
        },
        body: JSON.stringify(queryPassed)
      };
      break;

    case SC_IDS.WORDPRESS:
    case SC_IDS.DRUPAL_10:
      await initializeAuth();
      searchClientType == SC_IDS.WORDPRESS || searchClientType == SC_IDS.DRUPAL_10
        ? (queryPassed.JWTSecureGroup1 = true)
        : (queryPassed.aem = true);
      url = instanceName + '/ai/authSURecommendation';
      req = {
        ...req,
        headers: {
          ...req.headers,
          authorization: 'bearer ' + jwtBearer
        },
        body: JSON.stringify(queryPassed)
      };
      break;
    case SC_IDS.KHOROS_AURORA:
      const backendEndpointUrl = window.location.origin + '/endpoints/searches/';
      url = backendEndpointUrl;
      req = {
        ...req,
        headers: {
          ...req.headers,
          Action: 'recommendationsAurora',
          Instance: instanceName
        }
      };
      break;
  }

  return { url, req };
}

export async function sugptUrlReq(searchClientType, variables) {
  let body;
  let headers = new Headers();
  let searchBodyGpt;
  let instanceName = variables.searchClientProps.instanceName;
  let url = instanceName + variables.searchClientProps.gptEndPoint;
  if (searchClientType === SC_IDS.KHOROS) {
    searchBodyGpt = Object.fromEntries(new URLSearchParams(dataSentToGptCall.searchBody));
  } else {
    searchBodyGpt = JSON.parse(dataSentToGptCall.searchBody);
  }
  body = {
    query: variables.searchCallVariables.searchString,
    streaming: variables.gptStreaming,
    llm: true,
    separator: variables.STREAM_DELIMITER,
    articles: variables.gptLinks,
    searchBody: searchBodyGpt,
    llmContextId: variables.llmContextIdGpt
  };

  headers.append('uid', variables.searchCallVariables.uid);
  headers.append('search-client-type', variables.searchClientType);
  headers.append('search-id', _gza_analytics_id);

  try {
    headers.append(
      'taid-device',
      typeof _gr_utility_functions !== 'undefined'
        ? _gr_utility_functions.getCookie('_gz_taid')
        : ''
    );
    headers.append(
      'sid-session',
      typeof _gr_utility_functions !== 'undefined' ? _gr_utility_functions.getCookie('_gz_sid') : ''
    );
  } catch (error) {
    console.error('[ error ]', error);
  }

  switch (searchClientType) {
    case SC_IDS.WEB_APP:
    case SC_IDS.KHOROS_AURORA:
    case SC_IDS.ZENDESK_GUIDE:
    case SC_IDS.ZENDESK_SUPPORT:
    case SC_IDS.ZENDESK_SUPPORT_CONSOLE:
    case SC_IDS.NICE_CXONE:
    case SC_IDS.SHAREPOINT:
    case SC_IDS.JOOMLA:
    case SC_IDS.HIGHER_LOGIC_VANILLA:
    case SC_IDS.FRESHSERVICE:
      headers.append('Content-Type', 'application/json');
      headers.append('token', variables.searchCallVariables.accessToken);
      if (searchClientType === SC_IDS.KHOROS_AURORA) {
        searchBodyGpt.accessToken = variables.searchCallVariables.accessToken;
        searchBodyGpt.boardsArr = variables.boardsArr || [];
      }
      body = JSON.stringify(body);
      break;
    case SC_IDS.HIGHER_LOGIC_THRIVE:
      // Higher Logic
      body = JSON.stringify(body);
      break;
    case SC_IDS.WORDPRESS:
    case SC_IDS.DRUPAL_10:
      // Wordpress || Drupal10
      await initializeAuth();
      body = JSON.stringify(body);
      headers.append('Content-Type', 'application/json');
      headers.append('token', jwtBearer);
      break;
    case SC_IDS.JIVE_TILE:
    case SC_IDS.JIVE:
      //Jive
      break;
    case SC_IDS.SALESFORCE_VISUALFORCE_COMMUNITY:
    case SC_IDS.SALESFORCE_VISUALFORCE_INTERNAL:
    case SC_IDS.AEM:
    case SC_IDS.INTRANET_SEARCH:
      await initializeAuth();
      // su_vf_community || su_vf_internal || AEM || chrome extension
      body = JSON.stringify(body);
      headers.append('Content-Type', 'application/json');
      headers.append('token', window.jwtBearer);
      break;
    case SC_IDS.KHOROS:
      headers.append('token', window.scConfiguration.accessToken);
      headers.append('Content-Type', 'application/json');
      body = JSON.stringify(body);
      break;
    case SC_IDS.DRUPAL_7:
      body.streaming = false;
      body = JSON.stringify(body);
      headers.append('token', variables.searchCallVariables.accessToken);
      headers.append('Content-Type', 'application/json');
      url = BASEURL + '/su-gpt';
      break;
    case SC_IDS.MICROSOFT_DYNAMICS:
      // microsoft dynamics
      break;
    case SC_IDS.SERVICENOW:
      // serviceNow
      break;
    case SC_IDS.HOSTED_SEARCH_CLIENT:
      // hosted
      break;
  }
  Object.entries(dataSentToGptCall.searchHeaders).forEach(([key, value]) => {
    if (!headers.has(key)) {
      headers.append(key, value);
    }
  });
  let req = {
    method: 'POST',
    body: body,
    headers: headers,
    redirect: 'follow'
  };
  return { url, req };
}
export async function summarizationUrlReq(variables, { objName, sourceName, docId }) {
  let bodySent;
  let headers = new Headers();
  let searchBodyGpt;
  let instanceName = variables.searchClientProps.instanceName;
  let url = instanceName + variables.searchClientProps.summarizationEndPoint;
  if (variables.searchClientType === SC_IDS.KHOROS) {
    searchBodyGpt = Object.fromEntries(new URLSearchParams(dataSentToGptCall.searchBody));
  } else {
    searchBodyGpt = JSON.parse(dataSentToGptCall.searchBody);
  }
  bodySent = {
    query: variables.searchCallVariables.searchString,
    streaming: true,
    separator: variables.STREAM_DELIMITER,
    type: 'summary',
    object_name: objName,
    index_name: sourceName,
    doc_id: docId,
    search_payload: searchBodyGpt
  };

  headers.append('uid', variables.searchCallVariables.uid);
  headers.append('search-client-type', variables.searchClientType);
  headers.append(
    'token',
    variables.searchCallVariables?.accessToken || window.scConfiguration?.accessToken || ''
  );

  headers.append('Content-Type', 'application/json');
  try {
    headers.append(
      'taid-device',
      typeof _gr_utility_functions !== 'undefined'
        ? _gr_utility_functions.getCookie('_gz_taid')
        : ''
    );
    headers.append(
      'sid-session',
      typeof _gr_utility_functions !== 'undefined' ? _gr_utility_functions.getCookie('_gz_sid') : ''
    );
  } catch (error) {
    console.error('[ error ]', error);
  }
  if (typeof _gza_analytics_id !== 'undefined' && _gza_analytics_id) {
    headers.append('search-id', _gza_analytics_id);
  }

  let req = {
    method: 'POST',
    body: JSON.stringify(bodySent),
    headers: headers,
    redirect: 'follow'
  };
  return { url, req };
}

export async function advertismentCall(searchClientType) {
  let searchString = variables.searchCallVariables.searchString;
  let url =
    searchClientType === SC_IDS.KHOROS_AURORA
      ? `${window.location.origin}/endpoints/searches`
      : variables.searchClientProps.instanceName +
        variables.searchClientProps.adEndpoint +
        searchString;

  let options =
    searchClientType === SC_IDS.KHOROS_AURORA
      ? {
          method: 'GET',
          headers: {
            Action: 'advertisementAurora',
            Instance: variables.searchClientProps.instanceName,
            SearchString: searchString,
            uid: variables.searchCallVariables.uid
          }
        }
      : {};

  return { url, options };
}

export async function refreshJwtToken() {
  let url = `${variables.searchClientProps.instanceName}/saml/refreshJwtToken`;
  let body = {
    uid: variables.searchCallVariables.uid,
    token: window.jwtBearer
  };

  let req = {
    method: 'POST',
    body: JSON.stringify(body),
    headers: {
      'Content-Type': 'application/json'
    }
  };
  return { url, req };
}
export const previewContentUrlReq = (searchClientType, queryPassed) => {
  const instanceName = variables.searchClientProps.instanceName;
  return {
    url: `${instanceName}/search/getPreviewContent`,
    req: {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        searchRequest: variables.searchCallVariables,
        uid: variables.searchCallVariables.uid,
        uniqueField: queryPassed.uniqueField,
        previewType: queryPassed.previewType,
        sourceName: queryPassed.sourceName,
        objName: queryPassed.objName,
        accessToken: variables.searchCallVariables.accessToken
      })
    }
  };
};

export const attachmentPreviewUrlReq = (searchClientType, queryPassed) => {
  const instanceName = variables.searchClientProps.instanceName;
  return {
    url: `${instanceName}/search/getPreviewContent`,
    req: {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        searchRequest: { ...variables.searchCallVariables },
        uid: variables.searchCallVariables.uid,
        uniqueField: queryPassed.uniqueField,
        sourceName: queryPassed.sourceName,
        objName: queryPassed.objName,
        accessToken: variables.searchCallVariables.accessToken,
        attachmentPreview: true,
        attachmentName: queryPassed.attachmentName
      })
    }
  };
};

export default {
  searchUrlReq,
  recomendationsUrlReq,
  sugptUrlReq,
  refreshJwtToken,
  previewContentUrlReq,
  attachmentPreviewUrlReq
};
