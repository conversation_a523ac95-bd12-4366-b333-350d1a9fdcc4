import variables from './variables';
import { autocomplete } from './ducks';
import { searchUrlReq } from './searchClientTypes';
import { SC_IDS } from 'constants/constants';

/**
 * Make search call to backend on the basis of search client type.
 * @param {} queryPassed
 */
export const autocompleteEpic = async (store, action) => {
  const { queryPassed } = action;
  const controller = new AbortController();
  const { signal } = controller;
  // searchClientType SC_IDS.INTRANET_SEARCH Chrome extenstion
  if (queryPassed.searchString == '' && variables.searchClientType != SC_IDS.INTRANET_SEARCH) {
    return store.dispatch(autocomplete.success({}));
  }
  const inputElement =
    document.getElementById('search-box-search') ||
    document.getElementById('search-box-autocomplete');
  const inputListener = () => {
    controller.abort(); // Abort the ongoing fetch request
    inputElement.removeEventListener('input', inputListener); // Remove event listener
  };
  inputElement && inputElement.addEventListener('input', inputListener);
  variables.controllingVariables.processing = true;
  let start = new Date();
  // eslint-disable-next-line no-undef
  let searchUid = GzAnalytics.generate_uuid();
  variables.autocompleteCallVariables.searchUid = searchUid;
  queryPassed.searchUid = searchUid;
  let paramsUrlReq = await searchUrlReq(variables.searchClientType, queryPassed);
  try {
    /** Bypass standard search url with userDefined */
    if (variables.userDefinedAutoCompleteSearchUrl.url) {
      paramsUrlReq = variables.userDefinedAutoCompleteSearchUrl;
    }
    const response = await fetch(paramsUrlReq.url, { ...paramsUrlReq.req, signal });
    variables.controllingVariables.processing = false;
    if (!response.ok) {
      throw Error(response.statusText);
    }
    const resp = response;
    const results = await resp.json();
    variables.searchResposeTimer = new Date() - start;
    if (
      variables.searchClientType == SC_IDS.INTRANET_SEARCH &&
      variables.controllingVariables.firstTimeLoad
    ) {
      window.searchboxResponse = results;
      variables.controllingVariables.firstTimeLoad = false;
    }
    return store.dispatch(autocomplete.success(results));
  } catch (err) {
    return store.dispatch(autocomplete.fail(err));
  }
};

export default autocompleteEpic;
