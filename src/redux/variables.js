/* eslint-disable no-undef */
/* eslint-disable no-undef */

import { SC_IDS } from 'constants/constants';

let sid =
  typeof _gr_utility_functions !== `undefined` ? _gr_utility_functions.getCookie('_gz_taid') : '';
let searchUid = GzAnalytics.generate_uuid();

let variables = {
  searchSource: '',
  facetSearch: false,
  hasError: false,
  STREAM_DELIMITER: '$___$__$_$',
  // searchClientType: toString(SC_IDS.WEB_APP),
  sortingOrder: scConfiguration.default_results_sorting,
  searchClientType: scConfiguration.searchClientTypeId,
  searchCallVariables: {
    langAttr: '',
    react: 1,
    isRecommendationsWidget: false,
    searchString: '',
    from: 0,
    sortby: '_score',
    orderBy: 'desc',
    pageNo: 1,
    aggregations: [],
    clonedAggregations: [],
    // uid : 'b1037a27-5755-11ec-90ee-0242ac130006',
    uid: scConfiguration.uid,
    resultsPerPage: 10,
    exactPhrase: '',
    withOneOrMore: '',
    withoutTheWords: '',
    pageSize: 10,
    sid: sid,
    language: '',
    mergeSources: false,
    versionResults: true,
    showMoreSummary: false,
    minSummaryLength: false,
    suCaseCreate: false,
    visitedtitle: '',
    paginationClicked: false,
    email: window.su_utm || window.user_info?.email || window.email || '',
    storeContext: true,
    searchUid: searchUid
  },
  autocompleteCallVariables: {
    autocomplete: true,
    react: 1,
    searchString: '',
    from: 0,
    sortby: '_score',
    orderBy: 'desc',
    pageNo: 1,
    aggregations: [],
    resultsPerPage: 10,
    exactPhrase: '',
    withOneOrMore: '',
    withoutTheWords: '',
    pageSize: 10,
    sid: sid,
    language: 'en',
    mergeSources: false,
    versionResults: false,
    smartFacetsClicked: false,
    smartFacets:
      typeof _gr_utility_functions !== `undefined`
        ? _gr_utility_functions.getCookie('smartFacets') == '' ||
          _gr_utility_functions.getCookie('smartFacets') == 'true'
        : '', // Add for searchbox only
    email: window.su_utm || window.user_info?.email || '',
    searchUid: searchUid
  },
  userDefinedAutoCompleteSearchUrl: {
    url: '',
    req: { method: 'POST', body: '', headers: { 'Content-Type': 'application/json' } }
  },
  controllingVariables: {
    firstTimeLoad: true,
    processing: false,
    urlState: 0,
    currentUrlState: 0
  },
  toggleDisplayKeys: [
    { key: 'Title', hideEye: false },
    { key: 'Summary', hideEye: false },
    { key: 'Url', hideEye: false },
    { key: 'Metadata', hideEye: false },
    { key: 'Icon', hideEye: false },
    { key: 'Tag', hideEye: false }
  ],
  keepAutoCompleteResultOpenOnScroll: false,
  previousSearches: [],
  hiddenFilters: [],
  storeHiddenFilters: [],
  allSelected: true,
  facetSearchCheck: [],
  selectedStickyFilter: [],
  searchAnalyticsObject: null,
  isFreshSearch: true,
  searchResposeTimer: 0,
  currentClickedOrder: null,
  activeType: 'all',
  filtersInAggr: null,
  resultsInAllContentSources: false,
  searchResultClicked: false,
  visitedtitle: '',
  visitedUrl: '',
  visiteRank: '',
  getUserEmailId: '',
  dateStep: 86400000,
  numberStep: 1,
  isConsoleTypeSC:
    scConfiguration.searchClientTypeId === 16 || scConfiguration.searchClientTypeId === 18
};

variables.searchClientProps = {
  instanceName: scConfiguration.search,
  // instanceName: 'https://integration.searchunify.com/',
  searchEndpoint: '/search/searchResultByPost',
  adEndpoint: '/admin/searchClient/readAdHTML/' + variables.searchCallVariables.uid + '?phrase=',
  gptEndPoint: '/mlService/su-gpt',
  summarizationEndPoint: '/mlService/summary'
};

variables.autocompleteCallVariables.uid = variables.searchCallVariables.uid;

//specific params according to the search client.
if (
  variables.searchClientType == SC_IDS.JIVE ||
  variables.searchClientType == SC_IDS.WEB_APP ||
  variables.searchClientType == SC_IDS.JIVE_TILE ||
  variables.searchClientType == SC_IDS.ZENDESK_GUIDE ||
  variables.searchClientType == SC_IDS.ZENDESK_SUPPORT ||
  variables.searchClientType == SC_IDS.ZENDESK_SUPPORT_CONSOLE ||
  variables.searchClientType == SC_IDS.NICE_CXONE ||
  variables.searchClientType == SC_IDS.SHAREPOINT ||
  variables.searchClientType == SC_IDS.JOOMLA ||
  variables.searchClientType == SC_IDS.HIGHER_LOGIC_VANILLA ||
  variables.searchClientType == SC_IDS.KHOROS_AURORA ||
  variables.searchClientType == SC_IDS.FRESHSERVICE
) {
  variables.searchCallVariables['accessToken'] = scConfiguration.accessToken;
  variables.autocompleteCallVariables['accessToken'] = scConfiguration.accessToken;
}

if (
  variables.searchClientType == SC_IDS.SALESFORCE_VISUALFORCE_INTERNAL ||
  variables.searchClientType == SC_IDS.SALESFORCE_VISUALFORCE_COMMUNITY
) {
  variables.searchCallVariables['email'] = window.user;
  variables.autocompleteCallVariables['email'] = window.user;
}

if (variables.searchClientType == SC_IDS.MICROSOFT_DYNAMICS) {
  delete variables.searchCallVariables['uid'];
  delete variables.autocompleteCallVariables['uid'];
}

if (variables.searchClientType == SC_IDS.INTRANET_SEARCH) {
  variables.autocompleteCallVariables['autocompleteAggregations'] = true;
}

variables.pagerating = {
  instanceName: variables.searchClientProps.instanceName,
  pageratingEndpoint: '/pageRating/getPageRatingData',
  pageratingInstanceEndpoint: '/pageRating/getPageRatingDataInstance'
};
variables.pageratingVariable = {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    uid: variables.searchCallVariables.uid,
    _csrf: localStorage.getItem('_csrf')
  })
};

// Define getSid before using it
const getSid = async () => {
  return typeof _gr_utility_functions !== 'undefined'
    ? await _gr_utility_functions.getExtensionCookie('_gz_taid')
    : '';
};

if (
  window.location.protocol === 'chrome-extension:' &&
  variables.searchClientType == SC_IDS.INTRANET_SEARCH
) {
  getSid()
    .then((sid) => {
      variables.searchCallVariables.sid = sid;
      variables.autocompleteCallVariables.sid = sid;
    })
    .catch((error) => {
      console.error('Error getting SID:', error);
    });
}

export default variables;
