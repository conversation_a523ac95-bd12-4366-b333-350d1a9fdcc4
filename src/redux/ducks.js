import createReducer from './createReducer';
import createAction from './createAction';
import variables from './variables';
import { actionTypeStrings } from 'constants/constants';
export const initialState = {
  inProgress: true,
  error: false,
  searchResult: [],
  updatedSearchResult: [],
  autocomplete: [],
  recommendationsResults: [],
  adHtml: '',
  pageRatingResult: {},
  facetPreferenceResult: [],
  validationError: false,
  firstLoad: true,
  previewContentLoading: false,
  previewContentData: null,
  previewContentError: null,
  attachmentPreviewLoading: false,
  attachmentPreviewData: null,
  attachmentPreviewError: null
};

let val;
let filterSearch;
let start = 0;
export const search = {
  start: (queryPassed) => createAction(actionTypeStrings.SEARCH_START, { queryPassed }),
  fail: (error) => createAction(actionTypeStrings.SEARCH_ERROR, { error }),
  success: (queryPassed) => createAction(actionTypeStrings.SEARCH_SUCCESS, { queryPassed }),
  validationIssue: (validationErrors) =>
    createAction(actionTypeStrings.SEARCH_VALIDATION_ISSUE, { validationErrors })
};

export const autocomplete = {
  start: (queryPassed) => createAction(actionTypeStrings.AUTOCOMPLETE_START, { queryPassed }),
  fail: (error) => createAction(actionTypeStrings.AUTOCOMPLETE_ERROR, { error }),
  success: (queryPassed) => createAction(actionTypeStrings.AUTOCOMPLETE_SUCCESS, { queryPassed })
};

export const recommendations = {
  recommendationsStart: (data) => createAction(actionTypeStrings.RECOMMENDATIONS_START, { data }),
  recommendationsFail: (error) => createAction(actionTypeStrings.RECOMMENDATIONS_ERROR, { error }),
  recommendationsSuccess: (data) =>
    createAction(actionTypeStrings.RECOMMENDATIONS_SUCCESS, { data })
};

export const advertisementSearch = {
  advStart: (data) => createAction(actionTypeStrings.ADVERTISEMENT_START, { data }),
  advFail: (error) => createAction(actionTypeStrings.ADVERTISEMENT_ERROR, { error }),
  advSuccess: (data) => createAction(actionTypeStrings.ADVERTISEMENT_SUCCESS, { data })
};
export const suGPTSearch = {
  start: (data) => createAction(actionTypeStrings.SUGPT_START, { data }),
  fail: (error) => createAction(actionTypeStrings.SUGPT_ERROR, { error }),
  success: (data) => createAction(actionTypeStrings.SUGPT_SUCCESS, { data })
};
export const summarization = {
  start: (data) => createAction('SUMMARIZATION_START', { data }),
  fail: (error) => createAction('SUMMARIZATION_ERROR', { error }),
  success: (data) => createAction('SUMMARIZATION_SUCCESS', { data })
};
export const pageRating = {
  start: (queryPassed) => createAction(actionTypeStrings.PAGERATING_START, { queryPassed }),
  fail: (error) => createAction(actionTypeStrings.PAGERATING_ERROR, { error }),
  success: (queryPassed) => createAction(actionTypeStrings.PAGERATING_SUCCESS, { queryPassed })
};

export const facetPreferenceCall = {
  start: (queryPassed) => createAction(actionTypeStrings.FACETPERFERENCE_START, { queryPassed }),
  fail: (error) => createAction(actionTypeStrings.FACETPERFERENCE_ERROR, { error }),
  success: (queryPassed) => createAction(actionTypeStrings.FACETPERFERENCE_SUCCESS, { queryPassed })
};
export const previewContentSearch = {
  start: (queryPassed) => createAction(actionTypeStrings.PREVIEW_CONTENT_START, { queryPassed }),
  success: (data) => createAction(actionTypeStrings.PREVIEW_CONTENT_SUCCESS, { data }),
  fail: (error) => createAction(actionTypeStrings.PREVIEW_CONTENT_ERROR, { error })
};

export const attachmentPreviewSearch = {
  start: (queryPassed) => createAction(actionTypeStrings.ATTACHMENT_PREVIEW_START, { queryPassed }),
  success: (data) => createAction(actionTypeStrings.ATTACHMENT_PREVIEW_SUCCESS, { data }),
  fail: (error) => createAction(actionTypeStrings.ATTACHMENT_PREVIEW_ERROR, { error })
};
export default createReducer(initialState, {
  [search.start().type]: (state) => {
    start++;
    if (variables.facetSearch) {
      filterSearch = false;
    } else {
      filterSearch = true;
    }
    return {
      ...state,
      inProgress: filterSearch,
      adHtml: ''
    };
  },
  [search.success().type]: (state, { queryPassed }) => {
    start--;
    if (variables.facetSearchCheck.length && variables.searchCallVariables.aggregations.length) {
      const hashMap = new Map();
      variables.searchCallVariables.aggregations.forEach((value) => {
        hashMap.set(value.key, value);
      });
      queryPassed.aggregationsArray.forEach((element) => {
        const value = hashMap.get(element.key);
        if (value) {
          element.values.forEach((item) => {
            let name = item.Contentname || item.displayName;
            if (item.selected && variables.facetSearchCheck.includes(name)) {
              let backup = element.values[element.values.indexOf(item)];
              element.values.splice(element.values.indexOf(item), 1);
              element.values.unshift(backup);
            }
          });
        }
      });
    }
    variables.facetSearch = false;
    if (queryPassed?.searchClientSettings?.autoCompleteInstant) {
      val = Boolean(start);
    } else {
      val = false;
    }

    return {
      ...state,
      inProgress: val,
      searchResult: queryPassed,
      error: false,
      validationError: false,
      firstLoad: false
    };
  },
  [search.fail().type]: (state) => {
    return {
      ...state,
      inProgress: false,
      error: true,
      validationError: false,
      firstLoad: false
    };
  },
  [search.validationIssue().type]: (state) => {
    return {
      ...state,
      inProgress: false,
      error: false,
      validationError: true,
      firstLoad: false
    };
  },
  [autocomplete.start().type]: (state) => ({
    ...state
  }),
  [autocomplete.success().type]: (state, { queryPassed }) => ({
    ...state,
    autocomplete: queryPassed
  }),
  [suGPTSearch.start().type]: (state) => ({
    ...state
  }),
  [suGPTSearch.success().type]: (state, { data }) => ({
    ...state,
    suGPTStream: data
  }),
  [summarization.start().type]: (state) => ({
    ...state
  }),
  [summarization.success().type]: (state, { data }) => ({
    ...state,
    summarizationResult: data
  }),
  [advertisementSearch.advStart().type]: (state) => ({
    ...state
  }),
  [advertisementSearch.advSuccess().type]: (state, { data }) => ({
    ...state,
    adHtml: data
  }),
  [recommendations.recommendationsStart().type]: (state) => ({
    ...state
  }),
  [recommendations.recommendationsSuccess().type]: (state, { data }) => ({
    ...state,
    recommendationsResults: data
  }),
  [pageRating.start().type]: (state) => ({
    ...state
  }),
  [pageRating.success().type]: (state, { queryPassed }) => ({
    ...state,
    pageRatingResult: queryPassed
  }),
  [facetPreferenceCall.start().type]: (state) => ({
    ...state
  }),
  [facetPreferenceCall.success().type]: (state, { queryPassed }) => ({
    ...state,
    facetPreferenceResult: queryPassed
  }),
  // Add these cases to your existing createReducer
  [previewContentSearch.start().type]: (state) => ({
    ...state,
    previewContentLoading: true,
    previewContentError: null
  }),
  [previewContentSearch.success().type]: (state, { data }) => ({
    ...state,
    previewContentLoading: false,
    previewContentData: data,
    previewContentError: null
  }),
  [previewContentSearch.fail().type]: (state, { error }) => ({
    ...state,
    previewContentLoading: false,
    previewContentData: null,
    previewContentError: error
  }),
  [attachmentPreviewSearch.start().type]: (state) => ({
    ...state,
    attachmentPreviewLoading: true,
    attachmentPreviewError: null
  }),
  [attachmentPreviewSearch.success().type]: (state, { data }) => ({
    ...state,
    attachmentPreviewLoading: false,
    attachmentPreviewData: data,
    attachmentPreviewError: null
  }),
  [attachmentPreviewSearch.fail().type]: (state, { error }) => ({
    ...state,
    attachmentPreviewLoading: false,
    attachmentPreviewData: null,
    attachmentPreviewError: error
  })
});
