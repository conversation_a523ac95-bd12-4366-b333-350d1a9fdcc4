import { actionTypeStrings } from 'constants/constants';
import {
  pageRating,
  advertisementSearch,
  suGPTSearch,
  facetPreferenceCall,
  recommendations,
  previewContentSearch,
  attachmentPreviewSearch,
  summarization
} from '../redux/ducks';
import {
  advertismentCall,
  sugptUrlReq,
  searchUrlReq,
  recomendationsUrlReq,
  previewContentUrlReq,
  attachmentPreviewUrlReq,
  summarizationUrlReq
} from '../redux/searchClientTypes';
import variables from './variables';

/**
 * handleApiRequest The function checks action type and stores fetch parameters for API request in paramsUrlReq
 * and then proceed for fetch call, gets the response and then call success action by checking action type
 * @param {*} store The redux store instance
 * @param {*} action contains type of action dispatched
 */
export const handleApiRequest = async (store, action) => {
  const valueToUse = action.queryPassed || action.data;
  let paramsUrlReq;
  const { type } = action;

  const apiCallMap = {
    [actionTypeStrings.ADVERTISEMENT_START]: () => advertismentCall(variables.searchClientType),
    [actionTypeStrings.PAGERATING_START]: () => ({
      url: variables.pagerating.instanceName + variables.pagerating.pageratingEndpoint,
      req: variables.pageratingVariable
    }),
    [actionTypeStrings.SUGPT_START]: () => sugptUrlReq(variables.searchClientType, variables),
    [actionTypeStrings.FACETPERFERENCE_START]: () =>
      searchUrlReq(variables.searchClientType, valueToUse),
    [actionTypeStrings.RECOMMENDATIONS_START]: () =>
      recomendationsUrlReq(variables.searchClientType, valueToUse),
    [actionTypeStrings.PREVIEW_CONTENT_START]: () =>
      previewContentUrlReq(variables.searchClientType, valueToUse),
    [actionTypeStrings.ATTACHMENT_PREVIEW_START]: () =>
      attachmentPreviewUrlReq(variables.searchClientType, valueToUse),
    [actionTypeStrings.SUMMARIZATION_START]: () => summarizationUrlReq(variables, valueToUse)
  };

  const successActionMap = {
    [actionTypeStrings.ADVERTISEMENT_START]: advertisementSearch.advSuccess,
    [actionTypeStrings.PAGERATING_START]: pageRating.success,
    [actionTypeStrings.SUGPT_START]: suGPTSearch.success,
    [actionTypeStrings.FACETPERFERENCE_START]: facetPreferenceCall.success,
    [actionTypeStrings.RECOMMENDATIONS_START]: recommendations.recommendationsSuccess,
    [actionTypeStrings.PREVIEW_CONTENT_START]: previewContentSearch.success,
    [actionTypeStrings.ATTACHMENT_PREVIEW_START]: attachmentPreviewSearch.success,
    [actionTypeStrings.SUMMARIZATION_START]: summarization.success
  };

  const failActionMap = {
    [actionTypeStrings.ADVERTISEMENT_START]: advertisementSearch.advFail,
    [actionTypeStrings.PAGERATING_START]: pageRating.fail,
    [actionTypeStrings.SUGPT_START]: suGPTSearch.fail,
    [actionTypeStrings.FACETPERFERENCE_START]: facetPreferenceCall.fail,
    [actionTypeStrings.RECOMMENDATIONS_START]: recommendations.recommendationsFail,
    [actionTypeStrings.PREVIEW_CONTENT_START]: previewContentSearch.fail,
    [actionTypeStrings.ATTACHMENT_PREVIEW_START]: attachmentPreviewSearch.fail,
    [actionTypeStrings.SUMMARIZATION_START]: summarization.fail
  };

  const apiCall = apiCallMap[type];
  const successAction = successActionMap[type];
  const failAction = failActionMap[type];

  if (!apiCall) {
    console.error('Unknown action type:', type);
    return;
  }
  try {
    paramsUrlReq = await apiCall();
    const response = await fetch(paramsUrlReq.url, paramsUrlReq.req);
    let results;
    if (type !== actionTypeStrings.SUGPT_START && type !== actionTypeStrings.SUMMARIZATION_START) {
      results = await response.json();
    }
    store.dispatch(
      successAction(
        type === actionTypeStrings.SUGPT_START || type === actionTypeStrings.SUMMARIZATION_START
          ? response
          : results
      )
    );
  } catch (error) {
    if (failAction) {
      store.dispatch(failAction(error));
    } else {
      console.error('Unknown action type:', type);
    }
  }
};
