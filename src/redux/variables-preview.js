/* eslint-disable no-undef */
import { SC_IDS } from 'constants/constants';

let variables = {
  searchSource: '',
  hasError: false,
  searchClientType: toString(SC_IDS.WEB_APP),
  searchCallVariables: {
    react: 1,
    searchString: '',
    from: 0,
    sortby: '_score',
    orderBy: 'desc',
    pageNo: 1,
    aggregations: [],
    uid: scConfiguration.uid,
    resultsPerPage: 10,
    exactPhrase: '',
    withOneOrMore: '',
    withoutTheWords: '',
    pageSize: 10,
    sid: '',
    language: 'en',
    mergeSources: false,
    versionResults: true,
    showMoreSummary: false,
    minSummaryLength: false,
    suCaseCreate: false
  },
  autocompleteCallVariables: {
    autocomplete: true,
    react: 1,
    accessToken: scConfiguration.accessToken,
    searchString: '',
    from: 0,
    sortby: '_score',
    orderBy: 'desc',
    pageNo: 1,
    aggregations: [],
    resultsPerPage: 10,
    exactPhrase: '',
    withOneOrMore: '',
    withoutTheWords: '',
    pageSize: 10,
    sid: '',
    language: 'en',
    mergeSources: false,
    versionResults: false
  },
  userDefinedAutoCompleteSearchUrl: {
    url: '',
    req: { method: 'POST', body: '', headers: { 'Content-Type': 'application/json' } }
  },
  controllingVariables: {
    firstTimeLoad: true,
    processing: false,
    urlState: 0,
    currentUrlState: 0
  },
  toggleDisplayKeys: [
    { key: 'Title', hideEye: false },
    { key: 'Summary', hideEye: false },
    { key: 'Url', hideEye: false },
    { key: 'Metadata', hideEye: false },
    { key: 'Icon', hideEye: false },
    { key: 'Tag', hideEye: false }
  ],
  previousSearches: [],
  hiddenFilters: [],
  storeHiddenFilters: [],
  allSelected: true,
  facetSearchCheck: [],
  selectedStickyFilter: [],
  searchAnalyticsObject: null,
  isFreshSearch: true,
  searchResposeTimer: 0,
  currentClickedOrder: null,
  activeType: 'all',
  filtersInAggr: null,
  resultsInAllContentSources: false
};

variables.searchClientProps = {
  instanceName: scConfiguration.search,
  searchEndpoint: '/search/searchResultByPost',
  adEndpoint: '/admin/searchClient/readAdHTML/' + variables.searchCallVariables.uid + '?phrase='
};

variables.autocompleteCallVariables.uid = variables.searchCallVariables.uid;

//specific params according to the search client.
if (
  variables.searchClientType == SC_IDS.JIVE ||
  variables.searchClientType == SC_IDS.WEB_APP ||
  variables.searchClientType == SC_IDS.JIVE_TILE ||
  variables.searchClientType == SC_IDS.ZENDESK_GUIDE ||
  variables.searchClientType == SC_IDS.ZENDESK_SUPPORT ||
  variables.searchClientType == SC_IDS.ZENDESK_SUPPORT_CONSOLE ||
  variables.searchClientType == SC_IDS.NICE_CXONE ||
  variables.searchClientType == SC_IDS.SHAREPOINT ||
  variables.searchClientType == SC_IDS.JOOMLA
) {
  variables.searchCallVariables['accessToken'] = scConfiguration.accessToken;
}

if (
  variables.searchClientType == SC_IDS.SALESFORCE_VISUALFORCE_INTERNAL ||
  variables.searchClientType == SC_IDS.SALESFORCE_VISUALFORCE_COMMUNITY ||
  variables.searchClientType == SC_IDS.SALESFORCE_SERVICE_CONSOLE
) {
  variables.searchCallVariables['email'] = window.user;
}

if (variables.searchClientType == SC_IDS.MICROSOFT_DYNAMICS) {
  delete variables.searchCallVariables['uid'];
}

variables.pagerating = {
  instanceName: variables.searchClientProps.instanceName,
  pageratingEndpoint: '/pageRating/getPageRatingData',
  pageratingInstanceEndpoint: '/pageRating/getPageRatingDataInstance'
};
variables.pageratingVariable = {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    uid: variables.searchCallVariables.uid,
    _csrf: localStorage.getItem('_csrf')
  })
};

export default variables;
