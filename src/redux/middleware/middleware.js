import { searchEpic } from '../epic';
import { autocompleteEpic } from '../autoCompleteEpic';
import { handleApiRequest } from 'redux/apiMiddlewareHelper';
import { actionTypeStrings } from 'constants/constants';
/**
 * middleware is a Redux middleware that allows handling different actions with different functions.
 * It intercepts actions and dispatches them to the appropriate handler function based on their type.
 * @param {object} store - The Redux store instance.
 * @returns {function} A middleware function that takes `next` and `action` as arguments.
 */
const middleware = (store) => (next) => async (action) => {
  switch (action.type) {
    case actionTypeStrings.SEARCH_START:
      searchEpic(store, action);
      break;
    case actionTypeStrings.AUTOCOMPLETE_START:
      autocompleteEpic(store, action);
      break;
    case actionTypeStrings.ADVERTISEMENT_START:
    case actionTypeStrings.PAGERATING_START:
    case actionTypeStrings.FACETPERFERENCE_START:
    case actionTypeStrings.RECOMMENDATIONS_START:
    case actionTypeStrings.SUGPT_START:
    case actionTypeStrings.PREVIEW_CONTENT_START:
    case actionTypeStrings.ATTACHMENT_PREVIEW_START:
    case actionTypeStrings.SUMMARIZATION_START:
      handleApiRequest(store, action);
      break;
    default:
      return next(action);
  }
  return next(action);
};
export default middleware;
