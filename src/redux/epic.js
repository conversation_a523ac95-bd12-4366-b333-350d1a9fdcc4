/* eslint-disable no-undef */
import variables from './variables';
import { search } from './ducks';
import utilityMethods from './utilities/utility-methods';
import { searchUrlReq } from './searchClientTypes';
import searchtrack from '../analytics/search';
import { SC_IDS, STATUS_CODES } from 'constants/constants';

/**
 * Make search call to backend on the basis of search client type.
 * @param {} queryPassed
 */
const htmlDecode = function (input) {
  let e = document.createElement('div');
  e.innerHTML = input;
  return e.childNodes.length === 0 ? '' : e.childNodes[0].nodeValue;
};
export const searchEpic = async (store, action) => {
  const { queryPassed } = action;
  queryPassed['getAutoTunedResult'] = true;
  queryPassed['getSimilarSearches'] = true;
  variables.searchCallVariables.searchString = queryPassed.searchString;
  if (variables.controllingVariables.firstTimeLoad && !variables.controllingVariables.processing) {
    utilityMethods.importQueryParamsToVariables();
  }
  variables.controllingVariables.firstTimeLoad = false;
  if (!variables.onpopstate) {
    utilityMethods.updateURLParams();
  }
  variables.onpopstate = false;
  variables.searchCallVariables = JSON.parse(JSON.stringify(variables.searchCallVariables));
  variables.controllingVariables.processing = true;
  let filterAggregations = variables.searchCallVariables.aggregations;
  if (filterAggregations.length) {
    filterAggregations = filterAggregations.filter(function (filter, index) {
      if (filter.filter) {
        return filterAggregations[index];
      } else {
        return false;
      }
    });
  }

  queryPassed.mergeSources = filterAggregations.length == 0;
  queryPassed.versionResults = Boolean(queryPassed.mergeSources);
  queryPassed.suCaseCreate = Boolean(variables.searchCallVariables.suCaseCreate);
  queryPassed.storeContext = Boolean(variables.searchCallVariables.storeContext);
  let start = new Date();
  const maxRetries = 1; // Maximum number of retries
  let retryCount = 0;
  let searchUid = GzAnalytics.generate_uuid();
  variables.searchCallVariables.searchUid = searchUid;
  queryPassed.searchUid = searchUid;
  const executeSearchRequest = async () => {
    let paramsUrlReq = await searchUrlReq(variables.searchClientType, queryPassed);
    const response = await fetch(paramsUrlReq.url, paramsUrlReq.req);
    variables.controllingVariables.processing = false;
    if (response.status === 422) {
      if (retryCount <= maxRetries) {
        retryCount++;
        return window
          .cleanUpCookies(queryPassed.uid)
          .then((cleanupResult) => {
            console.log('Cleanup result:', cleanupResult);
            return executeSearchRequest();
          })
          .catch((cleanupError) => {
            console.error('Cleanup error:', cleanupError);
            return store.dispatch(search.fail('error in cookie cleanup'));
          });
      } else {
        console.log('Max retries reached');
        return store.dispatch(search.fail('error in search: max retries reached'));
      }
    }
    if (!response.ok) {
      if (response.status === STATUS_CODES.AUTH_FAILED.statusCode) {
        return store.dispatch(search.validationIssue('validation issue in search'));
      }
      throw Error(response.statusText);
    }
    const results = await response.json();
    return results;
  };

  // eslint-disable-next-line no-undef
  try {
    const results = await executeSearchRequest();
    if (
      results.message === STATUS_CODES.AUTH_EXPIRED.message &&
      results.statusCode === STATUS_CODES.AUTH_EXPIRED.statusCode
    ) {
      return store.dispatch(search.success(results));
    } else if (results.statusCode === STATUS_CODES.AUTH_FAILED.statusCode) {
      return store.dispatch(search.validationIssue('validation issue in search'));
    } else if (results.statusCode != STATUS_CODES.AUTH_SUCCESS.statusCode) {
      return store.dispatch(search.fail('error in search'));
    }
    for (let i of results.aggregationsArray) {
      for (let j of i.values) {
        if (j.Contentname) {
          j.ContentnameFrontend = htmlDecode(j.Contentname);
        }
      }
    }
    variables.searchResposeTimer = new Date() - start;
    searchtrack(results); /** search analytics tracking */
    if (variables.searchClientType === SC_IDS.KHOROS) {
      if (results.result.hits.length !== 0) {
        for (let i = 0; i < results.result.hits.length; i++) {
          let lastIndex = results.result.hits.length - 1;
          let checkLithiumResults = results.result.hits.filter(function (item) {
            if (item.boardName !== '') {
              return true;
            }
          });
          let lastIndexOfLithiumResult = checkLithiumResults.pop();
          if (results.result.hits[i].boardName) {
            let getSearchResultsLiveCountsUrl =
              window.su_community_live_count +
              '?postId=' +
              (results.result.hits[i].ID || results.result.hits[i]._id);
            let getSearchResultsLiveCountsReq = {
              method: 'GET'
            };
            if (results.result.hits[i].Id !== lastIndexOfLithiumResult.Id) {
              fetch(getSearchResultsLiveCountsUrl, getSearchResultsLiveCountsReq)
                .then((response) => response.json())
                .then((data) => {
                  results.result.hits[i].liveCounts = {};
                  results.result.hits[i].liveCounts = data;
                })
                .catch((error) => {
                  console.error('Error:', error);
                });
            } else {
              return fetch(getSearchResultsLiveCountsUrl, getSearchResultsLiveCountsReq)
                .then((response) => response.json())
                .then((data) => {
                  results.result.hits[i].liveCounts = {};
                  results.result.hits[i].liveCounts = data;
                  return store.dispatch(search.success(results));
                })
                .catch((error) => {
                  console.error('Error:', error);
                });
            }
          } else {
            if (i === lastIndex) {
              return store.dispatch(search.success(results));
            }
          }
        }
      } else {
        return store.dispatch(search.success(results));
      }
    } else {
      return store.dispatch(search.success(results));
    }
  } catch (err) {
    if (variables.searchClientType == SC_IDS.KHOROS) {
      window.location.href =
        't5/forums/searchpage/tab/message/search?standard=true&q=' + queryPassed.searchString;
    } else {
      return store.dispatch(search.fail(err));
    }
  }
};
export default searchEpic;
