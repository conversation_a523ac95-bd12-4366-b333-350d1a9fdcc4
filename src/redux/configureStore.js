import { createStore, applyMiddleware } from 'redux';

import rootReducer from './ducks';
import middleware from './middleware/middleware';

export default () => {
  const store = createStore(
    rootReducer,
    // eslint-disable-next-line no-underscore-dangle
    // window.__REDUX_DEVTOOLS_EXTENSION__ && window.__REDUX_DEVTOOLS_EXTENSION__(),
    // applyMiddleware(epicMiddleware)
    applyMiddleware(middleware)
  );
  return store;
};
