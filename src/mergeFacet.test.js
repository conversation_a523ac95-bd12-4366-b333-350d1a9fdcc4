/* global jest, describe, it, expect */
import { mergeFilters, mergeFilterClicked, traverseTheTree, facetClickNested } from './mergeFacet';
import variables from '__mocks__/variables';
import { SC_IDS } from 'constants/constants';

jest.mock('./redux/variables', () => require('__mocks__/variables'));

describe('Filters Utilities', () => {
  describe('mergeFilters', () => {
    it('should do nothing if no matching facet is found', () => {
      const h = { facetName: 'typeA', filterList: ['A'] };
      const aggrData = [{ key: 'otherType', values: [] }];

      variables.searchCallVariables = { aggregations: [] };

      mergeFilters(h, aggrData, 'keyword');

      expect(aggrData[0].merged).toBeUndefined();
    });

    it('should merge filters and inject merged facet', () => {
      const h = {
        facetName: 'category',
        filterList: ['Books', 'Videos'],
        filterDisplayName: ['Books', 'Videos'],
        filterNewName: 'My Selection',
        showChild: true
      };

      const aggrData = [
        {
          key: 'category',
          values: [
            { Contentname: 'Books', value: 10 },
            { Contentname: 'Videos', value: 20 }
          ],
          sort: 'term_asc'
        }
      ];

      variables.searchCallVariables = {
        aggregations: [{ type: 'category', filter: ['Books', 'Videos'] }]
      };

      mergeFilters(h, aggrData, 'keyword');

      const mergedFacet = aggrData[0].values.find((v) => v.Contentname.startsWith('merged_'));
      expect(mergedFacet).toBeDefined();
      expect(mergedFacet.childArray).toHaveLength(2);
      expect(mergedFacet.selected).toBe(true);
    });
    it('should set value as null when isSearched is true', () => {
      const h = {
        facetName: 'topic',
        filterList: ['NodeJS'],
        filterDisplayName: ['NodeJS'],
        filterNewName: 'Backend',
        showChild: true
      };

      const aggrData = [
        {
          key: 'topic',
          values: [{ Contentname: 'NodeJS', value: 10 }]
        }
      ];

      variables.searchCallVariables = {
        aggregations: [{ type: 'topic', filter: ['NodeJS'] }]
      };

      mergeFilters(h, aggrData, 'aggKey', true);

      const merged = aggrData[0].values.find((v) => v.Contentname.startsWith('merged_'));
      expect(merged.value).toBeNull(); // <-- branch for `isSearched === true`
    });
    it('should set indeterminateFlag true when only partial filters match', () => {
      const h = {
        facetName: 'type',
        filterList: ['PDF', 'DOCX'],
        filterDisplayName: ['PDF', 'DOCX'],
        filterNewName: 'Docs',
        showChild: true
      };

      const aggrData = [
        {
          key: 'type',
          values: [
            { Contentname: 'PDF', value: 5 },
            { Contentname: 'DOCX', value: 15 }
          ]
        }
      ];

      variables.searchCallVariables = {
        aggregations: [
          { type: 'type', filter: ['PDF'] } // Only one matches
        ]
      };

      mergeFilters(h, aggrData, 'xyz');

      const merged = aggrData[0].values.find((v) => v.Contentname.startsWith('merged_'));
      expect(merged.selected).toBe(false);
      expect(aggrData[0].indeterminateFlag[merged.Contentname]).toBe(true);
    });
  });

  describe('mergeFilterClicked', () => {
    it('should add children to aggregation filter when checked', () => {
      const aggrFilter = [];
      const childArray = [
        {
          Contentname: 'merged_category',
          childArray: [{ Contentname: 'Books' }, { Contentname: 'Videos' }]
        }
      ];

      mergeFilterClicked('merged_category', aggrFilter, childArray, true);

      expect(aggrFilter).toEqual(['Books', 'Videos']);
    });

    it('should remove children from filter when unchecked', () => {
      const aggrFilter = ['Books', 'Videos'];
      const childArray = [
        {
          Contentname: 'merged_category',
          childArray: [{ Contentname: 'Books' }, { Contentname: 'Videos' }]
        }
      ];

      mergeFilterClicked('merged_category', aggrFilter, childArray, false);

      expect(aggrFilter).toEqual([]);
    });
  });

  describe('traverseTheTree', () => {
    it('should push unrelated selected children into result array', () => {
      const childArray = [
        {
          Contentname: 'Parent',
          selected: false,
          childArray: [
            {
              Contentname: 'Child1',
              selected: true,
              path: ['Child1'],
              level: 2
            },
            {
              Contentname: 'Child2',
              selected: false
            }
          ]
        }
      ];

      const filtersInAggr = [];
      const childrenArr = [];

      traverseTheTree(childArray, 'ChildX', ['Parent'], filtersInAggr, childrenArr);

      expect(childrenArr).toEqual([
        {
          childName: 'Child1',
          level: 2,
          path: ['Child1']
        }
      ]);
    });
  });

  describe('facetClickNested', () => {
    it('should push a new facet into aggregations when no match exists', () => {
      variables.searchCallVariables = { aggregations: [] };
      variables.searchClientType = SC_IDS.KHOROS;

      const searchResult = {
        aggregationsArray: [
          {
            key: 'category_navigation',
            order: 1,
            values: []
          }
        ]
      };

      const filter = {
        Contentname: 'Books',
        selected: false
      };

      facetClickNested(filter, '', 1, 2, ['parent'], searchResult);

      expect(variables.searchCallVariables.aggregations).toHaveLength(1);
      expect(variables.searchCallVariables.aggregations[0].type).toBe('category_navigation');
    });
    it('should remove filter from aggregation if already selected', () => {
      const filter = {
        Contentname: 'PDF',
        selected: true
      };
      variables.searchCallVariables = {
        aggregations: [{ type: 'type', filter: ['PDF'] }]
      };

      const searchResult = {
        aggregationsArray: [
          {
            key: 'type',
            order: 0,
            values: []
          }
        ]
      };
      facetClickNested(filter, '', 0, 1, [], searchResult);
      const updated = variables.searchCallVariables.aggregations;
      expect(updated).toHaveLength(0);
    });
  });
});
