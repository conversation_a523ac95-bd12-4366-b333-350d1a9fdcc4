// Entry file for running the accessibility scanner
// Injects axe-core, recursively opens modal triggers, and generates an HTML report

const puppeteer = require('puppeteer');
const { buildHtmlReport, writeHtmlReport, runAccessibilityEvaluation } = require('./__tools');
const constants = require('./__constants');

// Configuration: controls execution flow and scan depth
const Config = {
  debug: true, // Enable logging and element highlight
  timeout: {
    firstLoad: 5000,
    pageLoad: 2000, // ms to wait after page load
    selector: 1000, // ms to wait for trigger selectors
    domIdleMax: 3000, // 🆕 Max time to wait for DOM idle
    domIdleSettle: 500 // 🆕 Time of no DOM changes before resolving
  },
  max_depth: 4, // Max nested modal depth
  urls: {
    Home_Page: constants.HOME_PAGE
  },
  attributes: {
    trigger: 'data-trigger-a11y', // Attribute used to locate elements that open modals or dropdowns when clicked
    name: 'data-name-a11y', // Logical name to identify the trigger (used in reports and de-duplication)
    timeout: 'data-timeout-a11y' // Optional per-trigger wait time (in ms) after click before scanning the opened content
  },
  skip: {
    urls: [], // URLs to skip scanning
    triggers: ['settings-mobile'], // Modal triggers to skip by name
    selectors: [] // Modal containers to skip
  }
};

// === Puppeteer Launch ===
(async () => {
  // Launch browser with recommended flags for headless automation
  const browser = await puppeteer.launch({
    headless: false, // true for CI / automation
    args: [
      '--no-sandbox', // Required for some environments
      '--disable-setuid-sandbox', // Improves compatibility
      '--start-maximized', // Fullscreen for consistent layout rendering
      '--window-size=1920,1080' // Ensures consistent rendering dimensions
    ],
    defaultViewport: null // Disable default viewport to match window-size
  });

  const page = await browser.newPage();

  // Evaluate page recursively and collect accessibility results
  const allResults = await runAccessibilityEvaluation(Config, page);

  // Render and write the HTML report
  const htmlReport = buildHtmlReport(allResults);
  writeHtmlReport(htmlReport);

  await browser.close();
})();
