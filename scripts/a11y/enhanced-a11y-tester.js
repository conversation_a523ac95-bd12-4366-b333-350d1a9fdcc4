// Enhanced WCAG AA Accessibility Testing Script
// Extends the existing axe-core testing with manual-like testing capabilities
// Includes zoom testing, keyboard navigation, contrast inspection, and focus management

require('puppeteer');
const fs = require('fs');
const path = require('path');
const { buildHtmlReport, runAccessibilityEvaluation } = require('./__tools');
const constants = require('./__constants');

// Enhanced configuration for comprehensive testing
const EnhancedConfig = {
  debug: true,
  timeout: {
    firstLoad: 5000,
    pageLoad: 2000,
    selector: 1000,
    domIdleMax: 3000,
    domIdleSettle: 500,
    keyboardNav: 500, // Time between keyboard navigation steps
    modalAnimation: 3000 // Wait time for modal animations to settle
  },
  max_depth: 4,
  urls: {
    Home_Page: constants.HOME_PAGE
  },
  attributes: {
    trigger: 'data-trigger-a11y',
    name: 'data-name-a11y',
    timeout: 'data-timeout-a11y'
  },
  skip: {
    urls: [],
    triggers: ['settings-mobile'],
    selectors: []
  },
  // Enhanced testing configuration
  enhanced: {
    zoomLevels: [100, 200, 400], // Zoom levels to test (400% is WCAG requirement)
    contrastThreshold: {
      normal: 4.5, // WCAG AA normal text
      large: 3.0 // WCAG AA large text (18pt+ or 14pt+ bold)
    },
    keyboardTestDepth: 50, // Maximum tab stops to test
    screenshotOnIssue: true // Take screenshots when issues are found
  }
};

/**
 * Enhanced accessibility testing class that performs comprehensive WCAG AA testing
 */
class EnhancedA11yTester {
  constructor(config, page) {
    this.config = config;
    this.page = page;
    this.enhancedResults = {
      zoomIssues: [],
      keyboardIssues: [],
      contrastIssues: [],
      focusIssues: [],
      modalIssues: []
    };
  }

  /**
   * Step 1: Review baseline automated report
   * Parse existing axe-core results for reference
   */
  async reviewBaselineReport(axeResults) {
    console.log('\n🔍 Step 1: Reviewing baseline axe-core results...');

    const violationSummary = {};
    for (const [, groupResults] of Object.entries(axeResults.sections)) {
      if (Array.isArray(groupResults)) {
        groupResults.forEach((item) => {
          if (item.results.violations && item.results.violations.length > 0) {
            item.results.violations.forEach((violation) => {
              if (!violationSummary[violation.id]) {
                violationSummary[violation.id] = {
                  count: 0,
                  impact: violation.impact,
                  description: violation.description
                };
              }
              violationSummary[violation.id].count += violation.nodes.length;
            });
          }
        });
      }
    }

    console.log(
      `📊 Baseline violations found: ${Object.keys(violationSummary).length} unique issues`
    );
    return violationSummary;
  }

  /**
   * Step 2: Launch and prepare browser context
   */
  async prepareBrowserContext() {
    console.log('\n🚀 Step 2: Preparing browser context...');

    // Set standard desktop viewport
    await this.page.setViewport({ width: 1920, height: 1080 });

    // Navigate to home page and wait for load
    await this.page.goto(this.config.urls.Home_Page, {
      waitUntil: 'domcontentloaded',
      timeout: this.config.timeout.firstLoad
    });

    // Wait for DOM to stabilize
    await this.page.waitForTimeout(this.config.timeout.pageLoad);

    console.log('✅ Browser context prepared');
  }

  /**
   * Step 3: Zoom testing at 400%
   */
  async performZoomTesting() {
    console.log('\n🔍 Step 3: Performing zoom testing...');

    for (const zoomLevel of this.config.enhanced.zoomLevels) {
      if (zoomLevel === 100) continue; // Skip default zoom

      console.log(`  Testing at ${zoomLevel}% zoom...`);

      // Set zoom level
      await this.page.evaluate((zoom) => {
        document.body.style.zoom = `${zoom}%`;
      }, zoomLevel);

      // Wait for layout to settle
      await this.page.waitForTimeout(1000);

      // Check for zoom-related issues
      const zoomIssues = await this.page.evaluate((zoom) => {
        const issues = [];

        // Check for horizontal scrollbar
        if (document.documentElement.scrollWidth > window.innerWidth) {
          issues.push({
            type: 'horizontal-scroll',
            zoom: zoom,
            description: 'Horizontal scrollbar appeared at zoom level',
            wcagCriteria: '1.4.4 Resize Text'
          });
        }

        // Check for clipped content
        const elements = document.querySelectorAll('*');
        elements.forEach((el, index) => {
          if (index > 100) return; // Limit check to avoid performance issues

          const rect = el.getBoundingClientRect();
          if (rect.right > window.innerWidth || rect.bottom > window.innerHeight) {
            if (el.offsetParent && getComputedStyle(el).overflow !== 'hidden') {
              issues.push({
                type: 'content-clipped',
                zoom: zoom,
                element:
                  el.tagName +
                  (el.id ? `#${el.id}` : '') +
                  (el.className ? `.${el.className.split(' ')[0]}` : ''),
                description: 'Content extends beyond viewport',
                wcagCriteria: '1.4.4 Resize Text'
              });
            }
          }
        });

        return issues;
      }, zoomLevel);

      this.enhancedResults.zoomIssues.push(...zoomIssues);

      if (zoomIssues.length > 0 && this.config.enhanced.screenshotOnIssue) {
        await this.takeScreenshot(`zoom-issues-${zoomLevel}percent`);
      }
    }

    // Reset zoom
    await this.page.evaluate(() => {
      document.body.style.zoom = '100%';
    });

    console.log(`✅ Zoom testing complete. Found ${this.enhancedResults.zoomIssues.length} issues`);
  }

  /**
   * Step 4: Keyboard-only navigation testing
   */
  async performKeyboardNavigation() {
    console.log('\n⌨️  Step 4: Performing keyboard navigation testing...');

    // Start from the beginning of the page
    await this.page.evaluate(() => {
      // Remove focus from any currently focused element
      if (document.activeElement) {
        document.activeElement.blur();
      }
      // Focus on the first focusable element or body
      const firstFocusable = document.querySelector(
        'a, button, input, select, textarea, [tabindex]:not([tabindex="-1"])'
      );
      if (firstFocusable) {
        firstFocusable.focus();
      } else {
        document.body.focus();
      }
    });

    const focusPath = [];
    let previousElement = null;
    let trapDetected = false;
    let trapCount = 0;

    for (let i = 0; i < this.config.enhanced.keyboardTestDepth; i++) {
      // Press Tab key
      await this.page.keyboard.press('Tab');
      await this.page.waitForTimeout(this.config.timeout.keyboardNav);

      // Get current focused element info
      const currentFocus = await this.page.evaluate(() => {
        const el = document.activeElement;
        if (!el || el === document.body) return null;

        const rect = el.getBoundingClientRect();
        const styles = getComputedStyle(el);

        return {
          tagName: el.tagName,
          id: el.id || '',
          className: el.className || '',
          selector:
            el.tagName.toLowerCase() +
            (el.id ? `#${el.id}` : '') +
            (el.className ? `.${el.className.split(' ')[0]}` : ''),
          visible:
            rect.width > 0 &&
            rect.height > 0 &&
            styles.visibility !== 'hidden' &&
            styles.display !== 'none',
          hasOutline:
            styles.outline !== 'none' && styles.outline !== '0px' && styles.outline !== '',
          hasBoxShadow: styles.boxShadow !== 'none',
          hasBorder: styles.border !== 'none' && styles.borderWidth !== '0px',
          inViewport:
            rect.top >= 0 &&
            rect.left >= 0 &&
            rect.bottom <= window.innerHeight &&
            rect.right <= window.innerWidth
        };
      });

      if (!currentFocus) break; // No more focusable elements

      focusPath.push(currentFocus);

      // Check for focus visibility issues
      if (!currentFocus.hasOutline && !currentFocus.hasBoxShadow && !currentFocus.hasBorder) {
        this.enhancedResults.focusIssues.push({
          type: 'no-focus-indicator',
          element: currentFocus.selector,
          description: 'Element lacks visible focus indicator',
          wcagCriteria: '2.4.7 Focus Visible'
        });
      }

      // Check for keyboard trap
      if (previousElement && previousElement.selector === currentFocus.selector) {
        trapCount++;
        if (trapCount > 2) {
          trapDetected = true;
          this.enhancedResults.keyboardIssues.push({
            type: 'keyboard-trap',
            element: currentFocus.selector,
            description: 'Focus appears to be trapped on this element',
            wcagCriteria: '2.1.2 No Keyboard Trap'
          });
          break;
        }
      } else {
        trapCount = 0;
      }

      previousElement = currentFocus;
    }

    console.log(`✅ Keyboard navigation complete. Tested ${focusPath.length} focusable elements`);
    console.log(
      `   Found ${this.enhancedResults.keyboardIssues.length} keyboard issues and ${this.enhancedResults.focusIssues.length} focus issues`
    );
  }

  /**
   * Step 5: Visual contrast inspection
   */
  async performContrastInspection() {
    console.log('\n🎨 Step 5: Performing contrast inspection...');

    const contrastIssues = await this.page.evaluate((thresholds) => {
      const issues = [];

      // Helper function to calculate relative luminance
      function getLuminance(r, g, b) {
        const [rs, gs, bs] = [r, g, b].map((c) => {
          c = c / 255;
          return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
        });
        return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs;
      }

      // Helper function to calculate contrast ratio
      function getContrastRatio(color1, color2) {
        const lum1 = getLuminance(...color1);
        const lum2 = getLuminance(...color2);
        const brightest = Math.max(lum1, lum2);
        const darkest = Math.min(lum1, lum2);
        return (brightest + 0.05) / (darkest + 0.05);
      }

      // Helper function to parse RGB color
      function parseColor(colorStr) {
        const canvas = document.createElement('canvas');
        canvas.width = canvas.height = 1;
        const ctx = canvas.getContext('2d');
        ctx.fillStyle = colorStr;
        ctx.fillRect(0, 0, 1, 1);
        const [r, g, b] = ctx.getImageData(0, 0, 1, 1).data;
        return [r, g, b];
      }

      // Check text elements for contrast
      const textElements = document.querySelectorAll(
        'p, span, div, h1, h2, h3, h4, h5, h6, a, button, label, input, textarea'
      );

      textElements.forEach((el, index) => {
        if (index > 200) return; // Limit to avoid performance issues

        const styles = getComputedStyle(el);
        const fontSize = parseFloat(styles.fontSize);
        const fontWeight = styles.fontWeight;

        // Determine if text is large (18pt+ or 14pt+ bold)
        const isLargeText =
          fontSize >= 18 ||
          (fontSize >= 14 && (fontWeight === 'bold' || parseInt(fontWeight) >= 700));
        const threshold = isLargeText ? thresholds.large : thresholds.normal;

        try {
          const textColor = parseColor(styles.color);
          const bgColor = parseColor(styles.backgroundColor);

          // Skip if background is transparent
          if (
            styles.backgroundColor === 'rgba(0, 0, 0, 0)' ||
            styles.backgroundColor === 'transparent'
          ) {
            return;
          }

          const contrastRatio = getContrastRatio(textColor, bgColor);

          if (contrastRatio < threshold) {
            issues.push({
              type: 'low-contrast',
              element:
                el.tagName.toLowerCase() +
                (el.id ? `#${el.id}` : '') +
                (el.className ? `.${el.className.split(' ')[0]}` : ''),
              contrastRatio: contrastRatio.toFixed(2),
              threshold: threshold,
              textColor: styles.color,
              backgroundColor: styles.backgroundColor,
              fontSize: fontSize,
              isLargeText: isLargeText,
              description: `Text contrast ratio ${contrastRatio.toFixed(
                2
              )}:1 is below WCAG AA threshold of ${threshold}:1`,
              wcagCriteria: '1.4.3 Contrast (Minimum)'
            });
          }
        } catch (e) {
          // Skip elements where color parsing fails
        }
      });

      return issues;
    }, this.config.enhanced.contrastThreshold);

    this.enhancedResults.contrastIssues.push(...contrastIssues);

    console.log(`✅ Contrast inspection complete. Found ${contrastIssues.length} contrast issues`);
  }

  /**
   * Helper function to take screenshots for documentation
   */
  async takeScreenshot(filename) {
    if (!this.config.enhanced.screenshotOnIssue) return;

    const screenshotDir = path.join(process.cwd(), 'coverage', 'a11y_screenshots');
    fs.mkdirSync(screenshotDir, { recursive: true });

    const screenshotPath = path.join(screenshotDir, `${filename}-${Date.now()}.png`);
    await this.page.screenshot({ path: screenshotPath, fullPage: true });

    console.log(`📸 Screenshot saved: ${screenshotPath}`);
  }

  /**
   * Generate enhanced findings report
   */
  generateEnhancedReport(baselineResults) {
    console.log('\n📊 Generating enhanced accessibility report...');

    const enhancedReport = {
      meta: {
        ...baselineResults.meta,
        enhancedTestingEnabled: true,
        testDate: new Date().toISOString(),
        zoomLevelsTested: this.config.enhanced.zoomLevels,
        keyboardNavigationDepth: this.config.enhanced.keyboardTestDepth
      },
      baseline: baselineResults.sections,
      enhanced: this.enhancedResults,
      summary: {
        baselineViolations: this.countBaselineViolations(baselineResults),
        enhancedIssues: {
          zoom: this.enhancedResults.zoomIssues.length,
          keyboard: this.enhancedResults.keyboardIssues.length,
          focus: this.enhancedResults.focusIssues.length,
          contrast: this.enhancedResults.contrastIssues.length,
          modal: this.enhancedResults.modalIssues.length
        },
        totalIssues: this.countTotalIssues(baselineResults)
      }
    };

    return enhancedReport;
  }

  /**
   * Count baseline violations from axe-core results
   */
  countBaselineViolations(results) {
    let count = 0;
    for (const [, groupResults] of Object.entries(results.sections)) {
      if (Array.isArray(groupResults)) {
        groupResults.forEach((item) => {
          if (item.results.violations) {
            item.results.violations.forEach((violation) => {
              count += violation.nodes.length;
            });
          }
        });
      }
    }
    return count;
  }

  /**
   * Count total issues including enhanced testing
   */
  countTotalIssues(baselineResults) {
    const baselineCount = this.countBaselineViolations(baselineResults);
    const enhancedCount = Object.values(this.enhancedResults).reduce(
      (sum, issues) => sum + issues.length,
      0
    );
    return baselineCount + enhancedCount;
  }

  /**
   * Step 6: Dynamic content and focus management testing
   */
  async performModalFocusTesting() {
    console.log('\n🔄 Step 6: Testing modal focus management...');

    // Find all modal triggers
    const modalTriggers = await this.page.evaluate((triggerAttr) => {
      const triggers = document.querySelectorAll(`[${triggerAttr}]`);
      return Array.from(triggers).map((trigger) => ({
        selector:
          trigger.tagName.toLowerCase() +
          (trigger.id ? `#${trigger.id}` : '') +
          (trigger.className ? `.${trigger.className.split(' ')[0]}` : ''),
        name: trigger.getAttribute('data-name-a11y') || 'unnamed-trigger',
        triggerSelector: trigger.getAttribute(triggerAttr)
      }));
    }, this.config.attributes.trigger);

    for (const trigger of modalTriggers.slice(0, 5)) {
      // Limit to first 5 modals
      try {
        console.log(`  Testing modal: ${trigger.name}`);

        // Click to open modal
        await this.page.click(trigger.selector);
        await this.page.waitForTimeout(this.config.timeout.modalAnimation);

        // Check if modal opened and has proper focus management
        const modalIssues = await this.page.evaluate((triggerSelector) => {
          const issues = [];
          const modal = document.querySelector(`#${triggerSelector}`);

          if (!modal) {
            issues.push({
              type: 'modal-not-found',
              description: 'Modal element not found after trigger click',
              wcagCriteria: '2.4.3 Focus Order'
            });
            return issues;
          }

          // Check if modal has proper ARIA attributes
          if (
            !modal.getAttribute('role') ||
            !['dialog', 'alertdialog'].includes(modal.getAttribute('role'))
          ) {
            issues.push({
              type: 'missing-modal-role',
              element: `#${triggerSelector}`,
              description: 'Modal lacks proper ARIA role (dialog or alertdialog)',
              wcagCriteria: '4.1.2 Name, Role, Value'
            });
          }

          // Check if modal has aria-labelledby or aria-label
          if (!modal.getAttribute('aria-labelledby') && !modal.getAttribute('aria-label')) {
            issues.push({
              type: 'missing-modal-label',
              element: `#${triggerSelector}`,
              description: 'Modal lacks accessible name (aria-labelledby or aria-label)',
              wcagCriteria: '4.1.2 Name, Role, Value'
            });
          }

          // Check if focus is trapped within modal
          const focusableElements = modal.querySelectorAll(
            'a, button, input, select, textarea, [tabindex]:not([tabindex="-1"])'
          );
          if (focusableElements.length === 0) {
            issues.push({
              type: 'no-focusable-elements',
              element: `#${triggerSelector}`,
              description: 'Modal contains no focusable elements',
              wcagCriteria: '2.1.1 Keyboard'
            });
          }

          return issues;
        }, trigger.triggerSelector);

        this.enhancedResults.modalIssues.push(...modalIssues);

        // Test keyboard navigation within modal
        await this.testModalKeyboardNavigation(trigger.triggerSelector);

        // Close modal (try ESC key first, then click outside)
        await this.page.keyboard.press('Escape');
        await this.page.waitForTimeout(500);

        // Check if modal closed
        const modalClosed = await this.page.evaluate((triggerSelector) => {
          const modal = document.querySelector(`#${triggerSelector}`);
          return !modal || getComputedStyle(modal).display === 'none' || !modal.offsetParent;
        }, trigger.triggerSelector);

        if (!modalClosed) {
          // Try clicking outside modal
          await this.page.click('body');
          await this.page.waitForTimeout(500);
        }
      } catch (error) {
        console.log(`  ⚠️ Error testing modal ${trigger.name}: ${error.message}`);
      }
    }

    console.log(
      `✅ Modal focus testing complete. Found ${this.enhancedResults.modalIssues.length} modal issues`
    );
  }

  /**
   * Test keyboard navigation within a modal
   */
  async testModalKeyboardNavigation(modalSelector) {
    const focusIssues = await this.page.evaluate((selector) => {
      const issues = [];
      const modal = document.querySelector(`#${selector}`);
      if (!modal) return issues;

      const focusableElements = modal.querySelectorAll(
        'a, button, input, select, textarea, [tabindex]:not([tabindex="-1"])'
      );

      // Simulate tab navigation within modal
      let currentIndex = 0;
      focusableElements.forEach((el) => {
        el.focus();

        // Check if element has visible focus indicator
        const styles = getComputedStyle(el);
        if (
          styles.outline === 'none' &&
          styles.boxShadow === 'none' &&
          !styles.border.includes('2px')
        ) {
          issues.push({
            type: 'modal-focus-indicator',
            element: el.tagName.toLowerCase() + (el.id ? `#${el.id}` : ''),
            description: 'Focusable element in modal lacks visible focus indicator',
            wcagCriteria: '2.4.7 Focus Visible'
          });
        }
      });

      return issues;
    }, modalSelector);

    this.enhancedResults.focusIssues.push(...focusIssues);
  }

  /**
   * Step 7: Combined conditions testing (zoom + keyboard)
   */
  async performCombinedConditionsTesting() {
    console.log('\n🔄 Step 7: Testing combined conditions (400% zoom + keyboard navigation)...');

    // Set 400% zoom
    await this.page.evaluate(() => {
      document.body.style.zoom = '400%';
    });

    await this.page.waitForTimeout(1000);

    // Perform limited keyboard navigation at 400% zoom
    const combinedIssues = [];

    for (let i = 0; i < 10; i++) {
      // Limited test at zoom
      await this.page.keyboard.press('Tab');
      await this.page.waitForTimeout(this.config.timeout.keyboardNav);

      const focusIssue = await this.page.evaluate(() => {
        const el = document.activeElement;
        if (!el || el === document.body) return null;

        const rect = el.getBoundingClientRect();

        // Check if focused element is visible at 400% zoom
        if (
          rect.right > window.innerWidth ||
          rect.bottom > window.innerHeight ||
          rect.left < 0 ||
          rect.top < 0
        ) {
          return {
            type: 'focus-not-visible-at-zoom',
            element: el.tagName.toLowerCase() + (el.id ? `#${el.id}` : ''),
            description: 'Focused element not fully visible at 400% zoom',
            wcagCriteria: '1.4.4 Resize Text, 2.4.7 Focus Visible'
          };
        }

        return null;
      });

      if (focusIssue) {
        combinedIssues.push(focusIssue);
      }
    }

    this.enhancedResults.focusIssues.push(...combinedIssues);

    // Reset zoom
    await this.page.evaluate(() => {
      document.body.style.zoom = '100%';
    });

    console.log(
      `✅ Combined conditions testing complete. Found ${combinedIssues.length} additional issues`
    );
  }

  /**
   * Main execution method
   */
  async runEnhancedTesting() {
    console.log('🚀 Starting Enhanced WCAG AA Accessibility Testing...\n');

    try {
      // Step 1: Run baseline axe-core testing
      console.log('Running baseline axe-core testing...');
      const baselineResults = await runAccessibilityEvaluation(this.config, this.page);

      // Step 1: Review baseline report
      await this.reviewBaselineReport(baselineResults);

      // Step 2: Prepare browser context
      await this.prepareBrowserContext();

      // Step 3: Zoom testing
      await this.performZoomTesting();

      // Step 4: Keyboard navigation
      await this.performKeyboardNavigation();

      // Step 5: Contrast inspection
      await this.performContrastInspection();

      // Step 6: Modal focus testing
      await this.performModalFocusTesting();

      // Step 7: Combined conditions testing
      await this.performCombinedConditionsTesting();

      // Step 8: Generate enhanced report
      const enhancedReport = this.generateEnhancedReport(baselineResults);

      // Step 9: Write enhanced HTML report
      await this.writeEnhancedHtmlReport(enhancedReport);

      console.log('\n✅ Enhanced accessibility testing completed successfully!');
      console.log(`📊 Total issues found: ${enhancedReport.summary.totalIssues}`);
      console.log(`   - Baseline (axe-core): ${enhancedReport.summary.baselineViolations}`);
      console.log(
        `   - Enhanced testing: ${Object.values(enhancedReport.summary.enhancedIssues).reduce(
          (a, b) => a + b,
          0
        )}`
      );

      return enhancedReport;
    } catch (error) {
      console.error('❌ Enhanced testing failed:', error);
      throw error;
    }
  }

  /**
   * Write enhanced HTML report combining baseline and enhanced results
   */
  async writeEnhancedHtmlReport(enhancedReport) {
    const reportDir = path.join(process.cwd(), 'coverage', 'a11y_report');
    fs.mkdirSync(reportDir, { recursive: true });

    // Generate enhanced HTML report
    const htmlContent = this.buildEnhancedHtmlReport(enhancedReport);

    const reportPath = path.join(reportDir, 'enhanced-a11y-report.html');
    fs.writeFileSync(reportPath, htmlContent);

    console.log(`📄 Enhanced accessibility report saved: ${reportPath}`);
  }

  /**
   * Build enhanced HTML report with both baseline and enhanced results
   */
  buildEnhancedHtmlReport(enhancedReport) {
    const { meta, baseline, enhanced, summary } = enhancedReport;

    let html = `
  <!DOCTYPE html>
  <html lang="en">
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced WCAG AA Accessibility Report</title>
    <style>
      body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
      .header { background: #2c3e50; color: white; padding: 20px; border-radius: 8px; margin-bottom: 30px; }
      .summary { background: #ecf0f1; padding: 20px; border-radius: 8px; margin-bottom: 30px; }
      .section { margin-bottom: 40px; }
      .issue-card { border: 1px solid #ddd; border-radius: 8px; padding: 15px; margin-bottom: 15px; }
      .critical { border-left: 5px solid #e74c3c; }
      .serious { border-left: 5px solid #f39c12; }
      .moderate { border-left: 5px solid #f1c40f; }
      .minor { border-left: 5px solid #27ae60; }
      .enhanced { border-left: 5px solid #9b59b6; }
      table { border-collapse: collapse; width: 100%; margin-bottom: 20px; }
      th, td { border: 1px solid #ddd; padding: 12px; text-align: left; }
      th { background-color: #34495e; color: white; }
      tr:nth-child(even) { background-color: #f9f9f9; }
      .wcag-ref { font-size: 0.9em; color: #7f8c8d; font-style: italic; }
      .issue-count { font-weight: bold; color: #e74c3c; }
      pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
    </style>
  </head>
  <body>
    <div class="header">
      <h1>🔍 Enhanced WCAG AA Accessibility Report</h1>
      <p>Comprehensive accessibility testing combining automated axe-core scanning with manual-like testing procedures</p>
      <p><strong>Test Date:</strong> ${meta.testDate}</p>
      <p><strong>URL:</strong> ${meta.url}</p>
    </div>

    <div class="summary">
      <h2>📊 Executive Summary</h2>
      <table>
        <tr><th>Test Category</th><th>Issues Found</th><th>Description</th></tr>
        <tr><td>Baseline (axe-core)</td><td class="issue-count">${summary.baselineViolations}</td><td>Automated WCAG AA violations detected</td></tr>
        <tr><td>Zoom Testing (400%)</td><td class="issue-count">${summary.enhancedIssues.zoom}</td><td>Layout and usability issues at 400% zoom</td></tr>
        <tr><td>Keyboard Navigation</td><td class="issue-count">${summary.enhancedIssues.keyboard}</td><td>Keyboard accessibility and navigation issues</td></tr>
        <tr><td>Focus Management</td><td class="issue-count">${summary.enhancedIssues.focus}</td><td>Focus visibility and management issues</td></tr>
        <tr><td>Color Contrast</td><td class="issue-count">${summary.enhancedIssues.contrast}</td><td>Text contrast ratio violations</td></tr>
        <tr><td>Modal Accessibility</td><td class="issue-count">${summary.enhancedIssues.modal}</td><td>Modal dialog accessibility issues</td></tr>
        <tr style="background-color: #e8f5e8;"><td><strong>Total Issues</strong></td><td class="issue-count">${summary.totalIssues}</td><td><strong>All accessibility issues combined</strong></td></tr>
      </table>
    </div>`;

    // Add enhanced testing results sections
    html += this.buildEnhancedIssuesSection(
      '🔍 Zoom Testing Issues (WCAG 1.4.4)',
      enhanced.zoomIssues
    );
    html += this.buildEnhancedIssuesSection(
      '⌨️ Keyboard Navigation Issues (WCAG 2.1.1, 2.1.2)',
      enhanced.keyboardIssues
    );
    html += this.buildEnhancedIssuesSection(
      '👁️ Focus Management Issues (WCAG 2.4.7)',
      enhanced.focusIssues
    );
    html += this.buildEnhancedIssuesSection(
      '🎨 Color Contrast Issues (WCAG 1.4.3)',
      enhanced.contrastIssues
    );
    html += this.buildEnhancedIssuesSection(
      '🔄 Modal Accessibility Issues (WCAG 4.1.2)',
      enhanced.modalIssues
    );

    // Add baseline axe-core results
    html += '<div class="section"><h2>🤖 Baseline axe-core Results</h2>';
    html += buildHtmlReport({ meta, sections: baseline })
      .replace(
        /.*<tbody>/s,
        '<table><thead><tr><th>Group > Screen</th><th>ID</th><th>Impact</th><th>Description</th><th>Help URL</th><th>Nodes Affected</th></tr></thead><tbody>'
      )
      .replace(/<\/body>.*$/s, '</table>');
    html += '</div>';

    html += `
    <div class="section">
      <h2>📋 Remediation Recommendations</h2>
      <div class="issue-card">
        <h3>Priority Actions</h3>
        <ol>
          <li><strong>Fix Critical axe-core Violations:</strong> Address all critical and serious violations found by automated testing</li>
          <li><strong>Implement Proper Focus Management:</strong> Ensure all interactive elements have visible focus indicators</li>
          <li><strong>Test at 400% Zoom:</strong> Verify all content remains accessible and usable at maximum zoom levels</li>
          <li><strong>Keyboard Navigation:</strong> Test and fix keyboard-only navigation paths</li>
          <li><strong>Color Contrast:</strong> Ensure all text meets WCAG AA contrast requirements (4.5:1 normal, 3:1 large text)</li>
          <li><strong>Modal Accessibility:</strong> Implement proper ARIA attributes and focus trapping for all modals</li>
        </ol>
      </div>
    </div>

    <div class="section">
      <h2>🔧 Testing Methodology</h2>
      <p>This report combines automated axe-core testing with enhanced manual-like testing procedures:</p>
      <ul>
        <li><strong>Automated Testing:</strong> axe-core WCAG AA rule evaluation</li>
        <li><strong>Zoom Testing:</strong> UI testing at ${meta.zoomLevelsTested?.join(
          '%, '
        )}% zoom levels</li>
        <li><strong>Keyboard Testing:</strong> Tab navigation through ${
          meta.keyboardNavigationDepth
        } focusable elements</li>
        <li><strong>Contrast Analysis:</strong> Programmatic color contrast ratio calculation</li>
        <li><strong>Modal Testing:</strong> Focus management and ARIA attribute validation</li>
      </ul>
    </div>
  </body>
  </html>`;

    return html;
  }

  /**
   * Build HTML section for enhanced testing issues
   */
  buildEnhancedIssuesSection(title, issues) {
    if (issues.length === 0) {
      return `<div class="section"><h2>${title}</h2><p style="color: #27ae60;">✅ No issues found in this category.</p></div>`;
    }

    let html = `<div class="section"><h2>${title}</h2>`;

    issues.forEach((issue, index) => {
      html += `
      <div class="issue-card enhanced">
        <h4>Issue #${index + 1}: ${issue.type
        .replace(/-/g, ' ')
        .replace(/\b\w/g, (l) => l.toUpperCase())}</h4>
        <p><strong>Description:</strong> ${issue.description}</p>
        ${issue.element ? `<p><strong>Element:</strong> <code>${issue.element}</code></p>` : ''}
        ${
          issue.contrastRatio
            ? `<p><strong>Contrast Ratio:</strong> ${issue.contrastRatio}:1 (Required: ${issue.threshold}:1)</p>`
            : ''
        }
        ${issue.zoom ? `<p><strong>Zoom Level:</strong> ${issue.zoom}%</p>` : ''}
        <p class="wcag-ref"><strong>WCAG Criteria:</strong> ${issue.wcagCriteria}</p>
      </div>`;
    });

    html += '</div>';
    return html;
  }
}

module.exports = { EnhancedA11yTester, EnhancedConfig };
