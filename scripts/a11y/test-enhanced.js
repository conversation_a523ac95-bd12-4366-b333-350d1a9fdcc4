#!/usr/bin/env node

/**
 * Test script for Enhanced WCAG AA Accessibility Testing
 * 
 * This script validates that the enhanced testing framework is working correctly
 * by running a quick test and checking for expected functionality.
 */

const puppeteer = require('puppeteer');
const { EnhancedA11yTester, EnhancedConfig } = require('./enhanced-a11y-tester');

async function testEnhancedFramework() {
  console.log('🧪 Testing Enhanced Accessibility Framework');
  console.log('==========================================\n');
  
  let browser;
  let page;
  
  try {
    // Launch browser
    console.log('🌐 Launching test browser...');
    browser = await puppeteer.launch({
      headless: true, // Use headless for testing
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    page = await browser.newPage();
    
    // Create a simple test page
    console.log('📄 Creating test page...');
    await page.setContent(`
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Accessibility Test Page</title>
        <style>
          body { font-family: Arial, sans-serif; padding: 20px; }
          .low-contrast { color: #999; background: #ccc; padding: 10px; }
          .modal { display: none; position: fixed; top: 50%; left: 50%; 
                   transform: translate(-50%, -50%); background: white; 
                   border: 2px solid #333; padding: 20px; z-index: 1000; }
          .modal.open { display: block; }
          .no-focus { outline: none !important; }
          button { margin: 10px; padding: 10px; }
        </style>
      </head>
      <body>
        <h1>Test Page for Enhanced A11y Testing</h1>
        
        <!-- Test elements for different issues -->
        <button id="btn1">Accessible Button</button>
        <button id="btn2" class="no-focus">Button Without Focus</button>
        
        <div class="low-contrast">
          This text has low contrast and should be flagged
        </div>
        
        <button 
          data-trigger-a11y="test-modal"
          data-name-a11y="test-modal-trigger"
          onclick="document.getElementById('test-modal').classList.add('open')">
          Open Test Modal
        </button>
        
        <div id="test-modal" class="modal" role="dialog" aria-labelledby="modal-title">
          <h2 id="modal-title">Test Modal</h2>
          <p>This is a test modal for accessibility testing.</p>
          <button onclick="document.getElementById('test-modal').classList.remove('open')">Close</button>
        </div>
        
        <div style="width: 2000px; height: 100px; background: #f0f0f0;">
          Wide content that might cause horizontal scroll at zoom
        </div>
        
        <script>
          // Add axe-core for testing
          const script = document.createElement('script');
          script.src = 'https://unpkg.com/axe-core@4.10.3/axe.min.js';
          document.head.appendChild(script);
        </script>
      </body>
      </html>
    `);
    
    // Wait for axe to load
    await page.waitForTimeout(2000);
    
    // Test configuration with reduced scope for quick testing
    const testConfig = {
      ...EnhancedConfig,
      enhanced: {
        ...EnhancedConfig.enhanced,
        zoomLevels: [100, 400], // Test only 400% zoom
        keyboardTestDepth: 10,  // Limit keyboard testing
        screenshotOnIssue: false // Disable screenshots for testing
      },
      timeout: {
        ...EnhancedConfig.timeout,
        modalAnimation: 1000 // Faster for testing
      }
    };
    
    // Create enhanced tester
    console.log('🔧 Initializing enhanced tester...');
    const enhancedTester = new EnhancedA11yTester(testConfig, page);
    
    // Test individual components
    console.log('🔍 Testing zoom functionality...');
    await enhancedTester.performZoomTesting();
    console.log(`   Found ${enhancedTester.enhancedResults.zoomIssues.length} zoom issues`);
    
    console.log('⌨️  Testing keyboard navigation...');
    await enhancedTester.performKeyboardNavigation();
    console.log(`   Found ${enhancedTester.enhancedResults.keyboardIssues.length} keyboard issues`);
    console.log(`   Found ${enhancedTester.enhancedResults.focusIssues.length} focus issues`);
    
    console.log('🎨 Testing contrast inspection...');
    await enhancedTester.performContrastInspection();
    console.log(`   Found ${enhancedTester.enhancedResults.contrastIssues.length} contrast issues`);
    
    console.log('🔄 Testing modal functionality...');
    await enhancedTester.performModalFocusTesting();
    console.log(`   Found ${enhancedTester.enhancedResults.modalIssues.length} modal issues`);
    
    // Generate test report
    console.log('📊 Generating test report...');
    const mockBaselineResults = {
      meta: {
        testEngine: { name: 'axe-core', version: '4.10.3' },
        testRunner: { name: 'axe' },
        testEnvironment: {
          userAgent: 'Test Environment',
          windowWidth: 1920,
          windowHeight: 1080,
          orientationAngle: 0,
          orientationType: 'landscape-primary'
        },
        timestamp: new Date().toISOString(),
        url: 'test://localhost'
      },
      sections: {
        'Test Page': [{
          name: 'test-page',
          results: {
            violations: []
          }
        }]
      }
    };
    
    const enhancedReport = enhancedTester.generateEnhancedReport(mockBaselineResults);
    
    // Validate results
    console.log('\n✅ VALIDATION RESULTS');
    console.log('=====================');
    
    const totalEnhancedIssues = Object.values(enhancedReport.summary.enhancedIssues)
      .reduce((sum, count) => sum + count, 0);
    
    console.log(`Total Enhanced Issues Found: ${totalEnhancedIssues}`);
    console.log(`├─ Zoom Issues: ${enhancedReport.summary.enhancedIssues.zoom}`);
    console.log(`├─ Keyboard Issues: ${enhancedReport.summary.enhancedIssues.keyboard}`);
    console.log(`├─ Focus Issues: ${enhancedReport.summary.enhancedIssues.focus}`);
    console.log(`├─ Contrast Issues: ${enhancedReport.summary.enhancedIssues.contrast}`);
    console.log(`└─ Modal Issues: ${enhancedReport.summary.enhancedIssues.modal}`);
    
    // Expected results validation
    const expectedIssues = {
      contrast: 1, // Low contrast text
      focus: 1,    // Button without focus indicator
      zoom: 1,     // Wide content causing horizontal scroll
    };
    
    let validationPassed = true;
    
    if (enhancedReport.summary.enhancedIssues.contrast < expectedIssues.contrast) {
      console.log('❌ Contrast testing may not be working correctly');
      validationPassed = false;
    }
    
    if (enhancedReport.summary.enhancedIssues.focus < expectedIssues.focus) {
      console.log('❌ Focus testing may not be working correctly');
      validationPassed = false;
    }
    
    if (enhancedReport.summary.enhancedIssues.zoom < expectedIssues.zoom) {
      console.log('❌ Zoom testing may not be working correctly');
      validationPassed = false;
    }
    
    if (validationPassed) {
      console.log('\n🎉 All tests passed! Enhanced framework is working correctly.');
      return 0;
    } else {
      console.log('\n⚠️  Some tests may need attention. Check the validation results above.');
      return 1;
    }
    
  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    
    if (testConfig?.debug) {
      console.error('Full error:', error);
    }
    
    return 1;
    
  } finally {
    if (page) await page.close();
    if (browser) await browser.close();
  }
}

// Run test if called directly
if (require.main === module) {
  testEnhancedFramework()
    .then(exitCode => {
      console.log(`\nTest completed with exit code: ${exitCode}`);
      process.exit(exitCode);
    })
    .catch(error => {
      console.error('Fatal test error:', error);
      process.exit(1);
    });
}

module.exports = { testEnhancedFramework };
