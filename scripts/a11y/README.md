# Enhanced WCAG AA Accessibility Testing

This directory contains a comprehensive accessibility testing framework that goes beyond automated axe-core scanning to include manual-like testing procedures that simulate human expert accessibility audits.

## 🎯 Overview

The enhanced testing system performs:

1. **Baseline Automated Testing** - Standard axe-core WCAG AA rule evaluation
2. **400% Zoom Testing** - Layout and usability testing at maximum zoom levels
3. **Keyboard-Only Navigation** - Complete keyboard accessibility evaluation
4. **Visual Contrast Inspection** - Programmatic color contrast analysis
5. **Modal Focus Management** - Dialog accessibility and focus trapping
6. **Combined Conditions Testing** - Testing interactions between different accessibility features

## 🚀 Quick Start

### Run Enhanced Testing

```bash
# Run comprehensive enhanced accessibility testing
npm run a11y:enhanced

# Run standard axe-core testing only
npm run a11y
```

### Prerequisites

- Node.js 14+ 
- Your application running on `http://localhost:5000`
- Puppeteer dependencies installed

## 📊 Testing Methodology

### Step 1: Baseline Automated Report
- Runs existing axe-core accessibility scanner
- Identifies standard WCAG AA violations
- Provides foundation for enhanced testing

### Step 2: Browser Context Preparation
- Launches <PERSON><PERSON>peteer with accessibility-optimized settings
- Sets standard desktop viewport (1920x1080)
- Prepares page for comprehensive testing

### Step 3: Zoom Testing at 400%
Tests compliance with **WCAG 1.4.4 Resize Text**:
- ✅ Content remains readable and functional at 400% zoom
- ✅ No horizontal scrolling introduced
- ✅ No content clipping or overlap
- ✅ All interactive elements remain accessible

### Step 4: Keyboard-Only Navigation
Tests compliance with **WCAG 2.1.1 Keyboard** and **2.1.2 No Keyboard Trap**:
- ✅ All interactive elements reachable via keyboard
- ✅ Logical tab order (top-to-bottom, left-to-right)
- ✅ No keyboard traps that prevent navigation
- ✅ Visible focus indicators on all focusable elements

### Step 5: Visual Contrast Inspection
Tests compliance with **WCAG 1.4.3 Contrast (Minimum)**:
- ✅ Normal text: 4.5:1 contrast ratio minimum
- ✅ Large text (18pt+ or 14pt+ bold): 3.0:1 contrast ratio minimum
- ✅ Programmatic color analysis beyond axe-core detection

### Step 6: Modal Focus Management
Tests compliance with **WCAG 4.1.2 Name, Role, Value** and **2.4.3 Focus Order**:
- ✅ Proper ARIA roles (dialog/alertdialog)
- ✅ Accessible names (aria-labelledby/aria-label)
- ✅ Focus trapping within modals
- ✅ Focus restoration when modals close
- ✅ Keyboard navigation within modals

### Step 7: Combined Conditions Testing
Tests interaction between accessibility features:
- ✅ Keyboard navigation at 400% zoom
- ✅ Focus visibility at high zoom levels
- ✅ Modal accessibility under zoom conditions

## 📄 Report Generation

The enhanced testing generates comprehensive HTML reports with:

### Executive Summary
- Total issues found across all testing categories
- Breakdown by test type (baseline, zoom, keyboard, etc.)
- Priority recommendations

### Detailed Issue Documentation
- **WCAG Criteria References** - Links to specific success criteria
- **Element Selectors** - Exact DOM elements with issues
- **Remediation Guidance** - Specific steps to fix each issue
- **Screenshots** - Visual documentation of issues (when enabled)

### Testing Methodology Documentation
- Complete description of testing procedures
- Configuration details and test parameters
- Reproducible testing steps

## ⚙️ Configuration

### Basic Configuration (`EnhancedConfig`)

```javascript
const EnhancedConfig = {
  debug: true,                    // Enable detailed logging
  timeout: {
    modalAnimation: 3000,         // Wait for modal animations
    keyboardNav: 500,            // Delay between keyboard steps
    // ... other timeouts
  },
  enhanced: {
    zoomLevels: [100, 200, 400], // Zoom levels to test
    contrastThreshold: {
      normal: 4.5,               // WCAG AA normal text
      large: 3.0                 // WCAG AA large text
    },
    keyboardTestDepth: 50,       // Max tab stops to test
    screenshotOnIssue: true      // Capture screenshots
  }
};
```

### Modal Testing Configuration

Add data attributes to your HTML elements:

```html
<!-- Modal trigger -->
<button 
  data-trigger-a11y="modal-selector-id"
  data-name-a11y="settings-modal"
  data-timeout-a11y="3000">
  Open Settings
</button>

<!-- Modal container -->
<div id="modal-selector-id" role="dialog" aria-labelledby="modal-title">
  <h2 id="modal-title">Settings</h2>
  <!-- Modal content -->
</div>
```

## 🔧 Customization

### Adding Custom Tests

Extend the `EnhancedA11yTester` class:

```javascript
class CustomA11yTester extends EnhancedA11yTester {
  async performCustomTest() {
    // Your custom accessibility test
    const customIssues = await this.page.evaluate(() => {
      // Custom accessibility checks
      return [];
    });
    
    this.enhancedResults.customIssues = customIssues;
  }
}
```

### Modifying Test Parameters

```javascript
// Custom zoom levels
EnhancedConfig.enhanced.zoomLevels = [100, 150, 200, 300, 400];

// Custom contrast thresholds
EnhancedConfig.enhanced.contrastThreshold = {
  normal: 7.0,  // WCAG AAA
  large: 4.5    // WCAG AAA
};

// Extended keyboard testing
EnhancedConfig.enhanced.keyboardTestDepth = 100;
```

## 📁 File Structure

```
scripts/a11y/
├── README.md                     # This documentation
├── index.js                      # Original axe-core testing
├── enhanced-a11y-tester.js       # Enhanced testing class
├── run-enhanced-testing.js       # Main execution script
├── __tools/
│   └── index.js                  # Shared utilities
└── __constants/
    └── index.js                  # Configuration constants
```

## 🐛 Troubleshooting

### Common Issues

**Browser Launch Fails**
```bash
# Install missing dependencies
sudo apt-get install -y gconf-service libasound2-dev libatk1.0-dev
```

**Timeout Errors**
- Increase timeout values in configuration
- Ensure application is running on correct port
- Check for slow-loading content

**Modal Detection Issues**
- Verify data attributes are correctly set
- Check modal selector accuracy
- Ensure modals are properly rendered in DOM

### Debug Mode

Enable detailed logging:

```javascript
EnhancedConfig.debug = true;
```

This provides:
- Step-by-step execution logs
- Element highlighting during testing
- Screenshot capture on failures
- Browser console output

## 🤝 Contributing

To add new accessibility tests:

1. Extend the `EnhancedA11yTester` class
2. Add new test methods following the existing pattern
3. Update the HTML report generation
4. Add configuration options as needed
5. Document the new test in this README

## 📚 WCAG References

- [WCAG 2.1 Guidelines](https://www.w3.org/WAI/WCAG21/quickref/)
- [1.4.4 Resize Text](https://www.w3.org/WAI/WCAG21/Understanding/resize-text.html)
- [2.1.1 Keyboard](https://www.w3.org/WAI/WCAG21/Understanding/keyboard.html)
- [2.1.2 No Keyboard Trap](https://www.w3.org/WAI/WCAG21/Understanding/no-keyboard-trap.html)
- [2.4.7 Focus Visible](https://www.w3.org/WAI/WCAG21/Understanding/focus-visible.html)
- [1.4.3 Contrast (Minimum)](https://www.w3.org/WAI/WCAG21/Understanding/contrast-minimum.html)
- [4.1.2 Name, Role, Value](https://www.w3.org/WAI/WCAG21/Understanding/name-role-value.html)
