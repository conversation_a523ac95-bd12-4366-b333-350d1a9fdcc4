const fs = require('fs');
const path = require('path');
const axeCore = require('axe-core');
const axe = require('axe-core');
const constants = require('../__constants');

/**
 * @function buildHtmlReport
 * @param {*} allResults - combined report of all actions by axe core
 * @returns {string} html content
 */
const buildHtmlReport = (allResults) => {
  const { meta, sections } = allResults;

  // Helper to escape HTML safely
  function escapeHtml(string) {
    return string.replace(/[&<>"']/g, function (match) {
      return {
        '&': '&amp;',
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#39;'
      }[match];
    });
  }

  // Generate unique IDs for modals
  let modalIdCounter = 0;

  let html = `
  <html lang="en">
  <head>
    <title>Accessibility Report</title>
    <style>
      body { font-family: Arial, sans-serif; margin: 20px; }
      h1, h2 { color: #2c3e50; }
      table { border-collapse: collapse; width: 100%; margin-bottom: 40px; }
      th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
      th { background-color: #f2f2f2; }
      tr:hover { background-color: #f9f9f9; }
      a { color: #2980b9; text-decoration: none; }
      a:hover { text-decoration: underline; }
      .section { margin-bottom: 60px; }
      .node-count {
        color: #2980b9;
        cursor: pointer;
        text-decoration: underline;
      }
      /* Modal styles */
      .modal {
        display: none; 
        position: fixed; 
        z-index: 1000; 
        padding-top: 60px; 
        left: 0; top: 0; width: 100%; height: 100%; 
        overflow: auto; 
        background-color: rgba(0,0,0,0.4);
      }
      .modal-content {
        position: relative;
        background-color: #fff;
        margin: auto;
        padding: 40px 20px 20px 20px; /* extra top padding for close button */
        border: 1px solid #888;
        width: 80%;
        border-radius: 8px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.3);
      }

      .scroll {
        max-height: 70vh;
        overflow-y: auto;
      }

      .close {
        position: absolute;
        top: 10px;
        right: 15px;
        color: #aaa;
        font-size: 28px;
        font-weight: bold;
        cursor: pointer;
        z-index: 10;
      }
      .close:hover,
      .close:focus {
        color: black;
        text-decoration: none;
      }
      pre {
        white-space: pre-wrap; 
        background:#f9f9f9; 
        padding: 8px; 
        border-radius: 4px;
        font-size: 13px;
        border: 1px solid #ddd;
      }
      ul {
        padding-left: 20px;
        margin: 0 0 15px 0;
      }
      li {
        margin-bottom: 12px;
      }
    </style>
  </head>
  <body>
    <h2>Accessibility Report</h2>
    <h3>Test Meta Information</h3>
    <ul>
      <li><strong>Test Engine:</strong> ${meta.testEngine?.name || ''} v${
    meta.testEngine?.version || ''
  }</li>
      <li><strong>Test Runner:</strong> ${meta.testRunner?.name || ''}</li>
      <li><strong>User Agent:</strong> ${meta.testEnvironment?.userAgent || ''}</li>
      <li><strong>Window Size:</strong> ${meta.testEnvironment?.windowWidth} x ${
    meta.testEnvironment?.windowHeight
  }</li>
      <li><strong>Orientation:</strong> ${meta.testEnvironment?.orientationType} (${
    meta.testEnvironment?.orientationAngle
  }°)</li>
      <li><strong>Timestamp:</strong> ${meta.timestamp || ''}</li>
      <li><strong>URL:</strong> <a href="${meta.url || '#'}" target="_blank">${
    meta.url || ''
  }</a></li>
    </ul>
    <table>
      <thead>
        <tr>
          <th>Group > Screen</th>
          <th>ID</th>
          <th>Impact</th>
          <th>Description</th>
          <th>Help URL</th>
          <th>Nodes Affected</th>
        </tr>
      </thead>
      <tbody>
  `;

  for (const [groupName, groupResults] of Object.entries(sections)) {
    if (Array.isArray(groupResults)) {
      groupResults.forEach((item) => {
        if (!item.results.violations || item.results.violations.length === 0) {
          html += `<tr>
            <td><strong>${groupName}</strong> > ${item.name}</td>
            <td>-</td>
            <td>-</td>
            <td>No accessibility violations found.</td>
            <td>-</td>
            <td>-</td>
          </tr>`;
        } else {
          item.results.violations.forEach((v) => {
            const modalId = `modal-${modalIdCounter++}`;
            const nodesHtml = v.nodes
              .map(
                (node) => `
                <li>
                  <strong>Target:</strong> ${node.target.map(escapeHtml).join(', ')}<br/>
                  ${node.failureSummary ? `<em>${escapeHtml(node.failureSummary)}</em><br/>` : ''}
                  <pre>${escapeHtml(node.html)}</pre>
                </li>
              `
              )
              .join('');

            html += `
              <tr>
                <td><strong>${groupName}</strong> > ${item.name}</td>
                <td>${escapeHtml(v.id)}</td>
                <td>${escapeHtml(v.impact || '')}</td>
                <td>${escapeHtml(v.description)}</td>
                <td>${
                  v.helpUrl === '-'
                    ? v.helpUrl
                    : `<a href="${escapeHtml(v.helpUrl)}" target="_blank">Link</a>`
                }</td>
                ${
                  v.nodes.length &&
                  `<td>
                  <span class="node-count" onclick="document.getElementById('${modalId}').style.display='block'">${
                    v.nodes.length
                  }</span>
                  <div id="${modalId}" class="modal">
                    <div class="modal-content">
                      <span class="close" onclick="document.getElementById('${modalId}').style.display='none'">&times;</span>
                      <div class="scroll">
                        <h3>Nodes affected by "${escapeHtml(v.id)}"</h3>
                        <ul>${nodesHtml}</ul>
                      </div>
                    </div>
                  </div>
                </td>`
                }
                ${!v.nodes.length && '<td>-</td>'}
              </tr>`;
          });
        }
      });
    }
  }

  html += `</tbody></table>`;

  // Add script to close modal when clicking outside content or pressing ESC
  html += `
    <script>
      window.onclick = function(event) {
        document.querySelectorAll('.modal').forEach(modal => {
          if (event.target === modal) {
            modal.style.display = 'none';
          }
        });
      };
      window.addEventListener('keydown', function(event) {
        if (event.key === 'Escape') {
          document.querySelectorAll('.modal').forEach(modal => {
            modal.style.display = 'none';
          });
        }
      });
    </script>
  `;

  html += `</body></html>`;
  return html;
};

/**
 * @function logScanSummaryToConsole
 * @param {*} results - results for each node
 * @param {*} action - section's action
 */
const logScanSummaryToConsole = (results, action) => {
  const separator = '─'.repeat(140);
  const verticalLine = '│';

  // Helper to pad string to fixed width
  const pad = (str, width) => {
    return String(str).padEnd(width);
  };
  // Print header
  console.log('\n' + separator);
  console.log(
    `${verticalLine} ${pad('Group', 20)} ${verticalLine} ${pad(
      'Section',
      15
    )} ${verticalLine} ${pad('ID', 15)} ${verticalLine} ${pad('Impact', 10)} ${verticalLine} ${pad(
      'Description',
      40
    )} ${verticalLine} ${pad('Nodes', 8)} ${verticalLine}`
  );
  console.log(separator);

  // Print results
  if (!results?.violations || results.violations.length === 0) {
    console.log(
      `${verticalLine} ${pad(action.group, 20)} ${verticalLine} ${pad(
        action.name,
        15
      )} ${verticalLine} ${pad('-', 15)} ${verticalLine} ${pad('-', 10)} ${verticalLine} ${pad(
        'No accessibility violations found.',
        40
      )} ${verticalLine} ${pad('-', 8)} ${verticalLine}`
    );
  } else {
    results.violations.forEach((v) => {
      // Handle long descriptions by truncating
      const description =
        v.description.length > 38 ? v.description.slice(0, 35) + '...' : v.description;

      console.log(
        `${verticalLine} ${pad(action.group, 20)} ${verticalLine} ${pad(
          action.name,
          15
        )} ${verticalLine} ${pad(v.id, 15)} ${verticalLine} ${pad(
          v.impact,
          10
        )} ${verticalLine} ${pad(description, 40)} ${verticalLine} ${pad(
          v.nodes.length || '-',
          8
        )} ${verticalLine}`
      );
    });
  }

  // Print footer
  console.log(separator + '\n');
};
/**
 * @function buildErrorResultObject
 * @param {any} error - error object
 * @returns {any} Results pleasholder object for errors
 */
const buildErrorResultObject = (error) => ({
  violations: [
    {
      id: 'err_exec',
      impact: '-',
      description: error.message,
      helpUrl: '-',
      nodes: []
    }
  ]
});

/**
 * @function writeHtmlReport - writes report to a file
 * @param {string} report HTML REPORT
 * @returns {void}
 */
const writeHtmlReport = (report) => {
  const rootDir = process.cwd();
  const reportDir = path.join(rootDir, 'coverage', 'a11y_report');
  fs.mkdirSync(reportDir, { recursive: true });
  const reportPath = path.join(reportDir, 'a11y-report.html');
  fs.writeFileSync(reportPath, report);
  console.log('Accessibility HTML report saved as a11y-report.html');
};

/**
 * @function performAxeScan
 * Injects axe-core into the page and performs an accessibility scan.
 * If a selector is provided, it limits the scan scope to that DOM subtree.
 *
 * @param {import('puppeteer').Page} page - Puppeteer page instance
 * @param {string|null} selector - Optional CSS selector to scope axe scan
 * @returns {Promise<Object>} - axe-core results object or error info
 */
const performAxeScan = async (page, selector) =>
  await page.evaluate(
    async (sel, type) => {
      try {
        const el = sel ? document.querySelector(sel) : null;
        if (sel && !el) throw new Error(`Selector "${sel}" not found`);
        return sel
          ? await axe.run(el, { runOnly: { type: 'tag', values: [type] } })
          : await axe.run({ runOnly: { type: 'tag', values: [type] } });
      } catch (e) {
        return { error: `Axe failed: ${e.message}` };
      }
    },
    selector,
    constants.WCAG_AA
  );

/**
 * @function findInteractiveTriggers
 * Extracts all clickable elements that trigger modals or dropdowns,
 * scoped within a specific container. Uses data attributes to identify triggers.
 *
 * @param {object} page - Puppeteer page instance
 * @param {string} containerSelector - The CSS selector for the scope (e.g., modal or dropdown root)
 * @param {object} config - The scanner config object containing attribute names
 * @param {string} groupName - Logical group name (e.g., page or section)
 * @returns {Promise<Array>} - List of unique triggers with metadata
 */
const findInteractiveTriggers = async (page, containerSelector, config, groupName) => {
  return await page.evaluate(
    (sel, cfg, group) => {
      // Find the scoped container to search within
      const container = document.querySelector(sel !== 'body' ? `#${sel}` : sel);
      if (!container) return [];

      // Find all elements with the specified trigger and name attributes
      const elements = Array.from(container.querySelectorAll(`[${cfg.trigger}]`));

      return elements
        .map((el) => ({
          name: el.getAttribute(cfg.name), // Unique logical name for trigger
          selector: el.getAttribute(cfg.trigger),
          timeout: el.getAttribute(cfg.timeout), // Selector string to identify the opened modal/dropdown
          tag: el.tagName, // Element type (e.g., BUTTON)
          attr: cfg.trigger, // Attribute used for trigger (e.g., "data-trigger-a11y")
          group // Group or page name
        }))
        .filter(
          (el, idx, self) =>
            el.name && // Skip triggers without a name
            !el.name.includes(group) && // Avoid self-referencing triggers
            self.findIndex((e) => e.name === el.name) === idx // De-duplicate by name
        );
    },
    containerSelector,
    config.attributes,
    groupName
  );
};

/**
 * @function highlightElementForDebug
 * Adds a red dashed outline and scrolls element into view.
 * Used for debug purposes.
 * @param {object} page - Puppeteer page
 * @param {string} selector - CSS selector of the element to highlight
 */
const highlightElementForDebug = async (page, selector) => {
  await page.evaluate((target) => {
    const el = document.querySelector(target);
    if (el) {
      el.style.outline = '2px dashed red';
      el.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
  }, selector);
};

/**
 * Executes a sequence of user interactions (navigation and clicks).
 * Uses DOM mutation observation (debounced) instead of fixed timeouts to wait
 * for UI to settle, with a maximum timeout fallback.
 *
 * @param {object} page - Puppeteer page instance
 * @param {Array} actions - List of structured action objects (type, value)
 * @param {object} config - Scanner configuration (timeouts, debug flags, etc.)
 */
const performActionSequence = async (page, actions, config, firstLoad) => {
  for (let currentAction of actions) {
    const { type, value, timeout } = currentAction;

    if (config.debug) {
      console.log(`[DEBUG] Action: ${type} → ${value}`);
    }

    switch (type) {
      case constants.actions.navigate:
        try {
          // Navigate to the page and wait for basic load
          await page.goto(value, {
            waitUntil: 'domcontentloaded',
            timeout: firstLoad ? config.timeout.firstLoad : config.timeout.pageLoad
          });

          // Wait for the DOM to become idle after navigation
          await page.evaluate(
            (selector, maxWait, settleTime) => {
              return new Promise((resolve) => {
                let timeout;

                const target = document.querySelector(selector) || document.body;

                if (!target) return resolve(); // fail-safe: no target

                const maxTimeout = setTimeout(() => {
                  observer.disconnect();
                  resolve(); // fallback in case nothing changes
                }, maxWait);

                const debounce = () => {
                  if (timeout) clearTimeout(timeout);
                  timeout = setTimeout(() => {
                    clearTimeout(maxTimeout);
                    observer.disconnect();
                    resolve(); // DOM idle
                  }, settleTime);
                };

                // Immediate debounce to handle static modals
                debounce();

                const observer = new MutationObserver(debounce);
                observer.observe(target, {
                  attributes: true,
                  childList: true,
                  subtree: true
                });
              });
            },
            null,
            config.timeout.domIdleMax,
            config.timeout.domIdleSettle
          );
        } catch (e) {
          console.log(`⚠️ Timeout navigating to ${value}. Proceeding anyway.`);
        }
        break;

      case constants.actions.click_element:
        // Wait for the trigger element to appear
        await page.waitForSelector(value, { timeout: config.timeout.selector });

        // Optional debug highlight
        if (config.debug) {
          await highlightElementForDebug(page, value);
        }

        // Click the trigger element
        await page.evaluate((selector) => {
          const el = document.querySelector(selector);
          if (el) el.click();
        }, value);

        // Wait for DOM updates after click using MutationObserver
        await page.evaluate(
          (selector, maxWait, settleTime) => {
            return new Promise((resolve) => {
              let timeout;

              const target = document.querySelector(selector) || document.body;

              if (!target) return resolve(); // fail-safe: no target

              const maxTimeout = setTimeout(() => {
                observer.disconnect();
                resolve(); // fallback in case nothing changes
              }, maxWait);

              const debounce = () => {
                if (timeout) clearTimeout(timeout);
                timeout = setTimeout(() => {
                  clearTimeout(maxTimeout);
                  observer.disconnect();
                  resolve(); // DOM idle
                }, settleTime);
              };

              // Immediate debounce to handle static modals
              debounce();

              const observer = new MutationObserver(debounce);
              observer.observe(target, {
                attributes: true,
                childList: true,
                subtree: true
              });
            });
          },
          value,
          timeout || config.timeout.domIdleMax,
          timeout || config.timeout.domIdleSettle
        );

        break;
    }
  }
};

/**
 * @function isActionSkippable
 * Determines if a trigger or modal should be skipped.
 * @param {object} config - Configuration object
 * @param {object} action - Action with name and selector
 * @returns {boolean} - true if action should be skipped
 */
const isActionSkippable = (config, { name, selector }) => {
  if (config.skip.triggers.some((trigger) => name.includes(trigger))) {
    console.log(`⏭️ Skipping trigger [${name}] due to config.skip.triggers`);
    return true;
  }
  if (selector && config.skip.selectors.some((sel) => selector.includes(sel))) {
    console.log(`⏭️ Skipping selector [${selector}] due to config.skip.selectors`);
    return true;
  }
  return false;
};

/**
 * Main evaluation function that scans a webpage using a breadth-first traversal.
 * It executes user interactions, runs accessibility scans using axe-core,
 * and recursively discovers nested modals or triggerable UI elements.
 *
 * @param {object} config - Configuration object (urls, selectors, max_depth)
 * @param {object} page - Puppeteer page instance
 * @returns {Promise<object>} - Collected accessibility results grouped by section
 */
const runAccessibilityEvaluation = async (config, page) => {
  const results = {
    meta: {},
    sections: {}
  };

  const visitedActionSet = new Set(); // Track visited actions to avoid re-processing
  const queue = [];

  // Seed queue with initial navigation actions
  for (let name in config.urls) {
    const url = config.urls[name];
    if (config.skip.urls.includes(url)) {
      console.log(`⏭️ Skipping URL [${url}] due to config.skip.urls`);
      continue;
    }

    queue.push({
      name,
      instructions: [
        {
          type: constants.actions.navigate,
          value: url
        }
      ],
      group: name,
      breadcrumb: ''
    });
  }

  while (queue.length > 0) {
    const action = queue.shift();
    const { name, instructions, group, selector, breadcrumb } = action;

    // Avoid deep infinite paths
    if (instructions.length > config.max_depth) {
      console.log(`⚠️ Max Depth reached for ${name}, skipping`);
      continue;
    }

    // Avoid reprocessing same steps
    const key = instructions.map(({ type, value }) => `${type}::${value}`).join('|');
    if (visitedActionSet.has(key)) continue;
    visitedActionSet.add(key);

    // Unified skip check
    if (isActionSkippable(config, action)) continue;

    try {
      // Perform interactions
      await performActionSequence(page, instructions, config, !results.meta?.url);

      // Inject axe and scan
      await page.evaluate(axeCore.source);
      const result = await performAxeScan(page, selector ? `#${selector}` : null);

      // Store result by logical group
      if (!results.sections[group]) results.sections[group] = [];
      results.sections[group].push({ name, results: result });

      // Save meta data once
      if (!results.meta?.url) {
        results.meta = {
          ...constants.META_INFO,
          testEngine: result.testEngine,
          testRunner: result.testRunner,
          testEnvironment: result.testEnvironment,
          timestamp: result.timestamp,
          url: constants.HOME_PAGE
        };
      }

      config.debug && logScanSummaryToConsole(result, action);

      // 🔍 Discover triggers dynamically from current page using new data attributes
      const triggers = await findInteractiveTriggers(page, selector || 'body', config, name);

      const nextActions = triggers.map((trigger) => ({
        name: `${instructions.length > 1 ? breadcrumb + ' > ' : ''}${trigger.name}`,
        group,
        instructions: [
          ...instructions,
          {
            type: constants.actions.click_element,
            value: `${trigger.tag}[${config.attributes.name}='${trigger.name}']`,
            timeout: trigger.timeout
          }
        ],
        selector: trigger.selector,
        breadcrumb: `${instructions.length > 1 ? breadcrumb + ' > ' : ''}${trigger.name}`
      }));

      queue.push(...nextActions);
    } catch (error) {
      if (config.debug) {
        const fs = require('fs');
        fs.mkdirSync('coverage/debug', { recursive: true });
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        await page.screenshot({ path: `coverage/debug/failure-${name}-${timestamp}.png` });
      }

      if (!results.sections[group]) results.sections[group] = [];
      results.sections[group].push({ name, results: buildErrorResultObject(error) });
      logScanSummaryToConsole(buildErrorResultObject(error), action);
    }
  }

  return results;
};

module.exports = {
  buildHtmlReport,
  writeHtmlReport,
  runAccessibilityEvaluation
};
