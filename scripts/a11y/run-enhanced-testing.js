#!/usr/bin/env node

/**
 * Enhanced WCAG AA Accessibility Testing Runner
 *
 * This script orchestrates comprehensive accessibility testing that goes beyond
 * automated axe-core scanning to include manual-like testing procedures:
 *
 * 1. Baseline axe-core automated testing
 * 2. 400% zoom testing for layout issues
 * 3. Keyboard-only navigation testing
 * 4. Visual contrast inspection
 * 5. Modal focus management testing
 * 6. Combined conditions testing
 *
 * Usage:
 *   node scripts/a11y/run-enhanced-testing.js
 *   npm run a11y:enhanced
 */

const puppeteer = require('puppeteer');
const { EnhancedA11yTester, EnhancedConfig } = require('./enhanced-a11y-tester');

/**
 * Main execution function
 */
async function runEnhancedAccessibilityTesting() {
  console.log('🚀 Enhanced WCAG AA Accessibility Testing');
  console.log('==========================================\n');

  let browser;
  let page;

  try {
    // Launch browser with enhanced configuration
    console.log('🌐 Launching browser...');
    browser = await puppeteer.launch({
      headless: false, // Set to true for CI/automated environments
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--start-maximized',
        '--window-size=1920,1080',
        '--disable-web-security', // For local testing
        '--disable-features=VizDisplayCompositor' // Improve stability
      ],
      defaultViewport: null
    });

    page = await browser.newPage();

    // Set up page for accessibility testing
    await page.setExtraHTTPHeaders({
      'Accept-Language': 'en-US,en;q=0.9'
    });

    // Enable console logging from the page
    page.on('console', (msg) => {
      if (EnhancedConfig.debug && msg.type() === 'log') {
        console.log('PAGE LOG:', msg.text());
      }
    });
    // Handle page errors
    page.on('pageerror', (error) => {
      console.log('PAGE ERROR:', error.message);
    });

    // Create enhanced tester instance
    const enhancedTester = new EnhancedA11yTester(EnhancedConfig, page);

    // Run comprehensive testing
    const results = await enhancedTester.runEnhancedTesting();

    // Display summary
    console.log('\n📊 TESTING SUMMARY');
    console.log('==================');
    console.log(`Total Issues Found: ${results.summary.totalIssues}`);
    console.log(`├─ Baseline (axe-core): ${results.summary.baselineViolations}`);
    console.log(`├─ Zoom Issues: ${results.summary.enhancedIssues.zoom}`);
    console.log(`├─ Keyboard Issues: ${results.summary.enhancedIssues.keyboard}`);
    console.log(`├─ Focus Issues: ${results.summary.enhancedIssues.focus}`);
    console.log(`├─ Contrast Issues: ${results.summary.enhancedIssues.contrast}`);
    console.log(`└─ Modal Issues: ${results.summary.enhancedIssues.modal}`);

    // Provide recommendations based on results
    console.log('\n💡 RECOMMENDATIONS');
    console.log('==================');

    if (results.summary.totalIssues === 0) {
      console.log('🎉 Excellent! No accessibility issues found.');
      console.log('   Your application meets WCAG AA standards in all tested areas.');
    } else {
      console.log('🔧 Issues found that need attention:');

      if (results.summary.baselineViolations > 0) {
        console.log(
          `   • Fix ${results.summary.baselineViolations} automated violations (highest priority)`
        );
      }

      if (results.summary.enhancedIssues.contrast > 0) {
        console.log(
          `   • Improve color contrast for ${results.summary.enhancedIssues.contrast} elements`
        );
      }

      if (results.summary.enhancedIssues.focus > 0) {
        console.log(
          `   • Add visible focus indicators to ${results.summary.enhancedIssues.focus} elements`
        );
      }

      if (results.summary.enhancedIssues.keyboard > 0) {
        console.log(
          `   • Fix ${results.summary.enhancedIssues.keyboard} keyboard navigation issues`
        );
      }

      if (results.summary.enhancedIssues.zoom > 0) {
        console.log(
          `   • Resolve ${results.summary.enhancedIssues.zoom} layout issues at 400% zoom`
        );
      }

      if (results.summary.enhancedIssues.modal > 0) {
        console.log(
          `   • Improve accessibility of ${results.summary.enhancedIssues.modal} modal dialogs`
        );
      }
    }

    console.log('\n📄 Reports generated:');
    console.log('   • Enhanced HTML Report: coverage/a11y_report/enhanced-a11y-report.html');
    console.log('   • Screenshots (if issues found): coverage/a11y_screenshots/');

    // Exit with appropriate code
    const exitCode = results.summary.totalIssues > 0 ? 1 : 0;
    console.log(`\n${exitCode === 0 ? '✅' : '❌'} Testing completed with exit code: ${exitCode}`);

    return exitCode;
  } catch (error) {
    console.error('\n❌ Enhanced accessibility testing failed:');
    console.error(error.message);

    if (EnhancedConfig.debug) {
      console.error('\nFull error details:');
      console.error(error);
    }

    return 1;
  } finally {
    // Clean up
    if (page) {
      try {
        await page.close();
      } catch (e) {
        console.log('Warning: Could not close page properly');
      }
    }

    if (browser) {
      try {
        await browser.close();
      } catch (e) {
        console.log('Warning: Could not close browser properly');
      }
    }
  }
}

/**
 * CLI execution
 */
if (require.main === module) {
  runEnhancedAccessibilityTesting()
    .then((exitCode) => {
      process.exit(exitCode);
    })
    .catch((error) => {
      console.error('Fatal error:', error);
      process.exit(1);
    });
}

module.exports = { runEnhancedAccessibilityTesting };
