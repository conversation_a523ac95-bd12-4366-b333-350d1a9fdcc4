/* global jest */
export const defaultThemeDataMock = {
  hideObject: [
    { key: 'Title', hideEye: true },
    { key: 'Summary', hideEye: false },
    { key: 'Url', hideEye: false },
    { key: 'Metadata', hideEye: false },
    { key: 'Icon', hideEye: false },
    { key: 'Tag', hideEye: false }
  ],
  initialTab: {
    contentName: 'all',
    index: '_index',
    mergeContentName: 'all',
    activeTabIndex: 'all'
  },
  hiddenFilters: [],
  facetsOrder: [
    {
      key: '_index',
      label: 'Index',
      values: [
        {
          displayName: 'wcagstack',
          filterOrder: 0,
          Contentname: 'server_wcagstack',
          value: 2437,
          immediateParent: '_index',
          parent: '_index',
          childName: 'Index_1',
          level: 1,
          ContentnameFrontend: 'server_wcagstack'
        },
        {
          displayName: 'WCAG dont delete Youtube',
          filterOrder: 1,
          Contentname: 'server_videos',
          value: 1171,
          immediateParent: '_index',
          parent: '_index',
          childName: 'Index_1',
          level: 1,
          ContentnameFrontend: 'server_videos'
        }
      ],
      order: 0
    },
    {
      key: '_type',
      label: 'Sources',
      values: [
        {
          displayName: 'Question',
          filterOrder: null,
          Contentname: 'stack_question',
          value: 2437,
          immediateParent: '_type',
          parent: '_type',
          childName: 'Sources_1',
          level: 1,
          ContentnameFrontend: 'stack_question'
        },
        {
          displayName: 'Youtube Content',
          filterOrder: null,
          Contentname: 'video',
          value: 1171,
          immediateParent: '_type',
          parent: '_type',
          childName: 'Sources_1',
          level: 1,
          ContentnameFrontend: 'video'
        }
      ],
      order: 1,
      sort: 'count_desc',
      hideEye: false
    },
    {
      key: '2_153_dummy___stack_question___is_answered',
      label: 'Answered',
      values: [
        {
          Contentname: '$ regex replace equivalent in python',
          value: 1,
          immediateParent: '2_153_dummy___stack_question___is_answered',
          parent: '2_153_dummy___stack_question___is_answered',
          childName: 'Title3_1',
          level: 1,
          ContentnameFrontend: '$ regex replace equivalent in python'
        },
        {
          Contentname:
            '&#191;How it&#39;s this works and not return a warning validateDOMNesting(...)?',
          value: 1,
          immediateParent: '2_153_dummy___stack_question___is_answered',
          parent: '2_153_dummy___stack_question___is_answered',
          childName: 'Title3_1',
          level: 1,
          ContentnameFrontend:
            "¿How it's this works and not return a warning validateDOMNesting(...)?"
        }
      ],
      order: 1,
      sort: 'count_desc',
      hideEye: false
    },
    {
      key: 'title',
      label: 'Title',
      values: [
        {
          Contentname: 'Help Center',
          value: 43,
          immediateParent: 'title',
          parent: 'title',
          childName: 'Title_1',
          level: 1,
          ContentnameFrontend: 'Help Center'
        },
        {
          Contentname: 'Help Center - Knowbler',
          value: 21,
          immediateParent: 'title',
          parent: 'title',
          childName: 'Title_1',
          level: 1,
          ContentnameFrontend: 'Help Center - Knowbler'
        }
      ],
      order: 2,
      sort: 'count_desc'
    },
    {
      key: 'server_wcagstack___stack_question___tag',
      label: 'Tag',
      values: [
        {
          Contentname: 'javascript',
          value: 2437,
          immediateParent: 'server_wcagstack___stack_question___tag',
          parent: 'server_wcagstack___stack_question___tag',
          childName: 'Tag_1',
          level: 1,
          ContentnameFrontend: 'javascript'
        }
      ],
      order: 3,
      sort: 'count_desc',
      hideEye: false
    }
  ]
};

/**
 * useBrowserStorageMock - local or session storage mocker for browser
 * @param {string} storageType - local / session storage
 */
export const useBrowserStorageMock = (storageType) => {
  if (!storageType || !['local', 'session'].includes(storageType)) {
    throw new Error('Invalid argument ! Must be either "local" or "session"');
  }
  const storageMock = (function () {
    let store = {};
    return {
      getItem: jest.fn((key) => {
        return store[key] || null;
      }),
      setItem: jest.fn((key, value) => {
        store[key] = value;
      }),
      clear: jest.fn(() => {
        store = {};
      }),
      removeItem: jest.fn((key) => {
        delete store[key];
      })
    };
  })();

  const mockStart = () => {
    Object.defineProperty(window, `${storageType}Storage`, { value: storageMock });
  };

  const clearMock = () => {
    storageMock.clear();
  };

  return [mockStart, clearMock];
};
