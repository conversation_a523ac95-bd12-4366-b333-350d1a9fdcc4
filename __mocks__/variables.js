import { v4 as uuid } from 'uuid';
const uid = uuid();
const variables = {
  searchSource: '',
  facetSearch: false,
  hasError: false,
  STREAM_DELIMITER: '$___$__$_$',
  sortingOrder: {
    default_results_sorting: {
      sortPreference: {
        keyLabelMapping: [
          { value: 'value1', label: 'label1' },
          { value: 'value2', label: 'label2' }
        ]
      }
    }
  },
  searchClientType: 6,
  searchCallVariables: {
    pagingAggregation: [],
    langAttr: 'en',
    react: 1,
    isRecommendationsWidget: false,
    searchString: 'test',
    from: 0,
    sortby: '_score',
    orderBy: 'desc',
    pageNo: 1,
    aggregations: [
      {
        type: 'category',
        filter: ['Electronics', 'Clothing']
      },
      {
        type: 'post_time',
        filter: ['Last week']
      }
    ],
    clonedAggregations: [],
    uid,
    resultsPerPage: 10,
    exactPhrase: '',
    withOneOrMore: '',
    withoutTheWords: '',
    pageSize: 10,
    sid: '',
    language: '',
    mergeSources: false,
    versionResults: true,
    showMoreSummary: false,
    minSummaryLength: false,
    suCaseCreate: false,
    visitedtitle: '',
    paginationClicked: false,
    email: window.su_utm || window.user_info?.email || window.email || '',
    smartFacetsClicked: false,
    smartFacets: true
  },
  autocompleteCallVariables: {
    autocomplete: true,
    react: 1,
    searchString: 'test',
    from: 0,
    sortby: '_score',
    orderBy: 'desc',
    pageNo: 1,
    aggregations: [],
    resultsPerPage: 10,
    exactPhrase: '',
    withOneOrMore: '',
    withoutTheWords: '',
    pageSize: 10,
    sid: '',
    language: 'en',
    mergeSources: false,
    versionResults: false,
    smartFacetsClicked: false,
    smartFacets: true,
    email: window.su_utm || window.user_info?.email || window.email || ''
  },
  languages: {
    rtl: {
      type: 'RTL'
    },
    ltr: {
      type: 'LTR'
    },
    config: {
      selectedLanguages: [
        { code: 'en', name: 'English', label: 'EN' },
        { code: 'es', name: 'Spanish', label: 'ES' }
      ],
      defaultLanguage: { code: 'en' }
    }
  },
  userDefinedAutoCompleteSearchUrl: {
    url: '',
    req: { method: 'POST', body: '', headers: { 'Content-Type': 'application/json' } }
  },
  controllingVariables: {
    firstTimeLoad: true,
    processing: false,
    urlState: 0,
    currentUrlState: 0
  },
  toggleDisplayKeys: [
    { key: 'Title', hideEye: false },
    { key: 'Summary', hideEye: false },
    { key: 'Url', hideEye: false },
    { key: 'Metadata', hideEye: false },
    { key: 'Icon', hideEye: false },
    { key: 'Tag', hideEye: false }
  ],
  previousDymSearchString: 'prev test',
  keepAutoCompleteResultOpenOnScroll: false,
  previousSearches: [
    {
      imageSource: '',
      icon: '',
      href: 'https://example.com',
      highlight: { TitleToDisplay: ['Example Title'], SummaryToDisplay: ['Example Summary'] },
      metadata: [{ key: 'Tag', value: [['Example Tag']] }],
      sourceLabel: 'Example Source',
      solved: 'Solved',
      liveCounts: { kudos: 10, replies: 5, views: 100 }
    },
    {
      imageSource: '',
      icon: '',
      href: 'https://example2.com',
      highlight: { TitleToDisplay: ['Example Title 2'], SummaryToDisplay: ['Example Summary 2'] },
      metadata: [{ key: 'Tag', value: [['Example Tag 2']] }],
      sourceLabel: 'Example Source 2',
      solved: 'Unsolved',
      liveCounts: { kudos: 20, replies: 10, views: 200 }
    }
  ],
  hiddenFilters: [],
  storeHiddenFilters: [],
  allSelected: true,
  facetSearchCheck: [],
  selectedStickyFilter: [],
  searchAnalyticsObject: null,
  isFreshSearch: true,
  searchResposeTimer: 0,
  currentClickedOrder: null,
  activeType: 'all',
  filtersInAggr: null,
  resultsInAllContentSources: false,
  searchResultClicked: false,
  visitedtitle: '',
  visitedUrl: '',
  visiteRank: '',
  getUserEmailId: ''
};
variables.searchClientProps = {
  instanceName: 'https://mock-instance.com'
};

export default variables;
