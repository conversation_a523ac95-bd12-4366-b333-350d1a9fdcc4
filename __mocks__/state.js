import variables from './variables';
const { TextEncoder } = require('util');
export const createMockStream = (response = mockSuccessResponse) => {
  const encodedResponse = new TextEncoder().encode(
    `${JSON.stringify(response)}${variables.STREAM_DELIMITER}`
  );
  return {
    body: {
      getReader: () => {
        let callCount = 0;
        return {
          read: () => {
            if (callCount === 0) {
              callCount++;
              return Promise.resolve({ done: false, value: encodedResponse });
            } else {
              return Promise.resolve({ done: true, value: undefined });
            }
          }
        };
      }
    }
  };
};

export const mockSuccessResponse = {
  status: '200',
  data: {
    id: 'test-id',
    choices: [{ delta: { content: 'Test successful response' } }]
  }
};
const state = {
  adHtml: {
    htmlString: ''
  },
  autocomplete: {
    recentSearchHistory: [
      { title: 'Recent Search 1', type: 'type1' },
      { title: 'Recent Search 2', type: 'type2' }
    ],
    result: {
      hits: [
        {
          highlight: { TitleToDisplayString: ['result 1'] },
          href: 'https://example.com/result1',
          sourceName: 'source1',
          objName: 'object1',
          _id: 'id1',
          trackAnalytics: { some: 'analytics' }
        },
        {
          highlight: { TitleToDisplayString: ['result 2'] },
          href: 'https://example.com/result2',
          sourceName: 'source2',
          objName: 'object2',
          _id: 'id2',
          trackAnalytics: { some: 'analytics' }
        }
      ]
    },
    smartAggregations: [
      {
        key: 'category',
        label: 'Category',
        order: 0,
        values: [
          { Contentname: 'Electronics', selected: false, parent: 'category' },
          { Contentname: 'Books', selected: false, parent: 'category' },
          { Contentname: 'Clothing', selected: true, parent: 'category' }
        ]
      },
      {
        key: 'post_time',
        label: 'Post Time',
        order: 1,
        values: [
          { Contentname: 'Last 24 hours', selected: false },
          { Contentname: 'Last week', selected: false },
          { Contentname: 'Last month', selected: false }
        ]
      },
      {
        key: '2_153_dummy___stack_question___is_answered',
        label: 'Answered',
        order: 2,
        values: []
      }
    ]
  },
  searchResult: {
    statusCode: 200,
    message: 'Success',
    result: {
      total: 10,
      max_score: 8.992924,
      hits: [
        {
          _id: '78490949',
          _score: 8.992924,
          highlight: {
            '2_153_dummy___stack_question___description.en': [
              "And I don't know why setting the last ___su-highlight-start___pointer___su-highlight-end___ to ___su-highlight-start___null___su-highlight-end___ doesn't get rid of them.",
              '= ___su-highlight-start___null___su-highlight-end___){ if (current.val !',
              'Then I overwrite the same list (1 -&gt; 2 -&gt; 4 -&gt; 5 -&gt; 5) and then I delete the ___su-highlight-start___pointer___su-highlight-end___ to the',
              '= num; current = current.next; } // disconnect the last node from the remaining ones current.next = ___su-highlight-start___null___su-highlight-end___'
            ],
            SummaryToDisplay: [
              'true',
              "And I don't know why setting the last <span class='highlight'>pointer</span> to <span class='highlight'>null</span> doesn't get rid of them. = <span class='highlight'>null</span>){ if (current.next = <span class='highlight'>null</span>....",
              '78490949',
              'jav'
            ],
            TitleToDisplay: ['Overwriting and deleting linked list elements'],
            TitleToDisplayString: ['Overwriting and deleting linked list elements']
          },
          metadata: [],
          autosuggestData: [],
          href: 'testHref',
          clientHref: 'abcddddd',
          Id: '78490949',
          objName: 'stack_question',
          objLabel: 'Question',
          sourceName: '2_153_dummy',
          uniqueField: '2_153_dummy_stack_question_78490949',
          sourceLabel: 'dummy',
          solved: 'Unsolved',
          indexedDate: '2024-05-27T06:35:08.729Z',
          sortOrder: null,
          contentTag: 'dummy',
          es_id: '78490949'
        },
        {
          _id: '78490949',
          _score: 8.992924,
          highlight: {
            '2_153_dummy___stack_question___description.en': [
              "And I don't know why setting the last ___su-highlight-start___pointer___su-highlight-end___ to ___su-highlight-start___null___su-highlight-end___ doesn't get rid of them.",
              '= ___su-highlight-start___null___su-highlight-end___){ if (current.val !',
              'Then I overwrite the same list (1 -&gt; 2 -&gt; 4 -&gt; 5 -&gt; 5) and then I delete the ___su-highlight-start___pointer___su-highlight-end___ to the',
              '= num; current = current.next; } // disconnect the last node from the remaining ones current.next = ___su-highlight-start___null___su-highlight-end___'
            ],
            SummaryToDisplay: [
              'true',
              "And I don't know why setting the last <span class='highlight'>pointer</span> to <span class='highlight'>null</span> doesn't get rid of them. = <span class='highlight'>null</span>){ if (current.next = <span class='highlight'>null</span>....",
              '78490949',
              'jav'
            ],
            TitleToDisplay: ['Overwriting and deleting linked list elements'],
            TitleToDisplayString: ['Overwriting and deleting linked list elements']
          },
          metadata: [],
          autosuggestData: [],
          href: 'testHref',
          clientHref: 'abcddddd',
          Id: '78490949',
          objName: 'stack_question',
          objLabel: 'Question',
          sourceName: '2_153_dummy',
          uniqueField: '2_153_dummy_stack_question_78490949',
          sourceLabel: 'dummy',
          solved: 'Unsolved',
          indexedDate: '2024-05-27T06:35:08.729Z',
          sortOrder: null,
          contentTag: 'dummy',
          es_id: '78490949'
        },
        {
          _id: '78490949',
          _score: 8.992924,
          highlight: {
            '2_153_dummy___stack_question___description.en': [
              "And I don't know why setting the last ___su-highlight-start___pointer___su-highlight-end___ to ___su-highlight-start___null___su-highlight-end___ doesn't get rid of them.",
              '= ___su-highlight-start___null___su-highlight-end___){ if (current.val !',
              'Then I overwrite the same list (1 -&gt; 2 -&gt; 4 -&gt; 5 -&gt; 5) and then I delete the ___su-highlight-start___pointer___su-highlight-end___ to the',
              '= num; current = current.next; } // disconnect the last node from the remaining ones current.next = ___su-highlight-start___null___su-highlight-end___'
            ],
            SummaryToDisplay: [
              'true',
              "And I don't know why setting the last <span class='highlight'>pointer</span> to <span class='highlight'>null</span> doesn't get rid of them. = <span class='highlight'>null</span>){ if (current.next = <span class='highlight'>null</span>....",
              '78490949',
              'jav'
            ],
            TitleToDisplay: ['Overwriting and deleting linked list elements'],
            TitleToDisplayString: ['Overwriting and deleting linked list elements']
          },
          metadata: [],
          autosuggestData: [],
          href: 'testHref',
          clientHref: 'abcddddd',
          Id: '78490949',
          objName: 'stack_question',
          objLabel: 'Question',
          sourceName: '2_153_dummy',
          uniqueField: '2_153_dummy_stack_question_78490949',
          sourceLabel: 'dummy',
          solved: 'Unsolved',
          indexedDate: '2024-05-27T06:35:08.729Z',
          sortOrder: null,
          contentTag: 'dummy',
          es_id: '78490949'
        },
        {
          _id: '78490949',
          _score: 8.992924,
          highlight: {
            '2_153_dummy___stack_question___description.en': [
              "And I don't know why setting the last ___su-highlight-start___pointer___su-highlight-end___ to ___su-highlight-start___null___su-highlight-end___ doesn't get rid of them.",
              '= ___su-highlight-start___null___su-highlight-end___){ if (current.val !',
              'Then I overwrite the same list (1 -&gt; 2 -&gt; 4 -&gt; 5 -&gt; 5) and then I delete the ___su-highlight-start___pointer___su-highlight-end___ to the',
              '= num; current = current.next; } // disconnect the last node from the remaining ones current.next = ___su-highlight-start___null___su-highlight-end___'
            ],
            SummaryToDisplay: [
              'true',
              "And I don't know why setting the last <span class='highlight'>pointer</span> to <span class='highlight'>null</span> doesn't get rid of them. = <span class='highlight'>null</span>){ if (current.next = <span class='highlight'>null</span>....",
              '78490949',
              'jav'
            ],
            TitleToDisplay: ['Overwriting and deleting linked list elements'],
            TitleToDisplayString: ['Overwriting and deleting linked list elements']
          },
          metadata: [],
          autosuggestData: [],
          href: 'testHref',
          clientHref: 'abcddddd',
          Id: '78490949',
          objName: 'stack_question',
          objLabel: 'Question',
          sourceName: '2_153_dummy',
          uniqueField: '2_153_dummy_stack_question_78490949',
          sourceLabel: 'dummy',
          solved: 'Unsolved',
          indexedDate: '2024-05-27T06:35:08.729Z',
          sortOrder: null,
          contentTag: 'dummy',
          es_id: '78490949'
        },
        {
          _id: '78490949',
          _score: 8.992924,
          highlight: {
            '2_153_dummy___stack_question___description.en': [
              "And I don't know why setting the last ___su-highlight-start___pointer___su-highlight-end___ to ___su-highlight-start___null___su-highlight-end___ doesn't get rid of them.",
              '= ___su-highlight-start___null___su-highlight-end___){ if (current.val !',
              'Then I overwrite the same list (1 -&gt; 2 -&gt; 4 -&gt; 5 -&gt; 5) and then I delete the ___su-highlight-start___pointer___su-highlight-end___ to the',
              '= num; current = current.next; } // disconnect the last node from the remaining ones current.next = ___su-highlight-start___null___su-highlight-end___'
            ],
            SummaryToDisplay: [
              'true',
              "And I don't know why setting the last <span class='highlight'>pointer</span> to <span class='highlight'>null</span> doesn't get rid of them. = <span class='highlight'>null</span>){ if (current.next = <span class='highlight'>null</span>....",
              '78490949',
              'jav'
            ],
            TitleToDisplay: ['Overwriting and deleting linked list elements'],
            TitleToDisplayString: ['Overwriting and deleting linked list elements']
          },
          metadata: [],
          autosuggestData: [],
          href: 'testHref',
          clientHref: 'abcddddd',
          Id: '78490949',
          objName: 'stack_question',
          objLabel: 'Question',
          sourceName: '2_153_dummy',
          uniqueField: '2_153_dummy_stack_question_78490949',
          sourceLabel: 'dummy',
          solved: 'Unsolved',
          indexedDate: '2024-05-27T06:35:08.729Z',
          sortOrder: null,
          contentTag: 'dummy',
          es_id: '78490949'
        },
        {
          _id: '78490949',
          _score: 8.992924,
          highlight: {
            '2_153_dummy___stack_question___description.en': [
              "And I don't know why setting the last ___su-highlight-start___pointer___su-highlight-end___ to ___su-highlight-start___null___su-highlight-end___ doesn't get rid of them.",
              '= ___su-highlight-start___null___su-highlight-end___){ if (current.val !',
              'Then I overwrite the same list (1 -&gt; 2 -&gt; 4 -&gt; 5 -&gt; 5) and then I delete the ___su-highlight-start___pointer___su-highlight-end___ to the',
              '= num; current = current.next; } // disconnect the last node from the remaining ones current.next = ___su-highlight-start___null___su-highlight-end___'
            ],
            SummaryToDisplay: [
              'true',
              "And I don't know why setting the last <span class='highlight'>pointer</span> to <span class='highlight'>null</span> doesn't get rid of them. = <span class='highlight'>null</span>){ if (current.next = <span class='highlight'>null</span>....",
              '78490949',
              'jav'
            ],
            TitleToDisplay: ['Overwriting and deleting linked list elements'],
            TitleToDisplayString: ['Overwriting and deleting linked list elements']
          },
          metadata: [],
          autosuggestData: [],
          href: 'testHref',
          clientHref: 'abcddddd',
          Id: '78490949',
          objName: 'stack_question',
          objLabel: 'Question',
          sourceName: '2_153_dummy',
          uniqueField: '2_153_dummy_stack_question_78490949',
          sourceLabel: 'dummy',
          solved: 'Unsolved',
          indexedDate: '2024-05-27T06:35:08.729Z',
          sortOrder: null,
          contentTag: 'dummy',
          es_id: '78490949'
        },
        {
          _id: '78490949',
          _score: 8.992924,
          highlight: {
            '2_153_dummy___stack_question___description.en': [
              "And I don't know why setting the last ___su-highlight-start___pointer___su-highlight-end___ to ___su-highlight-start___null___su-highlight-end___ doesn't get rid of them.",
              '= ___su-highlight-start___null___su-highlight-end___){ if (current.val !',
              'Then I overwrite the same list (1 -&gt; 2 -&gt; 4 -&gt; 5 -&gt; 5) and then I delete the ___su-highlight-start___pointer___su-highlight-end___ to the',
              '= num; current = current.next; } // disconnect the last node from the remaining ones current.next = ___su-highlight-start___null___su-highlight-end___'
            ],
            SummaryToDisplay: [
              'true',
              "And I don't know why setting the last <span class='highlight'>pointer</span> to <span class='highlight'>null</span> doesn't get rid of them. = <span class='highlight'>null</span>){ if (current.next = <span class='highlight'>null</span>....",
              '78490949',
              'jav'
            ],
            TitleToDisplay: ['Overwriting and deleting linked list elements'],
            TitleToDisplayString: ['Overwriting and deleting linked list elements']
          },
          metadata: [],
          autosuggestData: [],
          href: 'testHref',
          clientHref: 'abcddddd',
          Id: '78490949',
          objName: 'stack_question',
          objLabel: 'Question',
          sourceName: '2_153_dummy',
          uniqueField: '2_153_dummy_stack_question_78490949',
          sourceLabel: 'dummy',
          solved: 'Unsolved',
          indexedDate: '2024-05-27T06:35:08.729Z',
          sortOrder: null,
          contentTag: 'dummy',
          es_id: '78490949'
        },
        {
          _id: '78490949',
          _score: 8.992924,
          highlight: {
            '2_153_dummy___stack_question___description.en': [
              "And I don't know why setting the last ___su-highlight-start___pointer___su-highlight-end___ to ___su-highlight-start___null___su-highlight-end___ doesn't get rid of them.",
              '= ___su-highlight-start___null___su-highlight-end___){ if (current.val !',
              'Then I overwrite the same list (1 -&gt; 2 -&gt; 4 -&gt; 5 -&gt; 5) and then I delete the ___su-highlight-start___pointer___su-highlight-end___ to the',
              '= num; current = current.next; } // disconnect the last node from the remaining ones current.next = ___su-highlight-start___null___su-highlight-end___'
            ],
            SummaryToDisplay: [
              'true',
              "And I don't know why setting the last <span class='highlight'>pointer</span> to <span class='highlight'>null</span> doesn't get rid of them. = <span class='highlight'>null</span>){ if (current.next = <span class='highlight'>null</span>....",
              '78490949',
              'jav'
            ],
            TitleToDisplay: ['Overwriting and deleting linked list elements'],
            TitleToDisplayString: ['Overwriting and deleting linked list elements']
          },
          metadata: [],
          autosuggestData: [],
          href: 'testHref',
          clientHref: 'abcddddd',
          Id: '78490949',
          objName: 'stack_question',
          objLabel: 'Question',
          sourceName: '2_153_dummy',
          uniqueField: '2_153_dummy_stack_question_78490949',
          sourceLabel: 'dummy',
          solved: 'Unsolved',
          indexedDate: '2024-05-27T06:35:08.729Z',
          sortOrder: null,
          contentTag: 'dummy',
          es_id: '78490949'
        },
        {
          _id: '78490949',
          _score: 8.992924,
          highlight: {
            '2_153_dummy___stack_question___description.en': [
              "And I don't know why setting the last ___su-highlight-start___pointer___su-highlight-end___ to ___su-highlight-start___null___su-highlight-end___ doesn't get rid of them.",
              '= ___su-highlight-start___null___su-highlight-end___){ if (current.val !',
              'Then I overwrite the same list (1 -&gt; 2 -&gt; 4 -&gt; 5 -&gt; 5) and then I delete the ___su-highlight-start___pointer___su-highlight-end___ to the',
              '= num; current = current.next; } // disconnect the last node from the remaining ones current.next = ___su-highlight-start___null___su-highlight-end___'
            ],
            SummaryToDisplay: [
              'true',
              "And I don't know why setting the last <span class='highlight'>pointer</span> to <span class='highlight'>null</span> doesn't get rid of them. = <span class='highlight'>null</span>){ if (current.next = <span class='highlight'>null</span>....",
              '78490949',
              'jav'
            ],
            TitleToDisplay: ['Overwriting and deleting linked list elements'],
            TitleToDisplayString: ['Overwriting and deleting linked list elements']
          },
          metadata: [],
          autosuggestData: [],
          href: 'testHref',
          clientHref: 'abcddddd',
          Id: '78490949',
          objName: 'stack_question',
          objLabel: 'Question',
          sourceName: '2_153_dummy',
          uniqueField: '2_153_dummy_stack_question_78490949',
          sourceLabel: 'dummy',
          solved: 'Unsolved',
          indexedDate: '2024-05-27T06:35:08.729Z',
          sortOrder: null,
          contentTag: 'dummy',
          es_id: '78490949'
        },
        {
          _id: '78490949',
          _score: 8.992924,
          highlight: {
            '2_153_dummy___stack_question___description.en': [
              "And I don't know why setting the last ___su-highlight-start___pointer___su-highlight-end___ to ___su-highlight-start___null___su-highlight-end___ doesn't get rid of them.",
              '= ___su-highlight-start___null___su-highlight-end___){ if (current.val !',
              'Then I overwrite the same list (1 -&gt; 2 -&gt; 4 -&gt; 5 -&gt; 5) and then I delete the ___su-highlight-start___pointer___su-highlight-end___ to the',
              '= num; current = current.next; } // disconnect the last node from the remaining ones current.next = ___su-highlight-start___null___su-highlight-end___'
            ],
            SummaryToDisplay: [
              'true',
              "And I don't know why setting the last <span class='highlight'>pointer</span> to <span class='highlight'>null</span> doesn't get rid of them. = <span class='highlight'>null</span>){ if (current.next = <span class='highlight'>null</span>....",
              '78490949',
              'jav'
            ],
            TitleToDisplay: ['Overwriting and deleting linked list elements'],
            TitleToDisplayString: ['Overwriting and deleting linked list elements']
          },
          metadata: [],
          autosuggestData: [],
          href: 'testHref',
          clientHref: 'abcddddd',
          Id: '78490949',
          objName: 'stack_question',
          objLabel: 'Question',
          sourceName: '2_153_dummy',
          uniqueField: '2_153_dummy_stack_question_78490949',
          sourceLabel: 'dummy',
          solved: 'Unsolved',
          indexedDate: '2024-05-27T06:35:08.729Z',
          sortOrder: null,
          contentTag: 'dummy',
          es_id: '78490949'
        },
        {
          _id: '78490949',
          _score: 8.992924,
          highlight: {
            '2_153_dummy___stack_question___description.en': [
              "And I don't know why setting the last ___su-highlight-start___pointer___su-highlight-end___ to ___su-highlight-start___null___su-highlight-end___ doesn't get rid of them.",
              '= ___su-highlight-start___null___su-highlight-end___){ if (current.val !',
              'Then I overwrite the same list (1 -&gt; 2 -&gt; 4 -&gt; 5 -&gt; 5) and then I delete the ___su-highlight-start___pointer___su-highlight-end___ to the',
              '= num; current = current.next; } // disconnect the last node from the remaining ones current.next = ___su-highlight-start___null___su-highlight-end___'
            ],
            SummaryToDisplay: [
              'true',
              "And I don't know why setting the last <span class='highlight'>pointer</span> to <span class='highlight'>null</span> doesn't get rid of them. = <span class='highlight'>null</span>){ if (current.next = <span class='highlight'>null</span>....",
              '78490949',
              'jav'
            ],
            TitleToDisplay: ['Overwriting and deleting linked list elements'],
            TitleToDisplayString: ['Overwriting and deleting linked list elements']
          },
          metadata: [],
          autosuggestData: [],
          href: 'testHref',
          clientHref: 'abcddddd',
          Id: '78490949',
          objName: 'stack_question',
          objLabel: 'Question',
          sourceName: '2_153_dummy',
          uniqueField: '2_153_dummy_stack_question_78490949',
          sourceLabel: 'dummy',
          solved: 'Unsolved',
          indexedDate: '2024-05-27T06:35:08.729Z',
          sortOrder: null,
          contentTag: 'dummy',
          es_id: '78490949'
        }
      ],
      took: 31
    },
    aggregationsArray: [
      {
        key: '_index',
        label: 'Index',
        values: [
          {
            displayName: 'dummy',
            filterOrder: 0,
            Contentname: '2_153_dummy',
            value: 231,
            immediateParent: '_index',
            parent: '_index',
            childName: 'Index_1',
            level: 1
          },
          {
            displayName: 'dummy2',
            filterOrder: 0,
            Contentname: '2_153_dummy2',
            value: 233,
            immediateParent: '_index',
            parent: '_index',
            childName: 'Index_2',
            level: 1
          },
          {
            merged: true,
            showChild: 1,
            displayName: 'merged_dummy',
            filterOrder: 0,
            Contentname: '2_153_merged_dummy',
            value: 231,
            immediateParent: '_index',
            parent: '_index',
            childName: 'Merged_Index',
            level: 1,
            childArray: [
              {
                displayName: 'merged_dummy1',
                filterOrder: 0,
                Contentname: '2_153_merged_dummy1',
                value: 231,
                immediateParent: '_index',
                parent: '_index',
                childName: 'Merged_Index_1',
                level: 1
              },
              {
                displayName: 'merged_dummy2',
                filterOrder: 0,
                Contentname: '2_153_merged_dummy2',
                value: 233,
                immediateParent: '_index',
                parent: '_index',
                childName: 'Merged_Index_2',
                level: 1
              }
            ]
          }
        ],
        order: 0
      },
      {
        key: '_type',
        label: 'Sources',
        values: [
          {
            displayName: 'Question',
            filterOrder: null,
            Contentname: 'stack_question',
            value: 1,
            immediateParent: '_type',
            parent: '_type',
            childName: 'Sources_1',
            level: 1
          }
        ],
        order: 1,
        sort: 'count_desc'
      },
      {
        key: '2_153_dummy___stack_question___is_answered',
        label: 'Answered',
        values: [
          {
            selected: true,
            Contentname: 'true',
            value: 1,
            immediateParent: '2_153_dummy___stack_question___is_answered',
            parent: '2_153_dummy___stack_question___is_answered',
            childName: 'Answered_1',
            level: 1
          }
        ],
        order: 2,
        sort: 'count_desc'
      },
      {
        key: '2_153_dummy___stack_question___is_answered_nested',
        label: 'Answered_Nested',
        values: [
          {
            selected: true,
            Contentname: 'true_nested_val',
            value: 1,
            immediateParent: '2_153_dummy___stack_question___is_answered',
            parent: '2_153_dummy___stack_question___is_answered',
            childName: 'Answered_1',
            childArray: [
              {
                Contentname: 'true_nested_val_child_level_1',
                childArray: [
                  {
                    Contentname: 'true_nested_val_child_level_2'
                  }
                ]
              }
            ],
            level: 1
          }
        ],
        order: 2,
        sort: 'count_desc'
      },
      {
        key: '2_153_dummy___stack_question___description',
        label: 'Description',
        values: [],
        order: 3,
        sort: 'count_desc'
      },
      {
        key: '2_153_dummy___stack_question___id',
        label: 'id',
        values: [
          {
            selected: true,
            Contentname: '78490949',
            value: 1,
            immediateParent: '2_153_dummy___stack_question___id',
            parent: '2_153_dummy___stack_question___id',
            childName: 'id_1',
            level: 1
          },
          {
            Contentname: '78487609',
            value: 1,
            immediateParent: '2_153_dummy___stack_question___id',
            parent: '2_153_dummy___stack_question___id',
            childName: 'id_1',
            level: 1
          },
          {
            Contentname: '78488631',
            value: 1,
            immediateParent: '2_153_dummy___stack_question___id',
            parent: '2_153_dummy___stack_question___id',
            childName: 'id_1',
            level: 1
          },
          {
            Contentname: '78489272',
            value: 1,
            immediateParent: '2_153_dummy___stack_question___id',
            parent: '2_153_dummy___stack_question___id',
            childName: 'id_1',
            level: 1
          },
          {
            Contentname: '78489614',
            value: 1,
            immediateParent: '2_153_dummy___stack_question___id',
            parent: '2_153_dummy___stack_question___id',
            childName: 'id_1',
            level: 1
          },
          {
            Contentname: '78491216',
            value: 1,
            immediateParent: '2_153_dummy___stack_question___id',
            parent: '2_153_dummy___stack_question___id',
            childName: 'id_1',
            level: 1
          },
          {
            Contentname: '78491859',
            value: 1,
            immediateParent: '2_153_dummy___stack_question___id',
            parent: '2_153_dummy___stack_question___id',
            childName: 'id_1',
            level: 1
          },
          {
            Contentname: '78493268',
            value: 1,
            immediateParent: '2_153_dummy___stack_question___id',
            parent: '2_153_dummy___stack_question___id',
            childName: 'id_1',
            level: 1
          },
          {
            Contentname: '78494395',
            value: 1,
            immediateParent: '2_153_dummy___stack_question___id',
            parent: '2_153_dummy___stack_question___id',
            childName: 'id_1',
            level: 1
          },
          {
            Contentname: '78495616',
            value: 1,
            immediateParent: '2_153_dummy___stack_question___id',
            parent: '2_153_dummy___stack_question___id',
            childName: 'id_1',
            level: 1
          }
        ],
        order: 4,
        sort: 'count_desc'
      },
      {
        key: '2_153_dummy___stack_question___tag',
        label: 'Tag',
        values: [
          {
            selected: true,
            Contentname: 'java',
            value: 1,
            immediateParent: '2_153_dummy___stack_question___tag',
            parent: '2_153_dummy___stack_question___tag',
            childName: 'Tag_1',
            level: 1
          }
        ],
        order: 5,
        sort: 'count_desc'
      },
      {
        key: '2_153_dummy___stack_question___title',
        label: 'Title',
        values: [
          {
            Contentname: 'Overwriting and deleting linked list elements',
            value: 1,
            immediateParent: '2_153_dummy___stack_question___title',
            parent: '2_153_dummy___stack_question___title',
            childName: 'Title_1',
            level: 1
          }
        ],
        order: 6,
        sort: 'count_desc'
      }
    ],
    suggest: {
      simple_phrase: [
        {
          options: []
        }
      ]
    },
    searchClientSettings: {
      similarSearch: false,
      specialSearch: false,
      hiddenFacet: [],
      advertisements: 0,
      autoCompleteInstant: 0,
      recommendations: null,
      autoComplete: '10',
      redirectionUrl: '',
      scrollPagination: false,
      moreResultButton: false,
      pageNoButton: true,
      languageManager: 1,
      ViewedResults: 0,
      preview: false,
      SCsalesforceConsoleConfigurations:
        '{"caseSelection":1,"caseNumberView":1,"searchResultsOpensNewBrowserTab":1}',
      mergeSources: false,
      showMore: false,
      minSummaryLength: 100,
      contentTag: true,
      hideAllContentSources: false,
      smartFacets: false,
      userFeedbackEnabled: {
        contentSearchExp: false,
        searchExp: false,
        conversionExp: false
      },
      gptConfig: {
        gptContext: '',
        gptLinks: [],
        gptActive: false
      }
    },
    merged_facets:
      '[{"facetName":"2_153_dummy___stack_question___is_answered","filterList":["html","node.js"],"filterNewName":"code","showChild":"1","facetDisplayName":"Tag","filterDisplayName":["html","node.js"]}]',
    gptContext: '',
    gptActive: false,
    llmContextId: '23a1a22c-6afd-4aea-98d0-94eb08fc77c4',
    featuredSnippetResult: {
      steps: ['Step 1', 'Step 2', 'Step 3', 'Step 4'],
      header: 'Header',
      questionAnswer: {
        answer: 'test answer',
        short_summary: 'test summary'
      },
      highlight: {
        TitleToDisplayString: ['testFields']
      },
      href: 'http://test.com',
      multiMedia: [
        {
          title: ['testFields'],
          href: 'http://test.com',
          video_url: 'http://test.com/video',
          thumbnail: 'http://test.com/thumbnail',
          image_urls: 'http://test.com/image',
          alt_attributes: 'test alt'
        }
      ]
    },
    metaGraph: {
      link: 'http://example.com',
      title: 'Example Title',
      subtitle: 'Example Subtitle',
      img: 'http://example.com/image.png',
      description: 'Example Description',
      metaFields: [
        { key: 'Field1', value: 'Value1' },
        { key: 'Field2', value: 'Value2' }
      ]
    },
    relatedTiles: [
      {
        tagLine: 'Related Tile 1',
        relatedFields: [{ link: 'http://example.com/related1', heading1: 'Related Heading 1' }]
      }
    ]
  },
  pageRatingResult: {
    searchFeedback: JSON.stringify({
      submitButton: 'Submit Feedback',
      searchEmailId: '',
      followUpToggle: false,
      searchFeedbackToggle: false,
      selectedQuestionFour: 'tes ques 2???',
      selectedSearchHeader: 'how was ur se? test ques 1',
      selectedTextFeedback: 'Would you like to say more ?',
      selectedQuestionThree:
        "Would you have logged a case, if you couldn't find the relevant information?",
      selectedSearchFollowUp: 'Can we follow up on the feedback?',
      selectedSearchTemplate: 'Emoticons',
      selectedSearchSubHeader: 'Did you find what you were looking for?',
      searchFeedbackIconToggle: true,
      selectedMinuteAutoTrigger: '1',
      selectedSearchAcknowledgement: 'Thank You! Your feedback helps.',
      searchFeedbackAutoTriggerSurvey: true,
      selectedBasedActivityAutoTrigger: '2'
    })
  },
  summarizationResult: createMockStream(mockSuccessResponse)
};

export default state;
