include:
  - project: 'searchunify/gitlab-actions' # Use the path   to your common configuration repository
    file: '.gitlab-ci.yml'

sonarqube-check:
  stage: sonarqube
  script:
    - npm i
    - npm test
    - sonar-scanner -Dsonar.projectKey=react-search-client -Dsonar.sources=. -Dsonar.host.url=$SONAR_HOST_URL -Dsonar.login=$SONAR_TOKEN -Dsonar.qualitygate.wait=true -Dsonar.javascript.lcov.reportPaths=coverage/lcov.info -Dsonar.cpd.exclusions=**/*.test.jsx
  rules:
    - if: '$CI_MERGE_REQUEST_TARGET_BRANCH_NAME =~ /^((2[4-9]|[3-9][0-9])-((jun)|(jul)|(aug)|(sep)|(oct)|(nov)|(dec)|(jan)|(feb)|(feb-opensearch)|(mar)|(apr)|(may)))$/'
      when: on_success
build-deploy:
  stage: create_branch
  script:
    - |
      copy_files() {
          local src="$1"
          local dst="$2"
          local file="$3"
          rm -rf "$dst/$file"
          # If it's a file, copy it to the destination
          cp -rf "$src/$file" "$dst"
      }
      create_mr() {
          MR_TITLE="bot: pushing ${1} separate repo changes into ${2} sc service"
          JSON_DATA=$(cat <<EOF
      {
          "source_branch": "${1}",
          "target_branch": "${2}",
          "title": "${MR_TITLE}",
          "description": "${MR_TITLE}"
      }
      EOF
      )
          response=$(curl --request POST \
          --header "PRIVATE-TOKEN: ${4}" \
          --header "Content-Type: application/json" \
          --data "${JSON_DATA}" \
          "https://gitlab.searchunify.com/api/v4/projects/"${3}"/merge_requests"
      )

          if [[ $response == *'Another open merge request already exists for this source branch'* ]]; then
              mr_id=$(echo "$response" | grep -o '![0-9]*' | sed 's/!//')
              echo "https://gitlab.searchunify.com/searchunify/"${5}"/-/merge_requests/"${mr_id}
          elif [[ $response == *'web_url'* ]]; then
              mr=$(echo "$response" | grep -o '"web_url":"[^"]*' | grep 'merge' | sed 's/"web_url":"//')
              echo $mr
          else
              echo "invalid response"
          fi
      }

      close_mr() {
          response=$(curl --silent --request PUT \
          --header "PRIVATE-TOKEN: ${2}" \
          --data "state_event=close" \
          "https://gitlab.searchunify.com/api/v4/projects/${3}/merge_requests/${1}")  
      }

      # Function to check if branch exists
      branch_exists() {
        git ls-remote --heads "https://gitlab-ci-token:${GITLAB_TOKEN}@gitlab.searchunify.com/searchunify/${sc_target_repo}.git" "$1" | grep -q "$1"
      }

      # Echo current working directory
      prev_branch=""
      sc_target_repo="su-custom-search-clients"
      staging_suffix="-bot-staging-react"
      months=("jan" "feb" "mar" "apr" "may" "jun" "jul" "aug" "sep" "oct" "nov" "dec")
      # Array of files/folders to copy
      to_copy=("src"
      "__mocks__"
      ".babelrc"
      ".eslintrc.js"
      ".prettierrc.js"
      ".prettierignore"
      ".stylelintrc.json"
      "buildSearchClient.sh"
      "feedback.css"
      "main.css"
      "searchbox.css"
      "zendesk-console.css"
      "recommendation.css"
      "su__extension.css"
      "jsconfig.json"
      "communication.js"
      "README.md"
      "webpack.config.js"
      "package.json"
      "webpack.production.js")

      # Source and destination directories
      source_dir=".."
      dest_dir="./resources/search_clients_standard/s3Supported/react/codebase/"
      # Echo current working directory
      echo -e "\n[ BASH : INFO ] Current directory: $(pwd)"
      echo -e "\n[ BASH : INFO ]node version is :"
      node --version
      echo -e "\n[ NPM : INSTALL ] npm modules..."
      npm i
      npm run build-all
      # List contents of current directory
      echo -e "\n[ BASH : INFO ] Contents of build directory:"
      ls -lrth build
      echo -e "\n[ GIT : CLONE ] Creating clone of $sc_target_repo repo"
      echo -e "\n[ GIT : BRANCH ] CI_COMMIT_BRANCH = $CI_COMMIT_BRANCH"
      # Clone the repository
      if branch_exists "$CI_COMMIT_BRANCH"; then
        echo "Branch $CI_COMMIT_BRANCH found. Cloning..."
        git clone -b $CI_COMMIT_BRANCH "https://gitlab-ci-token:${GITLAB_TOKEN}@gitlab.searchunify.com/searchunify/${sc_target_repo}.git"
      else
        echo "Branch '$CI_COMMIT_BRANCH' not found. Cloning default branch..."
        git clone "https://gitlab-ci-token:${GITLAB_TOKEN}@gitlab.searchunify.com/searchunify/${sc_target_repo}.git"
      fi
      cd $sc_target_repo
      echo -e "\n[ BASH : INFO ] Current directory: $(pwd)"
      echo -e "\n[ BASH : INFO ] git branch $(git rev-parse --abbrev-ref HEAD 2>/dev/null)"

      # Check if the branch exists locally
      if git show-ref --verify --quiet "refs/heads/${CI_COMMIT_BRANCH}${staging_suffix}"; then
          # Delete the branch locally
          echo -e "\n[ GIT : DELETED ] As branch ${CI_COMMIT_BRANCH}${staging_suffix} was already present in local"
          git branch -D ${CI_COMMIT_BRANCH}${staging_suffix}
      else
          echo -e "\n[ GIT : DELETE SKIPPED ] As branch ${CI_COMMIT_BRANCH}${staging_suffix} was not present in local"
      fi

      # Check if the branch exists remotely
      if git ls-remote --exit-code --heads origin ${CI_COMMIT_BRANCH}${staging_suffix}; then
          # Delete the branch remotely
          echo -e "\n[ GIT : DELETED ] As branch ${CI_COMMIT_BRANCH}${staging_suffix} was already present in remote"
          git push origin --delete ${CI_COMMIT_BRANCH}${staging_suffix}
      else
          echo -e "\n[ GIT : DELETE SKIPPED ] As branch ${CI_COMMIT_BRANCH}${staging_suffix} was not present in remote"
      fi

      # Check if the string matches one of the specified patterns
      if [[ "$CI_COMMIT_BRANCH" =~ (feature|bugfix|fix|bugFix|enhancement)/([^/]+)/.*$ ]]; then
          prev_branch="${BASH_REMATCH[2]}"
          git checkout $prev_branch
      # Get the year and month from the current branch name
      elif [[ "$CI_COMMIT_BRANCH" == "m23" ]]; then
          prev_branch=""
      elif [[ "$CI_COMMIT_BRANCH" == "24-mar" ]]; then
          prev_branch="24-feb-opensearch"
      elif [[ "$CI_COMMIT_BRANCH" == "24-feb-opensearch" ]]; then
          prev_branch="24-feb"
      elif [[ "$CI_COMMIT_BRANCH" == "23-jun" ]]; then
          prev_branch="m23"
      else
          year="${CI_COMMIT_BRANCH:0:2}"
          
          month="${CI_COMMIT_BRANCH:3:3}"

          month_index=$(($(printf '%s\n' "${months[@]}" | grep -n -w "$month" | cut -d':' -f1) - 1))

          if [[ "$month_index" -eq 0 ]]; then
              prev_year=$((year - 1))
              prev_month="${months[11]}"
          else
              prev_year=$year
              prev_month="${months[$((month_index - 1))]}"
          fi

          # Create the previous branch name
          prev_branch="${prev_year}-${prev_month}"
      fi

      echo -e "\n[ BASH : INFO ] git branch $(git rev-parse --abbrev-ref HEAD 2>/dev/null)"

      git checkout -b "${CI_COMMIT_BRANCH}${staging_suffix}"
      echo -e "\n[ GIT : CHECKOUT ] Fresh checkout to ${CI_COMMIT_BRANCH}${staging_suffix}"

      if [[ "$prev_branch" != "" ]]; then
          echo -e "\n[ GIT : MERGE ] merging ${prev_branch} into ${CI_COMMIT_BRANCH}\n"
          git merge --no-ff --no-edit --allow-unrelated-histories --no-commit "origin/${prev_branch}" || true
      fi
      for item in "${to_copy[@]}"; do
          echo -e "\ncopying $item from $source_dir to $dest_dir"
          copy_files "$source_dir" "$dest_dir" "$item"
      done
      echo -e "\n[ BASH : INFO ] copying build files from ../build to $dest_dir"
      cp -rf ../build/*.js $dest_dir
      git add .
      git commit -am "bot: pushing build file and src changes in sc service $CI_COMMIT_BRANCH from react-search-client $CI_COMMIT_BRANCH branch" || true
      echo -e "\n[ GIT : COMMIT ]code committed, pushing..."
      git push origin "${CI_COMMIT_BRANCH}${staging_suffix}" || true
      echo -e "\n[ GIT : PUSH ]code pushed creating mr now"

      if [[ "$CI_COMMIT_BRANCH" =~ (feature|bugfix|fix|bugFix|enhancement)/([^/]+)/.*$ ]]; then
          mr_id=$(create_mr "${CI_COMMIT_BRANCH}${staging_suffix}" "$prev_branch" "68" "$GITLAB_TOKEN" "$sc_target_repo")
          mr_id_number=$(echo "$mr_id" | awk -F'/' '{print $NF}')
          close_mr "$mr_id_number" "$GITLAB_TOKEN" "68"
      else
          mr_id=$(create_mr "${CI_COMMIT_BRANCH}${staging_suffix}" "$CI_COMMIT_BRANCH" "68" "$GITLAB_TOKEN" "$sc_target_repo")
      fi


      echo -e "\n[ BASH : INFO ] mr created $mr_id\n[ BASH : INFO ]node version is :"
      node --version
      echo -e "\n[ BASH : INFO ] done"
  rules:
    - if: '$CI_COMMIT_BRANCH =~ /^\d{2}-(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)|release|m23|24-feb-opensearch$|^.*(feature|bugfix|fix|bugFix|enhancement).*$/'
      when: always
