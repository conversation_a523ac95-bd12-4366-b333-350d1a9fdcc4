/* eslint-disable no-undef */
const path = require('path');
const HtmlWebpackPlugin = require('html-webpack-plugin');
let webpack = require('webpack');
require('dotenv').config({ path: __dirname + '/.env' });
let lastChecked;
module.exports = () => {
  console.log('Current Build mode: ', process.env.BUILD_MODE);
  console.log('Current Build component: ', process.env.BUILD_COMPONENT);
  let entry;
  let plugins = [
    new webpack.ProvidePlugin({
      React: 'react',
      Promise: 'es6-promise'
    })
  ];
  if (process.env.BUILD_COMPONENT == 'search') {
    console.log('----------------Building Search Component-----------');
    plugins.unshift(
      new HtmlWebpackPlugin({
        filename: 'index.html',
        template: path.join(__dirname, 'src', 'index.html'),
        chunks: ['main']
      })
    );
    entry = {
      main: path.join(__dirname, 'src', 'index.js')
    };
  } else if (process.env.BUILD_COMPONENT == 'autocomplete') {
    console.log('----------------Building Autocomplete Component-----------');
    plugins.unshift(
      new HtmlWebpackPlugin({
        filename: 'autocomplete.html',
        template: path.join(__dirname, 'src', 'autocomplete.html'),
        chunks: ['searchbox']
      })
    );
    entry = {
      searchbox: path.join(__dirname, 'src', 'searchbox.js')
    };
  } else if (process.env.BUILD_COMPONENT == 'recommendation') {
    console.log('----------------Building recommendation Component-----------');
    plugins.unshift(
      new HtmlWebpackPlugin({
        filename: 'recommendation.html',
        template: path.join(__dirname, 'src', 'recommendation.html'),
        chunks: ['recommendation']
      })
    );
    entry = {
      recommendation: path.join(__dirname, 'src', 'recommendation.js')
    };
  } else if (process.env.BUILD_COMPONENT == 'pagerating') {
    console.log('----------------Building page rating Component-----------');
    plugins.unshift(
      new HtmlWebpackPlugin({
        filename: 'feedback.html',
        template: path.join(__dirname, 'src', 'feedback.html'),
        chunks: ['feedback']
      })
    );
    entry = {
      feedback: path.join(__dirname, 'src', 'feedback.js')
    };
  }
  return {
    mode: 'production',
    entry,
    output: {
      publicPath: '',
      path: path.join(__dirname, 'build'),
      filename: '[name].js'
    },
    resolve: {
      extensions: ['.jsx', '.js'],
      modules: [path.resolve(__dirname, 'src'), 'node_modules']
    },
    module: {
      rules: [
        {
          test: /\.(js|jsx)$/,
          exclude: /node_modules/,
          use: ['babel-loader']
        },
        {
          test: /\.(css|scss)$/,
          use: [
            'style-loader',
            // creates style nodes from JS strings
            'css-loader'
            // translates CSS into CommonJS
            // 'sass-loader'
            // compiles Sass to CSS, using Node Sass by default
          ]
        },
        {
          test: /\.(jpg|jpeg|png|gif|mp3|svg)$/,
          loaders: ['file-loader']
        }
      ]
    },
    plugins: [
      new webpack.ProgressPlugin({
        handler(percentage, message, ...args) {
          console.info(message, ...args);
          const percent = (percentage * 100).toFixed(2);
          if (percent % 2 === 0 && lastChecked !== percent) {
            console.log('\n==>' + percent + '<==\n');
            lastChecked = percent;
          }
        },
        modules: true
      })
    ],

    performance: {
      hints: false
    },
    optimization: {
      minimize: process.env.BUILD_MODE === 'development' ? false : true
    },
    devtool: process.env.BUILD_MODE === 'development' ? 'inline-source-map' : ''
  };
};
