#####################################################################
###   Purpose : shell script to build react search client         ###
###   Author : kanav.sharma                                       ###
###   Last Updated : 08/01/2024 [dd/mm/yyyy]                      ###
#####################################################################


#!/bin/sh

#!/bin/sh
echo "******************** Setting up Node modules ********************"
# if node module folder does not exist then npm install

# Function to update timestamp based on filename
update_timestamp() {
    filename="$1"
    timestamp="$(date -u +"%a, %d %b %Y %T GMT")"
    sed -i -E "s/\"$filename\": \".*\"/\"$filename\": \"$timestamp\"/" ${PWD}/src/buildTimestamp.json
}

if [ $2 == 'true' ]
then
    echo "******************** Removing Node modules ********************"
    rm -rf -v node_modules.tar.gz package-lock.json.zip node_modules/.bin package-lock.json
    cp "${PWD}/../../search_clients_standard/s3Supported/react/codebase/webpack.production.js" ${PWD}
    cp "${PWD}/../../search_clients_standard/s3Supported/react/codebase/webpack.development.js" ${PWD}
    cp "${PWD}/../../search_clients_standard/s3Supported/react/codebase/webpack.development-searchbox.js" ${PWD}
    cp "${PWD}/../../search_clients_standard/s3Supported/react/codebase/webpack.development-recommendation.js" ${PWD}
    cp "${PWD}/../../search_clients_standard/s3Supported/react/codebase/webpack.development-pagerating.js" ${PWD}
    cp "${PWD}/../../search_clients_standard/s3Supported/react/codebase/package.json" ${PWD}
    echo "******************** Node modules removed ********************"
fi

if [ ! -d "${PWD}/node_modules" ] || [ $2 == 'true' ]
then
    echo "Directory node_modules package-lock.json DOES NOT exists."
    cp "${PWD}/../../package-lock.json.zip" ${PWD}
    cp "${PWD}/../../node_modules.tar.gz" ${PWD}
    unzip -o package-lock.json.zip
    tar -xzf node_modules.tar.gz
    rm -rf node_modules.tar.gz package-lock.json.zip
fi
echo "******************** Node modules installed ********************"

echo "******************** $1 app build started ********************"
# npm i webpack@4.42.0
npm run build || exit 1

#post build steps - move files to parent loc
echo -e "\nUpdating $1 "
if [ $1 != 'preview-main.js' ];then
     cp build/$1 .
     update_timestamp $1
else
     cp build/main.js preview/main.js
     sed "1s/^/ var scConfiguration = /* scUpdateStart */<%- include('scConfiguration.json') %>/* scUpdateStop */\n/" preview/main.js
     cp src/redux/variables-backup.js src/redux/variables.js
     rm src/redux/variables-backup.js
fi
echo -e "\n******************** Updated $1 *******************"
rm -rf build
echo -e "\n******************** Build steps complete ********************"

################ End of Script #################
