{"name": "react-search-client", "version": "0.1.0", "private": true, "dependencies": {"@material-ui/core": "^4.12.3", "@material-ui/lab": "^4.0.0-alpha.45", "babel-polyfill": "^6.26.0", "babel-preset-react-app": "^9.1.2", "core-js": "^3.6.4", "dotenv": "^8.2.0", "es6-promise": "^4.2.8", "history": "^4.10.1", "i": "^0.3.7", "i18next": "^19.3.3", "i18next-browser-languagedetector": "^4.0.2", "isomorphic-fetch": "^2.2.1", "prop-types": "^15.7.2", "react": "^16.12.0", "react-app-polyfill": "^1.0.6", "react-dom": "^16.12.0", "react-i18next": "^11.3.4", "react-redux": "^7.1.3", "react-router-redux": "^4.0.8", "redux": "^4.0.5", "styled-components": "^5.0.1", "webpack": "4.42.0"}, "scripts": {"start": "webpack-dev-server --open --config webpack.development.js", "searchbox": "webpack-dev-server --open --config webpack.development-searchbox.js", "a11y": "node scripts/a11y", "feedback": "webpack-dev-server --open --config  webpack.development-pagerating.js", "recommendation": "webpack-dev-server --open --config webpack.development-recommendation.js", "build-dev": "webpack --config webpack.production-dev.js", "build": "webpack --config webpack.production.js", "build-all": "cross-env BUILD_COMPONENT=search webpack --config webpack.production.js && cross-env BUILD_COMPONENT=autocomplete webpack --config webpack.production.js && cross-env BUILD_COMPONENT=recommendation webpack --config webpack.production.js && cross-env BUILD_COMPONENT=pagerating webpack --config webpack.production.js", "test": "jest", "lint": "eslint . --ext .js,.jsx,.html --fix", "lintcss": "stylelint '**/*.css' --fix", "format": "prettier --write .", "lint:format": "npm run lint && npm run lintcss && npm run format", "prepare": "if [ \"$CI\" != \"true\" ]; then husky install; fi"}, "eslintConfig": {"extends": "react-app"}, "browserslist": {"production": ["ie 11", ">0.2%", "not dead", "not op_mini all"], "development": ["ie 11", "last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/core": "^7.21.0", "@babel/node": "^7.21.0", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/preset-env": "^7.21.0", "@babel/preset-react": "^7.21.0", "axe-core": "^4.10.3", "babel-loader": "^8.0.6", "cheerio": "1.0.0-rc.12", "cross-env": "^7.0.3", "css-loader": "^3.4.2", "enzyme": "^3.11.0", "enzyme-adapter-react-16": "^1.15.8", "eslint": "^7.32.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-html": "^6.2.0", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-react": "^7.24.0", "eslint-plugin-react-hooks": "^4.3.0", "file-loader": "^5.1.0", "html-webpack-plugin": "^3.2.0", "husky": "^6.0.0", "jest": "^26.0.0", "path": "^0.12.7", "prettier": "^2.3.2", "puppeteer": "^13.7.0", "react-test-renderer": "^18.3.1", "style-loader": "^1.1.3", "stylelint": "^13.13.1", "stylelint-config-prettier": "^8.0.2", "stylelint-config-standard": "^22.0.0", "url-loader": "^4.1.1", "webpack-cli": "^3.3.12", "webpack-dev-server": "^3.11.2"}}