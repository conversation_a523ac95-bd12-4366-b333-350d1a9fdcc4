module.exports = {
  root: true,
  parserOptions: {
    ecmaVersion: 2020, // Allows for the parsing of modern ECMAScript features
    sourceType: 'module', // Allows for the use of imports
    ecmaFeatures: {
      jsx: true // Allows for the parsing of JSX
    }
  },
  settings: {
    react: {
      version: '16.12.0' // Specify your React version
    }
  },
  env: {
    browser: true, // Enables browser global variables
    es2021: true, // Enables ES2021 features
    node: true // Enables Node.js global variables and Node.js scoping
  },
  extends: [
    'eslint:recommended', // Use the built-in recommended rule set
    'plugin:react/recommended', // Use the recommended rules from eslint-plugin-react
    'plugin:react-hooks/recommended', // Use the recommended rules from eslint-plugin-react-hooks
    'plugin:prettier/recommended' // Enables eslint-plugin-prettier to display prettier errors as ESLint errors
  ],
  plugins: ['html'],
  rules: {
    // Additional rules or overrides
    'react-hooks/rules-of-hooks': 'off',
    'react-hooks/exhaustive-deps': 'off',
    'no-case-declarations': 'off'
  },
  overrides: [
    {
      files: ['*.jsx', '*.js'],
      rules: {
        'react-hooks/rules-of-hooks': 'off',
        'react-hooks/exhaustive-deps': 'off',
        'no-case-declarations': 'off'
      }
    }
  ],
  ignorePatterns: ['node_modules', '.husky', 'build', 'coverage']
};
