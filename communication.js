const triggerSearch = (e) => {
  if (e.key == 'Enter') {
    setTimeout(function () {
      if (!window.analyticsRecordAlready) {
        document.getElementById('search-box-search').value =
          document.getElementById('search-box-autocomplete').value;
        document.getElementById('hit-me').click();
      }
      window.analyticsRecordAlready = false;
    });
  } else if (e.type == 'focusin') {
    setTimeout(function () {
      document.getElementById('search-box-autocomplete').value =
        document.getElementById('search-box-search').value;
      document.getElementById('search-box-autocomplete').blur();
    });
  }
};
let communication = setInterval(function () {
  let search_present = document.getElementById('search-box-search') ? true : false;
  let searchbox_present = document.getElementById('search-box-autocomplete') ? true : false;
  if (searchbox_present && search_present) {
    let input = document.getElementById('search-box-autocomplete');
    input.addEventListener('keyup', triggerSearch);
    input = document.getElementById('search-box-search');
    input.addEventListener('focusin', triggerSearch);
    document.getElementById('search-box-autocomplete').value = getParameterByName('searchString');
    clearInterval(communication);
  }
}, 100);

function getParameterByName(name, url) {
  if (!url) url = window.location.href;
  name = name.replace(/[[\]]/g, '\\$&');
  let regex = new RegExp('[?&]' + name + '(=([^&#]*)|&|#|$)'),
    results = regex.exec(url);
  if (!results) return null;
  if (!results[2]) return '';
  return decodeURIComponent(results[2].replace(/\+/g, ' '));
}
